<script setup>
import { onMounted, ref, nextTick } from 'vue'
import {
    Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
    BattAdd, BattEdit, BattDelete, BattByid, BatGetpaged, BattSetstatuz, Getattachmentconfigbyid
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import AppBox from "@/components/Approve/AppBox.vue";
import TablePage from '@/components/TablePagination/index.vue' //分页
import { integerLimit, UploadFileTypeList } from "@/utils/index.js";
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const WhetherList = ref([])
const ModuleList = ref([])
const filters = ref({ pageIndex: 1, pageSize: 10, isFirst: true, sortModel: [{ SortCode: "Sequence", SortType: "asc" }] })
const dialogVisible = ref(false)
const dialogData = ref({})
const formData = ref({})
const isAdd = ref(false)
const refForm = ref()
const ruleForm = {
    ModuleType: [
        { required: true, message: '请选择模块名称', trigger: 'change' },
    ],
    Name: [
        { required: true, message: '请输入附件名称', trigger: 'change' },
    ],
    IsFilled: [
        { required: true, message: '请选择是否必填', trigger: 'change' },
    ],
    Sequence: [
        { required: true, message: '请输入附件排序', trigger: 'change' },
    ],
    UploadFileType: [
        { required: true, message: '请选择附件类型', trigger: 'change' },
    ],
    FileCategory: [
        { required: true, message: '请输入类型编号', trigger: 'change' },
    ],
}
//加载数据
onMounted(() => {
    HandleTableData(true)
    GetattachmentconfigbyidUser()
})
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.Name = undefined
    filters.value.IsFilled = undefined
    filters.value.ModuleType = undefined
    filters.value.pageIndex = 1
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 修改
const HandleEdit = (row) => {
    isAdd.value = false
    BattByidUser(row.Id)
    dialogVisible.value = true
}
// 添加
const HandleAdd = () => {
    isAdd.value = true
    dialogVisible.value = true
    nextTick(() => {
        dialogData.value = {}
        refForm.value.resetFields()
    })
}
const moduleChange = (e) => {
    dialogData.value.ModuleName = ModuleList.value.filter(t => t.value == e)[0].label
}
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
// 确认 提交指标信息
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        formData.value.Statuz = 1;
        formData.value.ModuleType = Number(dialogData.value.ModuleType);//模块Id
        formData.value.ModuleName = dialogData.value.ModuleName;//模块名称
        formData.value.FileCategory = dialogData.value.FileCategory;//模块Id
        formData.value.Name = dialogData.value.Name;//附件名称
        formData.value.IsFilled = Number(dialogData.value.IsFilled);//是否必填
        formData.value.Sequence = Number(dialogData.value.Sequence);//排序
        formData.value.Memo = dialogData.value.Memo;//附件描述
        if (dialogData.value.MaxFileNumber) {
            formData.value.MaxFileNumber = Number(dialogData.value.MaxFileNumber);//最大个数
        } else {
            formData.value.MaxFileNumber = undefined
        }
        if (dialogData.value.FileSize) {
            formData.value.FileSize = Number(dialogData.value.FileSize);//大小
        } else {
            formData.value.FileSize = undefined
        }
        formData.value.UploadFileType = dialogData.value.UploadFileType.join('')//上传文件类型

        if (isAdd.value) {
            formData.value.Id = undefined
            BattAddUser()
        } else {
            formData.value.Id = dialogData.value.Id;//Id
            BattEditUser()
        }
    })
}
// 新增
const BattAddUser = (row) => {
    BattAdd(formData.value).then((res) => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            HandleTableData()
            ElMessage.success(res.data.msg || '新增成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 修改
const BattEditUser = (row) => {
    BattEdit(formData.value).then((res) => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            HandleTableData()
            ElMessage.success(res.data.msg || '修改成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const BattByidUser = (id) => {
    BattByid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dialogData.value = rows;
            dialogData.value.MaxFileNumber = rows.MaxFileNumber || '';//最大个数
            dialogData.value.FileSize = rows.FileSize || '';//最大个数
            dialogData.value.ModuleType = String(rows.ModuleType) //所属模块名称
            let arr = rows.UploadFileType.split('.')
            let ary = arr.filter(t => t != '')
            ary = ary.map(t => '.' + t)
            if (ary.indexOf('.jpg') > -1) {
                ary = ary.filter(t => t != '.jpg' && t != '.jpeg' && t != '.png')
                ary.push('.jpg.jpeg.png')

            }
            if (ary.indexOf('.doc') > -1) {
                ary = ary.filter(t => t != '.doc' && t != '.docx')
                ary.push('.doc.docx')

            }
            if (ary.indexOf('.xls') > -1) {
                ary = ary.filter(t => t != '.xlsx' && t != '.xls')
                ary.push('.xls.xlsx')
            }
            dialogData.value.UploadFileType = ary//上传文件类型

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定删除吗?')
        .then(() => {
            BattDelete({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
const GetattachmentconfigbyidUser = () => {
    Getattachmentconfigbyid({
        id: 0, isFirst: true
    })
        .then((res) => {
            // console.log("附件配置", res.data.data.other)
            const { other } = res.data.data
            if (other) {
                ModuleList.value = other.OpinionStatuz
            }
        })
}

//列表
const HandleTableData = (isFirst) => {
    if (isFirst) {
        filters.value.pageIndex = 1
        filters.value.IsFilled = undefined
        filters.value.Name = undefined
    }
    filters.value.isFirst = isFirst
    BatGetpaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            // console.log("备案附件列表", res.data.data)
            const { rows, total, other } = res.data.data
            tableData.value = rows || [];
            tableTotal.value = Number(total)
            if (isFirst) {
                WhetherList.value = other.WhetherList
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 修改状态
const HandleSwitchChange = (e, row) => {
    BattSetstatuz({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            if (e == 1) {
                ElMessage.success('启用成功')
            } else {
                ElMessage.success('禁用成功')
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.ModuleType" placeholder="模块名称" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in ModuleList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsFilled" placeholder="是否必填" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in WhetherList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="附件名称/附件备注文字"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="ModuleName" label="模块名称" min-width="100"></el-table-column>
            <el-table-column prop="Name" label="附件名称" min-width="120"></el-table-column>
            <el-table-column prop="IsFilled" label="是否必填" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.IsFilled == 1 ? '是' : '否' }}
                </template>
            </el-table-column>
            <el-table-column prop="Sequence" label="附件排序" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Memo" label="附件备注文字" min-width="160"></el-table-column>
            <el-table-column label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <span
                        v-if="row.FileCategory == '103001' || row.FileCategory == '103002' || row.FileCategory == '103003' || row.FileCategory == '103004'"
                        style="color: #67C23A;">启用</span>
                    <el-switch v-else v-model="row.Statuz" :active-value="1" :inactive-value="0" inline-prompt
                        active-text="启" inactive-text="禁" style="--el-switch-off-color: #ff4949"
                        @change="HandleSwitchChange($event, row)" />
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button
                        v-if="row.FileCategory != '103001' && row.FileCategory != '103002' && row.FileCategory != '103003' && row.FileCategory != '103004'"
                        type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="isAdd ? '添加指标' : '修改指标信息'" width="600px">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="140px"
                    status-icon>
                    <el-form-item label="模块名称：" prop="ModuleName">
                        <el-select v-model="dialogData.ModuleType" placeholder="模块名称" @change="moduleChange">
                            <el-option v-for="item in ModuleList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="类型编号：" prop="FileCategory">
                        <el-input v-model="dialogData.FileCategory" @input="integerLimitInput($event, 'FileCategory')"
                            :disabled="!isAdd" placeholder="请输入类型编号：如10201"></el-input>
                    </el-form-item>
                    <el-form-item label="附件名称：" prop="Name">
                        <el-input v-model="dialogData.Name" placeholder="请输入附件名称"></el-input>
                    </el-form-item>
                    <el-form-item label="是否必填：" prop="IsFilled">
                        <el-radio-group v-model="dialogData.IsFilled">
                            <el-radio :value="1">是</el-radio>
                            <el-radio :value="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="最大附件大小(M)：">
                        <el-input v-model="dialogData.FileSize" placeholder="如不填写大小，则限制最大为10M"
                            @input="integerLimitInput($event, 'FileSize')"></el-input>
                    </el-form-item>
                    <el-form-item label="附件最大数量：">
                        <el-input v-model="dialogData.MaxFileNumber" placeholder="如果不做限制请置空"
                            @input="integerLimitInput($event, 'MaxFileNumber')"></el-input>
                    </el-form-item>
                    <el-form-item label="附件类型：" prop="UploadFileType">
                        <el-select v-model="dialogData.UploadFileType" multiple placeholder="附件类型">
                            <el-option v-for="item in UploadFileTypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序：" prop="Sequence">
                        <el-input v-model="dialogData.Sequence" type="number" placeholder="请输入排序数字"></el-input>
                    </el-form-item>
                    <el-form-item label="附件备注文字：">
                        <el-input type="textarea" v-model="dialogData.Memo" :autosize="{ minRows: 2, maxRows: 6 }"
                            placeholder="请输入附件备注文字"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>