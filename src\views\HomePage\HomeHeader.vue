<script setup>
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Button, <PERSON> } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import avatar from '@/assets/img/default.png'
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores';
import router from '@/router'
import { onePage } from "@/utils/index.js";
import { useRoute } from 'vue-router'
const userStore = useUserStore()
const route = useRoute()
const props = defineProps({
    Name: {
        type: String,
        default: ''
    }
})
const Name = ref('')
const serviceData = ref({})
const isPass = ref(false)
onMounted(() => {
    Name.value = props.Name
    serviceData.value = userStore.serviceData

    // 判断是否只存在班主任角色，如果是，则不显示修改密码
    if (userStore.userInfo && userStore.userInfo.RoleIds) {
        if (userStore.userInfo.RoleIds.length == 1 && userStore.userInfo.RoleIds[0] == 370) {
            isPass.value = true
        } else {
            isPass.value = false
        }
    }
})

const handleHomePage = () => {
    // 获取当前账号第一个权限页面路径
    let path = ''
    if (userStore.platformType == 2) {
        if (userStore.userInfo.UnitType === 0 || (userStore.userInfo.UnitType > 0 && !['1200', '2200', '3200'].some(t => userStore.userInfo.RoleIds.includes(t)))) {
            path = onePage(userStore.menu)
        } else {
            // 工作流管理平台：首页
            path = 'approval/home/<USER>'
        }
    }
    else if (userStore.platformType == 1) {
        if (![1, 2, 3].includes(userStore.userInfo.UnitType)) {
            path = onePage(userStore.menu)
        } else {
            // 校服管理平台：内部首页
            path = 'uniform/home/<USER>'
        }
    }
    else {
        path = onePage(userStore.menu)
    }
    userStore.setOnePage(path)//存储起来用于tag标签
    if (userStore.currentPage) {
        router.push({ path: userStore.currentPage })
    } else {
        router.push({ path: path })
    }
}
const loginBtn = async (key) => {
    router.push(`${key}`)
}
// 其他
const handlePage = async (key) => {
    if (key == route.path) return
    router.push(`${key}`)
}
// 其他
const handleCommand = async (key, name) => {
    if (key === 'logout') {
        // 退出操作
        ElMessageBox.confirm('你确认要进行退出吗?', '温馨提示')
            .then(() => {
                // 清除本地的数据 (token + user信息)
                userStore.logout()
                // console.log("退出登录", key)
                router.push('/')
            })
            .catch((err) => {
                console.info(err)
            })
    } else {
        // 跳转操作
        router.push(key)
    }
}
// 子组件传参:校服展示
const emit = defineEmits(['sendData', 'exhibitionName']);
// 校服展示
const ExhibitionSearch = () => {
    if (route.path != '/exhibition') {
        if (!Name.value) return
        router.push({ path: "./exhibition", query: { Name: Name.value } })
    } else {
        emit('exhibitionName', { Name: Name.value });
    }
}
// 校服展示：清空
const clearSearch = () => {
    if (route.path == '/exhibition') {
        emit('exhibitionName', { Name: Name.value });
    }
}

// 查看资讯列表
const listClick = (item) => {
    if (route.path == '/articlelist') {
        emit('sendData', { code: 1002 });
    } else {
        router.push({ path: '/articlelist', query: { code: 1002 } })
    }
}

</script>
<template>
    <div class="header">
        <div class="headerDiv">
            <div class="header-left">
                <img src="@/assets/img/xfptlogo.png" alt="">
                <span>{{ userStore.defaultSet['8002_DLYM_BJ'] || '中小学校服管理与备案平台' }}</span>
            </div>
            <div v-if="userStore.platformType == 1">
                <el-input v-model="Name" clearable style="width: 280px" placeholder="学校/企业名称" @clear="clearSearch">
                    <template #append>
                        <el-button :icon="Search" @click="ExhibitionSearch" />
                    </template>
                </el-input>
            </div>
            <div class="header-right">
                <ul class="header-nav" :style="{ width: userStore.platformType == 1 ? '400px' : '300px' }">
                    <li v-if="userStore.platformType == 1" :class="route.path == '/' ? 'liHover' : ''"
                        @click="handlePage('/')">首页
                    </li>
                    <li v-if="userStore.platformType == 1" :class="route.path == '/exhibition' ? 'liHover' : ''"
                        @click="handlePage('/exhibition')">校服展示
                    </li>
                    <li :class="route.path == '/information' ? 'liHover' : ''" @click="handlePage('/information')">平台资讯
                    </li>
                    <li @click="listClick">联系客服</li>
                    <li v-if="userStore.token && (!userStore.userInfo.UnitStatus || userStore.userInfo.UnitStatus == 1)"
                        @click="handleHomePage">我的工作</li>
                    <li v-if="userStore.token && userStore.userInfo.UnitStatus == 2"
                        :class="route.path == '/unitauthentic' ? 'liHover' : ''" @click="handlePage('/unitauthentic')">
                        单位认证</li>
                    <li v-if="!userStore.token"
                        style="width: 50px; border-right: none;text-align: center;padding-left: 10px;">
                        <span v-if="userStore.platformType == 1" @click="loginBtn('/login')">登录</span>
                        <span v-if="userStore.platformType == 2 || userStore.platformType == 3"
                            @click="loginBtn('/')">登录</span>
                    </li>
                    <li v-if="!userStore.token && userStore.platformType == 1"
                        style="width: 50px;border-right: none; text-align: center;">
                        <span @click="loginBtn('/reg')">注册</span>
                    </li>
                </ul>
                <div v-if="userStore.token" style="width: 160px;text-align: right;">
                    <el-dropdown placement="bottom-end">
                        <span class="el-dropdown__box" style="display: flex; align-items: center; ">
                            <span style="padding-right: 10px;cursor: default;">
                                {{ userStore.userInfo.Name }}</span>
                            <el-avatar :src="userStore.userInfo.HeadPortrait || avatar" />
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item v-if="userStore.userInfo.UnitStatus != 2" :icon="User"
                                    @click="handleCommand('/user/my/useredit', '个人信息')">个人信息</el-dropdown-item>
                                <el-dropdown-item v-if="userStore.userInfo.UnitStatus != 2 && !isPass" :icon="Crop"
                                    @click="handleCommand('/user/my/changepass', '修改密码')">修改密码</el-dropdown-item>
                                <el-dropdown-item :icon="SwitchButton"
                                    @click="handleCommand('logout', '退出登录')">退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.header {
    background-color: #ffffff;
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    z-index: 999;

    :deep(.el-input-group__append) {
        background-color: #f06624;
        color: #ffffff;
        box-shadow: 0 0 0 1px #f06624 inset;
    }

    :deep(.el-input__wrapper.is-focus) {
        box-shadow: 0 0 0 1px #f06624 inset;
    }

    .input-with-select {
        border-color: #f06624;
    }
}

.headerDiv {
    width: 1380px;
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    height: 50px;
    line-height: 50px;
    min-width: 300px;
    color: #f26300;
    font-size: 22px;
    font-weight: bold;
    display: flex;
    align-items: center;

    img {
        width: 25px;
        height: 25px;
        margin-right: 10px;
    }

}

.header-right {
    display: flex;

    align-items: center;

    .header-nav {
        margin: 0;
        width: 400px;
        height: 50px;
        display: flex;
        align-items: center;
        color: #2f2f2f;
        font-size: 14px;

        li {
            width: 100px;
            height: 100%;
            text-align: center;
            list-style-type: none;
            line-height: 50px;
            cursor: pointer;
        }

        li:hover {
            color: #f26300;

        }
    }


}

.liHover {
    color: #f26300;
}

.header-top {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;



}

.el-tooltip__trigger:focus {
    outline: none; // unset 这个也行
}
</style>
