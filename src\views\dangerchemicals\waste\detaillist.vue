<script setup>
defineOptions({
    name: 'dangerchemicalswastedetaillist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Back
} from '@element-plus/icons-vue'
import {
    DcWasteDisposalDetailFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 50 })
const summation = ref({})
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {

    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = '总计';
            return;
        }
        if (column.property == 'Num') {
            sums[index] = summation.value.Num;
            return;
        }
    });
    console.log("sums", sums)
    return sums;
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    filters.value.WasteDisposalId = route.query.Id
    DcWasteDisposalDetailFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            summation.value = other[0]
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">处置批次：{{ route.query.batchNo }}
        </div>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            :summary-method="getSummaries" show-summary header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="OneClassName" label="一级分类" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="二级分类" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"> </el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="140" show-overflow-tooltip></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>