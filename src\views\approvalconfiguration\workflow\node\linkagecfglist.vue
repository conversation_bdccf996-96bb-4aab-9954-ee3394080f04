<script setup>
defineOptions({
    name: 'nodelinkagecfglist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
    PostLinkAgeHostList
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { integerLimit, tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})
// 配置
const HandleConfig = (row) => {
    router.push({ path: "./linkagecfgset", query: { id: row.Id } })
}
//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    filters.value.ProcessNodeId = route.query.ProcessNodeId
    PostLinkAgeHostList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    nextTick(() => {
        HandleTableData()
    })
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="字段Code/字段名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="ParentName" label="父级名称" min-width="160" align="center"></el-table-column>
            <el-table-column prop="ParentCode" label="父级Code" min-width="160"></el-table-column>
            <el-table-column prop="FieldName" label="子级名称" min-width="160"></el-table-column>
            <el-table-column prop="FieldCode" label="子级Code" min-width="120" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleConfig(row)">配置</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>