<script setup>
defineOptions({
    name: 'processmessageconfig'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Tools, Search, Refresh, FolderAdd
} from '@element-plus/icons-vue'
import {
    PostProcessMsgConfigList, PostProcessMsgConfigAdd, PostProcessMsgConfigEdit, PostProcessMsgConfigSetSatuz, GetProcessMsgConfigById
} from '@/api/workflow.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const msgNoList = ref([])
const processNodeList = ref([])
const isSet = ref(false)
const ruleForm = {
    ProcessNodeId: [
        { required: true, message: '请选择节点名称', trigger: 'change' },
    ],
    MsgCode: [
        { required: true, message: '请选择消息编号', trigger: 'change' },
    ],
    SwitchType: [
        { required: true, message: '请选择开关类型', trigger: 'change' },
    ]
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true)
    }
})

onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true)
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 批量设置
const HandleSetAll = (row) => {
    isSet.value = true
    dialogVisible.value = true
    formData.value = {}
    nextTick(() => {
        refForm.value.resetFields()
        formData.value = {
            Statuz: 1,
        }
    })
}

// 新增
const HandleAdd = () => {
    isSet.value = false
    dialogVisible.value = true
    formData.value = {}
    nextTick(() => {
        refForm.value.resetFields()
        formData.value = {
            Statuz: 1,
            MainSwitch: 1,
            MsgSwitch: 1,
            WeChatSwitch: 1,
            ClientIsShow: 1,
            WeChatTemplateId: '',
        }
    })
}
// 修改
const HandleEdit = (row) => {
    isSet.value = false
    GetProcessMsgConfigById({ id: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows
            dialogVisible.value = true
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (formData.value.Id) {
            // 修改
            let data = {
                Id: formData.value.Id,
                MsgExplain: formData.value.MsgExplain,
                MsgShowExplain: formData.value.MsgShowExplain,
                MainSwitch: formData.value.MainSwitch,
                MsgSwitch: formData.value.MsgSwitch,
                MsgTemplate: formData.value.MsgTemplate,
                WeChatSwitch: formData.value.WeChatSwitch,
                WeChatTemplateId: formData.value.WeChatTemplateId,
                WeChatTemplate: formData.value.WeChatTemplate,
                WeChatRedirectPage: formData.value.WeChatRedirectPage,
                ClientIsShow: formData.value.ClientIsShow,
            }

            PostProcessMsgConfigEdit(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '申领成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })

        } else {
            // 新增
            let data = {
                ProcessId: route.query.id,
                ProcessNodeId: formData.value.ProcessNodeId,
                ProcessNodeName: processNodeList.value.filter(item => item.value == formData.value.ProcessNodeId)[0].label,
                MsgCode: formData.value.MsgCode,
            }
            PostProcessMsgConfigAdd(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '申领成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        }
    })
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.ProcessNodeName = undefined
    // filters.value.MsgCode = undefined
    HandleTableData()
}
// 列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    filters.value.ProcessId = route.query.id
    PostProcessMsgConfigList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (isFirst) {
                msgNoList.value = other.listMsgNo;//编号
                processNodeList.value = other.listProcessNode;//节点
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 开关
const HandleSwitchChange = (e, row, type) => {
    let dataForm = {
        ListId: [row.Id],
        SwitchType: type,
        Statuz: e,
    }
    PostProcessMsgConfigSetSatuzUser(dataForm)
}
// 批量设置开关
const HandleSwitchAllChange = () => {
    let dataForm = {
        ListId: selectRows.value.map(item => item.Id),
        SwitchType: formData.value.SwitchType,
        Statuz: formData.value.Statuz,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        PostProcessMsgConfigSetSatuzUser(dataForm, true)
    })

}
const PostProcessMsgConfigSetSatuzUser = (form, isAll) => {
    PostProcessMsgConfigSetSatuz(form).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success('设置成功')
            dialogVisible.value = false
            if (isAll) HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
} 
</script>
<template>
    <div class="viewContainer">
        <div class="viewContainer" style="width: 100%;">
            <el-row class="navFlexBox">
                <el-col>
                    <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                        <el-form-item class="flexItem">
                            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">新增</el-button>
                        </el-form-item>
                        <el-form-item class="flexItem">
                            <el-button type="success" :icon="Tools" :disabled="selectRows.length == 0"
                                @click="HandleSetAll">批量设置</el-button>
                        </el-form-item>
                        <div class="verticalIdel"></div>
                        <el-form-item label="" class="flexItem">
                            <el-input v-model.trim="filters.ProcessNodeName" clearable placeholder="节点名称"
                                style="width: 200px"></el-input>
                        </el-form-item>
                        <!-- <el-form-item label="" class="flexItem">
                            <el-input v-model.trim="filters.MsgCode" clearable placeholder="消息编号"
                                style="width: 200px"></el-input>
                        </el-form-item> -->
                        <el-form-item class="flexItem">
                            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-col>
            </el-row>
            <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">
                模块名称：<span style="color: #999;">{{ route.query.ModuleName }}</span>
                流程名称：<span style="color: #999;">{{ route.query.ProcessName }}</span>
            </div>
            <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column prop="ProcessNodeName" label="节点名称" min-width="160" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="MsgCode" label="消息编号" min-width="140" align="center"></el-table-column>
                <el-table-column prop="MsgExplain" label="消息说明" min-width="140" show-overflow-tooltip></el-table-column>
                <el-table-column prop="MsgShowExplain" label="消息说明(显示)" min-width="140"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="MainSwitch" label="总开关" min-width="90" align="center">
                    <template #default="{ row }">
                        <el-switch v-model="row.MainSwitch" :active-value="1" :inactive-value="2" inline-prompt
                            active-text="开" inactive-text="关" style="--el-switch-off-color: #ff4949"
                            @change="HandleSwitchChange($event, row, 1)" />
                    </template>
                </el-table-column>
                <el-table-column prop="MsgSwitch" label="短信开关" min-width="90" align="center">
                    <template #default="{ row }">
                        <el-switch v-model="row.MsgSwitch" :active-value="1" :inactive-value="2" inline-prompt
                            active-text="开" inactive-text="关" style="--el-switch-off-color: #ff4949"
                            @change="HandleSwitchChange($event, row, 2)" />
                    </template>
                </el-table-column>
                <el-table-column prop="MsgTemplate" label="短信模版" min-width="160"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="WeChatSwitch" label="微信开关" min-width="90" align="center">
                    <template #default="{ row }">
                        <el-switch v-model="row.WeChatSwitch" :active-value="1" :inactive-value="2" inline-prompt
                            active-text="开" inactive-text="关" style="--el-switch-off-color: #ff4949"
                            @change="HandleSwitchChange($event, row, 3)" />
                    </template>
                </el-table-column>
                <el-table-column prop="WeChatTemplateId" label="微信模版Id" min-width="120"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="WeChatTemplate" label="微信模版" min-width="160"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="WeChatRedirectPage" label="微信跳转页面" min-width="140"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="ClientIsShow" label="用户端是否显示" min-width="130" align="center">
                    <template #default="{ row }">
                        {{ row.ClientIsShow == 1 ? '是' : '否' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="100" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    </template>
                </el-table-column>
                <template #empty>
                    <el-empty description="没有数据"></el-empty>
                </template>
            </el-table>
            <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
                @handleChange="handlePage" />
        </div>
        <app-box v-model="dialogVisible" :width="isSet ? 600 : 720" :lazy="true"
            :title="isSet ? '设置是否开启消息推送' : formData.Id ? '修改消息配置' : '新增消息配置'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="160px"
                    status-icon>
                    <div v-if="!isSet" style="padding: 16px 50px;">
                        <el-form-item label="节点名称：" prop="ProcessNodeId">
                            <el-select v-model="formData.ProcessNodeId" :disabled="formData.Id">
                                <el-option v-for="item in processNodeList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="消息编号： " prop="MsgCode">
                            <el-select v-model="formData.MsgCode" :disabled="formData.Id">
                                <el-option v-for="item in msgNoList" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <div v-if="formData.Id">
                            <el-form-item label="消息说明：">
                                <el-input v-model="formData.MsgExplain"></el-input>
                            </el-form-item>
                            <el-form-item label="消息说明(显示)：">
                                <el-input v-model="formData.MsgShowExplain"></el-input>
                            </el-form-item>
                            <el-form-item label="总开关：">
                                <el-switch v-model="formData.MainSwitch" :active-value="1" :inactive-value="2"
                                    inline-prompt active-text="开" inactive-text="关"
                                    style="--el-switch-off-color: #ff4949" />
                            </el-form-item>
                            <el-form-item label="短信开关：">
                                <el-switch v-model="formData.MsgSwitch" :active-value="1" :inactive-value="2"
                                    inline-prompt active-text="开" inactive-text="关"
                                    style="--el-switch-off-color: #ff4949" />
                            </el-form-item>
                            <el-form-item label="短信模版：">
                                <el-input v-model="formData.MsgTemplate" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
                            </el-form-item>
                            <el-form-item label="微信开关：">
                                <el-switch v-model="formData.WeChatSwitch" :active-value="1" :inactive-value="2"
                                    inline-prompt active-text="开" inactive-text="关"
                                    style="--el-switch-off-color: #ff4949" />
                            </el-form-item>
                            <el-form-item label="微信模版Id：">
                                <el-input v-model="formData.WeChatTemplateId"></el-input>
                            </el-form-item>
                            <el-form-item label="微信模版：">
                                <el-input v-model="formData.WeChatTemplate" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
                            </el-form-item>
                            <el-form-item label="微信跳转页面：">
                                <el-input v-model="formData.WeChatRedirectPage"></el-input>
                            </el-form-item>
                            <el-form-item label="用户端是否显示：">
                                <el-radio-group v-model="formData.ClientIsShow">
                                    <el-radio :value="1"> 是</el-radio>
                                    <el-radio :value="2"> 否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </div>

                    </div>
                    <div v-else>
                        <el-form-item label="开关类型：" prop="SwitchType">
                            <el-radio-group v-model="formData.SwitchType">
                                <el-radio :value="1"> 总开关</el-radio>
                                <el-radio :value="2"> 短信开关</el-radio>
                                <el-radio :value="3"> 微信开关</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="是否开启：">
                            <el-switch v-model="formData.Statuz" :active-value="1" :inactive-value="2" inline-prompt
                                active-text="开" inactive-text="关" style="--el-switch-off-color: #ff4949" />
                        </el-form-item>
                    </div>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit" v-if="!isSet"> 提交 </el-button>
                    <el-button type="primary" @click="HandleSwitchAllChange" v-else> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style></style>
<style lang="scss" scoped>
.section_left {
    width: 200px;
    margin-top: 10px;
    flex-shrink: 0;
}

:deep(.el-tree-node:focus>.el-tree-node__content) {

    background-color: #409eff1A;

    .el-text {
        color: #409eff;
    }
}
</style>