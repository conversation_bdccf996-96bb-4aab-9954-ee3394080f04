<script setup>
// 接口
import {
  user<PERSON>og<PERSON>, Get<PERSON><PERSON><PERSON>B<PERSON>, teacherGetvalidatecode, teacherUser<PERSON>ogin, LoginRefreshcaptchaimage
} from '@/api/user.js'
import {
  ArticleGetbottomcatetype, ArticleGetoperator, AnonGetconfigbymodule
} from '@/api/home.js'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { useUserStore } from '@/stores';
import router from '@/router'
import { addDynamicRoutes } from '@/router'
import md5 from 'js-md5';
import { integerLimit, onePage } from "@/utils/index.js";

const isAdmin = ref(true)
// 登录信息
const formData = ref({
  name: '',
  pass: ''
})
const userStore = useUserStore()
const imageUrl = ref('')
const imgUuid = ref('')
onMounted(() => {

  if (userStore.token) {
    if (userStore.platformType == 1) {
      router.push('/')
    } else if (userStore.platformType == 2 || userStore.platformType == 3) {
      const path = onePage(userStore.menu)
      router.push('/' + path)
    }
  }
  noticeChecked.value = userStore.noticeChecked
  // 是否记住账号密码
  if (userStore.isRemember) {
    formData.value.name = userStore.name
    formData.value.pass = atob(userStore.pass)
  }
  if (userStore.isImgCode == 5) {
    // LoginRefreshcaptchaimageUser()
    imageUrl.value = `data:image/jpg;base64,${userStore.captchaimage.Img}`;
    imgUuid.value = userStore.captchaimage.Uuid
  }
  ArticleGetoperatorUser({ code: 1010 })
  if (userStore.platformType == 2 || userStore.platformType == 3) {
    ArticleGetbottomcatetypeUser()
    AnonGetconfigbymoduleUser()
  }
  window.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress)
})
// 回车键登录
const handleKeyPress = (event) => {
  if (event.key === 'Enter') {
    if (isAdmin.value) {
      login()
    } else {
      teacherLogin()
    }
  }
}
// 监听变量
watch(() => userStore.isRemember, () => {
  userStore.setName('')
  userStore.setPass('')
})
// 登录禁用
const isLogin = computed(() => {
  let isLogin = true
  if (formData.value.name && formData.value.pass) isLogin = false
  return isLogin
})
// 班主任登录禁用
const isTeacherLogin = computed(() => {
  let isLogin = true
  if (formData.value.phoneNumber && formData.value.validateCode) isLogin = false
  return isLogin
})
// 校验信息
const formRules = {
  name: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  pass: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  imgCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  phoneNumber: [{ required: true, message: '请输入手机号', trigger: 'blur' },
  {
    pattern: /^1[0-9]{10}$/,
    message: '请输入正确11位手机号码',
    trigger: 'blur'
  }],
  validateCode: [{ required: true, message: '请输入验证码', trigger: 'blur' },
  {
    pattern: /^[0-9]{4,6}$/,
    message: '请输入正确的验证码',
    trigger: 'blur'
  }],
}
//输入整数
const integerLimitInput = (val, name) => {
  formData.value[name] = integerLimit(val);
}
const refForm = ref()
// 登录
const login = () => {
  console.log('登录', noticeChecked)
  if (noticeList.value.length > 0 && !noticeChecked.value) {
    ElMessage.error('请先阅读并同意用户条款')
    return
  }
  refForm.value
    .validate()
    .then((res) => {
      // console.info('表单验证成功', res)
      const loginParams = ref({
        name: formData.value.name,
        pass: md5(formData.value.pass),
      })
      if (userStore.isImgCode == 5) {
        loginParams.value.Code = formData.value.imgCode
        loginParams.value.Uuid = imgUuid.value
      }
      userLogin(loginParams.value).then((resUser) => {
        if (resUser.data.flag == 5) {
          userStore.setImgCode(resUser.data.flag)
          // LoginRefreshcaptchaimageUser()
          imageUrl.value = `data:image/jpg;base64,${resUser.data.data.footer.Img}`;
          imgUuid.value = resUser.data.data.footer.Uuid
          userStore.setCaptchaImage(resUser.data.data.footer)
          ElMessage.error(resUser.data.msg)
        } else if (resUser.data.flag == 6) {
          // resUser.data.flag == 6  判断为特定密码时，直接进入修改密码页面
          const { rows, footer } = resUser.data.data

          userStore.setToken(rows.token)
          userStore.setTokenType(rows.token_type)
          userStore.setExpiresin(rows.expires_in * 1000)
          userStore.setUserInfo(footer)

          // 记住账号密码
          if (userStore.isRemember) {
            userStore.setName(formData.value.name)
            userStore.setPass(btoa(formData.value.pass))
          }
          userStore.setNoticeChecked(true)

          router.replace('/user/my/changepass')

          userStore.setImgCode(0)
        } else if (resUser.data.flag == 1) {
          // console.info("登录信息", resUser.data.data)
          // console.info("用户信息", resUser.data.data.footer)
          const { rows, footer } = resUser.data.data

          userStore.setToken(rows.token)
          userStore.setTokenType(rows.token_type)
          userStore.setExpiresin(rows.expires_in * 1000)
          userStore.setUserInfo(footer)

          // 记住账号密码
          if (userStore.isRemember) {
            userStore.setName(formData.value.name)
            userStore.setPass(btoa(formData.value.pass))
          }
          userStore.setNoticeChecked(true)

          if (footer.UnitStatus == 1) {
            // 已认证，调用菜单
            GetNavigationBarUser(footer.Id)
          } else if (footer.UnitStatus == 2) {
            // 需要认证：跳到新页面申请认证
            router.replace('/unitauthentic')
          }
          userStore.setImgCode(0)
        } else {
          ElMessage.error(resUser.data.msg)
        }
      })
        .catch((errUser) => {
          // 在这里处理登录失败的额外操作
          console.log("errUser", errUser)
          ElMessage.error(errUser.msg);
        })
    })
    .catch((err) => {
      ElMessage.error('请填写信息');
      console.info('表单验证失败', err)
    })
}
// 班主任登录
const teacherLogin = () => {
  if (!noticeChecked.value) {
    ElMessage.error('请先阅读并同意用户条款')
    return
  }
  // 班主任注册登录
  let paraData = {
    phoneNumber: formData.value.phoneNumber, //'手机号
    validateCode: formData.value.validateCode, //验证码
    uuid: Uuid.value, //验证码uid
    userType: 6, //账号类型：5:家长；6：班主任,
  }
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    teacherUserLogin(paraData).then(res => {
      if (res.data.flag == 1) {
        const { rows, footer } = res.data.data
        userStore.setToken(rows.token)
        userStore.setTokenType(rows.token_type)
        userStore.setExpiresin(rows.expires_in * 1000)
        userStore.setUserInfo(footer)
        userStore.setNoticeChecked(true)
        if (!footer.UnitStatus || footer.UnitStatus == 1) {
          // 已认证，调用菜单
          GetNavigationBarUser(footer.Id)
        } else if (footer.UnitStatus == 2) {
          // 需要认证：跳到新页面申请认证
          router.replace('/unitauthentic')
        }
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })
}
// 切换账号
const inputDemoAccount = (name, pass) => {
  formData.value.name = name
  formData.value.pass = pass
}
// 注册
const reg = () => {
  router.replace('/reg')
}
// 切换登录方法
const tabClick = (e) => {
  isAdmin.value = e
  // noticeChecked.value = false
}

const codeText = ref('获取验证码')
const isCode = ref(false)
const CodeLength = ref(6)
const IsExistAccount = ref(false)
const Uuid = ref()
const time = ref(Date.now() + 1000 * 60)
// 获取校验码
const getCode = () => {
  refForm.value.validateField(['phoneNumber'], valid => {
    if (!valid) {
      return
    }

    // 班主任获取验证码
    let paraData = {
      phoneNumber: formData.value.phoneNumber, //手机号
      validateType: 3, //家长、教师登录
      userType: 6, //账号类型：5:家长；6：班主任,
    }

    teacherGetvalidatecode(paraData).then(res => {
      if (res.data.flag == 1) {
        const { rows } = res.data.data
        CodeLength.value = rows.CodeLength; //验证码长度
        IsExistAccount.value = rows.IsExistAccount; //是否存在账号
        Uuid.value = rows.Uuid; //是否存在账号
        time.value = Date.now() + 1000 * rows.WaitSecond
        isCode.value = true
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  });
}
// 校验码倒计时结束
const finishChange = () => {
  // console.log('倒计时结束')
  codeText.value = '重新获取'
  isCode.value = false
}

const GetNavigationBarUser = (uid) => {
  GetNavigationBar({ uid: uid }).then((res) => {
    // console.log("菜单res", res)
    // console.log("菜单res", res.data.response.children)
    if (res.data.response.children) {
      userStore.setMenu(res.data.response.children)
      // 添加vue router路由
      addDynamicRoutes(userStore.menu)
    }
    // console.log("userStore.curPage.path",userStore.curPage.path)
    const routerPath = ''
    if (routerPath && !['/login', '/reg', '/exhibition', '/information', '/articlelist', '/articledetail', '/unitauthentic', '/preview', '/'].includes(routerPath)) {
      router.replace(userStore.curPage.path)
    } else {
      let path = onePage(userStore.menu)
      if (userStore.platformType == 2) {
        if (userStore.userInfo.UnitType === 0 || (userStore.userInfo.UnitType > 0 && !['1200', '2200', '3200'].some(t => userStore.userInfo.RoleIds.includes(t)))) {
          path = onePage(userStore.menu)
        } else {
          // 工作流管理平台：首页
          path = 'approval/home/<USER>'
        }
      } else {
        path = onePage(userStore.menu)
      }
      // 获取当前账号第一个权限页面路径
      // const path = onePage(userStore.menu)
      console.log("获取当前账号第一个权限页面路径", path)
      userStore.setOnePage(path)//存储起来用于tag标签
      // 跳转路由
      // router.replace('/')
      router.replace('/' + path)
    }
  })
}

// 切换图片验证码
const imgCodeChange = () => {
  LoginRefreshcaptchaimageUser()
}
// 获取图片验证码 
const LoginRefreshcaptchaimageUser = () => {
  LoginRefreshcaptchaimage().then((res) => {
    console.log('获取验证码', res)
    const { footer } = res.data.data
    imageUrl.value = `data:image/jpg;base64,${footer.Img}`;
    imgUuid.value = footer.Uuid
  })
}
const noticeList = ref([])
const noticeChecked = ref(false)
// 获取用户条款
const ArticleGetoperatorUser = (parData) => {
  ArticleGetoperator(parData).then(res => {
    // console.log("资讯列表", res)
    if (res.data.flag == 1) {
      const { other } = res.data.data
      noticeList.value = other || []
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 查看用户须知
const detailClick = (item) => {
  const { href } = router.resolve({
    path: "/articledetail",
    query: { Id: item.Id }
  });
  window.open(href, "_blank");
}
//底部资讯分类信息
const ArticleGetbottomcatetypeUser = () => {
  ArticleGetbottomcatetype({ topCount: 3 }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      let articleFooterList = rows || [];//底部资讯分类信息
      userStore.setArticleFooter(articleFooterList)
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 获取配置信息
const AnonGetconfigbymoduleUser = () => {
  AnonGetconfigbymodule({ moduleCode: 8002 }).then(res => {
    let obj = {}
    const { rows } = res.data.data
    rows.map(item => {
      obj[item.TypeCode] = item.ConfigValue
    })
    userStore.setDefaultSet(obj)
  })
}

</script>
<template>
  <div class="login-container">
    <!-- 登录框 -->
    <div class="login-box">
      <!-- 系统标题 -->
      <div class="system-header">
        <h1 class="system-title">
          <span v-if="userStore.platformType == 1">{{ userStore.defaultSet['8002_DLYM_BJ'] || '智慧教育云平台' }}</span>
          <span v-if="userStore.platformType == 2">{{ userStore.defaultSet['8002_DLYM_BJ'] || '审批配置平台' }}</span>
          <span v-if="userStore.platformType == 3">{{ userStore.defaultSet['8002_DLYM_BJ'] || '危化品平台' }}</span>
        </h1>
      </div>

      <!-- 登录方式切换 -->
      <div v-if="userStore.platformType == 1" class="login-tabs">
        <div v-if="isAdmin" class="tab-switch">
          <span class="switch-link" @click="tabClick(false)">班主任登录 ></span>
        </div>
        <div v-else class="tab-switch">
          <span class="switch-link" @click="tabClick(true)">账号登录 ></span>
        </div>
      </div>
      <!-- 登录表单 -->
      <div class="login-form">
        <!-- 账号登录 -->
        <div v-if="isAdmin">
          <el-form @submit.prevent ref="refForm" :rules="formRules" :model="formData">
            <el-form-item prop="name">
              <el-input v-model.trim="formData.name" :prefix-icon="User" clearable auto-complete="off"
                placeholder="请输入用户名" size="large" class="custom-input"></el-input>
            </el-form-item>
            <el-form-item prop="pass">
              <el-input @keyup.enter="login" v-model.trim="formData.pass" :prefix-icon="Lock" clearable
                auto-complete="off" show-password placeholder="请输入密码" size="large" class="custom-input"></el-input>
            </el-form-item>
            <el-form-item prop="imgCode" v-if="userStore.isImgCode == 5">
              <div class="captcha-container">
                <el-input v-model="formData.imgCode" auto-complete="off" placeholder="请输入验证码" size="large"
                  class="captcha-input"></el-input>
                <div class="captcha-image" @click="imgCodeChange">
                  <img v-if="imageUrl" :src="imageUrl" alt="验证码" />
                </div>
              </div>
            </el-form-item>

            <div class="form-options">
              <el-checkbox class="remember-checkbox" v-model="userStore.isRemember">记住密码</el-checkbox>
              <div @click="reg" v-if="userStore.platformType == 1" class="register-link">
                立即注册
              </div>
            </div>

            <!-- 快速登录测试账号 -->
            <div class="demo-accounts">
              <div class="demo-title">快速登录</div>
              <div class="demo-buttons">
                <el-button size="small" plain @click="inputDemoAccount('yucai', '123456@cneefix')">学校</el-button>
                <el-button size="small" plain @click="inputDemoAccount('浦口教育局', '123456@cneefix')">区县</el-button>
                <el-button size="small" plain @click="inputDemoAccount('shiji', '123456@cneefix')">市级</el-button>
                <el-button size="small" plain @click="inputDemoAccount('超级管理员', '123456')">管理员</el-button>
                <el-button size="small" plain @click="inputDemoAccount('junfei', '123456@cneefix')">企业</el-button>
              </div>
            </div>

            <el-form-item class="login-button-item">
              <el-button type="primary" size="large" class="login-button" @click="login" :disabled="isLogin">
                <span>登录</span>
              </el-button>
            </el-form-item>

            <!-- 用户协议 -->
            <div v-if="noticeList.length > 0" class="agreement">
              <el-checkbox v-model="noticeChecked" class="agreement-checkbox">
                <span class="agreement-text">阅读并接受: </span>
              </el-checkbox>
              <span class="agreement-links" v-for="(item, index) in noticeList" :key="item.Id">
                <span class="agreement-link" @click="detailClick(item)">《{{ item.Title }}》</span>
                <span v-if="index < noticeList.length - 1">、</span>
              </span>
            </div>
          </el-form>
        </div>
        <!-- 班主任登录 -->
        <div v-else>
          <div class="teacher-login-tip">未注册将自动创建并登录</div>
          <el-form @submit.prevent ref="refForm" :rules="formRules" :model="formData">
            <el-form-item prop="phoneNumber">
              <el-input v-model="formData.phoneNumber" clearable @input="integerLimitInput($event, 'phoneNumber')"
                auto-complete="off" placeholder="请输入手机号码" size="large" class="custom-input"></el-input>
            </el-form-item>
            <el-form-item prop="validateCode">
              <div class="code-container">
                <el-input v-model="formData.validateCode" clearable @input="integerLimitInput($event, 'validateCode')"
                  auto-complete="off" placeholder="请输入验证码" @keyup.enter="teacherLogin" size="large"
                  class="code-input"></el-input>
                <el-button type="primary" plain :disabled="isCode" @click="getCode" class="code-button">
                  <span v-if="!isCode">{{ codeText }}</span>
                  <el-countdown v-else format="ss" :value="time" @finish="finishChange" suffix="秒后重新获取"
                    :value-style="{ color: '#606266', fontSize: '14px' }" />
                </el-button>
              </div>
            </el-form-item>

            <el-form-item class="login-button-item">
              <el-button type="primary" size="large" class="login-button" @click="teacherLogin"
                :disabled="isTeacherLogin">
                <span>登录</span>
              </el-button>
            </el-form-item>

            <!-- 用户协议 -->
            <div v-if="noticeList.length > 0" class="agreement">
              <el-checkbox v-model="noticeChecked" class="agreement-checkbox">
                <span class="agreement-text">阅读并接受: </span>
              </el-checkbox>
              <span class="agreement-links" v-for="(item, index) in noticeList" :key="item.Id">
                <span class="agreement-link" @click="detailClick(item)">《{{ item.Title }}》</span>
                <span v-if="index < noticeList.length - 1">、</span>
              </span>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
// 主容器 - 使用背景图片
.login-container {
  min-height: 100vh;
  // 默认背景图片 - 可根据不同平台替换
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.7) 0%, rgba(53, 122, 189, 0.7) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%234a90e2;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%23357abd;stop-opacity:0.1"/></linearGradient><pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"><path d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23bg)"/><rect width="100%" height="100%" fill="url(%23grid)"/><g opacity="0.3"><circle cx="300" cy="200" r="4" fill="rgba(255,255,255,0.4)"/><circle cx="600" cy="150" r="3" fill="rgba(255,255,255,0.3)"/><circle cx="900" cy="300" r="5" fill="rgba(255,255,255,0.2)"/><circle cx="1200" cy="100" r="3.5" fill="rgba(255,255,255,0.4)"/><circle cx="1500" cy="250" r="4.5" fill="rgba(255,255,255,0.3)"/><circle cx="200" cy="400" r="3" fill="rgba(255,255,255,0.2)"/><circle cx="500" cy="450" r="4" fill="rgba(255,255,255,0.4)"/><circle cx="800" cy="500" r="3.5" fill="rgba(255,255,255,0.3)"/><circle cx="1100" cy="420" r="3" fill="rgba(255,255,255,0.2)"/><circle cx="1400" cy="480" r="4.5" fill="rgba(255,255,255,0.4)"/><circle cx="1700" cy="460" r="3" fill="rgba(255,255,255,0.3)"/><circle cx="100" cy="600" r="3.5" fill="rgba(255,255,255,0.2)"/><circle cx="400" cy="650" r="3" fill="rgba(255,255,255,0.4)"/><circle cx="700" cy="700" r="4" fill="rgba(255,255,255,0.3)"/><circle cx="1000" cy="620" r="3.5" fill="rgba(255,255,255,0.2)"/><circle cx="1300" cy="680" r="3" fill="rgba(255,255,255,0.4)"/><circle cx="1600" cy="640" r="4.5" fill="rgba(255,255,255,0.3)"/><circle cx="250" cy="800" r="3" fill="rgba(255,255,255,0.2)"/><circle cx="550" cy="850" r="4" fill="rgba(255,255,255,0.4)"/><circle cx="850" cy="820" r="3.5" fill="rgba(255,255,255,0.3)"/><circle cx="1150" cy="880" r="3" fill="rgba(255,255,255,0.2)"/><circle cx="1450" cy="840" r="4.5" fill="rgba(255,255,255,0.4)"/></g></svg>');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

// 登录框
.login-box {
  width: 420px;
  min-height: 500px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px;
  position: relative;
  z-index: 1;
}

// 系统标题
.system-header {
  text-align: center;
  margin-bottom: 30px;
}

.system-title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #1a1a1a;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

// 登录方式切换
.login-tabs {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.tab-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.switch-link {
  color: #4a90e2;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #357abd;
  }
}

// 登录表单
.login-form {
  width: 100%;
}

// 自定义输入框样式
:deep(.custom-input) {
  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e1e8ed;
    transition: all 0.3s ease;

    &:hover {
      border-color: #4a90e2;
      box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
    }

    &.is-focus {
      border-color: #4a90e2;
      box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
    }
  }

  .el-input__inner {
    height: 40px;
    font-size: 14px;
    color: #2c3e50;

    &::placeholder {
      color: #bdc3c7;
    }
  }
}

// 验证码容器
.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 100px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #e1e8ed;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 验证码容器（班主任登录）
.code-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.code-input {
  flex: 1;
}

.code-button {
  height: 40px;
  border-radius: 8px;
  font-size: 13px;
  min-width: 110px;
}

// 表单选项
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}

.remember-checkbox {
  :deep(.el-checkbox__label) {
    color: #7f8c8d;
    font-size: 14px;
  }
}

.register-link {
  color: #4a90e2;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #357abd;
  }
}

// 班主任登录提示
.teacher-login-tip {
  margin-bottom: 20px;
  color: #7f8c8d;
  font-size: 14px;
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
}

// 快速登录区域
.demo-accounts {
  margin: 15px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.demo-title {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 10px;
  text-align: center;
}

.demo-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;

  :deep(.el-button) {
    border-radius: 16px;
    font-size: 11px;
    padding: 4px 10px;
    border-color: #dee2e6;
    color: #6c757d;
    height: 28px;

    &:hover {
      border-color: #4a90e2;
      color: #4a90e2;
      background: rgba(74, 144, 226, 0.05);
    }
  }
}

// 登录按钮
.login-button-item {
  margin: 20px 0 15px 0;
}

.login-button {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  &.is-disabled {
    background: #bdc3c7;
    box-shadow: none;
    transform: none;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  span {
    position: relative;
    z-index: 1;
  }
}

// 用户协议
.agreement {
  margin-top: 16px;
  font-size: 12px;
  color: #666;
  text-align: center;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
}

.agreement-checkbox {
  :deep(.el-checkbox__label) {
    color: #7f8c8d;
    font-size: 12px;
    line-height: 1.5;
  }
}

.agreement-text {
  color: #7f8c8d;
}

.agreement-links {
  color: #4a90e2;
  line-height: 1.5;
}

.agreement-link {
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #357abd;
    text-decoration: underline;
  }
}

// 表单项间距调整
:deep(.el-form-item) {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 复选框样式优化
:deep(.el-checkbox) {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #4a90e2;
    border-color: #4a90e2;
  }

  .el-checkbox__inner:hover {
    border-color: #4a90e2;
  }
}

// 倒计时样式
:deep(.el-statistic) {
  .el-statistic__content {
    font-size: 14px;
    color: #6c757d;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 15px;
    background-attachment: scroll;
  }

  .login-box {
    width: 100%;
    max-width: 380px;
    padding: 30px 25px;
  }

  .system-title {
    font-size: 22px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-box {
    width: 100%;
    max-width: 340px;
    padding: 25px 20px;
  }

  .system-title {
    font-size: 20px;
  }

  .system-header {
    margin-bottom: 25px;
  }
}

// 高分辨率优化
@media (min-width: 1920px) {
  .login-container {
    background-size: 100% 100%;
  }

  .login-box {
    width: 440px;
    padding: 45px;
  }

  .system-title {
    font-size: 26px;
  }
}

/*
  不同平台背景图片配置示例：

  // 教育平台
  .login-container.education {
    background-image: linear-gradient(135deg, rgba(74, 144, 226, 0.7) 0%, rgba(53, 122, 189, 0.7) 100%),
                      url('/images/education-bg.jpg');
  }

  // 政务平台
  .login-container.government {
    background-image: linear-gradient(135deg, rgba(220, 53, 69, 0.7) 0%, rgba(108, 117, 125, 0.7) 100%),
                      url('/images/government-bg.jpg');
  }

  // 企业平台
  .login-container.enterprise {
    background-image: linear-gradient(135deg, rgba(40, 167, 69, 0.7) 0%, rgba(23, 162, 184, 0.7) 100%),
                      url('/images/enterprise-bg.jpg');
  }
*/
</style>
