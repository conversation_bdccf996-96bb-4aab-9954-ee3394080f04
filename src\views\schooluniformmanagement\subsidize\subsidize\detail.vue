<script setup>
defineOptions({
    name: 'subsidizedetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    Punitgetschoolbycountyid, Getpagedbytype,
} from '@/api/user.js'
import {
    PurchaseGetsponsorpaged, PurchaseGetsponsordetailpaged
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { previousYearDate, fileDownload } from "@/utils/index.js";
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const CountyList = ref([])
const SchoolList = ref([])
const uploadFileData = ref([])
const detailVisible = ref(false)
const detailData = ref({ Type: '1' })
const detailList = ref([])
const PurchaseNo = ref('')
const OrderNum = ref('')

//加载数据
onMounted(() => {
    yearDateList.value = previousYearDate()
    GetpagedbytypeUser()
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// 查看
const HandleDetail = (row) => {
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    })
    PurchaseNo.value = row.PurchaseNo
    OrderNum.value = row.ContractPersonNum
    PurchaseGetsponsordetailpagedUser(row.Id)
    detailVisible.value = true
}

const CountyChange = (e) => {
    if (!e) {
        filters.value.SchoolId = undefined
    }
    HandleTableData()
    PunitgetschoolbycountyidUser(e)
}
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 105 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选择区县获取学校
const PunitgetschoolbycountyidUser = (id) => {
    Punitgetschoolbycountyid({ CountyId: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            filters.value.SchoolId = undefined
            SchoolList.value = rows || []//学校名称
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    PurchaseGetsponsorpaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            if (isFirst) {
                if (hUnitType.value == 1) {
                    CountyList.value = other.listCounty || []//区县名称
                }
                SchoolList.value = other.listSchool || []//学校名称
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.PurchaseYear = undefined
    filters.value.CountyId = undefined
    filters.value.SchoolId = undefined
    filters.value.Key = undefined
    if (hUnitType.value == 1) {
        SchoolList.value = []
    }
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }

// 详情
const PurchaseGetsponsordetailpagedUser = (id) => {
    PurchaseGetsponsordetailpaged({ uniformPurchaseId: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            detailList.value = rows || []
            detailList.value.forEach(item => {
                item.uploadFileData = uploadFileData.value
                item.ListAttachment = item.ListAttachment || []
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yearDateList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
                        <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称"
                            style="width: 160px" @change="CountyChange">
                            <el-option v-for="item in CountyList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem" v-if="hUnitType == 2">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="合同批次"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="PurchaseYear" label="年度" min-width="100" align="center"></el-table-column>
            <el-table-column prop="AreaName" label="区县名称" min-width="180" v-if="hUnitType == 1"></el-table-column>
            <el-table-column prop="SchoolName" label="学校名称" min-width="180"
                v-if="hUnitType == 1 || hUnitType == 2"></el-table-column>
            <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="SponsorUserNum" label="资助学生数" min-width="100" align="center"></el-table-column>
            <el-table-column prop="SponsorAmount" label="资助总额（元）" min-width="140" align="right"></el-table-column>
            <el-table-column prop="SponsorRatio" label="资助学生占比" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.SponsorRatio || 0 }}%
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="资助管理" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="detailVisible" :width="680" :lazy="true" title="资助信息">
            <template #content>
                <div style="font-size: 14px;color: #606266;text-align: center;margin-bottom: 10px;">合同批次：<span
                        style="color: #999;padding-right: 50px;">{{ PurchaseNo }}</span>订单人数：<span style="color: #999;">
                        {{ OrderNum }}</span> </div>
                <div class="detailDialog">
                    <div v-for="(item, index) in detailList" :key="index" class="detailItem">
                        <el-form @submit.prevent :model="detailData" label-width="180px" status-icon>
                            <el-form-item label="资助学生数（人）：">
                                <span>{{ item.SponsorUserNum }}</span>
                            </el-form-item>
                            <!-- <el-form-item label="每人资助金额（元）：">
                                <span>{{ item.PersonSupport }}</span>
                            </el-form-item> -->
                            <el-form-item label="资助总额（元）：">
                                <span> {{ item.SponsorAmount }}</span>
                            </el-form-item>
                            <el-form-item label="资助来源：">
                                <span> {{ item.SponsorSourceName }}</span>
                            </el-form-item>
                            <el-form-item v-for="(t, index) in item.uploadFileData" :key="index">
                                <template #label>
                                    <span> {{ t.Name }}：</span>
                                </template>
                                <div class="fileFlex1">
                                    <div v-for="(itemCate, indexCate) in item.ListAttachment" :key="itemCate.Id">
                                        <span v-if="t.FileCategory == itemCate.FileCategory" style="cursor: pointer;"
                                            @click="fileListDownload(itemCate, item.ListAttachment)">
                                            {{ itemCate.Title }}{{ itemCate.Ext }}
                                        </span>
                                        <span v-else style="width: 0;margin-right: 0;"></span>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="detailVisible = false">关闭</el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}

.detailDialog {

    max-height: 450px;
    overflow-y: auto;

    .detailItem {
        margin: 5px 10px;
        -webkit-box-shadow: 0 0 5px #dcdfe6;
        box-shadow: 0 0 5px #dcdfe6;
        padding: 5px 10px;
    }

    .drawer[data-v-570f675a] {
        text-align: center;
        margin: 5px 10px;
        -webkit-box-shadow: 0 0 10px #dcdfe6;
        box-shadow: 0 0 10px #dcdfe6;
        padding: 5px 10px;
    }

    :deep(.el-form-item__label) {
        color: #409EFF;
    }

    :deep(.el-form-item) {
        margin-bottom: 5px !important;
        margin-top: 5px !important;
        border-bottom: 1px solid #f5f5f5;
    }
}

.fileFlex1 {
    display: flex;
    flex-wrap: wrap;
}

.fileFlex1 span {
    display: inline-block;
    width: 80px;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>