<script setup>
defineOptions({
    name: 'dangerchemicalsdailycheckedschoollist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    GovernTaskUnitListFind
} from '@/api/daily.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const taskId = ref(0)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const summation = ref({})
//加载数据
onMounted(() => {
    taskId.value = route.query.taskId
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    taskId.value = route.query.taskId
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})

// 列表
const HandleTableData = () => {
    filters.value.GovernTaskId = taskId.value
    filters.value.Statuz = 1
    GovernTaskUnitListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (rows.Statistics) {
                summation.value = rows.Statistics
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        else if (index == 2) {
            sums[index] = summation.value.ProblemNum
            return;
        }
        else if (index == 3) {
            sums[index] = summation.value.DangerNum
            return;
        }
    });
    return sums;
}

//查看
const btnView = (param) => {
    router.push({
        path: "/dangerchemicals/daily/checkresultdetail", query: {
            taskId: param.GovernTaskId,
            schoolId: param.SchoolId,
            governTaskUnitId: param.GovernTaskUnitId,
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="请输入学校名称" style="width: 180px"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>

                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="SchoolName" label="学校名称" min-width="150" show-overflow-tooltip></el-table-column>
            <el-table-column prop="CheckingDate" label="检查时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.CheckingDate ? row.CheckingDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ProblemNum" label="存在问题" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.ProblemNum > -1 ? row.ProblemNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DangerNum" label="存在隐患" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.DangerNum > -1 ? row.DangerNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CheckingUserNum" label="检查人员数" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.CheckingUserNum > -1 ? row.CheckingUserNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="opt2" fixed="right" label="学校明细" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="btnView(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>