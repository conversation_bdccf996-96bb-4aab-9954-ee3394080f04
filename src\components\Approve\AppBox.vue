<script setup>
import { watch, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  lazy: {
    //是否开启懒加载2020.12.06
    type: Boolean,
    default: false,
  },
  icon: {
    type: String,
    default: "el-icon-warning-outline",
  },
  title: {
    type: String,
    default: "基本信息",
  },
  height: {
    type: Number,
    default: 200,
  },
  width: {
    type: Number,
    default: 650,
  },
  padding: {
    type: Number,
    default: 16,
  },
  modal: {
    //是否需要遮罩层
    type: Boolean,
    default: true,
  },
  draggable: {
    //启用可拖拽功能
    type: Boolean,
    default: true,
  },
  mask: {
    type: Boolean,
    default: true,
  },
  onModelClose: {
    //2021.07.11增加弹出框关闭事件
    type: Function,
    default: (iconClick) => {
      return true;
    }
  },
  footer: { //是否显示底部按钮
    type: Boolean,
    default: true
  },
  modalClose: { //是否可以通过点击 modal 关闭 Dialog
    type: <PERSON>olean,
    default: false
  },
  escapeClose: { //是否可以通过按下 ESC 关闭 Dialog
    type: Boolean,
    default: true
  },
})
const emit = defineEmits(['update:modelValue', 'update:tableModel']);
const clientHeight = document.body.clientHeight * 0.95 - 60;
const inited = ref(true);
const vmodel = ref(false);
// const footer = ref(false);
const top = ref(100);
vmodel.value = props.modelValue;
// footer.value = !!context.slots.footer;
const contentHeight = ref(200);
contentHeight.value = props.height;
const handleClose = () => {
  emit("update:modelValue", false);
  emit("update:tableModel", false);
};
const calcHeight = (val) => {
  //  if (props.height > clientHeight) {
  //  if(true){
  contentHeight.value = clientHeight - 30;
  return clientHeight / -2 + 'px';
  //  }
  // contentHeight.value = val || props.height;
  // return (props.height + 56) / -2 + 'px';
};
top.value = calcHeight();
watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    vmodel.value = newVal;
  }
);
watch(
  () => props.height,
  (newVal, oldVal) => {
    top.value = calcHeight();
  }
);
const fullscreen = ref(false);
const handleFullScreen = () => {
  fullscreen.value = !fullscreen.value;
}

</script>
<template>
  <div class="app-dialog">

    <el-dialog v-model="vmodel" :close-on-click-modal="modalClose" :close-on-press-escape="escapeClose" :width="width"
      :fullscreen="fullscreen" :draggable="draggable" :modal="modal" :before-close="handleClose">
      <template #header>
        <!-- <i :class="icon"></i> -->
        {{ title }}
        <button class="el-dialog__headerbtn" type="button" style="right: 35px; color: var(--el-color-info)"
          @click="handleFullScreen">
          <i class="el-icon el-icon-full-screen"></i>
        </button>
      </template>
      <el-scrollbar :max-height="contentHeight">
        <div v-if="inited" style="min-height: 50px;" class="srcoll-content" :style="{ padding: padding + 'px' }">
          <slot name="content"></slot>
          <slot></slot>
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dia-footer" v-if="footer">
          <slot name="footer"></slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.dia-footer {
  // text-align: right;
  width: 100%;
  border-top: 1px solid #e2e2e2;
  // text-align: right;
  padding: 8px 0;
}

.app-dialog :deep(.el-overlay-dialog) {
  display: flex !important;
}

.app-dialog :deep(.el-dialog) {
  margin: auto;
}

.app-dialog :deep(.el-dialog) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.app-dialog :deep(.el-dialog__header) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 0px 13px;
  line-height: 53px;
  border-bottom: 1px solid #e6e6e6;
  height: 50px;
  color: rgb(79, 79, 79);
  font-weight: bold;
  font-size: 14px;
  margin: 0;
  // background-image: linear-gradient(135deg, #0cd7bd 10%, #50c3f7);
}

// .app-dialog :deep(.el-dialog__footer),
.app-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.app-dialog :deep(.el-dialog__headerbtn) {
  top: 0;
  padding-top: 8px;
  height: 50px;
  width: 0;
  padding-right: 30px;
  padding-left: 5px;
}

.app-dialog :deep(.el-dialog) {
  padding: 0;
}
</style>
