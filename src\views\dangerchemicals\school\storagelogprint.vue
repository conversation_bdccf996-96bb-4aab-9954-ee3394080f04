<script setup>
defineOptions({
    name: 'dangerchemicalsschoolstoragelogprint'
});
import { onMounted, ref, nextTick, onActivated, watch } from 'vue'
import {
    Refresh, Back, DocumentCopy, Edit
} from '@element-plus/icons-vue'
import {
    DcScrapListFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const filters = ref({ pageIndex: 1, pageSize: 9999999, Statuz: 1, sortModel: [{ SortCode: "PurchaseBatchNo", SortType: "ASC" }] })
const printName = ref('危化品入库单')
const CompanyName = ref('')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: './materialbacklog' })
}
const isEdit = ref(false)
const handleEdit = () => {
    isEdit.value = true
}
// 列表
const HandleTableData = () => {
    if (route.query.keyword) {
        filters.value[route.query.datefiled] = route.query.keyword
    } else {
        filters.value[route.query.datefiled] = undefined
    }
    filters.value.LvCompanyId = route.query.companyid
    filters.value.RegDatege = route.query.dtBegin
    filters.value.RegDatele = route.query.dtEnd
    filters.value.Ids = route.query.ids
    DcScrapListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data || []
            CompanyName.value = tableData.value[0].CompanyName
            nextTick(() => {
                createWatermark();
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const handleNativePrint = () => {
    // 获取打印区域内容
    const printContent = document.getElementById('printArea').innerHTML;
    // 创建一个隐藏的iframe
    const iframe = document.createElement('iframe');
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');
    // 添加到DOM中
    document.body.appendChild(iframe);
    // 获取iframe的document对象
    const iframeWindow = iframe.contentWindow || iframe.contentDocument;
    const iframeDoc = iframeWindow.document || iframeWindow;
    // 写入打印内容
    iframeDoc.open();
    iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>${printName.value}</title>
            <style>
                @page { size: portrait; margin:10mm 5mm; }
                body { font-family: 宋体; margin: 0; padding: 0;   }
                .print-table { border-collapse: collapse; width: 100%; font-size: 15px; }
                .print-table th { border: 1px solid #ddd; padding: 5px 1px; } 
                .print-table td { border: 1px solid #ddd; padding: 6px 3px; }
                thead { display: table-header-group; }
                tr { page-break-inside: avoid; }
                .print-title { font-size: 35px; margin: 10px 0; text-align: center; } 
                .print-signleft { margin-top: 20px; margin-bottom: 10px; text-align: left; } 
            </style>
        </head>
        <body> 
            ${printContent}
        </body>
        </html>
    `);
    iframeDoc.close();

    // 等待内容加载完成后打印
    iframe.onload = function () {
        setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();

            // 打印完成后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 100);
        }, 200);
    };
};

const topTable = ref(0)
const settings = ref({
    watermark_txt: "JYZB",         // 水印文字内容
    watermark_x: 120,              // 水印起始x坐标
    watermark_y: 120,              // 水印起始y坐标
    watermark_rows: 4,             // 水印行数
    watermark_cols: 3,             // 水印列数
    watermark_x_space: 160,        // 水印x轴间隔
    watermark_y_space: 100,        // 水印y轴间隔
    watermark_color: '#000000',    // 水印颜色
    watermark_alpha: 0.2,          // 水印透明度
    watermark_fontsize: '26px',    // 水印字体大小
    watermark_width: 120,          // 水印宽度
    watermark_height: 120,        // 水印高度
    watermark_angle: 15            // 水印旋转角度
})

// 水印容器DOM引用
const watermarkContainer = ref(null);
// 记录最后一个水印的顶部位置
const LASTTOP = ref(0);
// 创建水印函数
const createWatermark = () => {
    // 创建文档片段(优化性能)
    const fragment = document.createDocumentFragment();
    // 解构配置参数
    const {
        watermark_txt,
        watermark_x,
        watermark_y,
        watermark_rows,
        watermark_cols,
        watermark_x_space,
        watermark_y_space,
        watermark_color,
        watermark_alpha,
        watermark_fontsize,
        watermark_width,
        watermark_height,
        watermark_angle
    } = settings.value;

    // 循环创建水印行
    for (let row = 0; row < watermark_rows; row++) {
        // 计算当前行y坐标
        const y = watermark_y + (watermark_y_space + watermark_height) * row;
        // 循环创建水印列
        for (let col = 0; col < watermark_cols; col++) {
            // 计算当前列x坐标
            const x = watermark_x + (watermark_width + watermark_x_space) * col;
            // 创建水印div元素
            const watermarkDiv = document.createElement('div');
            watermarkDiv.id = `watermark_${row}_${col}`;
            // 设置水印文字
            watermarkDiv.appendChild(document.createTextNode(watermark_txt));
            // 设置水印样式
            Object.assign(watermarkDiv.style, {
                transform: `rotate(-${watermark_angle}deg)`,  // 旋转
                position: 'absolute',        // 绝对定位
                left: `${x}px`,              // x坐标
                top: `${topTable.value + y}px`, // y坐标(加上顶部偏移)
                overflow: 'hidden',          // 隐藏溢出
                zIndex: '9998',              // 层级
                opacity: watermark_alpha,    // 透明度
                fontSize: watermark_fontsize, // 字体大小
                color: watermark_color,      // 字体颜色
                textAlign: 'center',         // 文字居中
                maxWidth: `${watermark_width}px`, // 最大宽度
                display: 'block',            // 显示为块元素
                pointerEvents: 'none'       // 禁止鼠标事件
            });
            // 添加到文档片段
            fragment.appendChild(watermarkDiv);
            // 更新最后一个水印的位置
            LASTTOP.value = topTable.value + y;
        }
    }
    // 将水印添加到容器
    watermarkContainer.value.appendChild(fragment);
};

// 监听属性变化，重新创建水印
watch(() => [topTable.value, settings.value], () => {
    createWatermark();
}, { deep: true });

// 计算每个单元格应该跨越的行数
const rowspans = ref([]);

// 计算合并信息
const calculateRowspans = () => {
    const spans = [];
    let count = 1;

    for (let i = 0; i < tableData.value.length; i++) {
        if (i === tableData.value.length - 1) {
            spans.push(count);
            break;
        }
        if (tableData.value[i].PurchaseBatchNo === tableData.value[i + 1].PurchaseBatchNo) {
            count++;
        } else {
            spans.push(count);
            count = 1;
        }
    }
    rowspans.value = spans;
};

// 获取指定行的rowspan值
const getRowspan = (index) => {
    for (let i = 0, sum = 0; i < rowspans.value.length; i++) {
        sum += rowspans.value[i];
        if (index < sum) {
            return rowspans.value[i];
        }
    }
    return 1;
};

// 判断是否应该显示用户名单元格
const shouldShowUserNameCell = (index) => {
    if (index === 0) return true;
    return tableData.value[index].PurchaseBatchNo !== tableData.value[index - 1].PurchaseBatchNo;
};

// 在数据加载后计算合并信息
watch(() => tableData.value, (newVal) => {
    if (newVal && newVal.length > 0) {
        calculateRowspans();
    }
}, { immediate: true, deep: true });

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="handleNativePrint">打印</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="!isEdit"></div>
                    <el-form-item class="flexItem" v-if="!isEdit">
                        <el-button type="primary" :icon="Edit" @click="handleEdit">修改标题</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="width: 880px;" id="printArea">
            <div class="print-title">
                <el-input v-if="isEdit" v-model.trim="printName" @blur="isEdit = false"
                    style="width: 300px;margin-left: 20px;"></el-input>
                <span v-else style="font-size: 35px;">{{ printName }}</span>
            </div>
            <div style="font-size: 16px;font-family: 宋体;margin: 10px 0;">
                <span>供应商名称：{{ CompanyName }} </span>
            </div>
            <table class="print-table" ref="watermarkContainer">
                <thead>
                    <tr>
                        <th style="width: 40px;">序号</th>
                        <th style="width: 130px;">危化品名称</th>
                        <th style="width: 160px;">规格属性</th>
                        <th style="width: 80px;">数量</th>
                        <th style="width: 40px;">单位</th>
                        <th style="width: 70px;">单价</th>
                        <th style="width: 90px;">金额</th>
                        <th style="width: 95px;">采购批次</th>
                        <th style="width: 110px;">入库时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in tableData" :key="row.Id">
                        <td style="text-align: center;">{{ index + 1 }}</td>
                        <td>{{ row.Name }}</td>
                        <td>{{ row.Model }}</td>
                        <td style="text-align: right;">{{ row.Num }}</td>
                        <td style="text-align: center;">{{ row.UnitName }}</td>
                        <td style="text-align: right;">{{ row.Price }}</td>
                        <td style="text-align: right;">{{ row.Sum }}</td>
                        <td style="text-align: center;" :rowspan="getRowspan(index)"
                            v-if="shouldShowUserNameCell(index)">{{ row.PurchaseBatchNo || '--' }}</td>
                        <td style="text-align: center;">{{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}</td>
                    </tr>
                    <!-- 添加合计行 -->
                    <tr>
                        <td></td>
                        <td>总计（元）：</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td style="text-align: right;">
                            {{tableData.reduce((sum, row) => sum + row.Sum, 0)}}
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
            <div class="print-signleft" style="display: flex;">
                <div style="width: 30%;"> 审核人： </div>
                <div style="width: 30%;"> 审批人： </div>
                <div style="width: 30%;"> 经办人： </div>
            </div>
        </div>
    </div>
</template>
<style>
/* 打印样式 - 必须放在style标签中 */
@media print {
    .el-table {
        border: 1px solid #ebeef5;

    }

    .el-table th,
    .el-table td {
        border: 1px solid #ebeef5 !important;
    }

    .el-table__header-wrapper {
        display: table-header-group !important;
    }

    .el-table__body-wrapper tr {
        page-break-inside: avoid !important;
    }

    .el-pagination {
        display: none !important;
    }


}

.print-title {
    font-size: 35px;
    font-family: 宋体;
    margin: 10px 0;
    text-align: center;
}


.print-signleft {
    margin-top: 20px;
    font-family: 宋体;
    margin-bottom: 10px;
    text-align: left;
}
</style>
<style lang="scss" scoped>
table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

table th {
    border: 1px solid #ddd;
    padding: 10px 1px;
}

table td {
    border: 1px solid #ddd;
    padding: 6px 3px;
}

.watermark-container {
    position: relative;
    width: 100%;
    height: 100%;
    pointer-events: none;
    /* 防止水印干扰页面交互 */
}

.print-signcenter {
    width: 150px;
    display: inline-block;
    margin-right: 20px;
}
</style>