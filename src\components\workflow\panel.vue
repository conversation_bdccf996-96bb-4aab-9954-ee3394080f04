<script setup>
import { onMounted, ref, nextTick, onActivated, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
    ProcessSetByid, ProcessSetSave, GetdataSourceBydicValue, GetProcessReturnSet, SetProcessReturn
} from '@/api/workflow.js'
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
import './jsplumb'// 使用修改后的jsplumb
import FlowNode from './node.vue'
import NodeMenu from './node_menu.vue'
import lodash from 'lodash'
const { proxy } = getCurrentInstance();
const userStore = useUserStore()
const route = useRoute()
import router from '@/router'
const props = defineProps({
    disabled: {
        typeof: Boolean,
        default: false
    },
    processId: {
        type: String,
        default: "",
    },
});

const isProcessNodeList = ref(false)
const processNodeList = ref([])
const processData = ref({})
const isLine = ref(false)
const jsPlumbObj = ref() // jsPlumb 实例
const easyFlowVisible = ref(true) // 控制画布销毁
const loadEasyFlowFinish = ref(false)// 是否加载完毕标志位
const data = ref({})
// 激活的元素、可能是节点、可能是连线
const activeElement = ref({
    type: undefined,// 可选值 node 、line
    nodeId: undefined,// 节点ID
    sourceId: undefined,// 连线ID
    targetId: undefined
})
const zoom = ref(1)
// 基础配置
const jsplumbSetting = ref({
    // 动态锚点、位置自适应
    Anchors: ['Top', 'TopCenter', 'TopRight', 'TopLeft', 'Right', 'RightMiddle', 'Bottom', 'BottomCenter', 'BottomRight', 'BottomLeft', 'Left', 'LeftMiddle'],
    // 容器ID
    Container: 'efContainer',
    // 连线的样式，直线或者曲线等，可选值:  StateMachine、Flowchart，Bezier、Straight
    //Connector: ['Bezier', {curviness: 100}],
    // Connector: ['Straight', {stub: 20, gap: 1}],
    Connector: ['Flowchart', { stub: 30, gap: 1, alwaysRespectStubs: false, midpoint: 0.5, cornerRadius: 10 }],
    // Connector: ['StateMachine', {margin: 5, curviness: 10, proximityLimit: 80}],
    // 鼠标不能拖动删除线
    ConnectionsDetachable: false,
    // 删除线的时候节点不删除
    DeleteEndpointsOnDetach: false,
    // 连线的两端端点类型：圆形 radius: 圆的半径，越大圆越大
    // Endpoint: ['Dot', {radius: 5, cssClass: 'ef-dot', hoverClass: 'ef-dot-hover'}],
    // 连线的两端端点类型：矩形 height: 矩形的高  width: 矩形的宽
    // Endpoint: ['Rectangle', {height: 20, width: 20, cssClass: 'ef-rectangle', hoverClass: 'ef-rectangle-hover'}],
    // 图像端点
    // Endpoint: ['Image', {src: 'https://www.easyicon.net/api/resizeApi.php?id=1181776&size=32', cssClass: 'ef-img', hoverClass: 'ef-img-hover'}],
    // 空白端点
    Endpoint: ['Blank', { Overlays: '' }],
    // Endpoints: [['Dot', {radius: 5, cssClass: 'ef-dot', hoverClass: 'ef-dot-hover'}], ['Rectangle', {height: 20, width: 20, cssClass: 'ef-rectangle', hoverClass: 'ef-rectangle-hover'}]],
    //  连线的两端端点样式 fill: 颜色值，如：#12aabb，为空不显示 outlineWidth: 外边线宽度
    EndpointStyle: { fill: '#1879ffa1', outlineWidth: 1 },
    // 是否打开jsPlumb的内部日志记录
    LogEnabled: true,
    // 连线的样式
    PaintStyle: {
        // 线的颜色
        stroke: '#b0b2b5',
        // 线的粗细，值越大线越粗
        strokeWidth: 1,
        // 设置外边线的颜色，默认设置透明，这样别人就看不见了，点击线的时候可以不用精确点击，参考 https://blog.csdn.net/roymno2/article/details/72717101
        outlineStroke: 'transparent',
        // 线外边的宽，值越大，线的点击范围越大
        outlineWidth: 10
    },
    DragOptions: { cursor: 'pointer', zIndex: 2000 },
    //叠加 参考： https://www.jianshu.com/p/d9e9918fd928
    Overlays: [
        // 箭头叠加
        ['Arrow', {
            width: 10, // 箭头尾部的宽度
            length: 8, // 从箭头的尾部到头部的距离
            location: 1, // 位置，建议使用0～1之间
            direction: 1, // 方向，默认值为1（表示向前），可选-1（表示向后）
            foldback: 0.623 // 折回，也就是尾翼的角度，默认0.623，当为1时，为正三角
        }],
        // ['Diamond', {
        //     events: {
        //         dblclick: function (diamondOverlay, originalEvent) {
        //             console.log('double click on diamond overlay for : ' + diamondOverlay.component)
        //         }
        //     }
        // }],
        ['Label', {
            label: '',
            location: 0.1,
            cssClass: 'aLabel'
        }]
    ],
    // 绘制图的模式 svg、canvas
    RenderMode: 'svg',
    // 鼠标滑过线的样式
    HoverPaintStyle: { stroke: '#1890ff', strokeWidth: 3 },
    // 滑过锚点效果
    // EndpointHoverStyle: {fill: 'red'}
    Scope: 'jsPlumb_DefaultScope' // 范围，具有相同scope的点才可连接
})
// 连线参数
const jsplumbConnectOptions = ref({
    isSource: true,
    isTarget: true,
    // 动态锚点、提供了4个方向 Continuous、AutoDefault
    anchor: 'Continuous',
    // 设置连线上面的label样式
    labelStyle: {
        cssClass: 'flowLabel'
    },
    // 修改了jsplumb 源码，支持label 为空传入自定义style
    emptyLabelStyle: {
        cssClass: 'emptyFlowLabel'
    }
})
// 源点配置参数
const jsplumbSourceOptions = ref({
    // 设置可以拖拽的类名，只要鼠标移动到该类名上的DOM，就可以拖拽连线
    filter: '.flow-node-drag',
    filterExclude: false,
    anchor: 'Continuous',
    // 是否允许自己连接自己
    allowLoopback: true,
    maxConnections: -1,
    onMaxConnections: function (info, e) {
        console.log(`超过了最大值连线: ${info.maxConnections}`)
    }
})
const jsplumbTargetOptions = ref({
    // 设置可以拖拽的类名，只要鼠标移动到该类名上的DOM，就可以拖拽连线
    filter: '.flow-node-drag',
    filterExclude: false,
    // 是否允许自己连接自己
    anchor: 'Continuous',
    allowLoopback: true,
    dropOptions: { hoverClass: 'ef-drop-hover' }
})

onMounted(() => {
    if (route.query.isTagRouter) {
        ProcessSetByidUser()
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            ProcessSetByidUser()
        }
    })
})
// 获取初始流程数据
const ProcessSetByidUser = () => {
    ProcessSetByid({ id: props.processId }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            nextTick(() => {
                isProcessNodeList.value = true;
                processNodeList.value = other.listNode || [];//节点列表
                filterTypeList.value = other.listConditionSymbol || [];//条件符号列表
                fieldsOptions.value = other.listGroupField || [];//条件字段列表  pid:'0':输入框：'1':下拉框调用接口  
                processData.value = rows || {};//已配置流程数据

                if (other.lineConfig) {
                    data.value.lineList = JSON.parse(other.lineConfig)
                } else {
                    data.value.lineList = [];
                }
                if (other.nodeConfig) {
                    data.value.nodeList = JSON.parse(other.nodeConfig)
                } else {
                    data.value.nodeList = [];
                }
                // console.log("data.valuedata.valuedata.valuedata.valuedata.value", data.value, data.value.lineList)
                dataReload(data.value)
            });
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const jsPlumbInit = () => {
    jsPlumbObj.value.ready(() => {
        // 导入默认配置
        jsPlumbObj.value.importDefaults(jsplumbSetting.value)
        // 会使整个jsPlumb立即重绘。
        jsPlumbObj.value.setSuspendDrawing(false, true);
        // 初始化节点
        loadEasyFlow()
        // 单点击了连接线
        jsPlumbObj.value.bind('click', (conn) => {
            console.log('鼠标左键单点击了连接线', conn, '起点节点id', conn.sourceId, '终点节点id', conn.targetId)
            // 点击设置条件
            isLine.value = true
            activeElement.value.type = 'line'
            nodeInit(data.value, conn.sourceId, conn.targetId)
            sourceId.value = conn.sourceId
            targetId.value = conn.targetId

            // 重置所有连线颜色
            jsPlumbObj.value.select({}).each(connection => {
                connection.setPaintStyle({
                    stroke: 'RGB(168,170,173)',
                    strokeWidth: 1
                })
            })
            // 设置当前连线高亮
            conn.setPaintStyle({
                stroke: '#1890ff', // 高亮颜色
                strokeWidth: 3  // 可选加粗效果
            })

        })
        // 连线右击
        jsPlumbObj.value.bind("contextmenu", (conn, originalEvent) => {
            console.log('鼠标右键点击了连接线', conn.sourceId, conn.targetId)
            activeElement.value.type = 'line'
            activeElement.value.sourceId = conn.sourceId
            activeElement.value.targetId = conn.targetId
            deleteElement();
        })
        // 连线
        jsPlumbObj.value.bind("connection", (evt) => {
            let from = evt.source.id
            let to = evt.target.id
            if (loadEasyFlowFinish.value) {
                data.value.lineList.push({ from: from, to: to })
            }
        })

        // 删除连线回调
        jsPlumbObj.value.bind("connectionDetached", (evt) => {
            deleteLine(evt.sourceId, evt.targetId)
        })

        // 改变线的连接节点
        jsPlumbObj.value.bind("connectionMoved", (evt) => {
            changeLine(evt.originalSourceId, evt.originalTargetId)
        })

        // 连线
        jsPlumbObj.value.bind("beforeDrop", (evt) => {
            let from = evt.sourceId
            let to = evt.targetId
            if (from === to) {
                ElMessage.error('节点不支持连接自己')
                return false
            }
            if (hasLine(from, to)) {
                ElMessage.error('该关系已存在,不允许重复创建')
                return false
            }
            // if (hashOppositeLine(from, to)) {
            //     ElMessage.error('不支持两个节点之间连线回环');
            //     return false
            // }
            ElMessage.success('连接成功')
            setTimeout(() => { setLineLabel(from, to, '') }, 50)
            return true
        })

        jsPlumbObj.value.bind("beforeDetach", (evt) => {
            console.log('beforeDetach', evt)
        })
        jsPlumbObj.value.setContainer(proxy.$refs.efContainer)
    })
}

// 加载流程图
const loadEasyFlow = () => {
    console.log("加载流程图data.value", data.value.lineList, data.value.nodeList)
    // 初始化节点
    for (var i = 0; i < data.value.nodeList.length; i++) {
        let node = data.value.nodeList[i]
        if (node.userId && node.userId != '') {
            // userId为数值类型
            if (typeof node.userId == 'number') {
                node.userId = [node.userId]
            } else {
                node.userId = node.userId.split(',').map(Number);
            }
        } else {
            node.userId = []
        }
        // 设置源点，可以拖出线连接其他节点
        jsPlumbObj.value.makeSource(node.id, lodash.merge(jsplumbSourceOptions.value, {}))
        // // 设置目标点，其他源点拖出的线可以连接该节点
        jsPlumbObj.value.makeTarget(node.id, jsplumbTargetOptions.value)
        if (!node.viewOnly && !props.disabled) {
            jsPlumbObj.value.draggable(node.id, {
                containment: 'parent',
                stop: function (el) {
                    // 拖拽节点结束后的对调
                    console.log('拖拽结束: ', el)
                }
            })
        }
    }
    // 初始化连线
    console.log('初始化连线data.value', data.value, data.value.lineList)
    for (var i = 0; i < data.value.lineList.length; i++) {
        let line = data.value.lineList[i]
        var connParam = {
            source: line.from,
            target: line.to,
            label: props.disabled ? null : (line.label ? line.label : ''),
            connector: line.connector ? line.connector : '',
            anchors: line.anchors ? line.anchors : undefined,

            paintStyle: line.paintStyle ? line.paintStyle : undefined,
        }
        // console.log('初始化连线', connParam, jsplumbConnectOptions.value)
        jsPlumbObj.value.connect(connParam, jsplumbConnectOptions.value)
    }
    nextTick(function () {
        loadEasyFlowFinish.value = true
    })
}
// 设置连线条件
const setLineLabel = (from, to, label) => {
    var conn = jsPlumbObj.value.getConnections({
        source: from,
        target: to
    })[0]
    if (!label || label === '') {
        conn.removeClass('flowLabel ')
        conn.addClass('emptyFlowLabel')
    } else {
        conn.addClass('flowLabel')
    }
    conn.setLabel({
        label: '' //label,
    })
    data.value.lineList.forEach(function (line) {
        if (line.from == from && line.to == to) {
            line.label = ''// label
        }
    })
}

// 删除连线
const deleteElement = () => {
    // console.log(props.disabled, "删除激活的元素", activeElement.value)
    if (props.disabled) return
    if (activeElement.value.type === 'line') {
        ElMessageBox.confirm('确定删除所点击的连线吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            var conn = jsPlumbObj.value.getConnections({
                source: activeElement.value.sourceId,
                target: activeElement.value.targetId
            })[0]
            jsPlumbObj.value.deleteConnection(conn)
            if (sourceId.value == activeElement.value.sourceId && targetId.value == activeElement.value.targetId) {
                isLine.value = false
            }
        }).catch(() => {
        })
    }
}
// 删除线
const deleteLine = (from, to) => {
    data.value.lineList = data.value.lineList.filter(function (line) {
        if (line.from == from && line.to == to) {
            return false
        }
        return true
    })
}
// 改变连线
const changeLine = (oldFrom, oldTo) => {
    deleteLine(oldFrom, oldTo)
}
// 改变节点的位置
const changeNodeSite = (d) => {
    // console.log("改变节点的位置", data)
    for (var i = 0; i < data.value.nodeList.length; i++) {
        let node = data.value.nodeList[i]
        if (node.id === d.nodeId) {
            node.left = d.left
            node.top = d.top
        }
    }
}
// 拖拽结束后添加新的节点 nodeMenu:被添加的节点对象  mousePosition:鼠标拖拽结束的坐标
const addNode = (evt, nodeMenu, mousePosition) => {
    // console.log("拖拽结束后data.value", data.value, nodeMenu)
    if (data.value.nodeList.some(x => { return x.id == nodeMenu.Id })) {
        ElMessage.error(`【${nodeMenu.NodeName}】节点已存在`);
        return
    }
    var screenX = evt.originalEvent.clientX, screenY = evt.originalEvent.clientY
    let efContainer = proxy.$refs.efContainer
    var containerRect = efContainer.getBoundingClientRect()
    var left = screenX, top = screenY
    // 计算是否拖入到容器中
    if (left < containerRect.x || left > containerRect.width + containerRect.x || top < containerRect.y || containerRect.y > containerRect.y + containerRect.height) {
        ElMessage.error("请把节点拖入到画布中")
        return
    }
    left = left - containerRect.x + efContainer.scrollLeft
    top = top - containerRect.y + efContainer.scrollTop
    // 居中
    left -= 85
    top -= 16
    var node = {
        id: nodeMenu.Id,
        name: nodeMenu.NodeName,
        type: nodeMenu.type,
        left: left + 'px',
        top: top + 'px',
        // ico: nodeMenu.ico,
        ico: "el-icon-news",
        state: 'success'
    }
    // console.log('节点属性', node)
    // 这里可以进行业务判断、是否能够添加该节点
    data.value.nodeList.push(node)
    nextTick(function () {
        jsPlumbObj.value.makeSource(nodeMenu.Id, jsplumbSourceOptions.value)
        jsPlumbObj.value.makeTarget(nodeMenu.Id, jsplumbTargetOptions.value)
        jsPlumbObj.value.draggable(nodeMenu.Id, {
            containment: 'parent',
            stop: function (el) {
                // 拖拽节点结束后的对调
                // console.log('拖拽结束: ', el)
            }
        })
    })
}
// 删除节点  nodeId 被删除节点的ID
const deleteNode = (node) => {
    // dialogVisible.value = false
    // console.log("删除节点", node)
    ElMessageBox.confirm('确定要删除节点' + node.name + '?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
    }).then(() => {
        //这里需要进行业务判断，是否可以删除
        data.value.nodeList = data.value.nodeList.filter(function (n) {
            if (n.id === node.id) {
                // 伪删除，将节点隐藏，否则会导致位置错位
                // node.show = false
                return false
            }
            return true
        })
        nextTick(function () {
            jsPlumbObj.value.removeAllEndpoints(node.id);
        })
    }).catch(() => {
    })
    return true
}
//节点不通过退回操作
const dialogVisible = ref(false)
const dialogData = ref({})
const BackWayList = ref([])
const refForm = ref()
const processNodeId = ref()

// 点击节点
const clickNode = (nodeId) => {
    // 节点Id
    console.log('节点Id', nodeId, "流程Id", props.processId)
    // 退回弹窗
    GetProcessReturnSet({ processId: props.processId, processNodeId: nodeId }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            console.log(rows, other, "退回")
            BackWayList.value = other.listBackWay
            dialogData.value.BackWay = String(rows.BackWay)
            dialogData.value.Sort = rows.Sort
            dialogData.value.Id = rows.Id
            dialogVisible.value = true

        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
// 退回提交
const HandleSubmit = (nodeId) => {
    SetProcessReturn(dialogData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '提交成功')
            dialogVisible.value = false
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
// 是否具有该线
const hasLine = (from, to) => {
    for (var i = 0; i < data.value.lineList.length; i++) {
        var line = data.value.lineList[i]
        if (line.from === from && line.to === to) {
            return true
        }
    }
    return false
}
// 是否含有相反的线
const hashOppositeLine = (from, to) => {
    return hasLine(to, from)
}
const nodeRightMenu = (nodeId, evt) => {
    menu.value.show = true
    menu.value.curNodeId = nodeId
    menu.value.left = evt.x + 'px'
    menu.value.top = evt.y + 'px'
}
// 加载流程图
const dataReload = (d) => {
    // console.log(d, "加载流程图", data.value)
    easyFlowVisible.value = false
    nextTick(() => {
        easyFlowVisible.value = true
        data.value = lodash.cloneDeep(data.value)
        nextTick(() => {
            jsPlumbObj.value = jsPlumb.getInstance()
            nextTick(() => {
                jsPlumbInit()
            })
        })
    })
}
// 放大
const zoomAdd = () => {
    if (zoom.value >= 1) {
        return
    }
    zoom.value = zoom.value + 0.1
    proxy.$refs.efContainer.style.zoom = zoom.value;
}
// 缩小
const zoomSub = () => {
    if (zoom.value <= 0) {
        return
    }
    zoom.value = zoom.value - 0.1;
    if (zoom.value < 0.3) {
        zoom.value = 0.3;
    }
    proxy.$refs.efContainer.style.zoom = zoom.value;
}
// 流程配置提交
const save = () => {
    // console.log("data.value.nodeList", data.value.nodeList)
    let nodeList = data.value.nodeList;
    let nodeListOptions = JSON.parse(JSON.stringify(nodeList));
    nodeListOptions.forEach(item => {
        if (item.filters && item.filters.data) {
            item.filters.data = undefined;
        }
        if (item.userId && item.userId.length) {
            item.userId = item.userId.join(',');
        }
    })
    // console.log("data.value.lineList", data.value.lineList)
    let lineList = data.value.lineList;
    lineList = JSON.parse(JSON.stringify(lineList));

    let rootNode = nodeList.map(c => {
        return {
            StepId: c.id,
            StepName: c.name,
            StepAttrType: c.type,
            StepAuditType: null,
            ParentId: [''],
            Filters: c.filters
        }
    });

    for (let index = 0; index < rootNode.length; index++) {
        const node = rootNode[index];
        node.OrderId = index;
        //这里有一节点有多个上级节点的时候数据重复了，比如线束节点
        lineList.filter(c => { return c.from == node.StepId }).forEach(c => {
            let item = nodeList.find(x => { return x.id == c.to });
            let _obj = rootNode.find(x => { return x.StepId === item.id });
            if (_obj) {
                _obj.ParentId.push(node.StepId);
            } else {
                rootNode.push({
                    ParentId: [node.StepId], //父级id
                    StepId: item.id,
                    StepName: item.name,
                    StepAttrType: item.type, //节点类型.start开始，end结束 
                    StepType: item.auditType,//审核类型,角色，用户，部门(这里后面考虑同时支持多个角色、用户、部门)
                    //审核选择的值角色，用户，部门(这里后面考虑同时支持多个角色、用户、部门)
                    StepValue: item.auditType == 1 ? item.userId.join(',') : (item.auditType == 2 ? item.roleId : item.deptId),
                    AuditRefuse: item.auditRefuse,//审核未通过(返回上一节点,流程重新开始,流程结束)
                    AuditBack: item.auditBack, //驳回(返回上一节点,流程重新开始,流程结束)
                    AuditMethod: item.auditMethod,//审批方式(启用会签)
                    SendMail: 0, //审核后发送邮件通知：
                    Filters: item.filters
                })
            }
        });
    }

    rootNode.forEach(item => {
        item.ParentId = item.ParentId.filter(x => { return x }).join(',');
        if (item.Filters && item.Filters.length) {
            item.Filters = item.Filters.map(m => {
                return {
                    "field": m.field,
                    filterType: m.filterType,
                    value: Array.isArray(m.value) ? m.value.join(',') : m.value
                }
            });
            item.Filters = JSON.stringify(item.Filters)
        } else {
            item.Filters = null;
        }
    })
    console.log('lineList', lineList)

    let params = {
        ProcessId: props.processId,
        NodeConfig: JSON.stringify(nodeListOptions),
        LineConfig: JSON.stringify(lineList)
    };
    console.log('流程配置保存params', params);
    ProcessSetSave(params).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            let tagsList = userStore.tagsList
            tagsList = tagsList.filter(t => t.path != route.path)
            userStore.setTagsList(tagsList)
            router.push({ path: "/approvalconfiguration/workflow/processmanagement" })
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

const clearItems = () => {
    data.value.nodeList = [];//清除所有节点
    data.value.lineList = [];
    jsPlumbObj.value.deleteEveryConnection();//清除所有连线
}

const sqlCondition = ref("")
const fieldsOptions = ref([]);
const filterTypeList = ref([]);
// 删除条件
const delItem = (index) => {
    filters.value.splice(index, 1);
}
// 添加字段
const addItem = () => {
    filters.value.push({ field: "", value: "", filterType: "", data: null })
}
// 选择字段判断是否为下拉选择
const fieldChange = (field, index) => {
    // console.log("fieldChange", field, index)
    let option = fieldsOptions.value.find(x => { return x.value == field });
    // console.log("option", option)
    filters.value[index].field = option.value;
    filters.value[index].fieldName = option.label;
    // 判断是否需要调用下拉框
    if (option.pid == '1') {
        // 根据字段类型获取值的数据
        GetdataSourceBydicValue({ code: option.pname }).then(res => {
            if (res.data.flag == 1) {
                const { rows, other } = res.data.data
                // console.log("根据数据源选择的编码获取数据", rows)
                filters.value[index].data = rows;
            } else {
                ElMessage.error(res.data.msg)
            }
        });

    } else {
        filters.value[index].data = null;
    }
    // 选择字段类型，清除值，避免冲突
    filters.value[index].value = '';
    filters.value[index].value1 = [];
    filters.value[index].filterType = '';

}
// 选择运算符
const filterTypeChange = (type, index) => {
    // console.log("filterTypeChange", type, index)
    filters.value[index].symbol = type;
    filters.value[index].value = '';
    filters.value[index].value1 = [];
}

const filters = ref([])
// 点击连线获取条件数据
const nodeInit = (d, sourceId, targetId) => {
    console.log("nodeInit", d)
    data.value = d;
    // console.log("nodeInit", data.value)
    d.lineList.filter((n) => {
        if (n.from === sourceId && n.to === targetId) {
            if (!n.filters) {
                n.filters = [];
            }
            filters.value = n.filters;
            sqlCondition.value = n.sqlCondition || "";
        }
    })
    // console.log("filters", filters.value)
}
const sourceId = ref()
const targetId = ref()
// 条件输入
const sqlConditionChange = (e) => {
    data.value.lineList.filter((n) => {
        if (n.from === sourceId.value && n.to === targetId.value) {
            n.sqlCondition = e;
        }
    })
}

// 使用 defineExpose 来暴露方法给父组件
defineExpose({
    dataReload, data
});

</script>
<template>
    <div v-if="easyFlowVisible" class="flow-panel">
        <div style="display: flex;height: 100%;position: relative;">
            <el-scrollbar style="height: 100%;border-right: 1px solid rgb(220, 227, 232);">
                <div style="width: 220px;" v-if="isProcessNodeList">
                    <node-menu @addNode="addNode" :processNodeList="processNodeList" ref="nodeMenu"></node-menu>
                </div>
            </el-scrollbar>
            <div class="center-top">
                <el-button type="primary" size="small" plain @click="save"><i class="el-icon-check"> </i>保存</el-button>
                <el-button type="primary" @click="clearItems" size="small" plain><i class="el-icon-delete">
                    </i>清空</el-button>
            </div>
            <div class="tools">
                <el-button circle @click="zoomAdd"><i class="el-icon-zoom-in"></i></el-button>
                <el-button circle @click="zoomSub"><i class="el-icon-zoom-out"></i></el-button>
            </div>
            <div style="flex: 1;" id="efContainer" ref="efContainer" class="container efContainer" v-flowDrag
                @contextmenu.prevent>
                <template :key="node.id" v-for="node in data.nodeList">
                    <flow-node :id="node.id" @delNode="deleteNode(node)" :node="node" :activeElement="activeElement"
                        :disabled="disabled" @changeNodeSite="changeNodeSite" @nodeRightMenu="nodeRightMenu"
                        @clickNode="clickNode">
                    </flow-node>
                </template>
                <!-- 给画布一个默认的宽度和高度 -->
                <div style="position:absolute;top: 3000px;left: 4000px;">&nbsp;</div>
            </div>
            <!-- 右侧表单 -->
            <div style="width: 400px;border-left: 1px solid #dce3e8;background-color: #FBFBFB">
                <div class="ef-node-pmenu-item"><i class="el-icon-news"></i>条件设置</div>
                <el-scrollbar style="height: 100%;padding-bottom: 10px;" v-if="isLine">
                    <div class="node-filter-container">
                        <div class="ef-node-pmenu-item" style="display: flex;align-items: center;">
                            <div style="flex:1;">
                                <span class="name">条件输入</span>
                            </div>
                            <div style="width: 80%;">
                                <el-input v-model="sqlCondition" @change="sqlConditionChange"></el-input>
                            </div>
                        </div>
                        <div style="background-color: #fff;padding: 5px 10px;  font-size: 14px;color: #333;">
                            <div>当使用“条件输入”时，输入的字段编码，需在“条件设置”内添加对应字段，同时运算符必须选择‘占位’</div>
                            <div>示例： </div>
                            <div style="color: #666;padding: 3px 0;">code == "1100" && (money < 1000 || money> 5000)
                            </div>
                            <div style="color: #666;">
                                在下方“条件设置”内：字段选择'code'，运算符选择'占位'，值输入'1100';字段选择'money'，运算符选择'占位'，值输入'1000';字段选择'money'，运算符选择'占位'，值输入'5000';
                            </div>
                        </div>
                        <div class="ef-node-pmenu-item" style="display: flex;">
                            <div style="flex:1;">
                                <span class="name">条件设置</span>
                            </div>
                            <div>
                                <el-button link size="small" @click="addItem" type="primary"> + 添加字段</el-button>
                            </div>
                        </div>
                        <div>
                            <table>
                                <tbody>
                                    <tr>
                                        <td style="width:200px;text-align: center;">字段</td>
                                        <td style="width:90px;text-align: center;">运算符</td>
                                        <td style="width:100px;text-align: center;">值</td>
                                        <td style="width: 40px;;text-align: center;">操作</td>
                                    </tr>
                                    <tr v-for="(item, index) in filters" :key="index">
                                        <td>
                                            <el-select @change="(field) => { fieldChange(field, index) }" size="small"
                                                v-model="item.field" placeholder="请选择">
                                                <el-option v-for="data in fieldsOptions" :key="data.value"
                                                    :label="data.label" :value="data.value" />
                                            </el-select>
                                        </td>
                                        <td>
                                            <el-select @change="(type) => { filterTypeChange(type, index) }"
                                                size="small" v-model="item.filterType" placeholder="请选择">
                                                <el-option v-for="data in filterTypeList" :key="data.value"
                                                    :label="data.label" :value="data.value" />
                                            </el-select>
                                        </td>
                                        <td>
                                            <template v-if="item.data">
                                                <el-select v-if="item.filterType == '==' || item.filterType == '!='"
                                                    size="small" v-model="item.value1" placeholder="请选择" multiple
                                                    @change="(e) => { item.value = e.join(',') }">
                                                    <el-option v-for="data in item.data" :key="data.value"
                                                        :label="data.label" :value="data.value" />
                                                </el-select>
                                                <el-select v-else size="small" v-model="item.value" placeholder="请选择">
                                                    <el-option v-for="data in item.data" :key="data.value"
                                                        :label="data.label" :value="data.value" />
                                                </el-select>
                                            </template>
                                            <el-input v-else v-model="item.value" size="small"></el-input>
                                        </td>
                                        <td @click="delItem(index)" class="item-del"><i class="el-icon-delete"></i>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog v-model="dialogVisible" title="退回" width="680px">
        <el-form @submit.prevent ref="refForm" :model="dialogData" label-width="140px" status-icon>
            <el-form-item label="退回级别：">
                <el-select v-model="dialogData.BackWay" style="width: 80%">
                    <el-option v-for="item in BackWayList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="排序值：">
                <el-input type="number" v-model="dialogData.Sort" style="width: 80%"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="HandleSubmit">
                    提交
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>
<style scoped lang="scss">
@import './index.css';

.flow-panel {
    height: 100%;
    width: 100%;
}

.flow-panel :deep(.el-form-item__label) {
    margin-bottom: -2px !important;
    text-align: left;
    padding: 0 !important;
    justify-content: flex-start;
}

.flow-panel :deep(.el-form-item) {
    display: flex;
    flex-direction: column;
    margin-bottom: 7px !important;

}

.ef-node-menu-form {
    padding: 0px;
}

::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

::-webkit-scrollbar-thumb {
    border-radius: 0px;
    background: #e0e3e7;
    height: 20px;
}

::-webkit-scrollbar-track {
    background-color: transparent;
}



.el-scrollbar {
    width: auto !important;
}

.center-top {
    position: absolute;
    left: 320px;
    height: 42px;
    line-height: 41px;
    // background: #f2f5fb;
    // border-bottom: 1px solid #eee;
    text-align: left;
    padding: 0 10px;
    font-size: 12px;
    color: #3391f3;
    z-index: 99;
}

.center-top span {
    margin-right: 10px;
}

.node-filter-container {
    margin-top: 15px;

    table {
        width: 100%;
        padding-left: 6px;

        td {
            font-size: 13px;
            padding: 5px;

        }

        tr:first-child {
            font-size: 12px;
            font-weight: bolder;
        }

        .item-del {
            text-align: center;
            color: rgb(226, 4, 4);
            cursor: pointer;
        }

    }

    .add-btn {
        text-align: right;
        padding-right: 10px;
        border-bottom: 1px solid #e8e8e8;
        padding-bottom: 5px;
    }

}
</style>