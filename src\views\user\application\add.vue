<template>
    <el-form style="width: 900px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
        label-width="180px" status-icon>
        <el-form-item label="显示名称" prop="displayName">
            <el-input v-model="formData.displayName" placeholder="请输入显示名称" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
            <el-input v-model="formData.description" type="textarea" placeholder="请输入描述" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="分类" prop="category">
            <el-input v-model="formData.category" placeholder="请输入分类" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="重定向URI" prop="redirectUrisArray">
            <div class="redirect-uris">
                <div v-for="(uri, index) in formData.redirectUrisArray" :key="index">
                    <el-input v-model="formData.redirectUrisArray[index]" placeholder="请输入有效的URL地址" style="width: 50%;"
                        @blur="$refs.refForm.validateField('redirectUrisArray')">
                        <template #append>
                            <el-button type="danger" @click="removeUri('redirectUrisArray', index)">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </el-button>
                        </template>
                    </el-input>
                </div>
                <el-button type="primary" @click="addUri('redirectUrisArray')" style="width: 50%;">
                    <el-icon>
                        <Plus />
                    </el-icon>添加重定向URI
                </el-button>
            </div>
        </el-form-item>
        <el-form-item label="登出重定向URI" prop="postLogoutRedirectUrisArray">
            <div class="redirect-uris">
                <div v-for="(uri, index) in formData.postLogoutRedirectUrisArray" :key="index">
                    <el-input v-model="formData.postLogoutRedirectUrisArray[index]" placeholder="请输入有效的URL地址"
                        style="width: 50%;" @blur="$refs.refForm.validateField('postLogoutRedirectUrisArray')">
                        <template #append>
                            <el-button type="danger" @click="removeUri('postLogoutRedirectUrisArray', index)">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </el-button>
                        </template>
                    </el-input>
                </div>
                <el-button type="primary" @click="addUri('postLogoutRedirectUrisArray')" style="width: 50%;">
                    <el-icon>
                        <Plus />
                    </el-icon>添加登出重定向URI
                </el-button>
            </div>
        </el-form-item>
        <el-form-item label="应用Logo">
            <el-upload class="avatar-uploader" :show-file-list="false" action="#" accept="image/png,image/jpg,image/jpeg"
                :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :http-request="httpRequest">
                <img v-if="formData.iconUri" :src="formData.iconUri" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon">
                    <Plus />
                </el-icon>
            </el-upload>
        </el-form-item>
        <el-form-item label="网站URI" prop="websiteUri">
            <el-input v-model="formData.websiteUri" placeholder="请输入网站URI" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
            <el-input v-model="formData.sort" placeholder="请输入排序权重" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" :icon="Select" @click="handleSubmit">{{ route.query.id ? '保存' : '新增'
                }}</el-button>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { onMounted, ref, nextTick, onActivated } from 'vue';
import { useRoute } from 'vue-router';
import router from '@/router';
import { addOrEditApplication, getApplicationById } from '@/api/application';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Select, Plus, Delete } from '@element-plus/icons-vue';
import { AttachmentUpload } from '@/api/user.js'

const route = useRoute();
const refForm = ref(null);
const fileToUpload = ref(null);
// 表单数据初始化
const formData = ref({
    id: '',
    displayName: '',
    description: '',
    category: '',
    redirectUris: '',
    postLogoutRedirectUris: '',
    redirectUrisArray: [],
    postLogoutRedirectUrisArray: [],
    iconUri: '',
    websiteUri: '',
    sort: 0,
});

const handleAvatarSuccess = (
  response,
  uploadFile
) => {
  formData.value.iconUri = response; 
  console.log("response", response, "uploadFile", uploadFile)
}

const beforeAvatarUpload = (rawFile) => {
  console.log("rawFile", rawFile)
  if (rawFile.size / 1024 / 1024 > 5) {
    ElMessage.error('上传Logo图片大小不能超过 5MB!')
    return false
  }
  fileToUpload.value = rawFile; 
  return true
}
// 附件上传
const httpRequest = ({ file }) => {
  AttachmentUpload({ file: file, filecategory: 501001 }).then(res => {
     if (res.data.flag == 1) {
      const { rows } = res.data.data
      formData.value.iconUri = rows[0].Path
    } else {
      formData.value.iconUri = undefined
      ElMessage.error(res.data.msg)
    }
  })
}

// URL验证函数
const validateUrl = (url) => {
    try {
        new URL(url);
        return true;
    } catch (e) {
        return false;
    }
};

// 重定向URI验证函数
const validateRedirectUris = (rule, value, callback) => {
    if (!formData.value.redirectUrisArray || formData.value.redirectUrisArray.length === 0) {
        callback(new Error('请至少添加一个重定向URI'));
        return;
    }

    const invalidUrls = formData.value.redirectUrisArray.filter(uri => uri.trim() && !validateUrl(uri.trim()));
    if (invalidUrls.length > 0) {
        callback(new Error('存在不合法的URL地址，请检查'));
        return;
    }

    callback();
};

// 登出重定向URI验证函数
const validatePostLogoutRedirectUris = (rule, value, callback) => {
    if (!formData.value.postLogoutRedirectUrisArray || formData.value.postLogoutRedirectUrisArray.length === 0) {
        callback(new Error('请至少添加一个登出重定向URI'));
        return;
    }

    const invalidUrls = formData.value.postLogoutRedirectUrisArray.filter(uri => uri.trim() && !validateUrl(uri.trim()));
    if (invalidUrls.length > 0) {
        callback(new Error('存在不合法的URL地址，请检查'));
        return;
    }

    callback();
};

const validateUri = (rule, value, callback) => {
    if (!value || value.trim() === '' || !validateUrl(value.trim())) {
        callback(new Error('请输入有效的URL地址'));
        return;
    }

    callback();
};

// 表单验证规则
const ruleForm = {
    displayName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
    description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
    category: [{ required: true, message: '请输入分类', trigger: 'blur' }],
    redirectUrisArray: [{
        required: true,
        validator: validateRedirectUris,
        trigger: ['change', 'blur']
    }],
    postLogoutRedirectUrisArray: [{
        required: true,
        validator: validatePostLogoutRedirectUris,
        trigger: ['change', 'blur']
    }],
    websiteUri: [{ required: true, validator: validateUri, trigger: 'blur' }],
    iconUri: [{ required: true, validator: validateUri, trigger: 'blur' }],
    sort: [{ required: true, message: '请输入排序权重', trigger: 'blur' }],
};

// 添加URI输入框
const addUri = (field) => {
    formData.value[field].push('');
};

// 删除URI输入框
const removeUri = (field, index) => {
    if (formData.value[field].length <= 1) {
        ElMessage.warning('至少保留一个URI');
        return;
    }
    formData.value[field].splice(index, 1);
};

// 获取应用详情
const getApplicationDetail = async (id) => {
    try {
        const res = await getApplicationById(id);
        if (res && res.data) {
            const data = res.data.data.rows;
            // 转换URI数组
            const redirectUrisArray = Array.isArray(data.RedirectUris) ? data.RedirectUris :
                (data.RedirectUris ? data.RedirectUris.split('\n').filter(uri => uri.trim()) : []);
            const postLogoutRedirectUrisArray = Array.isArray(data.PostLogoutRedirectUris) ? data.PostLogoutRedirectUris :
                (data.PostLogoutRedirectUris ? data.PostLogoutRedirectUris.split('\n').filter(uri => uri.trim()) : []);

            formData.value = {
                id: data.Id,
                displayName: data.DisplayName,
                description: data.Description,
                category: data.Category,
                redirectUrisArray: redirectUrisArray,
                postLogoutRedirectUrisArray: postLogoutRedirectUrisArray,
                iconUri: data.IconUri,
                websiteUri: data.WebsiteUri,
                sort: data.Sort,
            };
        }
    } catch (error) {
        ElMessage.error('获取应用详情失败');
    }
};

// 提交表单
const handleSubmit = async () => {
    if (!refForm.value) return;

    await refForm.value.validate(async (valid) => {
        if (valid) {
            try {
                const params = {
                    ...formData.value,
                    redirectUris: formData.value.redirectUrisArray.filter(uri => uri.trim()),
                    postLogoutRedirectUris: formData.value.postLogoutRedirectUrisArray.filter(uri => uri.trim()),
                    id: route.query.id || ''
                };

                const res = await addOrEditApplication(params);
                const { rows } = res.data.data;
                if(route.query.id) {
                    ElMessage.success('修改成功');
                    router.push({path:'/user/application/list'});
                }else{
                    ElMessageBox.confirm(
                    `
                    <div style="line-height: 1.5;">
                        <p>请保管密钥信息，确认后无法再查看</p>
                        <p>应用Id：${rows.ClientId}</p>
                        <p>应用密钥：${rows.ClientSecret}</p>
                    </div>
                    `,
                    'Warning',
                    {
                    confirmButtonText: '确认',
                    type: 'warning',
                    showCancelButton: false,
                    dangerouslyUseHTMLString: true,
                    }
                    )
                    .then(() => {
                        router.push({path:'/user/application/list'});
                    })
                    return;
                }
            } catch (error) {
                ElMessage.error(route.query.id ? '修改失败' : '新增失败');
            }
        }
    });
};


onMounted(() => {
    // 初始化空数组
    formData.value.redirectUrisArray = [];
    formData.value.postLogoutRedirectUrisArray = [];

    if (route.query.id) {
        getApplicationDetail(route.query.id);
    }
});
</script>

<style scoped>
.mobile-box {
    margin: 20px auto;
    padding: 20px;
}

.redirect-uris {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}

</style>