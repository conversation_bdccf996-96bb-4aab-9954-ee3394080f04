<script setup>
defineOptions({
    name: 'dangerchemicalsapplygivedlist'
});
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    Refresh, Search, DocumentCopy
} from '@element-plus/icons-vue'
import {
    DcApplyFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { pageQuery, ConsolidatedColumn } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const EndDate = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, ApplyType: 1, IsGrant: 1, sortModel: [{ SortCode: "BatchNo", SortType: "DESC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const statuzList = [{ value: 1, label: '已确认' }, { value: 2, label: '未确认' }]
const statuz = ref()
const routerObject = ref({})//成页面携带的参数对象 
const options = ref([
    { value: 'BatchNo', label: '申领批次', },
    { value: 'UserName', label: '领用人', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)
    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.BeginDate = undefined
    filters.value.EndDate = undefined
    EndDate.value = undefined
    statuz.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.BeginDate = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.EndDate = val + " 23:59:59"
    } else {
        filters.value.EndDate = undefined
    }
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    if (statuz.value == 1) {
        filters.value.IsAfterConfirm = true;
    } else if (statuz.value == 2) {
        filters.value.IsAfterConfirm = false;
    } else {
        filters.value.IsAfterConfirm = undefined;
    }
    DcApplyFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 打印
const HandlePrint = (num) => {

    let ids = undefined
    if (selectRows.value.length > 0) {
        ids = selectRows.value.map(item => item.Id).join(',')
    }
    router.push({
        path: "./print",
        query: {
            ids: ids,
            dtBegin: filters.value.BeginDate,
            dtEnd: EndDate.value,
            userName: filters.value.UserName || '',
            printType: num,
            path: '/dangerchemicals/apply/givedlist@t=' + routerObject.value.t,
        }
    })
}
// 计算合并信息
const spanArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'UserName')
})
// 计算合并信息
const spanBatchNoArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'BatchNo')
})
// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    // 只对第一列（UserName）进行合并
    if (columnIndex === 1) {
        const _row = spanArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
    if (columnIndex === 2) {
        const _row = spanBatchNoArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDate) return false;
    return time < new Date(filters.value.BeginDate + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    {{ routerObject.t == 1 ? '已发放危化品' : '已领用危化品' }}
                    &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill" v-if="routerObject.t == 1">
                    <li> 打印出库单：第一步：选择发放时间段，第二步：点击【打印出库单】； </li>
                    <li> “是否确认”是指老师实验后在线确认危化品是否有退回。 </li>
                </ol>
                <ol class="rowFill" v-else>
                    <li> 第一步：选择发放时间段； </li>
                    <li> 第二步：点击【打印出库单】 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" :disabled="!filters.BeginDate || !EndDate"
                            @click="HandlePrint(1)">打印出库单</el-button>
                        <el-button type="primary" :icon="DocumentCopy" :disabled="!filters.BeginDate || !EndDate"
                            @click="HandlePrint(2)">打印签字单</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="发放时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="发放时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" v-if="routerObject.t == 1">
                        <el-select v-model="statuz" clearable placeholder="是否确认" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in statuzList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :span-method="objectSpanMethod"
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="UserName" label="领用人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="BatchNo" label="申领批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="领用数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="GrantDate" label="发放时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.GrantDate ? row.GrantDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UseTime" label="使用时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.UseTime ? row.UseTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="IsAfterConfirm" label="是否确认" min-width="110" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsAfterConfirm == 1" style="color:green">√</span>
                    <span v-else style="color:grey">×</span>
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="用途" min-width="160" show-overflow-tooltip></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped>
:deep(.el-statistic__suffix) {
    font-size: 14px;
}

.marginLeft {
    margin-left: 10px;
}
</style>