<script setup>
defineOptions({
    name: 'dangerchemicalscityapplyyearnumstatistics'
});
import {
    Refresh, Search, Position
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DccatalogGetClassTwo, DcApplyNumStatisticsFind, GetDcApplyStatisticsYear, PUnitGetCountyByCityId
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ UserUnitType: "1", sortModel: [{ SortCode: "Id", SortType: "ASC" }] })
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
    PUnitGetCountyByCityIdUser()
    GetDcApplyStatisticsYearUser()

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 列表
const HandleTableData = () => {
    if (yearz.value?.length > 0) {
        filters.value.years = yearz.value.join(',')
    } else {
        filters.value.years = undefined
    }

    DcApplyNumStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.CountyId = undefined
    filters.value.ClassTwoId = undefined
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    yearz.value = []
    HandleTableData()
}

const filtersChange = () => { HandleTableData() }
const StatuzSolicitedList = ref([])
const cityList = ref([])
const yearzList = ref([])
const yearz = ref([])
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取下属单位集合。
const PUnitGetCountyByCityIdUser = () => {
    PUnitGetCountyByCityId({ CityId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            cityList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取年份
const GetDcApplyStatisticsYearUser = () => {
    GetDcApplyStatisticsYear().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            yearzList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 区县名称查看
const HandleLook = (row) => {
    router.push({
        path: '/dangerchemicals/apply/yearnumstatistics',
        query: {
            countyId: row.CountyId,
            countyName: row.AreaName,
            path: '/dangerchemicals/city/applyyearnumstatistics',
        }
    })
}
// 明细查看  
const HandleDetail = (row) => {
    router.push({
        path: '/dangerchemicals/apply/yearnumstatistics',
        query: {
            countyId: row.CountyId,
            twoCatalogId: row.ClassTwoId,
            model: row.Model,
            countyName: row.AreaName,
            name: row.DeviceName,
            path: '/dangerchemicals/city/applyyearnumstatistics',
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-select v-model="yearz" multiple placeholder="年度" @change="filtersChange"
                            style="min-width: 160px">
                            <el-option v-for="item in yearzList" ::key="item.Yearz" :label="item.Yearz"
                                :value="item.Yearz" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in cityList" ::key="item.UnitId" :label="item.CountyName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.ClassTwoId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Model" clearable placeholder="规格属性"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="AreaName" label="区县名称" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.AreaId" type="primary" link @click="HandleLook(row)">
                        {{ row.AreaName }}</el-button>
                    <span v-else>{{ row.AreaName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="ClassTwoName" label="危化品分类" min-width="120" align="center"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="DeviceName" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column v-for="item in yearzList" :key="item.Yearz" :prop="item.YearName"
                :label="String(item.Yearz)" min-width="120" align="right"></el-table-column>
            <el-table-column prop="SumYear" label="小计" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.SumYear || '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="明细" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<style lang="scss" scoped></style>