<script setup>
defineOptions({
    name: 'processstatedescription'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    FindProcessStatuzList, ProcessStatuzEdit
} from '@/api/workflow.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { tagsListStore } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        filters.value.ProcessId = route.query.id
        HandleTableData(true)
    }
})

onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.ProcessId = route.query.id
            HandleTableData(true)
        }
    })
})
// 修改
const HandleEdit = (row) => {
    dialogData.value.Id = row.Id
    dialogData.value.Sort = row.Sort || 0
    dialogData.value.StatuzDesc = row.StatuzDesc
    dialogVisible.value = true
}

//修改 提交
const HandleSubmit = () => {
    ProcessStatuzEditUser()
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    FindProcessStatuzList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 修改
const ProcessStatuzEditUser = () => {
    let formData = {
        Id: dialogData.value.Id,
        Sort: dialogData.value.Sort || 0,
        StatuzDesc: dialogData.value.StatuzDesc,
    }
    ProcessStatuzEdit(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="节点名称/节点显示名称/状态描述"
                            style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="NodeName" label="节点名称" min-width="160"></el-table-column>
            <el-table-column prop="NodeShowName" label="节点显示名称" min-width="160"></el-table-column>
            <el-table-column prop="StatuzDesc" label="状态描述" min-width="280"></el-table-column>
            <el-table-column prop="Statuz" label="状态值" min-width="100" align="center"> </el-table-column>
            <el-table-column prop="StrWaitOrBack" label="等待或退回" min-width="120" align="center"> </el-table-column>
            <el-table-column prop="IsEnable" label="使用状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.IsEnable == 1 ? 'primary' : 'danger'" disable-transitions>
                        {{ row.IsEnable == 1 ? "启用" : "禁用" }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="Sort" label="排序值" min-width="80" align="center"> </el-table-column>
            <el-table-column fixed="right" label="操作" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="修改状态描述">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" label-width="180px" status-icon>
                    <el-form-item label="排序值：">
                        <el-input type="number" v-model.number="dialogData.Sort" style="width: 80%;"></el-input>
                    </el-form-item>
                    <el-form-item label="状态描述：">
                        <el-input type="textarea" v-model="dialogData.StatuzDesc" style="width: 80%;"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>