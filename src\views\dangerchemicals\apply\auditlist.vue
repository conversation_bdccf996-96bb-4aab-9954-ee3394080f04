<script setup>
defineOptions({
    name: 'dangerchemicalsapplyauditlist'
});
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcApplyAuditListFind, DcApplyBatchConfirm, DcApplyBatchAudit
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { pageQuery, getApplyStatus, ConsolidatedColumn } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const routerObject = ref({})//成页面携带的参数对象 
const selectRows = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const memberUserList = ref([])//同领用人
const EndDate = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 20, sortModel: [{ SortCode: "BatchNo", SortType: "DESC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const bconfigSet = ref(0)
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    Remark: [
        { required: true, message: '请填写意见', trigger: 'change' },
    ]
}
const options = ref([
    { value: 'BatchNo', label: '申领批次', },
    { value: 'UserName', label: '领用人', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }

])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)
    // 修改tag标签名称########################
    let title = '待审核物品'
    if (routerObject.value.p == 30) {
        title = '待配货物品'
    } else {
        title = '待审核物品'
    }

    // 修改pinia数据
    userStore.$patch(state => {
        state.pageTitleObj.applyTitle = title
    })
    // pageTitleObj
    let tagsList = userStore.tagsList
    // 使用forEach遍历数组  修改tag标签名称
    tagsList.forEach(item => {
        if (item.path == route.path) {
            // 如果path匹配，则修改title  
            item.title = userStore.pageTitleObj.applyTitle;
        }
    });
    userStore.setTagsList(tagsList)
    // ########################

    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 审核||配货
const HandleEdit = (row) => {
    if (routerObject.value.p == 20) {
        router.push({ path: './audit', query: { id: row.Id, p: 20 } })
    } else {
        router.push({ path: './confirm', query: { id: row.Id, p: 30 } })
    }
}
const batchSet = (row) => {
    if (routerObject.value.p == 20) formData.value.Statuz = '30'
    formData.value.Remark = '同意'
    dialogVisible.value = true
}
//批量 审核||配货
const HandleSubmit = () => {
    let ids = selectRows.value.map(item => item.Id).join(',')
    if (routerObject.value.p == 20) {
        let paraData = {
            currentStatuz: 20,
            ids: ids,
            isWithdraw: 0,
            processNumber: 20,
            remark: formData.value.Remark,
            statuz: formData.value.Statuz,
        }
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            DcApplyBatchAudit(paraData).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '审核成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    } else {
        let paraData = {
            ids: ids,
            remark: formData.value.Remark,
        }
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            DcApplyBatchConfirm(paraData).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '配货成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    }
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.BeginDate = undefined
    filters.value.EndDate = undefined
    EndDate.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.BeginDate = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.EndDate = val + " 23:59:59"
    } else {
        filters.value.EndDate = undefined
    }
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    filters.value.Statuz = routerObject.value.p
    DcApplyAuditListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 计算合并信息
const spanArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'UserName')
})

// 计算合并信息
const spanBatchNoArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'BatchNo')
})
// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    // 只对第一列（UserName）进行合并
    if (columnIndex === 1) {
        const _row = spanArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
    if (columnIndex === 2) {
        const _row = spanBatchNoArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDate) return false;
    return time < new Date(filters.value.BeginDate + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse v-if="routerObject.p == 30">
            <el-collapse-item>
                <template #title>
                    待配货危化品 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 点击【配货】对申领危化品进行配货；当库存量不足时，可分批配货； </li>
                    <li> 配货完成后，请到【待发放危化品】栏中进行危化品发放。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="EditPen" :disabled="selectRows.length == 0" @click="batchSet">
                            {{ routerObject.p == 30 ? '批量配货' : '批量审核' }} </el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :span-method="objectSpanMethod"
            header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="UserName" label="领用人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="BatchNo" label="申领批次" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" v-if="routerObject.p == 20" min-width="120"
                align="right"></el-table-column>
            <el-table-column prop="ApplyNum" label="申请数量" v-if="routerObject.p == 30" min-width="120"
                align="right"></el-table-column>
            <el-table-column prop="StockNum" label="库存数量" v-if="routerObject.p == 30" min-width="120"
                align="right"></el-table-column>
            <el-table-column prop="BalanceNum" label="库存差额" v-if="routerObject.p == 30" min-width="120" align="right">
                <template #default="{ row }">
                    <span v-if="row.BalanceNum < 0" style="color: #F56C6C">{{ row.BalanceNum }}</span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.ApplyDate ? row.ApplyDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UseTime" label="使用时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.UseTime ? row.UseTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.Statuz ? getApplyStatus(row.Statuz) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="用途" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="WithUserName" label="同领用人" min-width="110" align="center">
                <template #default="{ row }">
                    {{ row.WithUserName || '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="120" align="center">
                <template #default="{ row }">
                    <el-button v-if="routerObject.p == 20" type="primary" link @click="HandleEdit(row)">审核</el-button>
                    <el-button v-else type="primary" link @click="HandleEdit(row)">配货</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="routerObject.p == 30 ? '配货' : '审核'">
            <template #content>
                <el-form style="min-width: 120px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <el-form-item label="审核结果：" v-if="routerObject.p == 20">
                        <el-radio-group v-model="formData.Statuz">
                            <el-radio value="30">通过</el-radio>
                            <el-radio value="21">不通过</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="routerObject.p == 30 ? '配货意见 :' : '审核意见 :'"
                        :prop="formData.Statuz == 21 || routerObject.p == 30 ? 'Remark' : ''">
                        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="formData.Remark"
                            style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
.mobile-box {
    :deep(.el-form-item) {
        margin-bottom: 8px;

    }

    :deep(.el-checkbox) {
        height: 32px;
        line-height: 32px;
    }
}
</style>