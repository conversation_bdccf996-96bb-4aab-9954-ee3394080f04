<script setup>
import { onMounted, ref, watch } from 'vue'
import {
    Search, Refresh
} from '@element-plus/icons-vue'
import {
    Schoolinfofind, Schoolinfogetbyid
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const dialogVisible = ref(false)
const refTable = ref()
const selectRows = ref([])
const formData = ref({})
const filters = ref({ Name: '', pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Sort", SortType: "ASC" }, { SortCode: "Id", SortType: "ASC" }] })
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

//加载数据
onMounted(() => {
    HandleTableData()
})
//获取列表 
const HandleTableData = () => {
    Schoolinfofind(filters.value).then(res => {
        if (res.data.flag == 1) {
            tableData.value = res.data.data.rows;
            tableTotal.value = Number(res.data.data.total);
        } else {
            ElMessage.error(res.data.msg)
        }

    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = ''
    HandleTableData()
}
// 用户账户
const HandleUser = (row) => {
    router.push({ path: "../school/user", query: { UnitId: row.Id } })
}
// 单位明细
const HandleDetail = (row) => {
    dialogVisible.value = true
    Schoolinfogetbyid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows[0]
            if (formData.value.SchoolNature == 1) {
                formData.value.SchoolNatureName = '公办校'
            } else if (formData.value.SchoolNature == 2) {
                formData.value.SchoolNatureName = '民办校'
            } else {
                formData.value.SchoolNatureName = '其他'
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                <el-form-item class="flexItem">
                    <el-input v-model.trim="filters.Name" placeholder="请输入单位名称" style="max-width: 360px">
                    </el-input>
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                    <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" border
        stripe>
        <el-table-column prop="Name" label="单位名称" min-width="160"></el-table-column>
        <el-table-column prop="SchoolStageName" label="单位属性" min-width="100" align="center"></el-table-column>
        <el-table-column prop="userCount" label="注册用户数" min-width="100" align="center">
            <template #default="{ row }">
                <span style="color: #F56C6C;">{{ row.userCount }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="ClassNum" label="班级总数" min-width="100" align="center"></el-table-column>
        <el-table-column prop="StudentNum" label="学生总数" min-width="100" align="center"></el-table-column>
        <el-table-column prop="TeacherNum" label="教职工数" min-width="100" align="center"></el-table-column>
        <el-table-column prop="UnderTeacherNum" label="其中在编教师" min-width="120" align="center"></el-table-column>
        <el-table-column label="查看" width="240" align="center" fixed="right">
            <template #default="{ row }">
                <el-button type="primary" link @click="HandleDetail(row)">单位明细</el-button>
                <el-button type="primary" link @click="HandleUser(row)">账号明细</el-button>
            </template>
        </el-table-column>
        <template #empty>
            <el-empty description="没有数据"></el-empty>
        </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
        @handleChange="handlePage" />
    <app-box v-model="dialogVisible" :height="600" :width="940" :lazy="true" title="单位明细">
        <template #content>
            <el-form class="mobile-box" @submit.prevent :model="formData" disabled label-width="140px" status-icon>
                <fieldset>
                    <legend>基础信息</legend>
                    <div class="dialogFlexBox">
                        <el-form-item label="单位全称：" prop="Name">
                            <el-input v-model="formData.Name"></el-input>
                        </el-form-item>
                        <el-form-item label="单位代码：">
                            <el-input v-model="formData.Code"></el-input>
                        </el-form-item>
                        <el-form-item label="组织机构代码：">
                            <el-input v-model="formData.OrganizationCode"></el-input>
                        </el-form-item>
                        <el-form-item label="单位性质：">
                            <el-input v-model="formData.SchoolNatureName"></el-input>
                        </el-form-item>
                        <el-form-item label="单位属性：">
                            <el-input v-model="formData.SchoolStageName"></el-input>
                        </el-form-item>
                        <el-form-item label="所在地区：">
                            <el-input v-model="formData.ProvinceCityCountyName"></el-input>
                        </el-form-item>
                        <el-form-item label="所属街道：">
                            <el-input v-model="formData.TownName"></el-input>
                        </el-form-item>
                        <el-form-item label="单位地址：">
                            <el-input v-model="formData.Address"></el-input>
                        </el-form-item>
                        <el-form-item label="单位官网：">
                            <el-input v-model="formData.Url"></el-input>
                        </el-form-item>
                    </div>
                    <el-form-item label="单位简介：">
                        <el-input type="textarea" v-model="formData.Introduction"></el-input>
                    </el-form-item>
                </fieldset>
                <fieldset>
                    <legend>其他信息</legend>
                    <div class="dialogFlexBox">
                        <el-form-item label="班级总数(班)：">
                            <el-input v-model="formData.ClassNum"></el-input>
                        </el-form-item>
                        <el-form-item label="学生总数(人)：">
                            <el-input v-model="formData.StudentNum"></el-input>
                        </el-form-item>
                        <el-form-item label="教职工数(人)：">
                            <el-input v-model="formData.TeacherNum"></el-input>
                        </el-form-item>
                        <el-form-item label="其中在编教师(人)：">
                            <el-input v-model="formData.UnderTeacherNum"></el-input>
                        </el-form-item>
                        <el-form-item label="占地面积(㎡)：">
                            <el-input v-model="formData.FloorArea"></el-input>
                        </el-form-item>
                        <el-form-item label="建筑面积(㎡)：">
                            <el-input v-model="formData.BuildArea"></el-input>
                        </el-form-item>
                    </div>
                </fieldset>
            </el-form>
        </template>
    </app-box>

</template>
<style lang="scss" scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}


.addCustom.el-form {
    width: 90%;
    margin: 0 auto;

    .el-form-item {
        margin-bottom: 10px;
    }

    .el-checkbox-group {
        display: flex;
        flex-wrap: wrap;

        label {
            width: 20%;
        }
    }

    .el-checkbox {
        height: var(--el-checkbox-height, 22px);
    }

}

.taskNameConent {
    width: 100%;
    /* 具体宽度，例如 200px 或 100% */
    ;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dialogFlexBox {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
        width: 50%;
        margin-bottom: 10px;
    }
}

fieldset {
    color: #333;
    border: #ccc dashed 1px;
    padding: 20px;
    margin-right: 20px;

    legend {
        font-size: 15px;
        padding-left: 20px;
        color: #666;
        font-weight: 800;
    }
}
</style>