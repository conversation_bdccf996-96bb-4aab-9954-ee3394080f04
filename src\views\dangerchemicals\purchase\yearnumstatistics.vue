<script setup>
defineOptions({
    name: 'dangerchemicalspurchaseyearnumstatistics'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Position, Back
} from '@element-plus/icons-vue'
import {
    DccatalogGetClassTwo, DcPurchaseNumStatisticsFind, GetDcPurchaseStatisticsYear, Punitgetschoolbycountyid
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const unitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({
    sortModel: [{ SortCode: "Id", SortType: "ASC" }],
    Name: route.query.name,
    Model: route.query.model,
    ClassTwoId: route.query.twoCatalogId,
    CountyId: route.query.countyId
})

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        DccatalogGetClassTwoUser()
        PunitgetschoolbycountyidUser()
        GetDcPurchaseStatisticsYearUser()
    }

})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    filters.value.Name = route.query.name
    filters.value.Model = route.query.model
    filters.value.ClassTwoId = route.query.twoCatalogId
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            DccatalogGetClassTwoUser()
            PunitgetschoolbycountyidUser()
            GetDcPurchaseStatisticsYearUser()
        }
    })
})

// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })
}
//  列表
const HandleTableData = () => {
    if (yearz.value?.length > 0) {
        filters.value.years = yearz.value.join(',')
    } else {
        filters.value.years = undefined
    }
    DcPurchaseNumStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    HandleTableData()
}
// 重置
const HandleReset = () => {
    if (unitType.value == 1 && route.query.name) {
        filters.value.Name = route.query.name
        filters.value.Model = route.query.model
        filters.value.TwoCatalogId = route.query.twoCatalogId
    } else {
        filters.value.Name = undefined;
        filters.value.Model = undefined;
        filters.value.ClassTwoId = undefined;
    }
    filters.value.SchoolId = undefined
    yearz.value = []
    HandleTableData()
}

const StatuzSolicitedList = ref([])
const schoolList = ref([])
const yearzList = ref([])
const yearz = ref([])
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: route.query.countyId || 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 根据区县获取学校
const PunitgetschoolbycountyidUser = () => {
    Punitgetschoolbycountyid({ CountyId: route.query.countyId || 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            schoolList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取年份
const GetDcPurchaseStatisticsYearUser = () => {
    GetDcPurchaseStatisticsYear().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            yearzList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = () => {
    HandleTableData()
} 
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="unitType == 1 && route.query.path">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="unitType == 1 && route.query.path"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="yearz" multiple placeholder="年度" @change="filtersChange"
                            style="min-width: 160px">
                            <el-option v-for="item in yearzList" ::key="item.Yearz" :label="item.Yearz"
                                :value="item.Yearz" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem" v-if="unitType != 3">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in schoolList" ::key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.ClassTwoId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Model" clearable placeholder="规格属性"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div v-if="unitType == 1 && route.query.countyName" style="font-size: 14px;color: #606266;margin-bottom: 10px;">
            【{{ route.query.countyName }}】：按年度数量统计
        </div>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column v-if="unitType != 3" prop="SchoolName" label="学校名称" min-width="200"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="ClassTwoName" label="危化品分类" min-width="120" align="center"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="DeviceName" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column v-for="item in yearzList" :key="item.YearName" :label="String(item.Yearz)"
                :prop="item.YearName" min-width="120" align="right"></el-table-column>
            <el-table-column prop="SumYear" label="小计" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.SumYear || '--' }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<style lang="scss" scoped></style>