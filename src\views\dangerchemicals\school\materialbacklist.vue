<script setup>
defineOptions({
    name: 'dangerchemicalsschoolmaterialbacklist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcMaterialsNumAuditAdd, DcPurchaseBatchNoFind, DcSchoolMaterialOptFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const batchNoList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const RegDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, InputType: 1, Statuz: 1, StockNumgt: 0, sortModel: [{ SortCode: "RegDate", SortType: "DESC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    ReturnNum: [
        { required: true, message: '请填写退货数量', trigger: 'change' },
    ],
    BackDate: [
        { required: true, message: '请选择退货时间', trigger: 'change' },
    ],
    Remark: [
        { required: true, message: '请填写退货原因', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'CompanyName', label: '供应商', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('CompanyName')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcPurchaseBatchNoFindUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const backId = ref('')
const stockNum = ref('')
// 发放
const HandleEdit = (row) => {
    dialogVisible.value = true
    backId.value = row.Id
    stockNum.value = row.StockNum
    formData.value = {}
    nextTick(() => {
        refForm.value.resetFields()
    })
}

const regDategeChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const regDateleChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}
//输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}

// 列表
const HandleTableData = () => {
    filters.value.CompanyName = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcSchoolMaterialOptFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.PurchaseBatchNo = undefined
    filters.value.RegDatege = undefined
    filters.value.RegDatele = undefined
    RegDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
//退货提交
const HandleSubmit = () => {
    if (formData.value.ReturnNum > stockNum.value) {
        ElMessage.error('退货数量不能大于库存数量')
        return
    }
    let paraData = {
        OptNum: Number(formData.value.ReturnNum),
        OptReason: formData.value.Remark,
        OptTime: formData.value.BackDate,
        OptType: 3,
        SchoolMaterialId: backId.value,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcMaterialsNumAuditAdd(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '退货成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })

}
// 获取采购批次
const DcPurchaseBatchNoFindUser = () => {
    DcPurchaseBatchNoFind().then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            batchNoList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!RegDatele.value) return false;
    return time >= new Date(RegDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    危化品退货 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>危化品退货：是指退货给原供应商。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="入库时间"
                            @change="regDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="RegDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="入库时间" @change="regDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PurchaseBatchNo" filterable clearable placeholder="采购批次"
                            style="width: 160px" @change="filtersChange">
                            <el-option v-for="item in batchNoList" :key="item.PurchaseBatchNo"
                                :label="item.PurchaseBatchNo" :value="item.PurchaseBatchNo" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="StockNum" label="数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Price" label="单价" min-width="160" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CompanyName" label="供应商" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="PurchaseBatchNo" label="采购批次" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Address" label="存放地点" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column prop="BackAuditNum" label="退货数量" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row.BackAuditNum == 0 ? '--' : row.BackAuditNum }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 1" type="primary" link @click="HandleEdit(row)">退货</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="退货">
            <template #content>
                <el-form style="min-width: 320px; " class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="退货数量：" prop="ReturnNum">
                        <el-input v-model="formData.ReturnNum" @input="limitInput($event, 'ReturnNum')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="退货时间：" prop="BackDate">
                        <el-date-picker v-model="formData.BackDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable style="width: 80%;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="退货原因：" prop="Remark">
                        <el-input type="textarea" v-model="formData.Remark" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 确认 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>