<script setup>
defineOptions({
    name: 'dangerchemicalspurchasefill'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    ShoppingCart, Search, Refresh, FolderAdd
} from '@element-plus/icons-vue'
import {
    DcPurchaseByCatalogFind, DcschoolCatalogFindCommonuseAll, DcPurchaseBatchAdd, DcPurchaseAdd,
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { limit, tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 100, Statuz: 1, sortModel: [{ SortCode: "FirstId", SortType: "ASC" }, { SortCode: "SecondId", SortType: "ASC" }, { SortCode: "SchoolCatalogId", SortType: "ASC" }] })
const filtersShowAll = ref(true)
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        DcschoolCatalogFindCommonuseAllUser()
    }

})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            DcschoolCatalogFindCommonuseAllUser()
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
const HandleRouter = (e) => {
    router.push({ path: './list', query: { path: '/dangerchemicals/purchase/fill' } })
}
// 批量加入领用车
const HandleAdd = (row) => {
    let find = selectRows.value.find(item => !item.PurchaseNum)
    if (find) {
        ElMessage.error('请检查已选中的危化品，是否已全部填写《采购数量》！')
        return
    }
    let list = selectRows.value.map(({
        SchoolCatalogId, SchoolMaterialModelId, PurchaseNum, Price, Remark, BaseCatalogId
    }) =>
    ({
        PurchaseOrderId: route.query?.orderId || 0, SchoolCatalogId, SchoolMaterialModelId, SchoolMaterialBrandId: 0, Num: Number(PurchaseNum), Price: Number(Price || 0), Remark, BaseCatalogId
    }))
    DcPurchaseBatchAdd(list).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '加入成功')
            // HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 加入领用车
const HandleBatchAdd = (row) => {
    let data = {
        PurchaseOrderId: route.query?.orderId || 0,
        SchoolCatalogId: row.SchoolCatalogId,
        SchoolMaterialModelId: row.SchoolMaterialModelId,
        SchoolMaterialBrandId: 0,
        Num: Number(row.PurchaseNum),
        Price: Number(row.Price || 0),
        Remark: row.Remark,
        BaseCatalogId: row.BaseCatalogId,
    }
    if (!row.PurchaseNum) {
        ElMessage.error('请填写采购数量')
        return
    }
    DcPurchaseAdd(data).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '加入成功')
            // HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.SecondId = undefined
    filters.value.Name = undefined
    menuIndex.value = ''
    defaultActive.value = ''
    filtersShowAll.value = true
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    DcPurchaseByCatalogFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            tableData.value.forEach(item => {
                item.Price = ''
                item.PurchaseNum = ''
                item.Remark = ''
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 获取学校物品类别选择
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let objId = rows.data.filter(item => item.Pid == '0')[0].Id
            let arr = rows.data.filter(item => item.Pid == objId)
            StatuzSolicitedList.value = arr
            // console.log(arr)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//输入框限制：输入整数
const limitInput = (val, row, index, name) => {
    tableData.value[index][name] = limit(val);
}
const menuIndex = ref('')
const defaultActive = ref('')
const nameClick = (item, index) => {
    menuIndex.value = item.Id
    filters.value.SecondId = item.Id
    HandleTableData()
}

const getCellClassName = ({ row, column }) => {
    // 判断数据是否存在或为空
    const value = row[column.property]
    if (value) {
        return 'requiredColumn'
    }
    return ''
}

</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    采购填报 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 您所需的危化品如不在列表中，请联系管理部门添加； </li>
                    <li> 申请的危化品需填写“参考单价”，否则不能生成采购预算。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <div style="display: flex;">
            <div class="section_left" style="width: 200px;flex-shrink: 0;">
                <el-menu :default-active="defaultActive" style="width: 180px;">
                    <el-menu-item v-for="(item, index) in StatuzSolicitedList" :key="item.Id" :index="item.Id"
                        @click="nameClick(item, index)" :class="{ active: menuIndex == item.Id }">
                        <div :class="{ activeName: menuIndex == item.Id }">{{ item.Name }}</div>
                    </el-menu-item>
                </el-menu>
            </div>
            <div style="width: 100%;" class="viewContainer">
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-button type="success" :icon="FolderAdd" :disabled="selectRows.length == 0"
                                    @click="HandleAdd">批量加入</el-button>
                            </el-form-item>
                            <div class="verticalIdel"></div>
                            <el-form-item label="" class="flexItem"
                                style="margin-left: 10px;margin-right: 10px !important;">
                                <el-checkbox v-model="filtersShowAll" label="全文搜索">
                                </el-checkbox>
                            </el-form-item>
                            <el-form-item label="" class="flexItem">
                                <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称/规格属性/品牌"
                                    style="width: 240px"></el-input>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                            </el-form-item>
                            <div class="verticalIdel"></div>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="ShoppingCart" @click="HandleRouter">采购车</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <el-table ref="refTable" :data="tableData" max-height="560" highlight-current-row border stripe
                    @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip> </el-table-column>
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="StockNum" label="存量" min-width="110" align="right"></el-table-column>
                    <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"></el-table-column>
                    <el-table-column prop="PurchaseNum" label="采购数量" min-width="100" align="center"
                        :class-name="'requiredColumn'">
                        <template #header>
                            <span style="color: #F56C6C;">*采购数量</span>
                        </template>
                        <template #default="{ row, $index }">
                            <el-input v-model="row.PurchaseNum" :class="row.PurchaseNum ? '' : 'requiredInput'"
                                @input="limitInput($event, row, $index, 'PurchaseNum')"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="Price" label="参考单价" min-width="100" align="center">
                        <template #default="{ row, $index }">
                            <el-input v-model="row.Price" @input="limitInput($event, row, $index, 'Price')"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="Remark" label="备注" min-width="160" align="center">
                        <template #default="{ row, $index }">
                            <el-input v-model="row.Remark"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="采购车" fixed="right" min-width="90" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="HandleBatchAdd(row)">加入</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </div>
        </div>

    </div>
</template>
<style lang="scss" scoped>
.section_left {
    width: 200px;
    margin-top: 50px;
}

:deep(.el-menu-item) {
    border-bottom: 1px solid #f2f2f2;

    white-space: normal !important;
    height: auto !important;
    line-height: 1.5 !important;
    padding: 10px 20px !important;
    word-break: break-word !important;

}

.el-menu-item.is-active {
    color: #303133;
}

.active {
    border-left: 5px solid var(--el-color-primary);
}

.activeName {
    color: var(--el-color-primary);
}

:deep(.requiredColumn) {
    .cell {
        padding: 0 2px !important;
    }
}

.requiredInput {
    :deep(.el-input__wrapper) {
        box-shadow: none !important;
        border: 1px solid red !important;
    }
}
</style>