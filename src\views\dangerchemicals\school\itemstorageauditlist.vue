<script setup>
defineOptions({
    name: 'dangerchemicalsschoolitemstorageauditlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    FolderAdd, UploadFilled, Setting, QuestionFilled, Search, Refresh, EditPen
} from '@element-plus/icons-vue'
import {
    UploadPostfile, DcCompanyComboxFind, DcDepositAddressFind, DcPurchaseBatchNoFind, DcPurchaseOrderGetById, DcCabinetAddressGet,
    DcPurchaseOrderUploadFile, DcschoolCatalogCommonuseFind, DcschoolCatalogFindCommonuseAll, DcSchoolMaterialBatchAudit,
    DcSchoolMaterialCatalogFind, DcSchoolMaterialCatalogGetById, DcSchoolMaterialDeleteById, DcSchoolMaterialInsertUpdate,
    DcSchoolMaterialModelGetByCatalogId, DcSchoolMaterialNext, DcSchoolModelBrandGetBrand
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { buildTree, fileDownload, tagsListStore, limit } from "@/utils/index.js";
import { AttachmentUpload } from '@/api/user.js'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const OrderId = ref(0)
const IsNeedReportFile = ref(0)
const bogetList = ref([])
const companyList = ref([])
const addressList = ref([])
const modelList = ref([])
const brandList = ref([])
const cabinetAddressList = ref([])
const dangerchemicalsList = ref([])
const selectRows = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 0, PurchaseListId: 0 })
const whpDialogVisible = ref(false)
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    SchoolMaterialModelId: [
        { required: true, message: '请选择规格属性', trigger: 'change' },
    ],
    SchoolMaterialBrandId: [
        { required: true, message: '请选择品牌', trigger: 'change' },
    ],
    Num: [
        { required: true, message: '请输入数量', trigger: 'change' },
    ],
    Price: [
        { required: true, message: '请输入单价', trigger: 'change' },
    ],
    oldDcCompanyId: [
        { required: true, message: '请选择供应商', trigger: 'change' },
    ],
    DepositAddressId: [
        { required: true, message: '请选择存放地点', trigger: 'change' },
    ],
    ValidDate: [
        { required: true, message: '请选择有效期至', trigger: 'change' },
    ],
}
const isSearch = ref(false)
const unitType = ref(userStore.userInfo.UnitType)
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', },
    { value: 'CompanyName', label: '供应商', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.OrderId) {
        OrderId.value = route.query.OrderId
        filters.value.PurchaseOrderId = route.query.OrderId
        DcPurchaseOrderGetByIdUser()
    } else {
        OrderId.value = 0
        filters.value.PurchaseOrderId = undefined
    }
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcschoolCatalogFindCommonuseAllUser()
    DcCompanyComboxFindUser()
    DcDepositAddressFindUser()
    DcCabinetAddressGetUser()
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            if (route.query.OrderId) {
                OrderId.value = route.query.OrderId
                filters.value.PurchaseOrderId = route.query.OrderId
                DcPurchaseOrderGetByIdUser()
            } else {
                OrderId.value = 0
                filters.value.PurchaseOrderId = undefined
            }
            if (route.query.isTagRouter) {
            }
            HandleTableData()
            DcschoolCatalogFindCommonuseAllUser()
            DcCompanyComboxFindUser()
            DcDepositAddressFindUser()
            DcCabinetAddressGetUser()
        }
    })
})
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 审核
const HandleEdit = (row, e) => {
    console.log(row)
    DcSchoolMaterialCatalogGetById({ Id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows
            dialogVisible.value = true
            formData.value.DcCompanyId = rows.LvCompanyId;
            formData.value.oldDcCompanyId = rows.LvCompanyId;

            let find = companyList.value.find(item => item.Id == rows.LvCompanyId)
            if (!find) {
                companyList.value.push({ Id: rows.LvCompanyId, Name: rows.CompanyName || '未知' })
            }

            if (rows.MsdsFile && rows.MsdsFile.includes("|")) {
                //解析出扩展名和名称，路径
                const fileStrings = rows.MsdsFile.split("|");
                formData.value.MsdsFilePath = fileStrings[0];
                formData.value.MsdsFileTitle = fileStrings[1];
                formData.value.Ext = "." + fileStrings[1].split(".").pop();
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
    DcSchoolMaterialModelGetByCatalogIdUser(row.SchoolCatalogId)
    DcSchoolModelBrandGetBrandUser(row.SchoolCatalogId)
}

// 不通过删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要不通过删除危化品信息吗?')
        .then(() => {
            DcSchoolMaterialDeleteById({ Id: formData.value.Id }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 通过入库
const HandleSubmit = () => {
    let paraData = {
        BaseCatalogId: formData.value.BaseCatalogId,
        Brand: formData.value.Brand,
        CabinetAddress: formData.value.CabinetAddress,
        CompanyName: formData.value.CompanyName,
        DcCompanyId: formData.value.DcCompanyId,
        DepositAddressId: formData.value.DepositAddressId,
        Id: formData.value.Id,
        MsdsFile: formData.value.MsdsFile,
        Name: formData.value.Name,
        Num: formData.value.Num,
        Price: formData.value.Price,
        Remark: formData.value.Remark,
        SchoolCatalogId: formData.value.Id,
        SchoolMaterialBrandId: formData.value.SchoolMaterialBrandId,
        SchoolMaterialModelId: formData.value.SchoolMaterialModelId,
        Statuz: 1,
        UnitName: formData.value.UnitName,
        ValidDate: formData.value.ValidDate,
        WarrantyMonth: formData.value.WarrantyMonth,
    }
    console.log("paraData", paraData)
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcSchoolMaterialInsertUpdate(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '审核成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })

    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    filters.value.CompanyName = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }

    DcSchoolMaterialCatalogFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            let find = tableData.value.find(t => t.IsNeedReport == 1)
            if (find) IsNeedReportFile.value = 1
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 批量审核
const batchSet = () => {
    let list = selectRows.value.map(t => t.Id)
    DcSchoolMaterialBatchAudit(list).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '批量审核成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }

    })
}

const activeName = ref('content')
const firstName = ref('')
const HandleSearch1 = () => {
    if (!firstName.value) return
    let paraData = {
        Name: firstName.value,
        Statuz: 1,
        pageIndex: 0,
        pageSize: 100000,
        sortModel: [{ SortCode: "Code", SortType: "asc" }]
    }
    DcschoolCatalogCommonuseFind(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerchemicalsList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入框限制：输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
// 品牌选择
const brandChange = (e) => {
    console.log(e)
    let find = brandList.value.filter(item => item.SchoolMaterialBrandId == e)
    if (find.length > 0) {
        formData.value.SchoolMaterialBrandId = e
        formData.value.Brand = find[0].Brand
    } else {
        // formData.value.SchoolMaterialBrandId = 0
        formData.value.Brand = e
    }
}
// 供应商选择
const companyChange = (e) => {
    let find = companyList.value.filter(item => item.Id == e)
    if (find.length > 0) {
        formData.value.DcCompanyId = e
        formData.value.CompanyName = find[0].Name
    } else {
        formData.value.DcCompanyId = 0
        formData.value.CompanyName = e
    }
}
// 选择危化品
const dangerchemicalsClick = (item) => {
    console.log("item", item)
    formData.value.BaseCatalogId = item.BaseCatalogId
    formData.value.Id = item.Id
    formData.value.Name = item.Name
    formData.value.UnitName = item.UnitsMeasurement
    formData.value.SchoolMaterialModelId = undefined
    formData.value.SchoolMaterialBrandId = undefined
    whpDialogVisible.value = false
    DcSchoolMaterialModelGetByCatalogIdUser(item.Id)
    DcSchoolModelBrandGetBrandUser(item.Id)

}

// 获取学校供应商信息-查询
const DcCompanyComboxFindUser = () => {
    DcCompanyComboxFind({ Statuz: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            companyList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取学校物品类别选择-查询
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let arr = rows.data || []
            bogetList.value = buildTree(arr)
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 存放地点-查询
const DcDepositAddressFindUser = () => {
    DcDepositAddressFind({ Statuz: 1, pageIndex: 0, pageSize: ********* }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            addressList.value = rows.data || []

        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取存储柜地址下拉列表-查询 
const DcCabinetAddressGetUser = () => {
    DcCabinetAddressGet().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            cabinetAddressList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}

// 获取规格属性  
const DcSchoolMaterialModelGetByCatalogIdUser = (id) => {
    DcSchoolMaterialModelGetByCatalogId({ schoolCatalodId: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            modelList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取品牌
const DcSchoolModelBrandGetBrandUser = (id) => {
    DcSchoolModelBrandGetBrand({ schoolCatalodId: id, modelId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            brandList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const path = ref('')
const title = ref('')
const ext = ref('')
const HandleLoad = (row) => {
    if (['.JPG', '.JPEG', '.PNG', '.GIF', '.BMP'].includes(ext.value)) {
        showViewer.value = true;
        viewPhotoList.value = [path.value];
    } else {
        fileDownload(path.value, title.value)
    }
}
// 根据Id采购申请单：公安报备文件的附件
const DcPurchaseOrderGetByIdUser = (id) => {
    DcPurchaseOrderGetById({ id: OrderId.value }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let reportFile = rows.ReportFile;
            if (reportFile) {
                let reportFileSplit = reportFile.split('|');
                if (reportFileSplit.length > 1) {
                    path.value = reportFileSplit[0];
                    title.value = reportFileSplit[1];
                }
                else {
                    path.value = reportFile;
                    title.value = '查看';
                }
                ext.value = path.value.substr(path.value.lastIndexOf(".")).toUpperCase();
            }


        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
// 选择危化品
const HandleAdd = () => {
    whpDialogVisible.value = true
    isSearch.value = false
}


//附件上传
const uploadFileData = ref([{ FileCategory: 2964, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 1, Memo: "文件小于5M，支持pdf和图片文件", Name: "MSDS：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" }])
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)

// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data

            formData.value.MsdsFilePath = rows[0].Path;
            formData.value.MsdsFileTitle = rows[0].Title;
            formData.value.Ext = rows[0].Ext;
            formData.value.MsdsFile = rows[0].Path + "|" + rows[0].Title + rows[0].Ext;
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)

// 附件图片预览与文件下载
const lookFileListDownload = (filePath, ext, title) => {
    viewPhotoList.value = [filePath];
    ext = (typeof ext === "string") ? ext.toLowerCase() : "";
    if (ext == ".png" || ext == ".jpg" || ext == ".jpng") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (filePath == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        fileDownload(filePath, title)
    }
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="EditPen" :disabled="selectRows.length == 0"
                            @click="batchSet">批量审核</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div v-if="OrderId != 0 && IsNeedReportFile == 1" style="color: #F56C6C;">
            <span>注意事项：须先上传“公安报备文件”，再审核！</span>
            <span>公安报备文件： 上传 <span @click="HandleLoad">{{ title }}</span></span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="CompanyName" label="供应商" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="center"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="Price" label="单价" min-width="100" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center"></el-table-column>
            <el-table-column prop="IsNeedReport" label="需报备" v-if="OrderId != 0" min-width="110" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsNeedReport == 1">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center" v-if="unitType == 3">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">审核</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="1100" :lazy="true" title="审核危化品信息">
            <template #content>
                <el-form style="min-width: 100px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <el-form-item label="危化品名称：" prop="Name">
                        <el-input v-model="formData.Name" readonly @click="HandleAdd" style="width: 400px"></el-input>
                        <el-button type="success" :icon="Search" @click="HandleAdd"
                            style="margin-left: 10px;">选择</el-button>
                    </el-form-item>
                    <el-form-item label="规格属性：" prop="SchoolMaterialModelId">
                        <el-select v-model="formData.SchoolMaterialModelId" style="width: 400px;">
                            <el-option v-for="item in modelList" :key="item.Id" :label="item.Model" :value="item.Id" />
                        </el-select>
                        <span class="placeholdertext">你所需的规格属性下拉菜单中如没有，请通知区危化品监管员添加！</span>
                    </el-form-item>
                    <el-form-item label="品牌：" prop="SchoolMaterialBrandId">
                        <el-select v-model="formData.SchoolMaterialBrandId" filterable allow-create default-first-option
                            @change="brandChange" style="width: 400px">
                            <el-option v-for="item in brandList" :key="item.SchoolMaterialBrandId" :label="item.Brand"
                                :value="item.SchoolMaterialBrandId" />
                        </el-select>
                        <span class="placeholdertext">你所需的品牌下拉菜单中如没有，请自行填写！</span>
                    </el-form-item>
                    <el-form-item label="数量：" prop="Num">
                        <el-input v-model="formData.Num" @input="limitInput($event, 'NUm')"
                            style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item label="单价(元)：" prop="Price">
                        <el-input v-model="formData.Price" @input="limitInput($event, 'Price')"
                            style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item label="供应商：" prop="oldDcCompanyId">
                        <el-select v-model="formData.oldDcCompanyId" filterable allow-create default-first-option
                            @change="companyChange" style="width: 400px">
                            <el-option v-for="item in companyList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                        <span class="placeholdertext">你所需的供应商下拉菜单中如没有，请自行添加，名称须与发票中一致！</span>
                    </el-form-item>
                    <el-form-item label="存放地点：" prop="DepositAddressId">
                        <el-select v-model="formData.DepositAddressId" filterable style="width: 400px">
                            <el-option v-for="item in addressList" :key="item.Id" :label="item.Address"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="储存柜层次：">
                        <el-select v-model="formData.CabinetAddress" filterable allow-create default-first-option
                            style="width: 400px">
                            <el-option v-for="item in cabinetAddressList" :key="item.CabinetAddress"
                                :label="item.CabinetAddress" :value="item.CabinetAddress" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="有效期至：" prop="ValidDate">
                        <el-date-picker type="date" placeholder="选择日期" v-model="formData.ValidDate" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" style="width: 400px"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="质保期(月)：">
                        <el-input v-model="formData.WarrantyMonth" style="width: 400px"></el-input>
                        <span class="placeholdertext">如无“质保期”，则免填写！</span>
                    </el-form-item>
                    <el-form-item label="备注：">
                        <el-input v-model="formData.Remark" type="textarea" style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                            :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                            :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                            <el-button type="success" size="small" :icon="UploadFilled">上传</el-button>
                        </el-upload>
                        <div class="fileFlex">
                            <div v-if="formData.MsdsFile" style="color:#409EFF ;width:200px">
                                <span style="cursor: pointer;"
                                    @click="lookFileListDownload(formData.MsdsFilePath, formData.Ext, formData.MsdsFileTitle)">
                                    {{ formData.MsdsFileTitle }}
                                </span>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="danger" @click="HandleDel">不通过删除</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 通过入库 </el-button>
                </span>
            </template>
        </app-box>
        <app-box v-model="whpDialogVisible" :width="960" :lazy="true" title="请选择危化品">
            <template #content>
                <el-tabs tab-position="left" v-model="activeName" style="height: 480px" class="demo-tabs">
                    <el-tab-pane label="搜索" name="search">
                        <div>
                            <el-input v-model.trim="firstName" placeholder="危化品名称" style="width: 280px"> </el-input>
                            <el-button type="primary" :icon="Search" @click="HandleSearch1">搜索</el-button>
                        </div>
                        <div>
                            <div v-if="isSearch && firstName && dangerchemicalsList.length == 0"
                                style="padding: 10px;color: #E6A23C;">危化品不存在，请重新搜索！</div>
                            <ul class="SearchUi" v-else>
                                <li v-for="item in dangerchemicalsList" :key="item.Id"
                                    @click="dangerchemicalsClick(item)">
                                    {{ item.FirstName }} > {{ item.SecondName }} > {{ item.Name }}</li>
                            </ul>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="危化品" name="content">
                        <ul class="whpUi">
                            <li v-for="item in bogetList[0].children" :key="item.Id">
                                <div class="content_left">
                                    {{ item.Name }}
                                </div>
                                <div class="content_right">
                                    <div v-for="item1 in item.children" :key="item1.Id" style="padding: 2px 0;">
                                        <el-divider direction="vertical" />
                                        <span class="text" style="padding: 2px;" @click="dangerchemicalsClick(item1)">
                                            {{ item1.Name }}</span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                </el-tabs>
            </template>
        </app-box>
        <!-- 图片预览 -->
        <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    </div>
</template>
<style lang="scss" scoped>
.whpUi {
    font-size: 12px;
    background-color: #f9f9f9;
    padding: 5px;
    padding-inline-start: 0px;
    margin: 0;

    li {
        list-style-type: none;
        font-size: 12px;
        display: flex;
        padding: 10px 5px;
        border: 1px solid #f9f9f9;
        border-bottom: 1px dotted #d1cfd0;

        .content_left {
            width: 140px;
            flex-shrink: 0;
            color: #723535;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .content_right {
            display: flex;
            flex-wrap: wrap;
            color: #3c3c3c;

            .text:hover {
                cursor: pointer;
                color: #ff9933 !important;
            }
        }
    }

    li:hover {
        background: #fff;
        border: 1px solid #f93 !important;
    }

}

.SearchUi {
    font-size: 14px;
    // background-color: #f9f9f9;
    padding: 5px;
    height: 500px;
    margin: 0;
    overflow-y: auto;

    li {
        list-style-type: none;
        padding: 5px;
        border-bottom: 1px dotted #d1cfd0;
    }

    li:hover {
        background: #fff;
        color: #ff9933 !important;
        border: 1px solid #f93 !important;
        cursor: pointer;
    }
}

:deep(.el-tabs__nav-scroll) {
    background-color: #f9f9f9;
}

.placeholdertext {
    padding-left: 10px;
    color: #999999;
}
</style>