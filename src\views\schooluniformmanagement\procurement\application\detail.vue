<script setup>
defineOptions({
    name: 'applicationdetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    Getpagedbytype,
} from '@/api/user.js'
import {
    UniformBuyGetById
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
const route = useRoute()
const formData = ref({})
const refForm = ref()
const uploadFileData = ref([])
const yearDateList = ref([])
const OrganizationFormList = ref([])
const BidPublicList = ref([])
const MethodList = ref([])
const UniformSchemeIdList = ref([])
const purchaseId = ref()

onMounted(() => {
    GetpagedbytypeUser()
    if (route.query.id) {
        purchaseId.value = route.query.id
    }
    nextTick(() => {
        if (route.query.isTagRouter) {
            UniformBuyGetByIdUser(purchaseId.value)
        }
    })
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)

    if (route.query.id) {
        purchaseId.value = route.query.id
    }
    nextTick(() => {
        if (!route.query.isTagRouter) {
            UniformBuyGetByIdUser(purchaseId.value)
        }
    })
})
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    let paraData = {}
    paraData = { moduleType: 701 }
    Getpagedbytype(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.categoryList = []
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const UniformBuyGetByIdUser = (id) => {
    UniformBuyGetById({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other, footer } = res.data.data
            uploadFileData.value.forEach(item => {
                item.categoryList = []
            })
            if (rows) {
                formData.value = rows
                if (rows.BidPublic === 0) {
                    formData.value.BidPublic = undefined
                } else {
                    formData.value.BidPublic = String(rows.BidPublic)
                }
                if (rows.Organizational === 0) {
                    formData.value.Organizational = undefined
                } else {
                    formData.value.Organizational = String(rows.Organizational)
                }
                if (rows.Method === 0) {
                    formData.value.Method = undefined
                } else {
                    formData.value.Method = String(rows.Method)
                }
                console.log('formData.value.Method', formData.value.Method, typeof (formData.value.Method))
            }

            let categoryList = footer || [];//附件集合
            console.log('categoryList', categoryList)
            if (categoryList.length > 0) {
                // 遍历数组 categoryList 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 uploadFileData中具有相同 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
            BidPublicList.value = other.listBidPublic;//招标公告公开
            MethodList.value = other.listMethod;//采购方式
            OrganizationFormList.value = other.listOrganizational;//采购年度
            UniformSchemeIdList.value = other.listUniformSchemeId;//选用批次
            UniformSchemeIdList.value.unshift({ value: '0', label: '无' })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}

</script>
<template>
    <el-form style="width: 100%;min-width: 560px;" :inline="true" class="mobile-box" @submit.prevent ref="refForm"
        :model="formData" label-width="260px" status-icon>
        <el-form-item label="年度" disabled class="formItem">
            <el-input v-model="formData.PlanYear" disabled></el-input>
        </el-form-item>
        <el-form-item label="采购批次" disabled class="formItem">
            <el-input v-model="formData.PurchaseNo" disabled></el-input>
        </el-form-item>
        <el-form-item label="选用批次：" class="formItem">
            <el-select v-model="formData.UniformSchemeId" disabled class="item_content">
                <el-option v-for="item in UniformSchemeIdList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="采购需求编制日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.PreparationDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="校服数量(套)：" class="formItem">
            <el-input v-model="formData.Num" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="预算金额(元)：" class="formItem">
            <el-input v-model="formData.BudgetAmount" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="选用组织家长和学生代表比例(%)：" class="formItem">
            <el-input v-model="formData.OrganizationRatio" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="家长同意选用校服比例(%)：" class="formItem">
            <el-input v-model="formData.AdoptRatio" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="是否需要招标：" class="formItem">
            <el-radio-group v-model="formData.IsNeedBidding" disabled>
                <el-radio :value="1"> 是</el-radio>
                <el-radio :value="2"> 否</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="组织形式：" class="formItem" v-if="formData.IsNeedBidding == 1">
            <el-select v-model="formData.Organizational" disabled class="item_content">
                <el-option v-for="item in OrganizationFormList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="招标公告公开：" class="formItem" v-if="formData.IsNeedBidding == 1">
            <el-select v-model="formData.BidPublic" disabled class="item_content">
                <el-option v-for="item in BidPublicList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="采购方式：" class="formItem" v-if="formData.IsNeedBidding == 1">
            <el-select v-model="formData.Method" disabled class="item_content">
                <el-option v-for="item in MethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="公开日期：" class="formItem" v-if="formData.IsNeedBidding == 1 && formData.BidPublic == 1">
            <el-date-picker type="date" v-model="formData.PublicDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="公开媒体名称：" class="formItem" v-if="formData.IsNeedBidding == 1 && formData.BidPublic == 1">
            <el-input v-model="formData.PublicMediaName" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%;">
            <el-input type="textarea" v-model="formData.Remark" disabled></el-input>
        </el-form-item>
        <template v-for="(item, index) in uploadFileData" :key="index">
            <el-form-item :label="item.Name + '：'" class="formItem"
                v-if="item.FileCategory != 107001 || item.FileCategory == 107001 && formData.IsNeedBidding == 1">
                <div class="fileFlex">
                    <div v-for="(itemCate, indexCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                        <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                            {{ itemCate.Title }}{{ itemCate.Ext }}
                        </span>
                    </div>
                </div>
            </el-form-item>
        </template>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-right: 0;
}

.item_content {
    // width: 60% !important;
}

:deep(.el-date-editor.el-input) {
    width: 100% !important;
}

.formItem {
    width: 50%;
}

@media (max-width: 768px) {
    .formItem {
        width: 100%;
    }
}
</style>