<script setup>
defineOptions({
    name: 'dangerchemicalsscrapedlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen, Back
} from '@element-plus/icons-vue'
import {
    DcScrapedListFind, DccatalogGetClassTwo, DcScrapDisposal
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { tagsListStore, disposalDate } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const ScrapDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1, DataType: 1, sortModel: [{ SortCode: "WasetStatuz", SortType: "ASC" }, { SortCode: "ScrapDate", SortType: "DESC" }] })
const options = ref([
    { value: 'Name', label: '危化品名称' },
    { value: 'Model', label: '规格属性' },
    { value: 'Brand', label: '品牌' }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        DccatalogGetClassTwoUser()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            DccatalogGetClassTwoUser()
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

const scrapDategeChange = (val) => {
    if (!val) filters.value.ScrapDatege = undefined
    HandleTableData()
}
const scrapDateleChange = (val) => {
    if (val) {
        filters.value.ScrapDatele = val + " 23:59:59"
    } else {
        filters.value.ScrapDatele = undefined
    }
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }

    if (route.query.Id) {
        filters.value.WasteDisposalId = route.query.Id
        filters.value.DataType = undefined
    }
    DcScrapedListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.WasetStatuz = undefined
    filters.value.TwoCatalogId = undefined
    filters.value.ScrapDatege = undefined
    filters.value.ScrapDatele = undefined
    ScrapDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
//处置申请
const HandleDisposal = () => {
    ElMessageBox.confirm('您确认处置所选择的危化品吗？')
        .then(() => {
            let ids = selectRows.value.map(t => t.Id).join(',')
            DcScrapDisposal({ ids: ids }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '处置成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })

}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!ScrapDatele.value) return false;
    return time >= new Date(ScrapDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.ScrapDatege) return false;
    return time < new Date(filters.value.ScrapDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    已报废危化品列表 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>勾选需要处置的危化品，点击【处置申请】；</li>
                    <li>考虑到处置企业部门危化品不能处置，建议现场处置完成后，再填报。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="!route.query.Id">
                        <el-button type="primary" :icon="EditPen" :disabled="selectRows.length == 0"
                            @click="HandleDisposal">处置申请</el-button>
                    </el-form-item>
                    <el-form-item class="flexItem" v-if="route.query.Id">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.ScrapDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="报废时间"
                            @change="scrapDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="ScrapDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="报废时间" @change="scrapDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.WasetStatuz" clearable placeholder="状态" style="width: 160px"
                            @change="filtersChange">
                            <el-option label="待处置" :value="1" />
                            <el-option label="处置中" :value="2" />
                            <el-option label="已处置" :value="3" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div v-if="route.query.Id" style="font-size: 14px;color: #606266;margin-bottom: 10px;">
            处置批次：{{ route.query.batchNo }}
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ScrapNum" label="报废数量" min-width="100" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Price" label="单价" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ScrapDate" label="报废时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.ScrapDate ? row.ScrapDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="WasetStatuz" label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.WasetStatuz == 4" style="color: #F56C6C;">审核不通过</span>
                    <span v-else>
                        {{ row.WasetStatuz == 1 ? '待处置' : row.WasetStatuz == 2 ? '处置中' : row.WasetStatuz == 3 ? '已处置' :
                            '--' }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="DisposalDate" label="处置时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ disposalDate(row.DisposalDate) }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="报废理由" min-width="140" align="left" show-overflow-tooltip>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>