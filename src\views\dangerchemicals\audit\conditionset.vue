<script setup>
defineOptions({
    name: 'dangerchemicalsauditconditionset'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    QuestionFilled, Select, UploadFilled, Delete
} from '@element-plus/icons-vue'
import {
    DcauditconditionFind, DcauditconditioninsertUpdate
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { pageQuery } from "@/utils/index.js";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
import router from '@/router'
const route = useRoute()
const formData = ref({})
const refForm = ref()
const setList = ref([])
const routerObject = ref({})
onMounted(() => {
    routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象 
    console.log(routerObject.value)
    if (route.query.isTagRouter) {
    }
    DcauditconditionFindUser()
})

onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            // 调用接口后判断 MaxValue==1000000000 为开启状态
            // 根据路径的 t显示：t=1 Statuz1、Statuz2
        }
    })
})

//提交
const HandleSubmit = (num) => {
    let params1 = {}
    let params2 = {}
    let params3 = {}
    let params4 = {}
    if (formData.value.Statuz1 == 1) {
        params1 = { AuditCode: "1001", MinValue: 0, MaxValue: 100000000 }
    } else {
        params1 = { AuditCode: "1001", MinValue: 0, MaxValue: 0 }
    }
    if (formData.value.Statuz2 == 1) {
        params2 = { AuditCode: "1002", MinValue: 0, MaxValue: 100000000 }
    } else {
        params2 = { AuditCode: "1002", MinValue: 0, MaxValue: 0 }
    }
    if (formData.value.Statuz3 == 1) {
        params3 = { AuditCode: "2001", MinValue: 0, MaxValue: 100000000 }
    } else {
        params3 = { AuditCode: "2001", MinValue: 0, MaxValue: 0 }
    }
    if (formData.value.Statuz4 == 1) {
        params4 = { AuditCode: "2002", MinValue: 0, MaxValue: 100000000 }
    } else {
        params4 = { AuditCode: "2002", MinValue: 0, MaxValue: 0 }
    }
    let params = []
    if (routerObject.value.t == 1) {
        params = [params1, params2]
    } else {
        params = [params3, params4]
    }
    DcauditconditioninsertUpdate(params).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const DcauditconditionFindUser = () => {
    DcauditconditionFind().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            setList.value = rows?.data || []
            if (setList.value.length > 0) {
                if (routerObject.value.t == 1) {
                    formData.value.Statuz1 = setList.value[0].MaxValue == 100000000 ? 1 : 2
                    formData.value.Statuz2 = setList.value[1].MaxValue == 100000000 ? 1 : 2
                } else {
                    formData.value.Statuz3 = setList.value[0].MaxValue == 100000000 ? 1 : 2
                    formData.value.Statuz4 = setList.value[1].MaxValue == 100000000 ? 1 : 2
                }

            } else {
                formData.value.Statuz1 = 1
                formData.value.Statuz2 = 1
                formData.value.Statuz3 = 1
                formData.value.Statuz4 = 1
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-collapse v-if="routerObject.t == 1">
            <el-collapse-item>
                <template #title>
                    采购审核审批设置 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>采购审核：是指学校安全管理部门（总务处或安全处）审核。</li>
                    <li>采购审批：是指学校一把手校长或分管校长审批。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-form style="width: 900px;margin-top: 10px;" class="mobile-box" @submit.prevent ref="refForm"
            :model="formData" label-width="180px" status-icon>
            <div v-if="routerObject.t == 1">
                <el-form-item label="采购审核：">
                    <el-radio-group v-model="formData.Statuz1">
                        <el-radio :value="1" label="开启"> </el-radio>
                        <el-radio :value="2" label="关闭"> </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="采购审批：">
                    <el-radio-group v-model="formData.Statuz2">
                        <el-radio :value="1" label="开启"> </el-radio>
                        <el-radio :value="2" label="关闭"> </el-radio>
                    </el-radio-group>
                </el-form-item>
            </div>
            <div v-else>
                <el-form-item label="部门审核：">
                    <el-radio-group v-model="formData.Statuz3">
                        <el-radio :value="1" label="开启"> </el-radio>
                        <el-radio :value="2" label="关闭"> </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="领用审核：">
                    <el-radio-group v-model="formData.Statuz4">
                        <el-radio :value="1" label="开启"> </el-radio>
                        <el-radio :value="2" label="关闭"> </el-radio>
                    </el-radio-group>
                </el-form-item>
            </div>
            <el-form-item>
                <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<style lang="scss" scoped></style>