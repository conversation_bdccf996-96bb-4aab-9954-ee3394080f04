<script setup>
defineOptions({
    name: 'nodeconditionset'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Checked, CirclePlus, DeleteFilled
} from '@element-plus/icons-vue'
import {
    PostControlSetList, GetSingLecontRolsetList, PostSaveControlData, DelAmountControlByid
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import { rulesIntegerLimit } from "@/utils/rules.js";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const masterControlList = ref([])
const controlledList = ref([])
const dropDownList = ref([])
const amountList = ref([])
const operatorList = ref([{ label: '<', value: '<' }, { label: '=', value: '=' }, { label: '<=', value: '<=' }])//运算符
const code = ref()
const isAmountControl = ref('')
const selectCode = ref('')//主控字段code
const selectName = ref('')//主控字段名称
const radio = ref(false)//选择被绑定的Id
const amountRadio = ref(false)//选择被绑定的Id
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        PostControlSetListUser(true);
    }
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    radio.value = false
    amountRadio.value = false
    code.value = undefined
    nextTick(() => {
        if (!route.query.isTagRouter) {
            PostControlSetListUser(true);
        }
    })
})

//获取设置控制列表信息(上部分主控、被控列表)
const PostControlSetListUser = (isFirst) => {
    console.log("code.value", code.value)
    const formData = {
        ProcessNodeId: route.query.processNodeId,
        MasterControl: Number(route.query.masterControl),
        isFirst: isFirst,
        Code: code.value,
    }
    PostControlSetList(formData).then(res => {
        if (res.data.flag == 1) {
            const { other } = res.data.data
            masterControlList.value = other.listMasterControl || [];//主控数据:(用于非金额)
            amountList.value = other.listMasterControl || [];//主控数据:(用于金额)
            controlledList.value = other.listControlled;//被控数据
            if (isFirst) {
                dropDownList.value = other.listDropDown;//主控字段
                code.value = other.listDropDown[0].FieldCode
                isAmountControl.value = other.listDropDown[0].IsAmountControl
            }
            selectCode.value = other.selectCode
            selectName.value = other.selectName
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}


const tableRef = ref(null); // 子：表格实例 
const selectedRows = ref([]); // 子级数据
const radioRows = ref({}); // 
const radioAmountRows = ref({}); //  
// 主控数据单选
const handleCurrentChange = (row) => {
    console.log("row", row)
    if (row) {
        radioRows.value = row
        radio.value = row.value
        GetSingLecontRolsetListUser(row.value)
    }
}
// 主控数据：行点击事件
const HandleRadioChange = (row) => {
    radioRows.value = row
    radio.value = row.value
    GetSingLecontRolsetListUser(row.value)
}
// 主控数据单选：金额
const HandleAmountRadioChange = (row) => {
    console.log("row", row)
    radioAmountRows.value = row
    amountRadio.value = row.Id
    if (!row.Id.startsWith('addAmount')) {
        GetSingLecontRolsetListUser(row.value)
    }
}
// 被控数据：行点击事件
const handleRowClick = (row) => {
    if (tableRef.value) {
        tableRef.value.toggleRowSelection(row); // 切换选中状态 
    }
};
// 被控数据：选中项变化事件
const HandleSelectChange = (val) => {
    selectedRows.value = val; // 更新选中数据 
};
//获取主控对应的被控code
const GetSingLecontRolsetListUser = (parentId) => {
    let formData = {};
    if (isAmountControl.value == 2) {
        formData = {
            processNodeId: route.query.processNodeId,
            GroupName: Number(route.query.masterControl),
            Codes: selectCode.value,
            Values: radioRows.value.value,
            Id: 0
        }
    } else {
        formData = {
            processNodeId: route.query.processNodeId,
            GroupName: Number(route.query.masterControl),
            Codes: selectCode.value,
            Id: amountRadio.value
        }
    }
    GetSingLecontRolsetList(formData).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            let dicIdList = []
            // 取childData内的value与rowdeDicId相同数据
            dicIdList = controlledList.value.filter(t => rows.some(s => s == t.FieldCode))
            // console.log("dicIdList", dicIdList)
            controlledList.value.forEach(row => {
                tableRef.value.toggleRowSelection(row, false)
            })
            nextTick(() => {
                dicIdList.forEach(row => {
                    tableRef.value.toggleRowSelection(row, true)
                })
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 保存联动关系数据信息
const HandleSave = () => {
    // if (selectedRows.value.length == 0) {
    //     ElMessage.error("请选择被控数据");
    //     return
    // }
    let formData = {}
    if (isAmountControl.value == 2) {
        if (!radio.value) {
            ElMessage.error("请选择主控字段数据");
            return
        }

        console.log("selectedRows.value", selectedRows.value)
        formData = {
            ModeType: 1,//主控非金额
            processNodeId: route.query.processNodeId,
            GroupName: Number(route.query.masterControl),
            Codes: selectCode.value,
            Values: radioRows.value.value,
            ListCode: selectedRows.value.map(t => t.FieldCode)
        }
        console.log("保存联动关系数据信息", formData)
    } else {
        if (!amountRadio.value) {
            ElMessage.error("请选择金额条件");
            return
        }
        let amountId = amountRadio.value
        if (amountRadio.value.startsWith('addAmount')) {
            amountId = undefined
        }

        formData = {
            ModeType: 2,//主控金额
            processNodeId: route.query.processNodeId,
            GroupName: Number(route.query.masterControl),
            Codes: selectCode.value,
            BeginAmount: radioAmountRows.value.BeginAmount,
            BeginSymbol: radioAmountRows.value.BeginSymbol,
            EndAmount: radioAmountRows.value.EndAmount,
            EndSymbol: radioAmountRows.value.EndSymbol,
            Id: amountId,
            ListCode: selectedRows.value.map(t => t.FieldCode)
        }
    }
    console.log("保存联动关系数据信息", formData)
    PostSaveControlData(formData).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')

            // 主控金额保存后修正Id
            if (isAmountControl.value == 1 && amountRadio.value.startsWith('addAmount')) {
                console.log("主控金额保存后修正Id", amountRadio.value)
                amountList.value.forEach(row => {
                    if (row.Id == amountRadio.value) {
                        row.Id = res.data.data.footer
                    }
                })
                amountRadio.value = res.data.data.footer
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
};
// 选择主控字段
const filtersChange = (e) => {
    code.value = e
    isAmountControl.value = dropDownList.value.find(t => t.FieldCode == e)?.IsAmountControl
    // console.log("isAmountControl", isAmountControl.value)
    radio.value = false
    amountRadio.value = false
    PostControlSetListUser()
}

// 添加金额条件
const addRow = () => {
    amountList.value.push({
        Id: 'addAmount' + Math.random().toString(36).substring(2),
        MasterControlCode: selectCode.value,
        BeginAmount: '',
        BeginSymbol: '',
        EndAmount: '',
        EndSymbol: '',
    });
}
// 删除金额条件
const delRow = (row) => {
    console.log("amountRadio.value", amountRadio.value, "row.Id", row.Id)

    if (amountRadio.value == row.Id) {
        console.log("删除")
        amountRadio.value = false
    }
    amountList.value = amountList.value.filter(t => t.Id != row.Id)
    if (!row.Id.startsWith('addAmount')) {
        DelAmountControlByid({ id: row.Id }).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '删除成功')
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}


//输入框限制：金额输入
const rulesInput = (val, item, name) => {
    let value = rulesIntegerLimit(val)
    if (value && value > 9999999999) {
        ElMessage.error('输入金额不能大于9999999999');
    }
    item[name] = rulesIntegerLimit(val);
}
</script>
<template>
    <div class="viewContainer">
        <el-select v-model="code" clearable placeholder="主控字段" style="width: 160px" @change="filtersChange">
            <el-option v-for="item in dropDownList" :key="item.FieldCode" :label="item.FieldName"
                :value="item.FieldCode" />
        </el-select>
        <div style="display: flex;">
            <div :style="{ width: isAmountControl == 2 ? '480px' : '680px' }" class="tableW">
                <el-table v-if="isAmountControl == 2" :data="masterControlList" style="width: 100%" height="520"
                    highlight-current-row @current-change="handleCurrentChange">
                    <el-table-column width="55" align="center">
                        <template #default="{ row }">
                            <el-radio v-model="radio" :value="row.value" @change="HandleRadioChange(row)"></el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column label="主控数据" prop="label"> </el-table-column>
                </el-table>
                <el-table v-else :data="amountList" style="width: 100%" height="520" highlight-current-row
                    header-cell-class-name="headerClassName">
                    <el-table-column width="55" align="center">
                        <template #header>
                            <el-icon color="#409efc" style="vertical-align: middle;cursor: pointer;" @click="addRow">
                                <CirclePlus />
                            </el-icon>
                        </template>
                        <template #default="{ row }">
                            <el-radio v-model="amountRadio" :value="row.Id"
                                @change="HandleAmountRadioChange(row)"></el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column label="最小金额" align="center">
                        <template #default="{ row }">
                            <div style="display: flex;">
                                <el-input v-model="row.BeginAmount" @input="rulesInput($event, row, 'BeginAmount')"
                                    placeholder="最小金额"></el-input>
                                <el-select v-model="row.BeginSymbol" placeholder="" style="width: 70px;flex-shrink: 0">
                                    <el-option v-for="item in operatorList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="字段名称" prop="FieldName" align="center" width="120" show-overflow-tooltip>
                        <template #default="{ row }">
                            {{ selectName }}
                        </template>
                    </el-table-column>
                    <el-table-column label="最大金额" align="center">
                        <template #default="{ row }">
                            <div style="display: flex;">
                                <el-select v-model="row.EndSymbol" placeholder="" style="width:70px;flex-shrink: 0">
                                    <el-option v-for="item in operatorList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                                <el-input v-model="row.EndAmount" @input="rulesInput($event, row, 'EndAmount')"
                                    placeholder="最大金额"></el-input>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" width="50">
                        <template #default="{ row }">
                            <el-icon color="#F56C6C" style="vertical-align: middle;cursor: pointer;"
                                @click="delRow(row)">
                                <DeleteFilled />
                            </el-icon>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-divider direction="vertical" style=" height: 520px;" />
            <div style="width: 460px;height: 520px;">
                <el-table ref="tableRef" :data="controlledList" style="width: 100%;" height="520"
                    @row-click="handleRowClick" @selection-change="HandleSelectChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="被控数据" prop="FieldName">
                        <template #default="{ row }">
                            {{ row.FieldName || row.DefaultValue || '' }}({{ row.FieldCode }})
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div style="margin: 10px 0 0 300px ;"><el-button type="success" :icon="Checked"
                @click="HandleSave(row)">保存</el-button>
        </div>
    </div>
</template>
<style lang="scss" scoped>
:deep(.el-table__header-wrapper) .el-checkbox {
    display: none;
}

.tableW {
    height: 520px;
    flex-shrink: 0
}
</style>