<script setup>
defineOptions({
    name: 'dangerchemicalswasterecordlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    DcWasteRecordFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页  
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 50, ObjectType: 1 })

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 列表
const HandleTableData = () => {
    filters.value.ObjectId = route.query.id
    DcWasteRecordFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 分页
const handlePage = (val) => {
    HandleTableData()
} 
</script>
<template>
    <div class="viewContainer">
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="OneClassName" label="一级分类" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="二级分类" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="160" show-overflow-tooltip></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>