<template>
    <el-row v-if="loading">
      <el-col :span="24" class="text-center">
        <el-spinner size="large"></el-spinner>
      </el-col>
    </el-row>
  </template>
  
  <script setup>
  import { onMounted, ref } from 'vue'
  import { userManager } from '@/utils/oidc-client.js'
  import { ElLoading } from 'element-plus';
  
  const loading = ref(true); 
  
  onMounted(async () => {
      const loadingInstance = ElLoading.service({
          fullscreen: true, 
          text: '正在跳转...', 
      });
  
      try {
          await userManager.signinRedirect();
      } catch (error) {
        //   router.push('/');
      } finally {
          loadingInstance.close(); 
          loading.value = false; 
      }
  });
  </script>
  
