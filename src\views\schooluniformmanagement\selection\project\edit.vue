<script setup>
defineOptions({
    name: 'projectedit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    QuestionFilled, Select, UploadFilled, View, Position, RefreshLeft, Delete
} from '@element-plus/icons-vue'
import {
    Getpagedbytype, AttachmentUpload
} from '@/api/user.js'
import {
    SchemeGetbyid, SchemeSaveadd, SchemeSaveedit, Delattachmentbyid, SchemeSavepublish, SchemePublishcancel
} from '@/api/selection.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit, yearDate, fileDownload, foundReturn, tagsListStore } from "@/utils/index.js";
import Editor from '@/components/Editor/index.vue'
import ModelText from '@/components/ModelText.vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
// 二维码生成下载
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
// 复制链接
import useClipboard from 'vue-clipboard3'
const { toClipboard } = useClipboard();
const userStore = useUserStore()
const route = useRoute()
const props = defineProps({
    id: {
        type: String,
        default: ''
    }
})
const formData = ref({})
const refForm = ref()
const uploadFileData = ref([])
const yearDateList = ref([])
const modelVisible = ref(false)
const SolicitedStatuz = ref(0)//是否发布
const ModelPath = ref('')
const FilingStatuz = ref(0)//审核不通过或退回 状态值
const FilingExplanation = ref('')//审核不通过或退回原因
const ruleForm = {
    SchemeYear: [
        { required: true, message: '请选择年度', trigger: 'change' },
    ],
    SolicitedNum: [
        { required: true, message: '请输入应征求人数（人）', trigger: 'change' },
    ],
    SolicitedDeadline: [
        { required: true, message: '请选择征求意见截止时间', trigger: 'change' },
    ],
}
const disabledDate = (time) => {
    return time.getTime() < Date.now()
}
onMounted(() => {
    yearDateList.value = yearDate()
    // GetpagedbytypeUser()// 获取附件信息
    if (route.query.id) {
        formData.value.Id = route.query.id
        if (route.query.id == '0') {
            formData.value.Id = 0
            SolicitedStatuz.value = 0
            nextTick(() => {
                refForm.value.resetFields()
            })
        }
    }
    if (route.query.isTagRouter) {
        SchemeGetbyidUser(formData.value.Id)
    }
})
onActivated(() => {
    // 修改tag标签名称########################
    if (route.query.title) {
        // 修改pinia数据
        userStore.$patch(state => {
            state.pageTitleObj.projectTitle = route.query.title
        })
    }
    // pageTitleObj
    let tagsList = userStore.tagsList
    // 使用forEach遍历数组  修改tag标签名称
    tagsList.forEach(item => {
        if (item.path == route.path) {
            // 如果path匹配，则修改title  
            item.title = userStore.pageTitleObj.projectTitle;
        }
    });
    userStore.setTagsList(tagsList)
    // ########################
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)

    fileIdList.value = []
    if (route.query.id) {
        formData.value.Id = route.query.id
        if (route.query.id == '0') {
            formData.value.Id = 0
            SolicitedStatuz.value = 0
            nextTick(() => {
                refForm.value.resetFields()

            })
        }
    }

    nextTick(() => {
        if (!route.query.isTagRouter) {
            SchemeGetbyidUser(formData.value.Id)
        }
    })
})
//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 101 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
            console.log('uploadFileData.value', uploadFileData.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const SchemeGetbyidUser = (id) => {
    SchemeGetbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, footer, other } = res.data.data

            ModelPath.value = other.ModelPath;//家长意见书模板
            // 清除页面缓存附件存留造成的影响
            uploadFileData.value.forEach(item => {
                item.fileLChildList = []
                item.categoryList = []
            })
            fileIdList.value = []
            if (rows) {
                formData.value.SchemeYear = rows.SchemeYear;//年度
                formData.value.SchemeNo = rows.SchemeNo;//选用批次
                formData.value.Id = rows.Id;//Id
                formData.value.SolicitedNum = rows.SolicitedNum;//应征求人数
                formData.value.SolicitedDeadline = rows.SolicitedDeadline.substring(0, 10);//截止日期
                formData.value.ParentOpinion = rows.ParentOpinion || '';//富文本
                SolicitedStatuz.value = rows.SolicitedStatuz;//状态
                if (rows.SolicitedStatuz) {
                    let url = window.location.protocol + '//' + window.location.host
                    qrText.value = url + "/ui/#" + "/pages/selection/newedit?id=" + rows.Id;//二维码信息
                }
                let categoryList = footer || []
                if (categoryList.length > 0) {
                    // 遍历数组 b 的每个元素
                    categoryList.forEach(item => {
                        // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                        let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                        // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                        if (match) {
                            match.categoryList.push(item);
                        }
                    });
                }
                FilingExplanation.value = rows.FilingExplanation || ''
                FilingStatuz.value = rows.FilingStatuz

            } else {
                formData.value = {}
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//提交
const HandleSubmit = (num) => {
    if (num == 1) {
        // 保存
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            // 判断必传的附件是否已传
            let found = foundReturn(uploadFileData.value)
            if (found) {
                return
            }
            ElMessageBox.confirm('确定提交保存吗?')
                .then(() => {
                    formData.value.AttachmentIdList = fileIdList.value.map(t => t.Id)
                    if (formData.value.Id) {
                        formData.value.SchemeNo = undefined
                        SchemeSaveeditUser()
                    } else {
                        formData.value.Id = undefined
                        SchemeSaveaddUser()
                    }
                })
                .catch((err) => {
                    console.info(err)
                })
        })
    } else if (num == 2) {
        // 发布
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            // 判断必传的附件是否已传
            let found = foundReturn(uploadFileData.value)
            if (found) {
                return
            }
            ElMessageBox.confirm('确定提交发布吗?')
                .then(() => {
                    formData.value.AttachmentIdList = fileIdList.value.map(t => t.Id)
                    if (formData.value.Id == 0) {
                        formData.value.Id = undefined
                    }
                    SchemeSavepublishUser()
                })
                .catch((err) => {
                    console.info(err)
                })
        })
    } else if (num == 3) {
        // 撤销发布
        ElMessageBox.confirm('确定撤销发布吗?')
            .then(() => {
                SchemePublishcancelUser()
            })
            .catch((err) => {
                console.info(err)
            })
    }
}

const editorChange = (getText, getHtml) => {
    // console.log("getText", getText, "getHtml", getHtml)
}
const editorRef = ref(null)//定义普通类型
//二维码图片的编码数据
const qrText = ref("");
const qrData = ref("");
//下载二维码图片
const downQr = () => {
    let name = new Date().getTime();
    let a = document.createElement("a");
    a.style.display = "none";
    a.download = name;
    a.href = qrData.value;
    document.body.appendChild(a);
    a.click();
}
//qr的回调，每次变动后把二维码的数据保存下来，供下载用
const qrBack = (dataUrl, id) => {
    qrData.value = dataUrl;
}
const copy = async () => {
    try {
        await toClipboard(qrText.value);
    } catch (e) {
        console.error(e);
    }
}
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.categoryList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    console.log("ary", ary, "name", name)
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    Delattachmentbyid({ id: formData.value.Id, attid: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选用方案添加保存
const SchemeSaveaddUser = (row) => {
    SchemeSaveadd(formData.value).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            ElMessage.success(res.data.msg || '保存成功')
            fileIdList.value = [];
            formData.value.Id = rows.Id;//Id
            // SchemeGetbyidUser(formData.value.Id)//重新获取数据，有附件再放开
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选用方案修改保存
const SchemeSaveeditUser = (row) => {
    SchemeSaveedit(formData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            const { rows } = res.data.data
            fileIdList.value = [];
            formData.value.Id = rows.Id;//Id
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选用方案发布
const SchemeSavepublishUser = (row) => {
    SchemeSavepublish(formData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '发布成功')
            const { rows, footer } = res.data.data
            formData.value.Id = rows.Id;//Id
            fileIdList.value = [];//Id
            SolicitedStatuz.value = rows.SolicitedStatuz
            let url = window.location.protocol + '//' + window.location.host
            qrText.value = url + "/ui/#" + "/pages/selection/newedit?id=" + rows.Id;//二维码信息
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选用方案撤销发布
const SchemePublishcancelUser = () => {
    SchemePublishcancel({ id: formData.value.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '撤销成功')
            const { rows } = res.data.data
            SolicitedStatuz.value = 0
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 模板下载||复制模板
const HandleDownload = () => {
    // fileDownload(ModelPath.value);
    modelVisible.value = true
}
const getCopy = (e) => {
    formData.value.ParentOpinion = e
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }

}

// 手机预览 二维码弹窗
const dialogPreviewVisible = ref(false)
const qrPreviewText = ref('')
// 预览
const HandlePreviewMobile = (num) => {
    dialogPreviewVisible.value = true
    let url = window.location.protocol + '//' + window.location.host
    qrPreviewText.value = url + "/ui/#" + "/pages/selection/editPreview?id=" + formData.value.Id;
}
</script>
<template>
    <el-form style="width: 900px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
        label-width="180px" status-icon>
        <el-form-item label-width="100px" style="border-bottom: 1px dashed #E4E7ED" v-if="!formData.SchemeNo">
            <span style="color: #E6A23C;">请选择正确，否则会影响选用批次编号！</span>
        </el-form-item>
        <el-form-item v-if="FilingStatuz == 11 || FilingStatuz == 21" :label="FilingStatuz == 11 ? '审核不通过原因：' : '退回原因：'"
            style="width: 100%; border-bottom: 1px dashed #E4E7ED; ">
            <span style="color: #F56C6C;padding-left: 10px;">{{ FilingExplanation }}</span>
        </el-form-item>
        <el-form-item label="选用批次：" v-if="formData.SchemeNo">
            <el-input v-model="formData.SchemeNo" disabled style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="年度：" prop="SchemeYear">
            <el-select v-model="formData.SchemeYear" placeholder="请选择年度" style="width: 50%;">
                <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="应征求人数（人）：" prop="SolicitedNum">
            <el-input v-model="formData.SolicitedNum" auto-complete="off"
                @input="integerLimitInput($event, 'SolicitedNum')" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="征求意见截止时间：" prop="SolicitedDeadline">
            <el-date-picker type="date" placeholder="选择日期" v-model="formData.SolicitedDeadline" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" style="width: 50%;"></el-date-picker>
        </el-form-item>
        <el-form-item v-for="(item, index) in uploadFileData" :key="index">
            <template #label>
                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> {{ item.Name }}： </span>
            </template>
            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" :accept="item.UploadFileTypeAccept"
                :before-upload="beforeAvatarUpload.bind(null, item)" :http-request="httpRequest.bind(null, item, index)"
                :disabled="numberDisabled">
                <el-button type="success" size="small" :icon="UploadFilled"
                    @click="MaxFileNumberClick(item)">上传</el-button>
            </el-upload>
            <div class="fileFlex">
                <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                    <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                        {{ itemCate.Title }}{{ itemCate.Ext }}
                    </span>
                </div>
                <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                    <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemChild, item.fileLChildList)">
                        {{ itemChild.Title }}{{ itemChild.Ext }}
                    </span>
                </div>
            </div>
        </el-form-item>
        <el-form-item label-width="0">
            <div style="width: 100%; text-align: center;font-size: 18px;">关于征订校服的征求家长意见书： <span
                    style="font-size: 16px; color: #409EFF;cursor: pointer;" @click="HandleDownload">《意见书》示例</span>
            </div>
        </el-form-item>
        <!-- 富文本 -->
        <el-form-item label-width="40px">
            <Editor v-model:get-html="formData.ParentOpinion" :defaultHtml="formData.ParentOpinion"
                @editorChange="editorChange">
            </Editor>
        </el-form-item>
        <el-form-item label-width="40px">
            <el-button v-if="SolicitedStatuz == 0" type="primary" :icon="Select" @click="HandleSubmit(1)">保存</el-button>
            <el-button v-if="formData.Id" type="success" :icon="View" @click="HandlePreviewMobile">预览</el-button>
            <el-button type="primary" :icon="Position" @click="HandleSubmit(2)">发布</el-button>
            <el-button v-if="SolicitedStatuz == 1" type="warning" :icon="RefreshLeft"
                @click="HandleSubmit(3)">撤销发布</el-button>
        </el-form-item>
        <el-divider content-position="left" v-if="SolicitedStatuz == 1"><span
                style="color:#E6A23C">下载二维码或复制链接发送给家长在线征求意见</span>
        </el-divider>
        <el-form-item label-width="40px" v-if="SolicitedStatuz == 1">
            <div>
                <!-- 复制链接 -->
                <div>
                    <el-input v-model="qrText" style="width: 560px" placeholder="http" disabled>
                        <template #append><el-button type="primary" plain @click="copy">复制链接</el-button></template>
                    </el-input>
                </div>
                <div style="display: flex;align-items: center;">
                    <vue-qr :callback="qrBack" :text="qrText" :size="120"></vue-qr>
                    <el-button type="primary" plain @click="downQr">下载二维码图片</el-button>
                </div>
            </div>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    <!-- 手机预览 二维码弹窗-->
    <el-dialog v-model="dialogPreviewVisible" title="手机预览二维码" width="460px">
        <div style="text-align: center;font-size: 18px;color:#999;">请使用手机扫描二维码预览</div>
        <div style="text-align: center;">
            <vue-qr :text="qrPreviewText" :size="160"></vue-qr>
        </div>
    </el-dialog>
    <ModelText v-model:modelVisible="modelVisible" @getCopy="getCopy"></ModelText>
</template>
<style lang="scss" scoped>
.editorBox {
    padding: 40px;
    width: 700px;
    background-color: #fff;
    margin: 50px auto auto;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .04), 0 4px 10px rgba(0, 0, 0, .08);
}

:deep(.el-input-group__append) {
    background-color: #ecf5ff;

    button {
        border-color: #409eff;
        color: #409eff;
    }
}

:deep(.el-input-group__append:hover) {
    background-color: #409eff;
    color: #fff;
}
</style>