<script setup>
defineOptions({
    name: 'dangerchemicalsschoolmaterialbacklog'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy
} from '@element-plus/icons-vue'
import {
    DcCompanyComboxFind, DcSchoolMaterialBackLogFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const companyList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const BackDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, InputType: 1, Statuz: 1, StockNumgt: 0, sortModel: [{ SortCode: "RegDate", SortType: "DESC" }] })
const options = ref([
    { value: 'PurchaseBatchNo', label: '采购批次', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('PurchaseBatchNo')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcCompanyComboxFindUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

const backDategeChange = (val) => {
    if (!val) filters.value.BackDatege = undefined
    HandleTableData()
}
const backDateleChange = (val) => {
    if (val) {
        filters.value.BackDatele = val + " 23:59:59"
    } else {
        filters.value.BackDatele = undefined
    }
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    filters.value.PurchaseBatchNo = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcSchoolMaterialBackLogFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.LvCompanyId = undefined
    filters.value.BackDatege = undefined
    filters.value.BackDatele = undefined
    BackDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 获取学校供应商信息-查询
const DcCompanyComboxFindUser = () => {
    DcCompanyComboxFind({ Statuz: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            companyList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}

const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 打印
const HandlePrint = (e) => {
    router.push({
        path: "./materialbacklogprint",
        query: {
            companyid: filters.value.LvCompanyId,
            dtBegin: filters.value.BackDatege,
            dtEnd: filters.value.BackDatele,
            datefiled: filtersValue.value,
            keyword: filtersKey.value,
            path: './materialbacklog',
        }
    })
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!BackDatele.value) return false;
    return time >= new Date(BackDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BackDatege) return false;
    return time < new Date(filters.value.BackDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    已退货危化品 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>第一步，选择“退货时间”和“供应商”；</li>
                    <li>第二步，点击【打印退货单】</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy"
                            :disabled="!filters.LvCompanyId || tableData.length == 0"
                            @click="HandlePrint">打印退货单</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BackDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="退货时间"
                            @change="backDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="BackDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="退货时间" @change="backDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.LvCompanyId" clearable filterable placeholder="供应商"
                            style="width: 160px" @change="filtersChange">
                            <el-option v-for="item in companyList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Price" label="单价" min-width="160" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="BackDate" label="退货时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.BackDate ? row.BackDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CompanyName" label="供应商" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="PurchaseBatchNo" label="采购批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Remark" label="退货原因" min-width="160" show-overflow-tooltip></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>