<script setup>
defineOptions({
    name: 'dangerchemicalscountypurchasesummary'
});
import {
    Refresh, Search, Position
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DcSchoolPurchaseSummaryListFind, Punitgetschoolbycountyid
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页  
import router from '@/router'
import { useRoute } from 'vue-router'
import { ExcelDownload } from "@/utils/index.js"
import {
    DcSchoolPurchaseSummaryListExport
} from '@/api/directdata.js'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: route.query.p, sortModel: [{ SortCode: "Sort", SortType: "ASC" }, { SortCode: "SchoolId", SortType: "ASC" }, { SortCode: "SecondId", SortType: "ASC" }, { SortCode: "AuditDate", SortType: "DESC" }] })
const SchoolData = ref([])
const fiveYearData = ref([])

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        LoadYear()
        LoadSchool()
        HandleTableData()
    }
})
onActivated(() => {

    nextTick(() => {
        if (!route.query.isTagRouter) {
            LoadYear()
            LoadSchool()
            HandleTableData()
        }
    })
})

//加载年度
const LoadYear = () => {
    const date = new Date().getFullYear()
    for (let i = 0; i < 5; i++) {
        fiveYearData.value.push({
            id: date - i,
            name: date - i
        })
    }
}

//加载学校信息
const LoadSchool = () => {
    Punitgetschoolbycountyid({ CountyId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            SchoolData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 列表
const HandleTableData = () => {
    DcSchoolPurchaseSummaryListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.AuditDate = undefined
    filters.value.SchoolId = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

const filtersChange = () => { HandleTableData() }
//导出
const HandleExport = () => {
    DcSchoolPurchaseSummaryListExport(filters.value).then(res => {
        ExcelDownload(res)
    });
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.AuditDate" clearable placeholder="年度" @change="filtersChange"
                            style="width: 120px">
                            <el-option v-for="item in fiveYearData" :key="item.id" :label="item.name"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable placeholder="学校名称" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in SchoolData" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="AuditYear" label="年度" min-width="80" align="center"></el-table-column>
            <el-table-column prop="SchoolName" label="学校名称" min-width="160"></el-table-column>
            <el-table-column prop="SecondName" label="危化品分类" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="Num" label="采购数量" min-width="120" align="right"></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>