<script setup>
import { onMounted, ref, watch } from 'vue'
import {
    Schoolinfopropertyfind,
} from '@/api/user.js'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
// 表格初始化
const userStore = useUserStore()
const route = useRoute()
const hUnitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()

const filtersKey = ref({ key: '', value: 'PlaceName' })
const filters = ref({ UnitId: route.query.UnitId, pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const options = ref([
    { value: 'PlaceName', label: '场所属性', },
    { value: 'ResponsibleName', label: '责任人', },
    { value: 'RoomName', label: '场所(室)名称', },
    { value: 'ProPertyName', label: '楼宇场馆名称', }
])

// 翻页
watch(() => filters.value.pageIndex, () => {
    HandleSearch()
})
watch(() => filters.value.pageSize, () => {
    filters.value.pageIndex = 1
    HandleSearch()
})

//加载数据
onMounted(() => {
    HandleSearch()
})


//搜索
const HandleSearch = (page) => {

    if (page) filters.value.pageIndex = page
    // 按类型搜索
    filters.value.PlaceName = undefined;
    filters.value.ResponsibleName = undefined;
    filters.value.RoomName = undefined;
    filters.value.ProPertyName = undefined;

    // 按类型搜索
    if (filtersKey.value.key) {
        filters.value[filtersKey.value.value] = filtersKey.value.key;
    } else {
        filters.value[filtersKey.value.value] = undefined;
    }

    Schoolinfopropertyfind(filters.value).then(res => {
        if (res.data.flag == 1) {
            tableData.value = res.data.data.rows;
            tableTotal.value = Number(res.data.data.total);
        } else {
            ElMessage.error(res.data.msg)
        }


    });
}
// 重置
const HandleReset = (page) => {
    filtersKey.value.key = ''
    HandleSearch(page)
}

</script>
<template>
    <!-- 搜索 -->
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                <el-form-item label="" class="flexItem" label-width="60">
                    <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="max-width: 300px"
                        class="input-with-select">
                        <template #prepend>
                            <el-select v-model="filtersKey.value" style="width: 80px">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" plain @click="HandleSearch(1)">查询</el-button>
                    <el-button type="primary" plain @click="HandleReset(1)">重置</el-button>
                </el-form-item>
            </el-form>

        </el-col>
    </el-row>
    <!-- 内容 -->
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
        @selection-change="HandleSelectChange" @row-click="HandleClickRow" header-cell-class-name="headerClassName">
        <el-table-column prop="ProPertyName" label="楼宇场馆名称" min-width="180" align="center"></el-table-column>
        <el-table-column prop="RoomName" label="场所(室)名称" min-width="180" align="center"></el-table-column>
        <el-table-column prop="Code" label="场所(室)别名" min-width="180" align="center">
            <template #default="{ row }">
                {{ row.userCount ? row.userCount : '-' }}
            </template>
        </el-table-column>
        <el-table-column prop="PlaceName" label="场所(室)属性" min-width="180" align="center">
            <template #default="{ row }">
                {{ row.userCount ? row.userCount : '-' }}
            </template>
        </el-table-column>
        <template #empty>
            <el-empty description="没有数据"></el-empty>
        </template>
    </el-table>
    <!-- 分页 -->
    <el-row>
        <el-col class="flexBox">
            <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
                :total="tableTotal" v-model:current-page="filters.pageIndex" v-model:page-size="filters.pageSize" />
        </el-col>
    </el-row>



</template>
<style lang="scss" scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}
</style>