<script setup>
defineOptions({
    name: 'dangerchemicalscountywastestatisticslist'
});
import {
    Refresh, Search, Back
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DcWasteDisposalStatisticsFind, Punitgetschoolbycountyid, DcWasteClassGet
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const unitType = ref(userStore.userInfo.UnitType)
const schoolList = ref([])
const columnList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ CountyId: route.query.countyId, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const summation = ref([])

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        PunitgetschoolbycountyidUser()
        DcWasteClassGetUser()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            PunitgetschoolbycountyidUser()
            DcWasteClassGetUser()
        }
    })
})
// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值  
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        console.log(column.property)
        for (let key in summation.value) {
            if (summation.value.hasOwnProperty(key)) {
                // console.log(`键名: ${key}, 键值: ${summation.value[key]}`);
                if (column.property == key) {
                    sums[index] = summation.value[key]
                    return;
                }
            }
        }
    });
    return sums;
}
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })
}
// 列表
const HandleTableData = () => {
    DcWasteDisposalStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            summation.value = rows.Other[0] || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.SchoolId = undefined
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }

// 根据区县获取学校
const PunitgetschoolbycountyidUser = () => {
    Punitgetschoolbycountyid({ CountyId: route.query.countyId || 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            schoolList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取危化品一级分类|| 二级分类
const DcWasteClassGetUser = () => {
    DcWasteClassGet({ id: 0, depth: 1, pid: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            columnList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
} 
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="unitType == 1 && route.query.path">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="unitType == 1 && route.query.path"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 200px">
                            <el-option v-for="item in schoolList" ::key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            :summary-method="getSummaries" show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="SchoolName" label="学校名称" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column v-for="item in columnList" :key="item.Id" :prop="item.Name"
                :label="item.Name + '(' + item.UnitsMeasurement + ')'" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row[item.Name] }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<style lang="scss" scoped></style>