<script setup>
import {
  Search, Refresh, FolderAdd, QuestionFilled, UploadFilled, Download
} from '@element-plus/icons-vue'
import { onMounted, ref, watch } from 'vue'
import { useUserStore } from '@/stores';

import {
  Unitgetbyid, Unitinsertupdate, Unitsortedit, Unitfind, Getdictionarycombox,
  Unitfindidname, Areagetbypid, Getareabyunitid, UploadPostexecl, SuperAdminUploadUnitFile
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit, limit, fileDownload } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页 
import AppBox from "@/components/Approve/AppBox.vue";
// 表格初始化
const userStore = useUserStore()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const unitType = ref(0)
const selectRows = ref([])
const schoolStageList = ref([])
const subjectNatureStageList = ref([])
const excelUrl = ref('')
const filtersKey = ref({ key: '', value: 'Name' })
const filters = ref({ pageIndex: 1, pageSize: 10, statuz: 1, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const provincialDataList = ref([{ Name: "全部", Id: "0" }])
const cityDataList = ref([{ Name: "全部", Id: "0" }])
const countyDataList = ref([{ Name: "全部", Id: "0" }])
const areaForm = ref({ ProvinceIdAreaId: '0', CityAreaId: '0', CountyAreaId: '0' })
const provincialName = ref('')
const cityName = ref('')
const countyName = ref('')
const options = ref([
  { value: 'Name', label: '名称', },
  { value: 'Code', label: '编号', },
  { value: 'Brief', label: '简称', },
])
const roleFormData = ref([
  { Id: 0, Name: '超级管理员', },
  { Id: 1, Name: '市级', },
  { Id: 2, Name: '区县', },
  { Id: 3, Name: '单位', },
  { Id: 4, Name: '企业', },
])
const industryFormData = ref([
  { Id: 1, Name: "普教" }
])

const HandleSelectChange = (selection) => {
  selectRows.value = selection
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}

import router from '@/router'
//加载数据
onMounted(() => {
  HandleTableData()
  MountedUser()
  UnitfindidnameUser()
  AreagetbypidUser(0, 1, false)
})
//输入整数
const integerLimitInput = (val, name) => {
  formData.value[name] = integerLimit(val);
}
const limitInput = (val, name) => {
  formData.value[name] = limit(val);
}
//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const isAdd = ref(true)
const refForm = ref()
const ruleForm = {
  Name: [
    { required: true, message: '单位名称不能为空', trigger: 'change' },
  ],
  Code: [
    { required: true, message: '单位代码不能为空', trigger: 'change' },
  ],
  UnitType: [
    { required: true, message: '请选择单位性质', trigger: 'change' },
  ],
  IndustryId: [
    { required: true, message: '请选择行业类型', trigger: 'change' },
  ],
  Mobile: [
    {
      pattern: /^1[0-9]{10}$/,
      message: '请输入11位手机号码',
      trigger: 'blur'
    }
  ],
  Tel: [
    {
      pattern: /^1[0-9]{10}$/,
      message: '请输入11位手机号码',
      trigger: 'blur'
    }
  ],
  ZipCode: [
    {
      pattern: /^\d{6}$/,
      message: '请输入6位数邮编',
      trigger: 'blur'
    }
  ],
}

const resetFormData = ref({ Id: '0', Sort: 0 })
const dialogResetVisible = ref(false)
const resetForm = ref()
const resetFormRule = {
  Sort: [
    { required: true, message: '请输入排序值', trigger: 'change' },
  ],
}
// 添加账户
const HandleAdd = () => {
  isAdd.value = true
  formData.value.Id = '0'
  formData.value = {}
  dialogVisible.value = true
}
//修改
const HandleEdit = (row) => {
  isAdd.value = false
  formData.value.Id = row.Id
  Unitgetbyid({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      console.log("详情", rows)
      formData.value.Id = rows.Id
      if (rows.PId == '0') {
        formData.value.PId = undefined
      } else {
        formData.value.PId = rows.PId
      }

      formData.value.UnitType = rows.UnitType
      formData.value.IndustryId = rows.IndustryId
      formData.value.Code = rows.Code
      formData.value.Name = rows.Name
      formData.value.Brief = rows.Brief
      formData.value.OrganizationCode = rows.OrganizationCode
      formData.value.Legal = rows.Legal
      formData.value.Mobile = rows.Mobile
      formData.value.Tel = rows.Tel
      formData.value.AreaId = rows.AreaId
      formData.value.Address = rows.Address
      formData.value.ZipCode = rows.ZipCode
      formData.value.Email = rows.Email
      formData.value.EmployeeNum = rows.EmployeeNum
      formData.value.Position = rows.Position
      formData.value.ContactUser = rows.ContactUser
      formData.value.SubjectNature = String(rows.SubjectNature)
      formData.value.Url = rows.Url
      formData.value.IsServiceProvider = rows.IsServiceProvider
      formData.value.IsSupplier = rows.IsSupplier
      formData.value.SchoolNature = rows.SchoolNature
      formData.value.ClassNum = rows.ClassNum
      formData.value.TeacherNum = rows.TeacherNum
      formData.value.StudentNum = rows.StudentNum
      formData.value.FloorArea = rows.FloorArea
      formData.value.BuildArea = rows.BuildArea
      formData.value.SchoolStage = String(rows.SchoolStage)
      formData.value.Introduction = rows.Introduction

      // Areagetbyid({ id: rows.AreaId }).then((res) => {
      //   console.log("resres1111111111",res.data)
      //   formData.value.AreaName = res.data.data.rows.FullName
      // })

      Getareabyunitid({ UnitId: rows.Id }).then(res => {
        const { rows } = res.data.data
        console.log("所在地详情", rows)
        AreagetbypidUser(rows.ProvinceId, 2, false)
        AreagetbypidUser(rows.CityId, 3, false)
        areaForm.value.ProvinceIdAreaId = rows.ProvinceId
        areaForm.value.CityAreaId = rows.CityId
        areaForm.value.CountyAreaId = rows.CountyId
        provincialName.value = rows.ProvinceName
        cityName.value = rows.CityName
        countyName.value = rows.CountyName
        formData.value.AreaName = rows.ProvinceName + rows.CityName + rows.CountyName
      })
    } else {
      ElMessage.error(res.data.msg)
    }
  }).catch((err) => {
    console.info(err)
  })
  dialogVisible.value = true
}

//获取列表 与 搜索
const HandleTableData = () => {
  filters.value.Name = undefined;
  filters.value.Code = undefined;
  filters.value.Brief = undefined;
  filters.value.UnitType = undefined;

  // 按类型搜索
  if (filtersKey.value.key) {
    filters.value[filtersKey.value.value] = filtersKey.value.key;
  } else {
    filters.value[filtersKey.value.value] = undefined;
  }

  selectRows.value = []

  Unitfind(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { other, rows, total } = res.data.data
      tableData.value = rows;
      excelUrl.value = other;
      tableTotal.value = Number(total);
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
//搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filtersKey.value.key = ''
  HandleTableData()
}

// 新增或修改提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    Unitinsertupdate(formData.value).then(res => {
      if (res.data.flag == 1) {
        HandleTableData()
        UnitfindidnameUser()
        ElMessage.success('保存成功')
        dialogVisible.value = false
      } else {
        ElMessage.error(res.data.msg)
      }
    }).catch((err) => {
      console.info(err)
    })
  })
}

// 添加修改单位用户信息提交
const SubmitReset = () => {

  resetForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    Unitsortedit(resetFormData.value).then(res => {
      if (res.data.flag == 1) {
        HandleTableData()
        UnitfindidnameUser()
        ElMessage.success('修改成功')
        dialogResetVisible.value = false
      } else {
        ElMessage.error(res.data.msg)
      }
    }).catch((err) => {
      console.info(err)
    })
  })
}

const MountedUser = () => {
  // 单位属性
  Getdictionarycombox({ typeCode: 1101 }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      // console.log("单位属性", rows)
      schoolStageList.value = rows
    } else {
      ElMessage.error(res.data.msg)
    }
  })
  // 主体性质
  Getdictionarycombox({ typeCode: 15000 }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      // console.log("主体性质", rows)
      subjectNatureStageList.value = rows
    } else {
      ElMessage.error(res.data.msg)
    }
  })

}

const UnitfindidnameUser = () => {

  // 父级单位
  Unitfindidname({ UnitCityOrCounty: true }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      console.log("父级单位", rows)
      unitType.value = rows
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

// 获取省市区 p: 1：省  2：市  3：区县   cut:是否为通过切换省市查找下一级
const AreagetbypidUser = (pid, p, cut) => {
  Areagetbypid({ pid: pid }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      rows.unshift({ Name: "全部", Id: "0" });

      if (p == 1) {
        provincialDataList.value = rows
      } else if (p == 2) {
        cityDataList.value = rows
        if (cut) {
          let find = provincialDataList.value.find(t => t.Id == pid)
          if (find) provincialName.value = find.Name
          formData.value.AreaName = provincialName.value
          areaForm.value.CityAreaId = '0'
          areaForm.value.CountyAreaId = '0'
        }
      } else if (p == 3) {
        countyDataList.value = rows
        if (cut) {
          let find = cityDataList.value.find(t => t.Id == pid)
          if (find) cityName.value = find.Name
          formData.value.AreaName = provincialName.value + ',' + cityName.value
          areaForm.value.CountyAreaId = '0'
        }
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  })
  formData.value.AreaId = pid
}

const CountyChange = (pid) => {
  countyName.value = countyDataList.value.find(t => {
    return t.Id == pid
  }).Name
  formData.value.AreaName = provincialName.value + ',' + cityName.value + ',' + countyName.value
  formData.value.AreaId = pid
}
// 仅显示单位创建
const handleFilter = (e) => {
  console.log(e)
  if (e) {
    filters.value.SchoolCreate = e
  } else {
    filters.value.SchoolCreate = undefined
  }
  HandleTableData()
}
const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
//  本单位用户导入
const importExecl = () => {
  uploadVisible.value = true
}
// 模板下载
const HandleDownload = () => {
  fileDownload(excelUrl.value);
}
const fileFile = ref()
const uploadRef = ref()

// 导入前校验
const beforeAvatarUpload = (file) => {
  fileFile.value = file
  const extension = file.name.split('.')[1] === 'xls'
  const extension2 = file.name.split('.')[1] === 'xlsx'
  if (!extension && !extension2) {
    ElMessage({
      message: '上传模板只能是 xls、xlsx格式!',
      type: 'error'
    })
  }
  return extension || extension2
}
const httpRequest = () => {

  UploadPostexecl([fileFile.value]).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data

      SuperAdminUploadUnitFile({ FilePath: rows }).then(res1 => {
        if (res1.data.flag == 1) {
          ElMessage.success(res1.data.msg || '导入成功')
          setTimeout(function () {
            HandleTableData()
            uploadVisible.value = false
          }, 1000);
        } else {
          ElMessage({
            showClose: true,
            message: res1.data.msg,
            type: 'error',
            duration: 5000
          })
        }
      })
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}


</script>
<template>
  <!-- 搜索 -->
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filtersKey" class="flexBox" label-width="60">
        <el-form-item class="flexItem">
          <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
          <el-button type="primary" :icon="UploadFilled" @click="importExecl">企业单位导入</el-button>
        </el-form-item>
        <div class="verticalIdel"></div>
        <el-form-item class="flexItem">
          <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="width: 240px" class="input-with-select">
            <template #prepend>
              <el-select v-model="filtersKey.value" style="width: 80px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-checkbox v-model="filtersKey.SchoolCreate" label="仅显示单位创建" size="large" @change="handleFilter" />
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
  <!-- 内容 -->
  <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" border stripe
    header-cell-class-name="headerClassName">
    <el-table-column type="selection" width="50"></el-table-column>
    <el-table-column prop="Code" label="单位代码" min-width="140" align="center"></el-table-column>
    <el-table-column prop="Name" label="单位名称" min-width="180"></el-table-column>
    <el-table-column prop="Brief" label="简称" min-width="140"></el-table-column>
    <el-table-column prop="UnitType" label="性质" min-width="100" align="center">
      <template #default="{ row }">
        {{ row.UnitType == 1 ? '市级' : row.UnitType == 2 ? '区县' : row.UnitType == 3 ? '单位' : row.UnitType == 4 ? '企业' :
          '未知' }}
      </template>
    </el-table-column>
    <el-table-column prop="EmployeeNum" label="员工数" min-width="80" align="center"></el-table-column>
    <el-table-column prop="opt" label="操作" min-width="80" align="center">
      <template #default="{ row }">
        <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>
  <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
    @handleChange="handlePage" />
  <!-- 添加修改单位用户信息 -- 弹窗 -->
  <app-box v-model="dialogVisible" :height="600" :width="940" :lazy="true" :title="isAdd ? '添加单位信息' : '修改单位信息'">
    <template #content>
      <el-form class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="140px"
        status-icon>
        <fieldset>
          <legend>单位信息</legend>
          <el-form-item label="名称：" prop="Name" class="dialogFlexRulesBox">
            <el-input v-model="formData.Name"></el-input>
          </el-form-item>
          <el-form-item label="父级单位：">
            <el-select v-model="formData.PId">
              <el-option class="flexItem" v-for="item in unitType" :key="item.Id" :label="item.Name" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="简称：">
            <el-input v-model="formData.Brief" auto-complete="off"></el-input>
          </el-form-item>
          <el-form-item label="社会统一信用代码：" class="Organiz">
            <el-input v-model="formData.OrganizationCode" auto-complete="off"></el-input>
          </el-form-item>
          <div class="dialogFlexBox">
            <el-form-item label="单位代码：" prop="Code" class="dialogFlexRulesBox">
              <el-input v-model="formData.Code" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="法人：">
              <el-input v-model="formData.Legal" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="单位性质：" prop="UnitType" class="dialogFlexRulesBox">
              <el-select v-model="formData.UnitType">
                <el-option class="flexItem" v-for="item in roleFormData" :key="item.Id" :label="item.Name"
                  :value="item.Id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="行业类型：" prop="IndustryId" class="dialogFlexRulesBox">
              <el-select v-model="formData.IndustryId">
                <el-option class="flexItem" v-for="item in industryFormData" :key="item.Id" :label="item.Name"
                  :value="item.Id">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="dialogFlexBox">
            <el-form-item label="省市区：">
              <el-select v-model="areaForm.ProvinceIdAreaId" style="width: 101px"
                @change="AreagetbypidUser($event, 2, true)">
                <el-option v-for="item in provincialDataList" :key="item.Id" :label="item.Name" :value="item.Id" />
              </el-select>
              <el-select v-model="areaForm.CityAreaId" style="width: 101px" @change="AreagetbypidUser($event, 3, true)">
                <el-option v-for="item in cityDataList" :key="item.Id" :label="item.Name" :value="item.Id" />
              </el-select>
              <el-select v-model="areaForm.CountyAreaId" style="width: 101px" @change="CountyChange($event)">
                <el-option v-for="item in countyDataList" :key="item.Id" :label="item.Name" :value="item.Id" />
              </el-select>
            </el-form-item>
            <el-form-item prop="AreaName：">
              <template #label>
                <el-tooltip class="item" effect="dark" content="请在左侧选择省市区进行修改" placement="top">
                  <div>
                    <el-icon color="#E6A23C">
                      <QuestionFilled />
                    </el-icon>
                  </div>
                </el-tooltip>
                <span>
                  区域
                </span>
              </template>
              <el-input v-model="formData.AreaName" auto-complete="off" readonly></el-input>
            </el-form-item>
            <el-form-item label="地址：">
              <el-input v-model="formData.Address" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="邮编：" prop="ZipCode" class="dialogFlexRulesBox">
              <el-input v-model="formData.ZipCode" auto-complete="off"
                @input="integerLimitInput($event, 'ZipCode')"></el-input>
            </el-form-item>
            <el-form-item label="联系人：">
              <el-input v-model="formData.ContactUser" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="联系电话：" prop="Tel" class="dialogFlexRulesBox">
              <el-input v-model="formData.Tel" auto-complete="off" @input="integerLimitInput($event, 'Tel')"></el-input>
            </el-form-item>
            <el-form-item label="短信手机号：" prop="Mobile" class="dialogFlexRulesBox">
              <el-input v-model="formData.Mobile" auto-complete="off"
                @input="integerLimitInput($event, 'Mobile')"></el-input>
            </el-form-item>
            <el-form-item label="邮箱：">
              <el-input v-model="formData.Email" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="员工数：">
              <el-input v-model="formData.EmployeeNum" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="经纬度：">
              <el-input v-model="formData.Position" auto-complete="off"
                placeholder="	如：118.803312,32.057527"></el-input>
            </el-form-item>
            <el-form-item label="主体性质：" v-if="formData.UnitType == 3">
              <el-select v-model="formData.SubjectNature">
                <el-option class="flexItem" v-for="item in subjectNatureStageList" :key="item.DicValue"
                  :label="item.DicName" :value="item.DicValue">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="网址：">
              <el-input v-model="formData.Url" auto-complete="off"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="企业证件：">
            <span>组织机构证</span> <span>营业执照证</span>
          </el-form-item>
          <el-form-item label="是否服务商：" v-if="formData.UnitType == 4">
            <el-radio-group v-model="formData.IsServiceProvider">
              <el-radio class="radio" :value="1">是</el-radio>
              <el-radio class="radio" :value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否供应商：" v-if="formData.UnitType == 4">
            <el-radio-group v-model="formData.IsSupplier">
              <el-radio class="radio" :value="1">是</el-radio>
              <el-radio class="radio" :value="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </fieldset>
        <fieldset v-if="formData.UnitType == 3">
          <legend>单位信息</legend>
          <el-form-item label="单位性质：">
            <el-radio-group v-model="formData.SchoolNature">
              <el-radio class="radio" :value="1">公办</el-radio>
              <el-radio class="radio" :value="2">民办</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="dialogFlexBox">
            <el-form-item label="单位属性：">
              <el-select v-model="formData.SchoolStage">
                <el-option class="flexItem" v-for="item in schoolStageList" :key="item.DicValue" :label="item.DicName"
                  :value="item.DicValue">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="班级总数(班)：">
              <el-input v-model="formData.ClassNum" auto-complete="off"
                @input="integerLimitInput($event, 'ClassNum')"></el-input>
            </el-form-item>
            <el-form-item label="教职工数人)：">
              <el-input v-model="formData.TeacherNum" auto-complete="off"
                @input="integerLimitInput($event, 'TeacherNum')"></el-input>
            </el-form-item>
            <el-form-item label="学生总数人)：">
              <el-input v-model="formData.StudentNum" auto-complete="off"
                @input="integerLimitInput($event, 'StudentNum')"></el-input>
            </el-form-item>
            <el-form-item label="占地面积(㎡)：">
              <el-input v-model="formData.FloorArea" auto-complete="off"
                @input="limitInput($event, 'FloorArea')"></el-input>
            </el-form-item>
            <el-form-item label="建筑面积(㎡)：">
              <el-input v-model="formData.BuildArea" auto-complete="off"
                @input="limitInput($event, 'BuildArea')"></el-input>
            </el-form-item>
          </div>
        </fieldset>
        <fieldset>
          <legend>简介信息</legend>
          <el-form-item label="单位简介：">
            <el-input type="textarea" v-model="formData.Introduction"></el-input>
          </el-form-item>
        </fieldset>
      </el-form>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
      </span>
    </template>
  </app-box>
  <el-dialog v-model="dialogResetVisible" title="修改排序" draggable width="500px">
    <el-form @submit.prevent ref="resetForm" :model="resetFormData" :rules="resetFormRule" label-width="80px"
      status-icon>
      <el-form-item label="排序值" class="flexItem" prop="Sort">
        <el-input v-model="resetFormData.Sort" auto-complete="off"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogResetVisible = false">取消</el-button>
        <el-button type="primary" @click="SubmitReset(resetForm)">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 导入单位 -->
  <el-dialog v-model="uploadVisible" title="企业单位导入" width="480px">
    <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" label-width="80px" status-icon>
      <el-form-item>
        <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" accept=".xlsx,.xls"
          style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload" :http-request="httpRequest">
          <el-button type="primary" :icon="UploadFilled">企业单位导入 </el-button>
        </el-upload>
        <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}

// .addCustom {

.addCustom.el-form {
  width: 90%;
  margin: 0 auto;

  .el-form-item {
    margin-bottom: 10px;
  }

  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;

    label {
      width: 20%;
    }
  }

  .el-checkbox {
    height: var(--el-checkbox-height, 22px);
  }

}


.dialogFlexBox {
  display: flex;
  flex-wrap: wrap;

  .el-form-item {
    width: 50%;
    margin-bottom: 10px;
  }
}

.el-form-item {
  margin-bottom: 10px;
}

.el-form-item.dialogFlexRulesBox {
  margin-bottom: 18px;
}

fieldset {
  // color: #333;
  border: #ccc dashed 1px;
  padding: 10px;
  margin: 10px 0;

  legend {
    font-size: 16px;
  }
}
</style>