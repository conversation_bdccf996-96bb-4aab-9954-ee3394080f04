<script setup>
defineOptions({
    name: 'swapordermanagelist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    PurchaseGetmanagerorder, PurchaseConfirm
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const SchoolList = ref([])
const StatuzList = ref([])

//加载数据
onMounted(() => {
    yearDateList.value = previousYearDate()
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})

onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})
// 修改
const HandleDetail = (row) => {
    router.push({ path: "./managedetail", query: { id: row.Id } })
}
//调换确认
const HandleSubmit = (row) => {
    ElMessageBox.confirm('确定提交调换确认吗?')
        .then(() => {
            PurchaseConfirm({ uniformPurchaseId: row.Id }).then((res) => {
                HandleTableData()
                ElMessage.success(res.data.msg || '调换确认成功')
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    PurchaseGetmanagerorder(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            if (isFirst) {
                StatuzList.value = other.listStatuz || [];//调换状态
                SchoolList.value = other.listSchool || [];//学校
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.PurchaseYear = undefined
    filters.value.SchoolId = undefined
    filters.value.SwapStatuz = undefined
    filters.value.Key = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yearDateList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SwapStatuz" clearable placeholder="调换状态" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in StatuzList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="合同批次"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="PurchaseYear" label="年度" min-width="100" align="center"></el-table-column>
            <el-table-column prop="SchoolName" label="学校名称" min-width="200"></el-table-column>
            <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="OrderedNum" label="订购总人数" min-width="120" align="center"></el-table-column>
            <el-table-column prop="SwapStudentNum" label="总调换人数" min-width="120" align="center"></el-table-column>
            <el-table-column prop="SwapDeadline" label="调换截止时间" min-width="140" align="center">
                <template #default="{ row }">
                    <span v-if="!row.SwapDeadline">--</span>
                    <span v-else-if="new Date(row.SwapDeadline).setHours(23, 59, 59, 0) < new Date()"
                        style="color: #F56C6C;">
                        {{ row.SwapDeadline.substring(0, 10) }}</span>
                    <span v-else>{{ row.SwapDeadline.substring(0, 10) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="SwapStatuz" label="调换状态" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.SwapStatuz == 1 ? '正在填报' : '调换结束' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="调换明细" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.SwapStudentNum > 0" type="primary" link
                        @click="HandleDetail(row)">查看</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="调换确认" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="new Date(row.SwapDeadline).setHours(23, 59, 59, 0) > new Date()" type="primary"
                        link @click="HandleSubmit(row)">确认</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>