<script setup>
defineOptions({
    name: 'dangerchemicalscountypurchaseauditlist'
});
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DcPurchaseDetailListFind, Punitgetschoolbycountyid
} from '@/api/dangerchemicals.js'
import { ExcelDownload } from "@/utils/index.js"
import {
    DcPurchaseDetailListExport
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { pageQuery } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
import { purchaseStatuzArray, getPurchaseStatuz } from "@/utils/index.js"
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: route.query.p, sortModel: [{ SortCode: "Sort", SortType: "ASC" }, { SortCode: "SchoolId", SortType: "ASC" }, { SortCode: "SecondId", SortType: "ASC" }, { SortCode: "AuditDate", SortType: "DESC" }] })
const EndDate = ref()
const summation = ref({})
const SchoolData = ref([])
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        LoadSchool()
        HandleTableData()
    }

})
onActivated(() => {

    nextTick(() => {
        if (!route.query.isTagRouter) {
            LoadSchool()
            HandleTableData()
        }
    })
})

//加载学校信息
const LoadSchool = () => {
    Punitgetschoolbycountyid({ CountyId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            SchoolData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcPurchaseDetailListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (rows.Statistics) {
                summation.value = res.data.data.rows.Statistics[0]
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.AuditDatege = undefined
    filters.value.AuditDatele = undefined
    EndDate.value = undefined
    filters.value.SchoolId = undefined
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.AuditDatege = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.AuditDatele = val + " 23:59:59"
    } else {
        filters.value.AuditDatele = undefined
    }
    HandleTableData()
}

const filtersChange = () => { HandleTableData() }
//导出
const HandleExport = () => {
    DcPurchaseDetailListExport(filters.value).then(res => {
        ExcelDownload(res)
    });
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.AuditDatege) return false;
    return time < new Date(filters.value.AuditDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem" label="申请时间：">
                        <el-date-picker v-model="filters.AuditDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable placeholder="学校名称" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in SchoolData" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="AuditYear" label="年度" min-width="80" align="center">
                <template #default="{ row }">
                    <span v-if="row.AuditDate">{{ row.AuditDate.substring(0, 4) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="SchoolName" label="学校名称" min-width="160"></el-table-column>
            <el-table-column prop="SecondName" label="危化品分类" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="Num" label="采购数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UserName" label="申请人" min-width="100" align="center"></el-table-column>
            <el-table-column prop="AuditDate" label="申请时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.AuditDate ? row.AuditDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>