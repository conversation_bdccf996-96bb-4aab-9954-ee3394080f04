<script setup>
defineOptions({
    name: 'dangerchemicalsdailyreportlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy
} from '@element-plus/icons-vue'
import { DcGovernDeclareSummaryFind } from '@/api/daily.js'
import { PUnitGetCountyByCityId, Punitgetschoolbycountyid } from '@/api/dangerchemicals.js'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { ElMessageBox, ElMessage } from 'element-plus'
import { pageQuery, urlQuery } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const unitType = ref(userStore.userInfo.UnitType)
const routerObject = ref({})//成页面携带的参数对象 
const routerUrl = ref('')//生成页面携带的参数

const declareStatuz = [
    { Id: 1, Name: '已申报' },
    { Id: 0, Name: '未申报' },
];
const schoolArr = ref([])
const countyArr = ref([])
const constweekArr = ref([]);
const constMontnhArr = ref([]);
const loadWeekData = (row) => {
    if (routerObject.value.t == 1) {
        let date = new Date();
        let year = date.getFullYear();
        let weekMax = 53;
        if (year % 4 == 0) {
            weekMax = 54;
        }
        for (let index = 1; index < weekMax; index++) {
            constweekArr.value.push({ Id: index, Name: '第' + index + '周' });
        }
    } else if (routerObject.value.t == 2) {
        let months = "一,二,三,四,五,六,七,八,九,十,十一,十二";
        let monthArr = months.split(',');
        for (let i = 0; i < monthArr.length; i++) {
            constMontnhArr.value.push({ Id: (i + 1), Name: (monthArr[i] + '月份') });
        }
    }
}
const declareYearArr = ref([]);
const loadYearData = (row) => {
    const date = new Date();
    const year = date.getFullYear();
    declareYearArr.value.push({ Id: (year + 1), Name: (year + 1) });

    for (let index = year; index > (year - 9); index--) {
        declareYearArr.value.push({ Id: index, Name: index });
    }
}

//加载学校信息
const LoadSchool = (countyid) => {
    Punitgetschoolbycountyid({ CountyId: countyid }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            schoolArr.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 获取下属单位集合。
const loadCountyData = () => {
    PUnitGetCountyByCityId({ CityId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            countyArr.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersCountyChange = (row) => {
    filters.value.pageIndex = 1;
    LoadSchool(filters.value.CountyId);
    HandleTableData()
}

//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)
    routerUrl.value = urlQuery(route.path); // 获取生成页面携带的参数
    if (!routerObject.value.c || routerObject.value.c != 1) {
        routerObject.value.c = 0;
    }
    loadWeekData();
    loadYearData();
    filters.value.GovernYear = new Date().getFullYear();

    if (route.query.isTagRouter) {
    }

    if (routerObject.value.c == 1) {
        //市级的时候加载区县
        loadCountyData();
    } else {

        LoadSchool(0);
    }
    HandleTableData(true);
})
onActivated(() => {

    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

//表格参数
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "CountyId", SortType: "ASC" }, { SortCode: "Sort", SortType: "ASC" }, { SortCode: "SchoolId", SortType: "ASC" }] })

//搜索条件
const declareUserList = ref([])
const filtersChange = (row) => {
    filters.value.pageIndex = 1;
    if (!filters.value.GovernYear) {
        filters.value.GovernYear = new Date().getFullYear();
    }
    if (!filters.value.NumberCyclez) {
        filters.value.NumberCyclez = defaultNumberCyclez.value;
    }
    HandleTableData()
}

const defaultNumberCyclez = ref();
const declareSelectId = ref();
// 列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    filters.value.Idgt = undefined;
    filters.value.Id = undefined;
    if (declareSelectId.value == 1) {
        filters.value.Idgt = 0;
    } else if (declareSelectId.value == 0) {
        filters.value.Id = 0;
    }
    filters.value.ReportType = routerObject.value.t;
    DcGovernDeclareSummaryFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = Number(rows.dataCount);
            let other = rows.Other;
            if (isFirst && other) {
                if (routerObject.value.t == 1) {
                    defaultNumberCyclez.value = other.WeekNo;
                } else {
                    defaultNumberCyclez.value = other.MonthNo;
                }
                filters.value.NumberCyclez = defaultNumberCyclez.value;
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.SchoolId = undefined;
    filters.value.GovernYear = new Date().getFullYear();
    filters.value.NumberCyclez = defaultNumberCyclez.value;
    if (unitType.value < 2) {
        filters.value.CountyId = undefined;
        schoolArr.value = [];//清空对应的学校
    }
    filters.value.Idgt = undefined;
    filters.value.Id = undefined;
    declareSelectId.value = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

//查看上报详情
const HandleDetail = (row, e) => {
    router.push({ path: "./governdetail", query: { SchoolId: row.SchoolId, Id: row.Id, c: routerObject.value.c, t: routerObject.value.t } })
}

// 打印
const HandlePrint = (e) => {
    router.push({
        path: "./problemdangerstatisticsprint",
        query: {
            CountyId: filters.value.LvCompanyId,
            PlanYear: filters.value.GovernYear,
            NumberValue: filters.value.NumberCyclez,
            TypeCode: routerObject.value.t,
            SchoolId: filters.value.SchoolId,
            Statuz: declareSelectId.value,
            path: './reportlist' + routerUrl.value,
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="HandlePrint">打印入库单</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.GovernYear" clearable placeholder="年度" style="width: 120px"
                            @change="filtersChange">
                            <el-option v-for="item in declareYearArr" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">

                        <el-select v-if="routerObject.t == 1" v-model="filters.NumberCyclez" clearable placeholder="周次"
                            style="width: 100px" @change="filtersChange">
                            <el-option v-for="item in constweekArr" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                        <el-select v-else-if="routerObject.t == 2" v-model="filters.NumberCyclez" clearable
                            placeholder="月份" style="width: 100px" @change="filtersChange">
                            <el-option v-for="item in constMontnhArr" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-if="unitType == 1" v-model="filters.CountyId" clearable filterable
                            placeholder="区县名称" @change="filtersCountyChange" style="width: 160px">
                            <el-option v-for="item in countyArr" :key="item.UnitId" :label="item.CountyName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable placeholder="学校名称" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in schoolArr" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="declareSelectId" clearable placeholder="申报结果" style="width: 100px"
                            @change="filtersChange">
                            <el-option v-for="item in declareStatuz" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <!-- <el-table-column prop="GovernYear" label="年度" width="100" align="center"></el-table-column>
            <el-table-column v-if="unitType < 2" prop="AreaName" label="区县名称" width="120"
                align="center"></el-table-column> -->
            <el-table-column prop="SchoolName" label="学校名称" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column prop="RegDate" label="申报时间" width="140" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UserName" label="申报人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Statuz" label="申报结果" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.Id > 0">已申报</span>
                    <span v-else>
                        <span v-if="routerObject.t == 1"> {{ row.CurrentWeekNum <= row.NumberCyclez ? '--' : '未申报' }}
                                </span>
                                <span v-if="routerObject.t == 2">
                                    {{ row.CurrentMonthNum < row.NumberCyclez ? '--' : '未申报' }} </span>
                                </span>
                </template>
            </el-table-column>
            <el-table-column prop="ProblemNum" label="发现问题（个）" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.Id > 0 ? row.ProblemNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RectifyProblemNum" label="已整改问题（个）" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.Id > 0 ? row.RectifyProblemNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DangerNum" label="发现隐患（个）" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.Id > 0 ? row.DangerNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RectifyDangerNum" label="已整改隐患（个）" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.Id > 0 ? row.RectifyDangerNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id > 0" type="primary" link @click="HandleDetail(row)">查看</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>