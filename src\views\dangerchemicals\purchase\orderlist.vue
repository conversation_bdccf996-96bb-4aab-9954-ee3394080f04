<script setup>
defineOptions({
    name: 'dangerchemicalsorderlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    DcPurchaseOrderFind, DcPurchaseRevoke, DcPurchaseOrderDelete
} from '@/api/dangerchemicals.js'
import { ExcelDownload } from "@/utils/index.js"
import {
    DcPurchaseEndExport
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { fileDownload, tagsListStore } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
import { purchaseStatuzArray, getPurchaseStatuz } from "@/utils/index.js"
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const EndDate = ref()
const summation = ref({})

const options = ref([
    { value: 'BatchNo', label: '采购批次', },
    { value: 'UserName', label: '申请人', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcPurchaseOrderFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (rows.Statistics) {
                summation.value = res.data.data.rows.Statistics[0]
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.RegDatege = undefined
    filters.value.RegDatele = undefined
    filters.value.Statuz = undefined
    EndDate.value = undefined
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}

//查看危化品明细
const HandleDetailView = (row) => {
    router.push({
        path: "/dangerchemicals/purchase/detaillist", query: {
            Id: row.Id,
            batchNo: row.BatchNo
        }
    })
}

//导出
const HandleExport = (id) => {
    console.log("id:", id);
    filters.value.PurchaseOrderId = id;
    DcPurchaseEndExport(filters.value).then(res => {
        ExcelDownload(res)
    });
}
const filtersChange = () => { HandleTableData() }

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = '总计：';
            return;
        }
        else if (index == 2) {
            sums[index] = summation.value.Amount ? '￥' + Number(summation.value.Amount).toFixed(2) : '--'
            return;
        }
    });
    return sums;
}

//撤回
const btnRevoke = (id) => {
    DcPurchaseRevoke({ id: id }).then(res => {
        if (res.data.flag == 1) {
            HandleTableData()
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '撤回成功')

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

//查看
const HandleDetail = (row) => {
    router.push({
        path: "/dangerchemicals/purchase/detailview", query: {
            Id: row.Id
        }
    })
}

// 修改
const HandleEdit = (row) => {
    router.push({
        path: "./list", query: {
            id: row.Id,
            batchNo: row.BatchNo,
            path: '/dangerchemicals/purchase/orderlist'
        }
    })
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('您确认要删除该数据吗？')
        .then(() => {
            DcPurchaseOrderDelete({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="状态" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in purchaseStatuzArray" :key="item.id" :label="item.Name"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="UserName" label="申请人" min-width="160"></el-table-column>
            <el-table-column prop="BatchNo" label="采购批次" min-width="160" align="center"></el-table-column>
            <el-table-column prop="Amount" label="参考金额" min-width="160" align="right">
                <template #default="{ row }">
                    {{ row.Amount ? '￥' + Number(row.Amount).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DeviceDetail" label="危化品明细" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetailView(row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <span :style="{ color: [-1, 11, 21].includes(row.Statuz) ? 'red' : '' }">
                        {{ getPurchaseStatuz(row.Statuz) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.Statuz % 10 > 0">
                        <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                        <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                    </span>
                    <span v-else-if="row.Statuz == 100">
                        <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                        <el-button type="primary" link @click="HandleExport(row.Id)">导出</el-button>
                    </span>
                    <span v-else>
                        <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                        <el-button v-if="row.ApprovalCount == 0" type="primary" link
                            @click="btnRevoke(row.Id)">撤回</el-button>
                    </span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>