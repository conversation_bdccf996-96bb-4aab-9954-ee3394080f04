import request from '@/utils/request.js'

// 首页流程审批数据展示
export const GetProcessIndexInfo = (params) => {
  return request.get('/api/hyun/process/getprocessindexinfo', { params: params })
}
// 模块管理 -查询模块列表
export const FindmoduleList = (params) => {
  return request.post('/api/hyun/workflow/findmodulelist', params)
}
// 模块管理 -新增/修改模块信息
export const ModuleinsertUpdate = (params) => {
  return request.post('/api/hyun/workflow/moduleinsertupdate', params)
}
// 模块管理 -根据Id获取模块信息
export const Modulefindbyid = (params) => {
  return request.get('/api/hyun/workflow/modulefindbyid', { params: params })
}
// 模块管理 -启用-禁用模块信息
export const ModulesetStatuz = (params) => {
  return request.get('/api/hyun/workflow/modulesetstatuz', { params: params })
}
// 节点管理 -查询节点列表
export const FindnodeList = (params) => {
  return request.post('/api/hyun/workflow/findnodelist', params)
}
// 节点管理 -新增/修改节点信息
export const NodeinsertUpdate = (params) => {
  return request.post('/api/hyun/workflow/nodeinsertupdate', params)
}
// 节点管理 -根据Id获取节点信息
export const Nodefindbyid = (params) => {
  return request.get('/api/hyun/workflow/nodefindbyid', { params: params })
}
// 节点管理 -根据模块Id和流程节点Id获取该模块下所有节点数据
export const GetNextProcessNode = (params) => {
  return request.get('/api/hyun/workflow/getnextprocessnode', { params: params })
}
// 节点管理 -删除节点
export const NodeDeletebyid = (params) => {
  return request.delete('/api/hyun/workflow/nodedeletebyid', { params: params })
}
// 表单设计 -保存节点Json数据
export const NodeConfigSave = (params) => {
  return request.post('/api/hyun/workflow/nodeconfigsave', params)
}
// 表单设计 -获取下拉框数据源
export const GetDataSource = (params) => {
  return request.get('/api/hyun/workflow/getdatasource', { params: params })
}
// 表单设计 -根据数据源选择的编码获取数据
export const GetdataSourceBydicValue = (params) => {
  return request.get('/api/hyun/workflow/getdatasourcebydicvalue', { params: params })
}
// 表单设计 -点击设置 根据Id获取节点Json数据
export const NodeJsonfindByid = (params) => {
  return request.get('/api/hyun/workflow/nodejsonfindbyid', { params: params })
}

// 表单设计 -根据节点Id及编码获取编码配置信息
export const GetGenerateCodeSet = (params) => {
  return request.get('/api/hyun/workflow/getgeneratecodeset', { params: params })
}
// 表单设计 -保存编码配置信息
export const PostSaveGenerateCodeSet = (params) => {
  return request.post('/api/hyun/workflow/postsavegeneratecodeset', params)
}
// 节点列表-获取选择列列表
export const FindChooseColumnPageList = (params) => {
  return request.post('/api/hyun/workflow/findchoosecolumnpagelist', params)
}
// 节点列表-选择列确认
export const ChooseColumnSave = (params) => {
  return request.post('/api/hyun/workflow/choosecolumnsave', params)
}
// 节点列表-获取显示列列表
export const FindPageColumnconfigList = (params) => {
  return request.post('/api/hyun/workflow/findpagecolumnconfiglist', params)
}
// 节点列表-列启用禁用
export const ListColumnSet = (params) => {
  return request.get('/api/hyun/workflow/listcolumnset', { params: params })
}
// 节点列表-根据Id获取列信息
export const ListColumnGetByid = (params) => {
  return request.get('/api/hyun/workflow/listcolumngetbyid', { params: params })
}
// 节点列表-新增/修改 按钮数据信息
export const ListColumnInsertUpdate = (params) => {
  return request.post('/api/hyun/workflow/listcolumninsertupdate', params)
}
// 节点列表-批量保存列表内容
export const ListColumnBatchUpdate = (params) => {
  return request.post('/api/hyun/workflow/listcolumnbatchinsertupdate', params)
}

// 节点列表-获取联动数据配置列表
export const PostLinkAgeHostList = (params) => {
  return request.post('/api/hyun/workflow/postlinkagehostlist', params)
}

// 联动数据配置-根据审批联动主表Id获取联动父子联动数据及关联关系
export const GetLinkageHostDetailList = (params) => {
  return request.get('/api/hyun/workflow/getlinkagehostdetaillist', { params: params })
}

//联动数据配置- 根据联动主表Id获取配置列表信息（配置页面下方的列表信息）
export const GetDetailSetList = (params) => {
  return request.get('/api/hyun/workflow/getdetailsetlist', { params: params })
}

// 联动数据配置-根据联动主表Id父级Id获取选中子集数据信息
export const GetDetaibyParentId = (params) => {
  return request.get('/api/hyun/workflow/getdetaibyparentid', { params: params })
}

// 联动数据配置-保存联动关系数据信息
export const PostSaveLinkageDetail = (params) => {
  return request.post('/api/hyun/workflow/postsavelinkagedetail', params)
}

// 条件控制：获取主控制列表信息
export const PostControlList = (params) => {
  return request.post('/api/hyun/workflow/postcontrollist', params)
}
// 条件控制：根据节点Id主控分组值获取设置控制列表信息
export const PostControlSetList = (params) => {
  return request.post('/api/hyun/workflow/postcontrolsetlist', params)
}

// 条件控制：根据左侧点击获取右侧字段选中信息
export const GetSingLecontRolsetList = (params) => {
  return request.post('/api/hyun/workflow/getsinglecontrolsetlist', params)
}
// 条件控制：保存字段配置信息
export const PostSaveControlData = (params) => {
  return request.post('/api/hyun/workflow/postsavecontroldata', params)
}
// 条件控制：根据金额控制主表Id删除数据信息
export const DelAmountControlByid = (params) => {
  return request.delete('/api/hyun/workflow/delamountcontrolbyid', { params: params })
}

// 流程管理 -获取流程列表
export const FindprocessList = (params) => {
  return request.post('/api/hyun/workflow/findprocesslist', params)
}
// 流程管理 -新增/修改流程信息
export const ProcessinsertUpdate = (params) => {
  return request.post('/api/hyun/workflow/processinsertupdate', params)
}
// 流程管理 -根据Id获取流程信息
export const Processfindbyid = (params) => {
  return request.get('/api/hyun/workflow/processfindbyid', { params: params })
}
// 流程管理：分组查询列表
export const GetGroupSetList = (params) => {
  return request.post('/api/hyun/workflow/getgroupsetlist', params)
}

// 流程管理：添加修改分组信息
export const GroupSetInsertUpdate = (params) => {
  return request.post('/api/hyun/workflow/groupsetinsertupdate', params)
}
// 流程管理 -删除流程
export const ProcessDeletebyid = (params) => {
  return request.delete('/api/hyun/workflow/processdeletebyid', { params: params })
}
// 流程管理 -启用-禁用流程信息
export const ProcesssetStatuz = (params) => {
  return request.get('/api/hyun/workflow/processsetstatuz', { params: params })
}
// 流程配置-根据Id获取流程配置信息
export const ProcessSetByid = (params) => {
  return request.get('/api/hyun/workflow/processsetbyid', { params: params })
}
// 流程配置-提交流程信息
export const ProcessSetSave = (params) => {
  return request.post('/api/hyun/workflow/processsetsave', params)
}
// 流程配置-节点退回配置-获取节点退回信息
export const GetProcessReturnSet = (params) => {
  return request.get('/api/hyun/workflow/getprocessreturnset', { params: params })
}
// 流程配置-节点退回配置-节点退回信息提交
export const SetProcessReturn = (params) => {
  return request.post('/api/hyun/workflow/setprocessreturn', params)
}
// 审批消息配置：消息配置列表
export const PostProcessMsgConfigList = (params) => {
  return request.post('/api/hyun/workflow/postprocessmsgconfiglist', params)
}
// 审批消息配置：添加配置信息
export const PostProcessMsgConfigAdd = (params) => {
  return request.post('/api/hyun/workflow/postprocessmsgconfigadd', params)
}
// 审批消息配置：修改配置信息
export const PostProcessMsgConfigEdit = (params) => {
  return request.post('/api/hyun/workflow/postprocessmsgconfigedit', params)
}
// 审批消息配置：根据Id获取信息
export const GetProcessMsgConfigById = (params) => {
  return request.get('/api/hyun/workflow/getprocessmsgconfigbyid', { params: params })
}
// 批量开启关闭消息
export const PostProcessMsgConfigSetSatuz = (params) => {
  return request.post('/api/hyun/workflow/postprocessmsgconfigsetsatuz', params)
}

// 资金库管理-资金库字段管理选择列表
export const FindChooseFundpageList = (params) => {
  return request.post('/api/hyun/workflow/findchoosefundpagelist', params)
}
// 资金库管理-资金库字段管理字段选择确认
export const ChooseFundSave = (params) => {
  return request.post('/api/hyun/workflow/choosefundsave', params)
}
// 资金库管理-资金库字段绑定
export const SetSourceFundField = (params) => {
  return request.get('/api/hyun/workflow/setsourcefundfield', { params: params })
}
// 资金库管理-修改流程资金来源信息
export const SourceFundInputDataSet = (params) => {
  return request.post('/api/hyun/workflow/sourcefundinputdataset', params)
}
// 预算清单配置-查询审批预算清单配置列表
export const ProjectListFieldSetList = (params) => {
  return request.post('/api/hyun/workflow/projectlistfieldsetlist', params)
}
// 预算清单配置-根据Id查询审批预算清单配置数据
export const ProjectListFieldSetByid = (params) => {
  return request.get('/api/hyun/workflow/projectlistfieldsetbyid', { params: params })
}
// 预算清单配置-保存审批预算清单配置信息
export const ProjectListFieldSetSave = (params) => {
  return request.post('/api/hyun/workflow/projectlistfieldsetsave', params)
}
// 预算清单配置-根据模块Id获取项目清单Code集合
export const GetFieldCodeListBymoduleId = (params) => {
  return request.get('/api/hyun/workflow/getfieldcodelistbymoduleid', { params: params })
}
// 预算清单配置-根据查询统计页面表Id获取审批预算清单选择的列表信息
export const FindProjectColumnPageList = (params) => {
  return request.post('/api/hyun/workflow/findprojectcolumnpagelist', params)
}
// 预算清单配置-项目清单查询统计选择清单列功能
export const ChooseProjectListcolumnSave = (params) => {
  return request.post('/api/hyun/workflow/chooseprojectlistcolumnsave', params)
}
// 状态描述-获取状态描述列表
export const FindProcessStatuzList = (params) => {
  return request.post('/api/hyun/workflow/findprocessstatuzlist', params)
}
// 状态描述-修改状态值/描述
export const ProcessStatuzEdit = (params) => {
  return request.post('/api/hyun/workflow/processstatuzedit', params)
}
// // 字典管理-获取字典列表
// export const FinddictionaryList = (params) => {
//   return request.post('/api/hyun/workflow/finddictionarylist', params)
// }
// 字典管理-获取字典列表
export const FindDictionaryList = (params) => {
  return request.post('/api/hyun/workflow/finddictionarylist', params)
}
// 字典管理-新增/修改字典值
export const DictionaryinsertUpdate = (params) => {
  return request.post('/api/hyun/workflow/dictionaryinsertupdate', params)
}
// 字典管理-根据Id获取字典值信息
export const Dictionaryfindbyid = (params) => {
  return request.get('/api/hyun/workflow/dictionaryfindbyid', { params: params })
}
// 字典管理-设置字典值状态
export const DictionarysetStatuz = (params) => {
  return request.get('/api/hyun/workflow/dictionarysetstatuz', { params: params })
}
// 查询统计配置-获取查询统计配置列表
export const FindpageDefinitionList = (params) => {
  return request.post('/api/hyun/workflow/findpagedefinitionlist', params)
}
// 查询统计配置-保存查询统计页面
export const PageDefinitionSave = (params) => {
  return request.post('/api/hyun/workflow/pagedefinitionsave', params)
}
// 查询统计配置-根据Id查询统计页面表配置信息
export const GetPageDefinitionByid = (params) => {
  return request.get('/api/hyun/workflow/getpagedefinitionbyid', { params: params })
}
// 查询统计配置-根据Id删除查询统计配置
export const DelPageDefinitionByid = (params) => {
  return request.delete('/api/hyun/workflow/delpagedefinitionbyid', { params: params })
}
// 查询统计配置-根据选择的数据源获取
export const GetDatabydicValue = (params) => {
  return request.get('/api/hyun/workflow/getdatabydicvalue', { params: params })
}
// 查询统计配置-禁用启用及提交处理
export const PageDefinitionStatuzSet = (params) => {
  return request.get('/api/hyun/workflow/pagedefinitionstatuzset', { params: params })
}
//业务权限- 权限设置列表
export const GetProcessAuditUserList = (params) => {
  return request.get('/api/hyun/process/getprocessaudituserlist', { params: params })
}
//业务权限- 获取设置的审核审批人数据信息
export const GetSetUserList = (params) => {
  return request.get('/api/hyun/process/getsetuserlist', { params: params })
}
//业务权限- 设置节点审核审批人
export const SetNodeAuditUser = (params) => {
  return request.post('/api/hyun/process/setnodeaudituser', params)
}
// 单位配置-单位分组设置列表
export const GetUnitGroupSetList = (params) => {
  return request.post('/api/hyun/workflow/getunitgroupsetlist', params)
}
// 单位配置-根据分组Id获取分组中每项的信息（获取下拉框的值）
export const GetGroupItemList = (params) => {
  return request.get('/api/hyun/workflow/getgroupitemlist', { params: params })
}
// 单位配置-批量保存单位分组信息
export const UnitGroupBatchSet = (params) => {
  return request.post('/api/hyun/workflow/unitgroupbatchset', params)
}
// 单位配置-检测是否填写完成
export const GroupItemCheck = (params) => {
  return request.get('/api/hyun/workflow/groupitemcheck', { params: params })
}

//分级授权-分级授权列表
export const GetGroupItemUserList = (params) => {
  return request.post('/api/hyun/process/getgroupitemuserlist', params)
}
//分级授权-获取待授权用户列表信息
export const GetWaitAuditUserList = (params) => {
  return request.get('/api/hyun/process/getwaitaudituserlist', { params: params })
}
//分级授权-设置分组项对应人员信息
export const SetGroupUnitAuditUser = (params) => {
  return request.post('/api/hyun/process/setgroupunitaudituser', params)
}
//单位权限控制-获取单位控制数据信息
export const GetUnitPermissionInfo = (params) => {
  return request.post('/api/hyun/workflow/getunitpermissioninfo', params)
}
//单位权限控制-配置单位控制数据
export const SetUnitPermission = (params) => {
  return request.post('/api/hyun/workflow/setunitpermission', params)
}
//用户使用页面- 获取填报页面信息
export const GetFillingInfo = (params) => {
  return request.post('/api/hyun/process/getfillinginfo', params)
}
// 用户使用页面-填报页面保存
export const SaveFillingInfo = (params) => {
  return request.post('/api/hyun/process/savefillinginfo', params)
}
// 用户使用页面-填报页面转交下一步
export const SubmitFillingInfo = (params) => {
  return request.post('/api/hyun/process/submitfillinginfo', params)
}
// 用户使用页面-根据编码获取被控制字段显示信息
export const GetControlDetailDataSource = (params) => {
  return request.post('/api/hyun/process/getcontroldetaildatasource', params)
}

// 用户填报页面-根据父级编码获取子级数据信息
export const GetLinkAgeDataSource = (params) => {
  return request.get('/api/hyun/process/getlinkagedatasource', { params: params })
}
// 用户填报页面-根据一级二级分类Id获取项目名称及编码
export const GetProjectDeclarationCodeName = (params) => {
  return request.get('/api/hyun/process/getprojectdeclarationcodename', { params: params })
}
// 用户填报页面-根据流程节点Id获取项目编号
export const GetProjectCode = (params) => {
  return request.get('/api/hyun/process/getprojectcode', { params: params })
}
// 项目清单填报-查询审批预算清单表数据
export const ProjectListSearch = (params) => {
  return request.post('/api/hyun/process/projectlistsearch', params)
}
// 项目清单填报-保存预算清单信息
export const ProjectListSave = (params) => {
  return request.post('/api/hyun/process/projectlistsave', params)
}
// 项目清单填报-根据Id查询预算清单信息
export const ProjectListByid = (params) => {
  return request.get('/api/hyun/process/projectlistbyid', { params: params })
}
// 项目清单填报-根据Id删除预算清单信息
export const ProjectListDeleteByid = (params) => {
  return request.delete('/api/hyun/process/projectlistdeletebyid', { params: params })
}
// 项目清单填报-根据Id集合删除预算清单信息
export const ProjectListDeleteByids = (params) => {
  return request.delete('/api/hyun/process/projectlistdeletebyids', { params: params })
}
// 项目清单填报-项目清单导入
export const ProjectListImport = (params) => {
  return request.post('/api/hyun/process/projectlistimport', params)
}
// 项目清单审核-项目清单审核列表
export const FindSuditProjectList = (params) => {
  return request.post('/api/hyun/process/findauditprojectlist', params)
}
// 项目清单审核-项目清单保存、删除、审核处理
export const SaveProjectListReview = (params) => {
  return request.post('/api/hyun/process/saveprojectlistreview', params)
}
// 项目清单-获取项目清单历史审批记录数据
export const FindHistoryProjectList = (params) => {
  return request.post('/api/hyun/process/findhistoryprojectlist', params)
}
// 用户使用页面-根据填报Id获取清单总金额
export const GetListSumbyProjectDeclarationId = (params) => {
  return request.get('/api/hyun/process/getlistsumbyprojectdeclarationid', { params: params })
}
// 获取列表页面数据:待处理
export const GetFillSearchList = (params) => {
  return request.post('/api/hyun/process/getfillsearchlist', params)
}
// 获取列表页面数据:已处理
export const GetProcessedList = (params) => {
  return request.post('/api/hyun/process/getprocessedlist', params)
}
// 填报列表-删除
export const DelFillingInfo = (params) => {
  return request.delete('/api/hyun/process/delfillinginfo', { params: params })
}
// 查看详情
export const FillingInfoDetail = (params) => {
  return request.get('/api/hyun/process/fillinginfodetail', { params: params })
}
// 审核审批页面信息
export const FillingInfoAudit = (params) => {
  return request.get('/api/hyun/process/fillinginfoaudit', { params: params })
}
// 审核审批提交
export const SubmitAppRoval = (params) => {
  return request.post('/api/hyun/process/submitapproval', params)
}
// 审核审批暂存
export const SaveApproval = (params) => {
  return request.post('/api/hyun/process/saveapproval', params)
}
// 已审批撤销
export const Operaterevoke = (params) => {
  return request.get('/api/hyun/process/operaterevoke', { params: params })
}
// 查询统计页面-总列表
export const FindPagedeList = (params) => {
  return request.get('/api/hyun/process/findpagedelist', { params: params })
}
// 查询统计页面 -动态生成查询列表信息
export const GetSearchList = (params) => {
  return request.post('/api/hyun/process/getsearchlist', params)
}
// 查询统计页面 - 导出查询统计数据
export const DownloadSearchList = (params) => {
  return request.post('/api/hyun/process/downloadsearchlist', params)
}
// 资金库页面
export const FindSourceFundList = (params) => {
  return request.post('/api/hyun/process/findsourcefundlist', params)
}
