<script setup>
defineOptions({
    name: 'dangerchemicalsapplyfill'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    ShoppingCart, Search, Refresh, FolderAdd, View, QuestionFilled
} from '@element-plus/icons-vue'
import {
    BconfigSetgetPunit, DcapplyAdd, DcapplyBatchAdd, DcapplyDirectApply, DcapplyFillListFind, DcapplyGrantUserComboGet, DcschoolCatalogFindCommonuseAll
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { limit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 100, StockNumgt: 0, sortModel: [{ SortCode: "OneCatalogId", SortType: "ASC" }, { SortCode: "TwoCatalogId", SortType: "ASC" }, { SortCode: "SchoolCatalogId", SortType: "ASC" }] })
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const memberUserList = ref([])
const filtersShowAll = ref(true)
const bconfigSet = ref(0)
const ruleForm = {
    UseTime: [
        { required: true, message: '请选择使用时间', trigger: 'change' },
    ],
    WithUserId: [
        { required: true, message: '请选择同领用人', trigger: 'change' },
    ]
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcschoolCatalogFindCommonuseAllUser()
    BconfigSetgetPunitUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
const HandleRouter = (e) => {
    if (e == 1) {
        router.push({ path: './carlist' })
    } else {
        router.push({ path: '../school/materialmodelset' })
    }
}
// 批量加入领用车
const HandleAdd = (row) => {
    console.log(selectRows.value)
    let find = selectRows.value.find(item => !item.ApplyNum || !item.Remark)
    if (find) {
        ElMessage.error('请检查已选中的危化品，是否已全部填写《领用数量》和《用途》！')
        return
    }
    let list = selectRows.value.map(({
        SchoolCatalogId, SchoolMaterialModelId, SchoolMaterialBrandId, ApplyNum, Remark
    }) =>
    ({
        Id: 0, SchoolCatalogId, SchoolMaterialModelId, SchoolMaterialBrandId, Num: Number(ApplyNum), Remark
    }))
    DcapplyBatchAdd(list).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '加入成功')
            // HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 加入领用车
const HandleBatchAdd = (row) => {
    if (!row.ApplyNum) {
        ElMessage.error('请填写领用数量！')
        return
    }
    if (!row.Remark) {
        ElMessage.error('请填写用途！')
        return
    }
    let data = {
        Id: 0,
        SchoolCatalogId: row.SchoolCatalogId,
        SchoolMaterialModelId: row.SchoolMaterialModelId,
        SchoolMaterialBrandId: row.SchoolMaterialBrandId,
        Num: row.ApplyNum,
        Remark: row.Remark,
    }
    DcapplyAdd(data).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '加入成功')
            // HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const applyData = ref({})
// 立即申领
const HandleEdit = (row) => {
    if (!row.ApplyNum) {
        ElMessage.error('请填写领用数量！')
        return
    }
    if (!row.Remark) {
        ElMessage.error('请填写用途！')
        return
    }
    applyData.value = row
    dialogVisible.value = true
}

// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        let data = {
            Id: '0',
            SchoolCatalogId: applyData.value.SchoolCatalogId,
            SchoolMaterialModelId: applyData.value.SchoolMaterialModelId,
            SchoolMaterialBrandId: applyData.value.SchoolMaterialBrandId,
            Num: applyData.value.ApplyNum,
            Remark: applyData.value.Remark,
            UseTime: formData.value.UseTime,
            WithUserId: formData.value.WithUserId,
        }
        DcapplyDirectApply(data).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '申领成功')
                // HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}


const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.TwoCatalogId = undefined
    filters.value.Statuz = undefined
    filters.value.Name = undefined
    menuIndex.value = ''
    defaultActive.value = ''
    filtersShowAll.value = true
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    DcapplyFillListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            tableData.value.forEach(item => {
                item.ApplyNum = ''
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 获取学校物品类别选择
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let objId = rows.data.filter(item => item.Pid == '0')[0].Id
            let arr = rows.data.filter(item => item.Pid == objId)
            StatuzSolicitedList.value = arr
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 根据TypeCode获取上级单位配置信息
const BconfigSetgetPunitUser = () => {
    BconfigSetgetPunit({ moduleCode: 9, typeCode: 'WHPLYMS' }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bconfigSet.value = rows
            if (rows == 2 || rows == 3) {
                DcapplyGrantUserComboGetUser()
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取同领用人、发放人
const DcapplyGrantUserComboGetUser = () => {
    DcapplyGrantUserComboGet({ type: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            memberUserList.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
const lookImageUrl = ref('')
const lookRemark = ref('')
// 查看状态对应的人员信息
const HandleLookAuditUser = (row) => {
    detailDialogVisible.value = true
    lookImageUrl.value = row.ImageUrl
    lookRemark.value = row.Remark
}
//输入框限制：输入两位小数
const limitInput = (val, row, index, name) => {
    tableData.value[index][name] = limit(val);
}
const menuIndex = ref('')
const defaultActive = ref('')
const nameClick = (item, index) => {
    menuIndex.value = item.Id
    filters.value.TwoCatalogId = item.Id
    HandleTableData()
}
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    领用填报 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>
                        <div> 第一步，填写领用数量和用途（填写实验名称）； </div>
                        <div> 第二步，加入到领用车或立即申领； </div>
                        <div> 第三步，填写使用时间。 </div>
                    </li>
                    <li> 下表中只显示仓库中存有的危化品，如没有你所需的危化品，请联系危化品保管员。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <div style="display: flex;">
            <div class="section_left" style="width: 200px;flex-shrink: 0;">
                <el-menu :default-active="defaultActive" style="width: 180px;">
                    <el-menu-item v-for="(item, index) in StatuzSolicitedList" :key="item.Id" :index="item.Id"
                        @click="nameClick(item, index)" :class="{ active: menuIndex == item.Id }">
                        <div :class="{ activeName: menuIndex == item.Id }">{{ item.Name }}</div>
                    </el-menu-item>
                </el-menu>
            </div>
            <div class="viewContainer" style="width: 100%;">
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-button type="success" :icon="FolderAdd" :disabled="selectRows.length == 0"
                                    @click="HandleAdd">批量加入</el-button>
                            </el-form-item>
                            <div class="verticalIdel"></div>
                            <el-form-item label="" class="flexItem"
                                style="margin-left: 10px;margin-right: 10px !important;">
                                <el-checkbox v-model="filtersShowAll" label="全文搜索">
                                </el-checkbox>
                            </el-form-item>
                            <el-form-item label="" class="flexItem">
                                <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称/规格属性/品牌"
                                    style="width: 240px"></el-input>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                            </el-form-item>
                            <div class="verticalIdel"></div>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="ShoppingCart" @click="HandleRouter(1)">领用车</el-button>
                                <el-button type="primary" :icon="View" @click="HandleRouter(2)">查看危化品目录</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
                    @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip>
                        <template #default="{ row }">
                            <span v-if="row.Remark || row.ImageUrl" style="color:#0000ee;cursor: pointer;"
                                @click="HandleLookAuditUser(row)">
                                {{ row.Name }}
                            </span>
                            <span v-else>
                                {{ row.Name }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center"></el-table-column>
                    <el-table-column prop="StockNum" label="库存" min-width="100" align="right"></el-table-column>
                    <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"></el-table-column>
                    <el-table-column prop="ApplyNum" label="领用数量" min-width="120" align="center"
                        :class-name="'requiredColumn'">
                        <template #header>
                            <span style="color: #F56C6C;">*领用数量</span>
                        </template>
                        <template #default="{ row, $index }">
                            <el-input v-model="row.ApplyNum" :class="row.ApplyNum ? '' : 'requiredInput'"
                                @input="limitInput($event, row, $index, 'ApplyNum')"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="Remark" label="用途" min-width="160" align="center"
                        :class-name="'requiredColumn'">
                        <template #header>
                            <span style="color: #F56C6C;">*用途</span>
                        </template>
                        <template #default="{ row, $index }">
                            <el-input v-model="row.Remark" :class="row.Remark ? '' : 'requiredInput'"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="领用车" fixed="right" width="100" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="HandleBatchAdd(row)">加入</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="领用申请" fixed="right" width="100" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="HandleEdit(row)">立即申领</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>

                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </div>
        </div>
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="立即申领">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="120px"
                    status-icon>
                    <el-form-item label="使用时间：" prop="UseTime">
                        <el-date-picker v-model="formData.UseTime" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable style="width: 240px;">
                        </el-date-picker>
                        <span style="color: #999;padding-left: 5px;"> 是指做实验的时间</span>

                    </el-form-item>
                    <el-form-item label="同领用人：" v-if="bconfigSet == 2 || bconfigSet == 3" prop="WithUserId">
                        <el-select v-model="formData.WithUserId" filterable style="width: 240px">
                            <el-option v-for="item in memberUserList" :key="item.MemberUserId"
                                :label="item.MemberUserName" :value="item.MemberUserId" />
                        </el-select>
                        <span style="color: #999;padding-left: 5px;"> 是指同去仓库领用危化品的人</span>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
        <app-box v-model="detailDialogVisible" :width="560" :lazy="true" title="危化品详情描述">
            <template #content>
                <div style="padding:0 20px;">
                    <div style="padding: 5px;">
                        备注：{{ lookRemark }}
                    </div>
                    <div style="padding: 5px;">
                        危化品图片：<span style="cursor: pointer;"></span>{{ lookImageUrl }}
                    </div>
                </div>
            </template>
        </app-box>
    </div>
</template>
<style></style>
<style lang="scss" scoped>
.section_left {
    width: 200px;
    margin-top: 50px;
}

:deep(.el-menu-item) {
    border-bottom: 1px solid #f2f2f2;

    white-space: normal !important;
    height: auto !important;
    line-height: 1.5 !important;
    padding: 10px 20px !important;
    word-break: break-word !important;

}

.el-menu-item.is-active {
    color: #303133;
}

.active {
    border-left: 5px solid var(--el-color-primary);
}

.activeName {
    color: var(--el-color-primary);
}

:deep(.requiredColumn) {
    .cell {
        padding: 0 2px !important;
    }
}

.requiredInput {
    :deep(.el-input__wrapper) {
        box-shadow: none !important;
        border: 1px solid red !important;
    }
}
</style>