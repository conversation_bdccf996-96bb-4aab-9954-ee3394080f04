<script setup>
defineOptions({
    name: 'dangerchemicalscitywastestatisticslist'
});
import {
    Refresh, Search, Position
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DcCityWasteDisposalStatisticsFind, PUnitGetCountyByCityId, DcWasteClassGet
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const columnList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const summation = ref([])

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    PUnitGetCountyByCityIdUser()
    DcWasteClassGetUser()

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值  
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        for (let key in summation.value) {
            if (summation.value.hasOwnProperty(key)) {
                // console.log(`键名: ${key}, 键值: ${summation.value[key]}`);
                if (column.property == key) {
                    sums[index] = summation.value[key]
                    return;
                }
            }
        }
    });
    return sums;
}
// 列表
const HandleTableData = () => {
    DcCityWasteDisposalStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            summation.value = rows.Other[0] || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.CountyId = undefined
    HandleTableData()
}

const filtersChange = () => { HandleTableData() }
const cityList = ref([])

// 获取下属单位集合。
const PUnitGetCountyByCityIdUser = () => {
    PUnitGetCountyByCityId({ CityId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            cityList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取危化品一级分类|| 二级分类
const DcWasteClassGetUser = () => {
    DcWasteClassGet({ id: 0, depth: 1, pid: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            columnList.value = rows.data || []

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 区县名称查看  County/WasteStatisticsList
const HandleLook = (row) => {
    router.push({
        path: '/dangerchemicals/county/wastestatisticslist',
        query: {
            countyId: row.CountyId,
            countyName: row.AreaName,
            path: '/dangerchemicals/city/wastestatisticslist',
        }
    })
} 
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in cityList" ::key="item.UnitId" :label="item.CountyName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            :summary-method="getSummaries" show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="AreaName" label="区县名称" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id" type="primary" link @click="HandleLook(row)">
                        {{ row.AreaName }}</el-button>
                    <span v-else>{{ row.AreaName }}</span>
                </template>
            </el-table-column>
            <el-table-column v-for="item in columnList" :key="item.Id" :prop="item.Name"
                :label="item.Name + '(' + item.UnitsMeasurement + ')'" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row[item.Name] }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<style lang="scss" scoped></style>