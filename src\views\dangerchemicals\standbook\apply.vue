<script setup>
defineOptions({
    name: 'dangerchemicalsstandbookapply'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcApplyFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { ExcelDownload } from "@/utils/index.js"
import {
    ExportDcApplyFind
} from '@/api/directdata.js'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const GrantDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, IsGrant: 1, sortModel: [{ SortCode: "GrantDate", SortType: "DESC" }] })
const attribute = ref()
//加载数据
onMounted(() => {

    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    attribute.value = ''
    filters.value.GrantDatege = undefined
    filters.value.GrantDatele = undefined
    GrantDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const GrantDategeChange = (val) => {
    if (!val) filters.value.GrantDatege = undefined
    HandleTableData()
}
const GrantDateleChange = (val) => {
    if (val) {
        filters.value.GrantDatele = val + " 23:59:59"
    } else {
        filters.value.GrantDatele = undefined
    }
    HandleTableData()
}

const bindList = [
    { Id: 'IsDetonate', Name: '易制爆' },
    { Id: 'IsPoison', Name: '易制毒' },
    { Id: 'IsHyperToxic', Name: '剧毒' }]
// 列表
const HandleTableData = () => {
    filters.value.IsDetonate = undefined;
    filters.value.IsPoison = undefined;
    filters.value.IsHyperToxic = undefined;
    if (attribute.value) {
        filters.value[attribute.value] = 1
    } else {
        filters.value[attribute.value] = undefined;
    }
    DcApplyFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 打印
const HandlePrint = (e) => {
    router.push({
        path: "./applyprint",
        query: {
            attribute: attribute.value,
            GrantDatege: filters.value.GrantDatege,
            GrantDatele: filters.value.GrantDatele,
        }
    })
}
//导出
const HandleExport = () => {
    ExportDcApplyFind(filters.value).then(res => {
        ExcelDownload(res)
    });
}
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!GrantDatele.value) return false;
    return time >= new Date(GrantDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.GrantDatege) return false;
    return time < new Date(filters.value.GrantDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    操作提示 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 打印与导出请先选择出库时间范围（开始结束时间都需选择）；</li>
                    <li> 有数据才可进行打印或导出。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="HandlePrint"
                            :disabled="!filters.GrantDatege || !filters.GrantDatele || tableData.length == 0">打印</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport"
                            :disabled="!filters.GrantDatege || !filters.GrantDatele || tableData.length == 0">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.GrantDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="出库时间"
                            @change="GrantDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="GrantDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="出库时间" @change="GrantDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="attribute" clearable placeholder="危化品属性" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in bindList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" id="printArea" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Num" label="申领数量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="BackNum" label="退回数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="Num" label="实用数量" min-width="110" align="right">
                <template #default="{ row }">
                    {{ row.Num - row.BackNum }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="药品用途" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="GrantDate" label="出库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.GrantDate ? row.GrantDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="GrantUserName" label="发放人" min-width="140" show-overflow-tooltip align="center">
                <template #default="{ row }">
                    {{ row.GrantWithUserName ? row.Brand + ',' + row.GrantWithUserName : row.Brand }}
                </template>
            </el-table-column>
            <el-table-column prop="UserName" label="领用人" min-width="140" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.WithUserName ? row.UserName + ',' + row.WithUserName : row.UserName }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>