<script setup>
defineOptions({
    name: 'dangerchemicalsschoolcataloglist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Setting, Search, Refresh, FolderAdd
} from '@element-plus/icons-vue'
import {
    AnonDangerchemicals, DccatalogGetClassTwo, DcschoolCatalogFind, DcschoolCatalogFindCommonuseAll, DcschoolCatalogGetByid,
    DcschoolCataloginsertUpdate, DcschoolCatalogparentGet, DcschoolCatalogsetisCommonuse, DcschoolCatalogsetStatuz
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { buildTree, arrStatuz } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const bogetList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 50, sortModel: [{ SortCode: "OneCatalogId", SortType: "ASC" }, { SortCode: "TwoCatalogId", SortType: "ASC" }] })
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const yesNoList = ref([{ value: 1, label: '是' }, { value: 2, label: '否' }])
const dangerChemicalsLevel = ref('')
const unitType = ref(userStore.userInfo.UnitType)
const dialogNum = ref(1)
const ruleForm = {
    Name: [
        { required: true, message: '请输入危化品名称', trigger: 'change' },
    ],
    Pid: [
        { required: true, message: '请选择危化品分类', trigger: 'change' },
    ],
    UnitsMeasurement: [
        { required: true, message: '请选择计量单位', trigger: 'change' },
    ],
    Statuz: [
        { required: true, message: '请选择状态', trigger: 'change' },
    ],
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    AnonDangerchemicalsUser()
    HandleTableData()
    DccatalogGetClassTwoUser()
    DcschoolCatalogFindCommonuseAllUser()
    DcschoolCatalogparentGetUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 设置状态
const HandleSetAll = (row) => {
    dialogNum.value = 1
    dialogVisible.value = true
}
// 添加
const HandleAdd = (row) => {
    dialogNum.value = 2
    dialogData.value = {}
    dialogVisible.value = true
}
// 修改
const HandleEdit = (row) => {
    dialogNum.value = 3
    dialogVisible.value = true
    DcschoolCatalogGetByid({ id: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dialogData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交
const HandleSubmit = () => {
    if (dialogNum.value == 1) {
        // 批量修改状态
        DcschoolCatalogsetStatuzUser({ ids: selectRows.value.map(item => item.Id).join(','), statuz: dialogData.value.Statuz })
    } else {
        let Pid = ''
        if (typeof dialogData.value.Pid == 'string') {
            Pid = dialogData.value.Pid
        } else {
            Pid = dialogData.value.Pid[dialogData.value.Pid.length - 1]
        }
        let data = {
            Id: dialogData.value.Id,
            IsCommonUse: '0',
            IsNeedBack: false,
            Name: dialogData.value.Name,
            Pid: Pid,
            Statuz: dialogData.value.Statuz,
            UnitsMeasurement: dialogData.value.UnitsMeasurement,
        }
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            DcschoolCataloginsertUpdate(data).then(res => {
                if (res.data.flag == 1) {
                    dialogVisible.value = false
                    ElMessage.success(res.data.msg || '设置成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    }
}

// 修改状态
const HandleSwitchChange = (e, row) => {
    DcschoolCatalogsetStatuzUser({ ids: row.Id, statuz: e })
}
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.TwoCatalogId = undefined
    filters.value.Statuz = undefined
    filters.value.Name = undefined
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    DcschoolCatalogFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取学校物品类别选择
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let objId = rows.data.filter(item => item.Pid == '0')[0].Id
            let arr = rows.data.filter(item => item.Pid == objId || item.Pid == '0')
            bogetList.value = buildTree(arr)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 物品一二级分类下拉框数据
const DcschoolCatalogparentGetUser = () => {
    DcschoolCatalogparentGet().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取配置文件
const AnonDangerchemicalsUser = () => {
    AnonDangerchemicals().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerChemicalsLevel.value = rows.Level
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 设置状态 
const DcschoolCatalogsetStatuzUser = (data) => {
    DcschoolCatalogsetStatuz(data).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    危化品名称配置 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 是否启用：是指学校是否使用该危化品，如不用则设置为“否”；</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="unitType != 2 && dangerChemicalsLevel == unitType">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加危化品</el-button>
                    </el-form-item>
                    <el-form-item class="flexItem" v-if="unitType == 2 || dangerChemicalsLevel == unitType">
                        <el-button type="success" :icon="Setting" :disabled="selectRows.length == 0"
                            @click="HandleSetAll">设置状态</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="是否启用" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yesNoList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="Statuz" label="是否启用" min-width="100" align="center">
                <template #default="{ row }" v-if="dangerChemicalsLevel == unitType">
                    <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="2" inline-prompt active-text="启"
                        inactive-text="禁" style="--el-switch-off-color: #ff4949"
                        @change="HandleSwitchChange($event, row)" />
                </template>
                <template #default="{ row }" v-else>
                    <span v-if="row.Statuz == 1">是</span>
                    <span v-else style="color: #F56C6C;">否</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" min-width="90" align="center"
                v-if="unitType != 2 && dangerChemicalsLevel == unitType">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true"
            :title="dialogNum == 1 ? '设置状态' : dialogNum == 1 ? '添加危化品' : '修改危化品'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="140px"
                    status-icon>
                    <el-form-item label="选择分类：" prop="Pid" v-if="dialogNum != 1">
                        <el-cascader style="width: 80%" v-model="dialogData.Pid" :options="bogetList"
                            :props="{ value: 'Id', label: 'Name' }">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="危化品名称：" prop="Name" v-if="dialogNum != 1">
                        <el-input v-model="dialogData.Name" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="计量单位：" prop="UnitsMeasurement" v-if="dialogNum != 1">
                        <el-select v-model="dialogData.UnitsMeasurement" filterable allow-create default-first-option
                            style="width: 80%">
                            <el-option v-for="item in arrStatuz" :key="item.id" :label="item.Name" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态：">
                        <el-radio-group v-model="dialogData.Statuz">
                            <el-radio :value="1" label="启用"> </el-radio>
                            <el-radio :value="2" label="禁用"> </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>