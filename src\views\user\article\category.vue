<script setup>
defineOptions({
  name: 'articlecategory'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  FolderAdd, Refresh, Search
} from '@element-plus/icons-vue'
import {
  ArticleGetcategorypaged, ArticleAddcategory, ArticleEditcategory, ArticleDelcategory, ArticleGetbycategoryid
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { integerLimit } from "@/utils/index.js";
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const isAdd = ref(false)
const CateTypeList = ref([])
const CategoryList = ref([])
const IsManyList = ref([])
//新增&编辑操作
const dialogVisible = ref(false)
const dialogData = ref({ Type: '1' })
const refForm = ref()
const ruleForm = {
  Name: [
    { required: true, message: '请输入分类名称', trigger: 'change' },
  ],
  CateType: [
    { required: true, message: '请选择类别', trigger: 'change' },
  ],
  Pid: [
    { required: true, message: '请选择父级分类', trigger: 'change' },
  ],
  IsMany: [
    { required: true, message: '请选择是否支持多条子数据', trigger: 'change' },
  ],
  IsShowBottom: [
    { required: true, message: '请选择是否在页面底部显示此分类', trigger: 'change' },
  ],
}

//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})
//新增
const HandleAdd = () => {
  isAdd.value = true
  dialogVisible.value = true
  nextTick(() => {
    dialogData.value = { Sort: 0 }
    refForm.value.resetFields()

  })
}
// 修改
const HandleEdit = (row) => {
  isAdd.value = false
  ArticleGetbycategoryidUser(row.Id)
  dialogVisible.value = true
}
// 提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    if (isAdd.value) {
      ArticleAddcategoryUser()
    } else {
      ArticleEditcategoryUser()
    }
  })
}
//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      ArticleDelcategoryUser(row.Id)
    })
    .catch((err) => {
      console.info(err)
    })
}
//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst

  ArticleGetcategorypaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        CateTypeList.value = other.CateTypeList || [];//分类
        CategoryList.value = other.CategoryList || [];//父级分类
        IsManyList.value = other.IsManyList || [];//是否支持多条子数据
        let find = CategoryList.value.find(t => t.value === '0')
        if (!find) {
          CategoryList.value.unshift({ value: '0', label: '根节点' })
        }
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()

}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.Key = undefined
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}
//输入整数
const integerLimitInput = (val, name) => {
  dialogData.value[name] = integerLimit(val);
}
// 获取资讯分类信息
const ArticleGetbycategoryidUser = (id) => {
  ArticleGetbycategoryid({ id: id }).then(res => {
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      dialogData.value.Id = rows.Id
      dialogData.value.Pid = rows.Pid
      dialogData.value.CateType = String(rows.CateType)
      dialogData.value.Name = rows.Name
      dialogData.value.IsMany = String(rows.IsMany)
      dialogData.value.IsShowBottom = rows.IsShowBottom
      dialogData.value.Code = rows.Code
      dialogData.value.Sort = rows.Sort
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 添加资讯分类
const ArticleAddcategoryUser = () => {
  ArticleAddcategory(dialogData.value).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '添加成功')
      HandleTableData(true)
      dialogVisible.value = false
      dialogData.value = {}
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 修改资讯分类
const ArticleEditcategoryUser = () => {
  ArticleEditcategory(dialogData.value).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '修改成功')
      HandleTableData(true)
      dialogVisible.value = false
      dialogData.value = {}
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 删除资讯分类
const ArticleDelcategoryUser = (id) => {
  ArticleDelcategory({ id: id }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '删除成功')
      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Key" clearable placeholder="分类名称" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="Name" label="分类名称" min-width="160"></el-table-column>
      <el-table-column prop="PName" label="父级分类名称" min-width="160"></el-table-column>
      <el-table-column prop="CateTypeName" label="类型" min-width="100" align="center"></el-table-column>
      <el-table-column prop="CateTypeName" label="类型" min-width="100" align="center"></el-table-column>
      <el-table-column prop="Sort" label="排序" min-width="80" align="center"></el-table-column>
      <el-table-column fixed="right" label="操作" min-width="100" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
          <el-button v-if="row.Code != 1002 && row.Code != 1003 && row.Code != 1010" type="primary" link
            @click="HandleDel(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
    <el-dialog v-model="dialogVisible" title="添加分类" draggable width="600px" :close-on-click-modal="false">
      <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="150px" status-icon>
        <el-form-item label="父级分类：" prop="Pid">
          <el-select v-model="dialogData.Pid">
            <el-option v-for="item in CategoryList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="类别：" prop="CateType">
          <el-select v-model="dialogData.CateType">
            <el-option v-for="item in CateTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称：" prop="Name">
          <el-input v-model="dialogData.Name" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item label="多条子数据：" prop="IsMany">
          <el-radio-group v-model="dialogData.IsMany">
            <el-radio v-for="item in IsManyList" :key="item.value" :value="item.value">
              {{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="页面底部是否显示：" prop="IsShowBottom">
          <el-radio-group v-model="dialogData.IsShowBottom">
            <el-radio :value="1">是</el-radio>
            <el-radio :value="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序：">
          <el-input type="number" v-model="dialogData.Sort"></el-input>
        </el-form-item>
        <el-form-item label="自定义编码：">
          <el-input v-model="dialogData.Code" auto-complete="off"
            :disabled="dialogData.Code == 1002 || dialogData.Code == 1003 || dialogData.Code == 1010"
            @input="integerLimitInput($event, 'Code')"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>
<style lang="scss" scoped>
.dialog-content {
  height: 500px;
  overflow: auto;
}

.el-form {
  padding: 10px 30px 10px 10px;
}
</style>