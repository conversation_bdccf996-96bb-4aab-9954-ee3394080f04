<script setup>
defineOptions({
    name: 'managementschooluniformlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
    GetPurchaseListPaged, GetPurchaseListById, PurchaseListEdit, DeletePurchaseListById
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { UniformtypeList, SexList, NameList, integerLimit, limit, formatNumberWithCommas } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const userStore = useUserStore()
const PurchaseNo = ref('')
const route = useRoute()
const StageList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, isFirst: true })
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData();
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData();
        }
    })
})
//新增&编辑操作
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    SchoolStage: [
        { required: true, message: '请选择学段', trigger: 'change' },
    ],
    Uniformtype: [
        { required: true, message: '请选择种类', trigger: 'change' },
    ],
    Sex: [
        { required: true, message: '请选择适合性别', trigger: 'change' },
    ],
    Unit: [
        { required: true, message: '请选择单位', trigger: 'change' },
    ],
    Num: [
        { required: true, message: '请输入选购总套数', trigger: 'change' },
    ],
    Price: [
        { required: true, message: '请输入单价', trigger: 'change' },
    ],
    PersonNum: [
        { required: true, message: '请输入选购人数', trigger: 'change' },
    ],
}
//新增
const HandleAdd = () => {
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            UniformPurchaseId: route.query.id,
            Unit: '套'
        }
        if (StageList.value.length == 1) {
            dialogData.value.SchoolStage = StageList.value[0].value
        }
    })
}
// 修改
const HandleEdit = (row) => {
    console.log(row.Id)
    dialogVisible.value = true
    dialogData.value = {
        Id: row.Id,
        UniformPurchaseId: row.UniformPurchaseId,
        Uniformtype: row.Uniformtype,
        Sex: row.Sex,
        Num: row.Num,
        Unit: row.Unit,
        Price: row.Price,
        PersonNum: row.PersonNum,
        SchoolStage: String(row.SchoolStage),
    }
}
// 提交
const HandleSubmit = (row) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        PurchaseListEdit(dialogData.value).then((res) => {
            if (res.data.flag == 1) {
                HandleTableData()
                dialogVisible.value = false
                ElMessage.success(res.data.msg || '提交成功')
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
        .catch((err) => {
            console.info(err)
        })
}

//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定删除吗?')
        .then(() => {
            DeletePurchaseListById({ id: row.Id, purchaseid: row.UniformPurchaseId }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}

//列表
const HandleTableData = () => {
    // filters.value.Id = route.query.id
    filters.value.UniformPurchaseId = route.query.id
    GetPurchaseListPaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            // console.log("采购管理列表", res.data.data)
            const { rows, headers, other } = res.data.data
            tableData.value = rows.data || [];
            tableTotal.value = rows.dataCount
            PurchaseNo.value = headers.PurchaseNo
            if (other) summation.value = other[0]
            StageList.value = headers.StageList

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
const summation = ref({})
// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计';
            return;
        }
        if (column.property == 'Amount') {
            sums[index] = formatNumberWithCommas(summation.value.Amount);
            return;
        }
    });
    return sums;
}


//输入保留两位小数
const limitInput = (val, name, len) => {
    dialogData.value[name] = limit(val, len);
}

//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
</script>
<template>
    <div class="viewContainer">
        <el-collapse v-if="route.query.isEdit">
            <el-collapse-item>
                <template #title>
                    操作提示 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li style="color: #F56C6C;"> 请正确填报校服订购信息，否则会影响上级部门的数据统计！</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox" v-if="route.query.isEdit">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                </el-form>

            </el-col>
        </el-row>
        <div style="font-size: 14px;color: #606266;margin-bottom: 10px;margin-top: 20px;">
            合同批次：<span style="color: #999;">{{ PurchaseNo }}</span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="SchoolStageName" label="学段" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Sex" label="适合性别" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : '男/女' }}
                </template>
            </el-table-column>
            <el-table-column prop="Num" label="选购总套数" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Unit" label="单位" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Price" label="单价（元）" min-width="140" align="right"></el-table-column>
            <el-table-column prop="Amount" label="金额（元）" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.Amount ? formatNumberWithCommas(row.Amount) : '' }}
                </template>
            </el-table-column>
            <el-table-column prop="PersonNum" label="选购人数（人）" min-width="140" align="center"></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120" align="center" v-if="route.query.isEdit">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row,)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="560" :lazy="true" :title="dialogData.Id == '0' ? '添加校服清单' : '修改校服清单'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" status-icon
                    label-width="140px">
                    <el-form-item label="学段：" prop="SchoolStage">
                        <el-select v-model="dialogData.SchoolStage">
                            <el-option v-for="item in StageList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="种类：" prop="Uniformtype">
                        <el-select v-model="dialogData.Uniformtype">
                            <el-option v-for="item in UniformtypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="适合性别：" prop="Sex">
                        <el-select v-model="dialogData.Sex">
                            <el-option v-for="item in SexList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="选购总套数：" prop="Num">
                        <el-input v-model="dialogData.Num" @input="integerLimitInput($event, 'Num')"
                            placeholder="实际选购该类型校服的总套数"></el-input>
                    </el-form-item>
                    <el-form-item label="单位：" prop="Unit">
                        <el-select v-model="dialogData.Unit" filterable>
                            <el-option v-for="item in NameList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>

                    </el-form-item>
                    <el-form-item label="单价：" prop="Price">
                        <el-input v-model="dialogData.Price" @input="limitInput($event, 'Price', 4)"></el-input>
                    </el-form-item>
                    <el-form-item label="选购人数（人）：" prop="PersonNum">
                        <el-input v-model="dialogData.PersonNum" @input="integerLimitInput($event, 'PersonNum')"
                            placeholder="实际选购该类型的校服的学生数"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 保存 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>