<script setup>
import { onMounted, ref, watch } from 'vue'
import {
    Refresh, Search,
} from '@element-plus/icons-vue'
import {
    GetSupplierFindPage,
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
// 表格初始化
const StatuzIsRenewalList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, isFirst: true })
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()

//加载数据
onMounted(() => {
    HandleTableData()
})
// 查看
const HandleDetail = (row, e) => {
    dialogData.value = row
    dialogVisible.value = true
}
//列表
const HandleTableData = () => {
    GetSupplierFindPage(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = undefined
    filters.value.ContactUser = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
    <div class="viewContainer">
        <!-- 搜索 -->
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="企业全称" style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsContractRenewal" clearable placeholder="所在地区"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in StatuzIsRenewalList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.ContactUser" placeholder="联系人" style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>

        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column type="index" width="50" align="center" />
            <el-table-column prop="Name" label="企业全称" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ContactUser" label="联系人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Tel" label="联系电话" min-width="120" align="center"></el-table-column>
            <el-table-column prop="AreaName" label="所在地区" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" label="详情" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <!-- 分页 -->
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :height="600" :width="680" :lazy="true" title="企业详情">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" label-width="140px" disabled status-icon>
                    <el-form-item label="企业全称：">
                        <el-input v-model="dialogData.Name"></el-input>
                    </el-form-item>
                    <el-form-item label="组织机构代码：">
                        <el-input v-model="dialogData.OrganizationCode"></el-input>
                    </el-form-item>
                    <el-form-item label="所在地区：">
                        <el-input v-model="dialogData.AreaName"></el-input>
                    </el-form-item>
                    <el-form-item label="详细地址：">
                        <el-input v-model="dialogData.Address"></el-input>
                    </el-form-item>
                    <el-form-item label="联系电话：">
                        <el-input v-model="dialogData.Tel"></el-input>
                    </el-form-item>
                    <el-form-item label="电商地址：">
                        <el-input v-model="dialogData.Url"></el-input>
                    </el-form-item>
                    <el-form-item label="公司简介：">
                        <el-input v-model="dialogData.Introduction" type="textarea"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                </span>
            </template>
        </app-box>
    </div>

</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>