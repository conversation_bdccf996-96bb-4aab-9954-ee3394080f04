<script setup>
defineOptions({
    name: 'dangerchemicalssystemteambuild'
});
import { onMounted, ref, onActivated, nextTick, watch } from 'vue'
import {
    Select, UploadFilled
} from '@element-plus/icons-vue'
import { AttachmentUpload } from '@/api/user.js'
import {
    BaseFieldConfigFind, BaseFieldConfigSaveList
} from '@/api/dangerchemicals.js'
import { useUserStore } from '@/stores';
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { ElMessageBox, ElMessage } from 'element-plus';
import { useRoute } from 'vue-router'
import router from '@/router'

// 初始化
const userStore = useUserStore()
const route = useRoute()
const Id = ref(0)
const formData = ref({})
const refForm = ref()
//加载数据
onMounted(() => {
    Id.value = route.query.id || 0
    if (route.query.isTagRouter) {
        BconfigsetgetbyunitUser();
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    Id.value = route.query.id || 0
    nextTick(() => {
        if (!route.query.isTagRouter) {
            BconfigsetgetbyunitUser();
        }
    })
})
const BconfigsetgetbyunitUser = () => {
    BaseFieldConfigFind({ id: Id.value }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data;
            formData.value.formItems = rows.data;
            const { other } = res.data.data;
            uploadFileData.value = other;
            // console.log("制度与队伍建other:", other)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const HandleSubmit = () => {
    //根据数据集合提取需要的属性
    const requestParam = formData.value.formItems.map(({ Id, FieldValue }) => ({ Id, FieldValue }));
    var imageArr = [];
    if (uploadFileData.value && uploadFileData.value.length > 0) {
        uploadFileData.value.map(function (item, index) {
            if (item.AttachmentList.length > 0) {
                item.AttachmentList.map(function (olditem, oldindex) {
                    imageArr.push({ Id: olditem.Id, RelationId: olditem.RelationId, FilePath: olditem.FieldValue, Title: olditem.FieldName });
                });
            }
            if (item.fileLChildList.length > 0) {
                item.fileLChildList.map(function (newitem, newindex) {
                    imageArr.push({ Id: 0, RelationId: newitem.FileCategory, FilePath: newitem.Path, Title: newitem.Title });
                });
            }
        });
    }

    BaseFieldConfigSaveList({ list: requestParam, attrs: imageArr }).then(res => {
        if (res.data.flag == 1) {
            BconfigsetgetbyunitUser()
            ElMessage.success('保存成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

const uploadFileData = ref([])
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.AttachmentList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    console.log("ary", ary, "name", name)
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].AttachmentList = uploadFileData.value[index].AttachmentList.filter(t => t.Id != item.Id)
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }

}

</script>
<template>
    <el-form style="width: 80%; min-width: 600px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
        label-width="180px" status-icon>

        <div v-for="(item, index) in formData.formItems" :key="item.Id" class="flexItem">
            <el-form-item :label="item.FieldName" :prop="'formItems.' + index + '.FieldValue'"
                :rules="[{ message: '请输入' + item.FieldName, trigger: 'blur' }]">
                <el-input :disabled="Id > 0" v-model="item.FieldValue" auto-complete="off"
                    style="width: 50%;"></el-input>
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">
                    {{ item.Remark }}</span>
            </el-form-item>
        </div>
        <div v-for="(item, index) in formData.formAttrTitles" :key="item.Id" class="flexItem">
            <el-form-item :label="item.FieldName" :prop="'formAttrTitles.' + index + '.FieldValue'"
                :rules="[{ message: '请上传' + item.FieldName, trigger: 'blur' }]">
                <div v-for="(fileitem, fileindex) in formData.formAttrVals" :key="fileitem.Id" class="flexItem">
                    <el-form-item :label="item.TypeName" :prop="'formItems.' + fileindex + '.ConfigValue'"
                        v-if="item.Id == fileitem.RelationId">
                        <a style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;"
                            src="{{ fileitem.FieldValue }}">{{ fileitem.FieldName }}</a>
                    </el-form-item>
                </div>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                    :before-upload="beforeAvatarUpload" :http-request="httpRequest">
                    <el-button type="primary" @click="httpRequestIndex(index)" style="margin: 0 5px;">上传</el-button>
                </el-upload>
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">{{ item.Remark
                }}</span>
            </el-form-item>
        </div>
        <!-- 附件上传 -->
        <el-form-item v-for="(item, index) in uploadFileData" :key="index">
            <template #label>
                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> {{ item.Name }}： </span>
            </template>
            <el-upload v-if="!Id" ref="uploadRef" class="upload-demo" :show-file-list="false"
                :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                <el-button type="success" size="small" :icon="UploadFilled"
                    @click="MaxFileNumberClick(item)">上传</el-button>
            </el-upload>
            <div class="fileFlex">
                <div v-for="itemCate in item.AttachmentList" :key="itemCate.Id" style="color:#409EFF ;">
                    <el-icon v-if="!Id" color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.AttachmentList)">
                        {{ itemCate.FieldName }}
                    </span>
                </div>
                <div v-for="itemChild in item.fileLChildList" :key="itemChild.Id">
                    <el-icon v-if="!Id" color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemChild, item.fileLChildList)">
                        {{ itemChild.Title }}{{ itemChild.Ext }}
                    </span>
                </div>
            </div>
        </el-form-item>
        <el-form-item v-if="!Id">
            <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>
