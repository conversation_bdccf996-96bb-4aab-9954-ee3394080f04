<script setup>
defineOptions({
    name: 'evaluateedit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Position
} from '@element-plus/icons-vue'
import {
    GetPurchaseListPaged, EvaluateLaunch, EvaluateGetbyId
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import { useUserStore } from '@/stores'
import router from '@/router'
import { useRoute } from 'vue-router'
// 二维码生成下载
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
// 复制链接
import useClipboard from 'vue-clipboard3'
const { toClipboard } = useClipboard();
const route = useRoute()
const userStore = useUserStore()
// 表格初始化
const tableData = ref([])
const PurchaseNo = ref('')
const refTable = ref()
const formData = ref({})
const refForm = ref()
const isSaveedit = ref(false)//是否已提交
const routeId = ref()
const EvaluateDeadline = ref()//截止日期
const UniformPurchaseList = ref()//截止日期
const msg = ref('')
const ruleForm = {
    UniformPurchaseId: [
        { required: true, message: '请选择合同批次', trigger: 'change' },
    ],
    EvaluateDeadline: [
        { required: true, message: '请选择评价截止日期', trigger: 'change' },
    ]
}
//加载数据
onMounted(() => {
    if (route.query.id) {
        formData.value.Id = route.query.id
        EvaluateDeadline.value = route.query.EvaluateDeadline
        if (route.query.EvaluateDeadline) {
            formData.value.EvaluateDeadline = route.query.EvaluateDeadline.substring(0, 10)
        }
    }
    let url = window.location.protocol + '//' + window.location.host
    qrText.value = url + "/ui/#" + "/pages/evaluate/edit?id=" + formData.value.Id;//二维码信息
    if (route.query.isTagRouter) {
        EvaluateGetbyIdUser()
    }
})
onActivated(() => {
    // 修改tag标签名称########################
    if (route.query.title) {
        // 修改pinia数据
        userStore.$patch(state => {
            state.pageTitleObj.evaluateTitle = route.query.title
        })
    }
    let tagsList = userStore.tagsList
    // 使用forEach遍历数组  修改tag标签名称
    tagsList.forEach(item => {
        if (item.path == route.path) {
            // 如果path匹配，则修改title  
            item.title = userStore.pageTitleObj.evaluateTitle;
        }
    });
    userStore.setTagsList(tagsList)
    // ########################

    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)

    if (route.query.id) {
        formData.value.Id = route.query.id
        EvaluateDeadline.value = route.query.EvaluateDeadline
        if (route.query.EvaluateDeadline) {
            formData.value.EvaluateDeadline = route.query.EvaluateDeadline.substring(0, 10)
        }
    }
    let url = window.location.protocol + '//' + window.location.host
    qrText.value = url + "/ui/#" + "/pages/evaluate/edit?id=" + formData.value.Id;//二维码信息
    nextTick(() => {
        if (!route.query.isTagRouter) {
            EvaluateGetbyIdUser()
        }
    })
})
const PurchaseIdChange = (val) => {
    GetPurchaseListPagedUser(val)
}
//搜索
const GetPurchaseListPagedUser = (id) => {
    GetPurchaseListPaged({ UniformPurchaseId: id, pageIndex: 1, pageSize: 1000 }).then(res => {
        // if (res.data.flag == 1) {
        //     if (res.data.flag == 1) {
        //         const { rows, other } = res.data.data
        //         if (other) {
        //             PurchaseNo.value = other.PurchaseNo
        //         }
        //         tableData.value = rows || []
        //         if (tableData.value.length == 0) {
        //             msg.value = '没有需要评价的校服信息'
        //         } else {
        //             msg.value = ''
        //         }
        //     } else {
        //         ElMessage.error(res.data.msg)
        //     }
        // } else {
        //     ElMessage.error(res.data.msg)
        // }

        if (res.data.flag == 1) {
            // console.log("采购管理列表", res.data.data)
            const { rows, headers, other } = res.data.data
            tableData.value = rows.data || [];
            if (tableData.value.length == 0) {
                msg.value = '没有需要评价的校服信息'
            } else {
                msg.value = ''
            }
            // tableTotal.value = rows.dataCount
            PurchaseNo.value = headers.PurchaseNo
        } else {
            ElMessage.error(res.data.msg)
        }

    });
}
// 生成、修改 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        ElMessageBox.confirm('确定提交吗?')
            .then(() => {
                let paraData = {
                    UnifomEvaluateMainId: formData.value.Id,
                    UniformPurchaseId: formData.value.UniformPurchaseId,//合同批次Id
                    EvaluateDeadline: formData.value.EvaluateDeadline
                }
                EvaluateLaunch(paraData).then(res => {
                    if (res.data.flag == 1) {
                        ElMessage.success(res.data.msg || '提交成功')
                        const { rows } = res.data.data
                        formData.value.Id = rows.Id
                        EvaluateGetbyIdUser(true)
                    } else {
                        ElMessage.error(res.data.msg)
                    }
                });
            })
            .catch((err) => {
                console.info(err)
            })
    })

}
// 获取详情
const EvaluateGetbyIdUser = (isEdit) => {
    EvaluateGetbyId({ id: formData.value.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            UniformPurchaseList.value = other || []
            if (rows) {
                formData.value = rows
                let url = window.location.protocol + '//' + window.location.host
                qrText.value = url + "/ui/#" + "/pages/evaluate/edit?id=" + formData.value.Id;//二维码信息
                isSaveedit.value = true

            } else {
                formData.value = {}
            }
            if (formData.value.Id && formData.value.Id != '0' && !isEdit) {
                GetPurchaseListPagedUser(formData.value.UniformPurchaseId);
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}


//下载二维码图片
const downQr = () => {
    let name = new Date().getTime();
    let a = document.createElement("a");
    a.style.display = "none";
    a.download = name;
    a.href = qrData.value;
    document.body.appendChild(a);
    a.click();
}
//二维码图片的编码数据
const qrText = ref("");
const qrData = ref("");
//qr的回调，每次变动后把二维码的数据保存下来，供下载用
const qrBack = (dataUrl, id) => {
    qrData.value = dataUrl;
}
const copy = async () => {
    try {
        await toClipboard(qrText.value);
    } catch (e) {
        console.error(e);
    }
}
</script>

<template>
    <div class="viewContainer">
        <div style="width: 900px;margin: 10px auto;">
            <el-form class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
                label-width="150px" status-icon>
                <el-form-item label="合同批次：" prop="UniformPurchaseId" class="formItem">
                    <el-select v-model="formData.UniformPurchaseId" class="item_content" @change="PurchaseIdChange"
                        style="width: 50%;">
                        <el-option v-for="item in UniformPurchaseList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="评价截止时间：" prop="EvaluateDeadline">
                    <el-date-picker type="date" placeholder="选择日期" v-model="formData.EvaluateDeadline"
                        format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 50%;"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" :icon="Position" @click="HandleSubmit">生成</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-divider border-style="dashed"> </el-divider>
        <div style="width: 900px;margin: 10px auto;">
            <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                header-cell-class-name="headerClassName">
                <el-table-column prop="Uniformtype" label="种类" min-width="80" align="center"></el-table-column>
                <el-table-column prop="Sex" label="适合性别" min-width="120" align="center">
                    <template #default="{ row }">
                        {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : '男/女' }}
                    </template>
                </el-table-column>
                <el-table-column prop="Unit" label="单位" min-width="100" align="center"></el-table-column>
                <el-table-column prop="Price" label="单价（元）" min-width="140" align="right"></el-table-column>
            </el-table>
        </div>
        <el-divider v-if="formData.Id && formData.Id != '0'" content-position="left"><span style="color:#E6A23C">
                下载二维码或复制链接发送给该“合同批次”的家长、学生、教师 进行在线评价</span>
        </el-divider>
        <div v-if="formData.Id && formData.Id != '0'" style="width: 900px;margin: 10px auto;">
            <div>
                <el-input v-model="qrText" style="width: 560px" placeholder="http" disabled>
                    <template #append><el-button type="primary" plain @click="copy">复制链接</el-button></template>
                </el-input>
            </div>
            <div style="display: flex;align-items: center;">
                <vue-qr :callback="qrBack" :text="qrText" :size="120"></vue-qr>
                <el-button type="primary" plain @click="downQr">下载二维码图片</el-button>
            </div>
        </div>
    </div>

</template>
<style lang="scss" scoped>
:deep(.el-input-group__append) {
    background-color: #ecf5ff;

    button {
        border-color: #409eff;
        color: #409eff;
    }
}

:deep(.el-input-group__append:hover) {
    background-color: #409eff;
    color: #fff;
}
</style>