<script setup>
defineOptions({
  name: 'treasurydetail'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import { Link } from '@element-plus/icons-vue'
import { FillingInfoDetail } from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { ElMessage } from 'element-plus'
import { pageQuery, urlQuery, tagsListStore, fileDownload } from "@/utils/index.js";
import router from '@/router'
// 初始化
const route = useRoute()
const userStore = useUserStore()
const size = ref('default')
const routerObject = ref({})
const routerUrl = ref('')
const refForm = ref()
const detailData = ref([])
const activeNames = ref([])
//加载数据
onMounted(() => {
  routerObject.value = pageQuery(route.path)
  if (route.query.isTagRouter) {
    FillingInfoDetailUser(route.query.id)
  }
})
onActivated(() => {
  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
  routerUrl.value = urlQuery(route.path); // 获取生成页面携带的参数
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  nextTick(() => {
    if (!route.query.isTagRouter) {
      FillingInfoDetailUser(route.query.id)
    }
  })
})

// 获取详情信息
const FillingInfoDetailUser = (id) => {
  FillingInfoDetail({ id: id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      rows.forEach((item, index) => {
        let FormContent = JSON.parse(item.FormContent)//表单元素
        let DataContent = JSON.parse(item.DataContent)//数据
        // 创建一个映射表，用于快速查找DataContent中的 InfoText
        const infotextMap = DataContent.reduce((map, item) => {
          map[item.FieldId] = item.InfoText;
          return map;
        }, {});
        // 遍历表单元素FormContent，并替换field对应的InfoText到DefaultName
        FormContent.forEach(item => {
          if (infotextMap[item.field]) {
            item.DefaultName = infotextMap[item.field];
          }
        });
        item.data = FormContent.filter(t => !t.isRedact)
      })
      detailData.value = rows//表单数据
      activeNames.value = rows.map((item, index) => item.TabId + index)//默认展开所有
      console.log(" detailData.value", detailData.value)
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
  let path = e.Path;
  viewPhotoList.value = imgList.map(t => t.Path)
  if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
    showViewer.value = true;
    viewPhotoList.value.forEach((item, index) => {
      if (path == item) {
        imgSrcIndex.value = index;
      }
    });
    // //大图预览从点击的那张开始
    let tempImgList = [...viewPhotoList.value];
    let temp = [];
    for (let i = 0; i < imgSrcIndex.value; i++) {
      temp.push(tempImgList.shift());
    }
    viewPhotoList.value = tempImgList.concat(temp);

  } else {
    let title = e.Title + e.Ext
    fileDownload(e.Path, title)
  }
}
// 项目清单查看
const projectDetail = (item, processNodeId) => {
  router.push({
    path: "../node/projectdetail@moduleId=" + routerObject.value.moduleId, query: {
      ProcessId: route.query.ProcessId,
      ProjectDeclarationId: route.query.id,
      ProcessNodeId: processNodeId,
      FieldCode: item.code,
      routerUrl: routerUrl.value,
      page: 'treasurydetail',
    }
  })
}
</script>
<template>
  <div class="demo-collapse">
    <el-collapse v-model="activeNames">
      <el-collapse-item v-for="(item, index) in detailData" :key="item.TabId" :name="item.TabId + index">
        <template #title>
          <div>{{ item.TabName }}</div>
        </template>
        <el-form style="width: 100%;min-width: 560px;" :inline="true" ref="refForm" @submit.prevent :model="item.data">
          <div class="edit-form-item">
            <template v-for="(item1, index) in item.data" :key="item1.code">
              <el-form-item v-if="!(item1.Controlled > 0 && !item1.isControlShow)"
                :label-width="item1.type == 'line' ? '0px' : item1.labelWidth ? item1.labelWidth + 'px' : '200px'"
                :style="{ width: item1.width + '%' }">
                <template #label>
                  <span> {{ item1.name ? item1.name + '：' : '' }} </span>
                </template>
                <div v-if="item1.type == 'upload'">
                  <div class="fileFlex" style="line-height: 24px;background-color: #67C23A;color: #ffffff; ">
                    <div v-for="(item2, index2) in item1.DefaultName" :key="item2.Id"
                      @click="fileListDownload(item2, item1.DefaultName)" style="cursor: pointer;">
                      <!-- <el-icon >
                        <Link />
                      </el-icon> -->
                      {{ item2.Title }}
                    </div>
                  </div>
                </div>
                <div v-else-if="item1.type == 'projectlist' || item1.type == 'projectexaminelist'">
                  <el-button type="primary" size="small" @click="projectDetail(item1, item.ProcessNodeId)">
                    查看</el-button>
                </div>
                <div v-else-if="item1.type == 'statisticstable'">
                  <ul>
                    <li v-for="(item2, index2) in item1.DefaultName" :key="item2.Id" class="col-line">
                      {{ item2.ProjectName }}:{{ item2.CurrentAmount }}(元)</li>
                  </ul>
                </div>
                <el-divider v-else-if="item1.type == 'line'" :border-style="item1.isDashed"
                  :content-position="item1.isCenter"
                  :style="{ 'width': '100%', '--el-border-color': item1.dividerColor ? item1.dividerColor : '#dedfe6' }">
                  <span :style="{ 'color': item1.dividerTextColor ? item1.dividerTextColor : '#303133' }">
                    {{ item1.dividerText }}</span>
                </el-divider>
                <span v-else-if="item1.type == 'descriptivetext'" style="color: #999999;font-size: 14px;">{{
                  item1.DefaultName
                }}</span>
                <span v-else-if="item1.type === 'projectallamount'">
                  {{ formatNumberWithCommas(item1.DefaultName) }} &nbsp;元</span>
                <el-input v-else v-model="item1.DefaultName" :size="size" disabled></el-input>
              </el-form-item>
            </template>
          </div>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
  </div>
</template>
<style lang="scss" scoped>
.edit-form-item {
  display: flex;
  flex-wrap: wrap;
}

:deep(.el-form-item) {
  margin-right: 0;
  margin-bottom: 10px;
  // align-items: center;
}

.col-line {
  line-height: 25px;
  font-weight: bold;
  border-bottom: 1px solid rgb(218 218 218);
}
</style>
