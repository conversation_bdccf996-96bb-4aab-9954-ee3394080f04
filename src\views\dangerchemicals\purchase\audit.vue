<script setup>
defineOptions({
    name: 'dangerchemicalspurchaseaudit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { AttachmentUpload } from '@/api/user.js'
import {
    DcPurchaseEndGetById, DcPurchaseAudit
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore, fileDownload } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const route = useRoute()
const userStore = useUserStore()
const purchase = ref({})
const approvalList = ref([])
const activeNames = ref([])
const isNeedApproval = ref(0)
const securityApprovalFile = ref('')
const formData = ref({ Statuz: 20, Remark: '同意' })
const refForm = ref()
const ruleForm = {
    Remark: [
        { required: true, message: '请填写意见', trigger: 'change' },
    ]
}
//审核
const HandleSubmit = () => {
    var objFile = uploadFileData.value[0].fileLChildList[0];
    if (objFile != undefined) {
        securityApprovalFile.value = objFile.Path + '|' + objFile.Title + objFile.Ext
    }
    let paraData = {
        Id: route.query.Id,
        processNumber: route.query.p,
        remark: formData.value.Remark,
        statuz: formData.value.Statuz,
        SecurityApprovalFile: securityApprovalFile.value
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcPurchaseAudit(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '审核成功')
                let tagsList = userStore.tagsList
                tagsList = tagsList.filter(t => t.path != route.path)
                userStore.setTagsList(tagsList)
                router.push({ path: '/dangerchemicals/purchase/auditlist@p=10' })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

//审批
const HandleAuditSubmit = () => {
    let paraData = {
        Id: route.query.Id,
        processNumber: route.query.p,
        remark: formData.value.Remark,
        statuz: formData.value.Statuz
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcPurchaseAudit(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '审核成功')
                let tagsList = userStore.tagsList
                tagsList = tagsList.filter(t => t.path != route.path)
                userStore.setTagsList(tagsList)
                router.push({ path: '/dangerchemicals/purchase/auditlist@p=20' })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
//加载数据
onMounted(() => {
    securityApprovalFile.value = ''
    HandleViewData()
    if (route.query.isTagRouter) {
        HandleViewData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleViewData()
        }
    })
})
// 详情
const HandleViewData = () => {
    DcPurchaseEndGetById({ id: route.query.Id }).then(res => {
        if (res.data.flag == 1) {
            purchase.value = res.data.data.rows;
            approvalList.value = res.data.data.other;
            isNeedApproval.value = res.data.data.headers.IsNeedApproval;
            activeNames.value = approvalList.value.map((item) => item.Id)
            activeNames.value.push(purchase.value.Id)
            activeNames.value.push(100)
            activeNames.value.push(200)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const HandleDetailView = () => {
    router.push({
        path: "/dangerchemicals/purchase/detaillist", query: {
            Id: purchase.value.Id,
            batchNo: purchase.value.BatchNo
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileClick = (e) => {
    let index = e[1].lastIndexOf('.');
    let Ext = e[1].substring(index + 1).toUpperCase();
    if (Ext == "PNG" || Ext == "JPG" || Ext == "JPEG" || Ext == "GIF") {
        showViewer.value = true;
        viewPhotoList.value = ['http://localhost:9291' + e[0]]
    } else {
        fileDownload('http://localhost:9291' + e[0], e[1])
    }
}

// 附件上传
const uploadFileData = ref([{ FileCategory: 1010, fileLChildList: [], categoryList: [], MaxFileNumber: 1, IsFilled: 0, FileSize: 10, Memo: "文件小于5M，支持pdf和图片文件", Name: "公安审批文件：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" }])
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}

// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }
}

</script>

<template>
    <div class="demo-collapse">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="采购信息" :name="purchase.Id">
                <div class="content">
                    <div class="content_label_div"><span class="content_label">采购批次：</span>{{ purchase.BatchNo }}</div>
                    <div><span class="content_label">参考金额：</span>
                        {{ purchase.Amount ? '￥' + Number(purchase.Amount).toFixed(2) : '--' }}</div>
                    <div><span class="content_label">危化品清单：</span><el-button :icon="Search"
                            @click="HandleDetailView">查看</el-button></div>
                    <div><span class="content_label">申请人：</span>{{ purchase.UserName }}</div>
                    <div><span class="content_label">申请时间：</span>
                        {{ purchase.RegDate ? purchase.RegDate.substring(0, 10) : '--' }}</div>
                </div>
            </el-collapse-item>
            <el-collapse-item v-for="(item, index) in approvalList" :key="index"
                :title="item.ProcessNumber == 10 ? '采购审核' : '采购审批'" :name="item.Id">
                <div class="content">
                    <div class="content_label_div"><span class="content_label">审核结果：</span>
                        {{ item.ApprovalStatuz % 10 == 1 ? "不通过" : "通过" }}</div>
                    <div class="content_label_div"><span class="content_label">审核意见：</span>{{ item.ApprovalRemark }}
                    </div>
                    <div><span class="content_label">审核人：</span>{{ item.AuditUserName }}</div>
                    <div><span class="content_label">审核日期：</span>
                        {{ item.RegTime ? item.RegTime.substring(0, 10) : '--' }}</div>
                    <div v-if="item.ProcessNumber == 10">
                        <span class="content_label_div" v-if="item.SecurityApprovalFile"
                            @click="fileClick(item.SecurityApprovalFile.split('|'))">
                            公安审批文件：{{ item.SecurityApprovalFile.split('|')[1] }}
                        </span>
                    </div>
                </div>
            </el-collapse-item>
            <el-collapse-item v-if="route.query.p == 10" :key="100" title="采购审核" :name="100">
                <el-form style="min-width: 600px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="140px" status-icon>
                    <el-form-item label="审核结果：">
                        <el-radio-group v-model="formData.Statuz">
                            <el-radio :value="20">通过</el-radio>
                            <el-radio :value="11">不通过</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="审核意见：" :prop="formData.Statuz == 11 ? 'Remark' : ''">
                        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="formData.Remark"
                            style="width: 400px"></el-input>
                    </el-form-item>
                    <div v-if="isNeedApproval == 1">
                        <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                            <template #label>
                                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                                <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                    <div>
                                        <el-icon color="#E6A23C" class="tipIcon">
                                            <QuestionFilled />
                                        </el-icon>
                                    </div>
                                </el-tooltip>
                                <span> {{ item.Name }} </span>
                            </template>
                            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                                :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                                :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                                <el-button type="success" size="small" :icon="UploadFilled"
                                    @click="MaxFileNumberClick(item)">上传</el-button>
                            </el-upload>
                            <div class="fileFlex">
                                <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                                    <el-icon color="#F56C6C" style="cursor: pointer;"
                                        @click="delCateFile(itemCate, index)">
                                        <Delete />
                                    </el-icon>
                                    <span style="cursor: pointer;"
                                        @click="fileListDownload(itemCate, item.categoryList)">
                                        {{ itemCate.Title }}{{ itemCate.Ext }}
                                    </span>
                                </div>
                                <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                                    <el-icon color="#F56C6C" style="cursor: pointer;"
                                        @click="delFile(itemChild, index)">
                                        <Delete />
                                    </el-icon>
                                    <span style="cursor: pointer;"
                                        @click="fileListDownload(itemChild, item.fileLChildList)">
                                        {{ itemChild.Title }}{{ itemChild.Ext }}
                                    </span>
                                </div>
                            </div>
                        </el-form-item>
                    </div>
                    <el-form-item>
                        <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                    </el-form-item>
                </el-form>
            </el-collapse-item>
            <el-collapse-item v-if="route.query.p == 20" :key="200" title="采购审批" :name="200">
                <el-form style="min-width: 120px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <el-form-item label="审核结果：">
                        <el-radio-group v-model="formData.Statuz">
                            <el-radio :value="100">通过</el-radio>
                            <el-radio :value="21">不通过</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="审核意见：" :prop="formData.Statuz == 11 ? 'Remark' : ''">
                        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="formData.Remark"
                            style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="HandleAuditSubmit"> 提交 </el-button>
                    </el-form-item>
                </el-form>
            </el-collapse-item>
        </el-collapse>
        <!-- 图片预览 -->
        <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    </div>
</template>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-wrap: wrap;
    color: #333;
    font-size: 14px;
    width: 760px;
    padding-left: 30px;

    &>div {
        width: 360px;
        padding: 5px 10px;
    }

    .content_label_div {
        width: 600px;
    }

    .content_label {
        display: inline-block;
        width: 120px;
        text-align: right;
        font-weight: bold;
    }

}

:deep(.el-collapse-item__header) {

    font-size: 14px;
    color: #0000ee;
}
</style>