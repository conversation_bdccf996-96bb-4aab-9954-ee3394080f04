<script setup>
defineOptions({
    name: 'dangerchemicalsapplygivelist'
});
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    BconfigSetgetPunit, DcApplyConfirmDetailResendCode, DcApplyConfirmDetailCollar, DcApplyFind, DcApplyConfirmDetailGetById, DcapplyGrantUserComboGet
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, integerLimit, ConsolidatedColumn } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const memberUserList = ref([])//同发放人
const EndDate = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 12, IsGrant: 0, sortModel: [{ SortCode: "BatchNo", SortType: "DESC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const bconfigSet = ref(0)
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    Num: [
        { required: true, message: '请填写实际领用数量', trigger: 'change' },
    ],
    CheckCode: [
        { required: true, message: '请填写领用码', trigger: 'change' },
    ],
    WithCheckCode: [
        { required: true, message: '请填写领用码', trigger: 'change' },
    ],
    WithUserId: [
        { required: true, message: '请选择', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'BatchNo', label: '申领批次', },
    { value: 'UserName', label: '领用人', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    BconfigSetgetPunitUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const oldId = ref('')
// 发放
const HandleEdit = (row) => {
    if (row.Id != oldId.value) {
        codeText.value = '获取校验码'
        isCode.value = false
        time.value = Date.now() + 1000 * 60 * 2
        withOdeText.value = '获取校验码'
        withIsCode.value = false
        withTime.value = Date.now() + 1000 * 60 * 2
    }
    oldId.value = row.Id

    dialogVisible.value = true
    formData.value = row
    if (memberUserList.value.length > 0) {
        formData.value.WithUserId = memberUserList.value[0].MemberUserId
    } else {
        formData.value.WithUserId = ''
    }
}
//发放提交
const HandleSubmit = () => {
    let paraData = {
        id: formData.value.Id,
        checkCode: formData.value.CheckCode,
        withCode: formData.value.WithCheckCode || undefined,
        withUserId: formData.value.WithUserId,
        num: formData.value.Num,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcApplyConfirmDetailCollar(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '发放成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.BeginDate = undefined
    filters.value.EndDate = undefined
    EndDate.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.BeginDate = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.EndDate = val + " 23:59:59"
    } else {
        filters.value.EndDate = undefined
    }
    HandleTableData()
}
//输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcApplyFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 根据TypeCode获取上级单位配置信息
const BconfigSetgetPunitUser = () => {
    BconfigSetgetPunit({ moduleCode: 9, typeCode: 'WHPFFMS' }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bconfigSet.value = rows
            if (rows == 2) {
                DcapplyGrantUserComboGetUser()
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取同领用人、发放人
const DcapplyGrantUserComboGetUser = () => {
    DcapplyGrantUserComboGet({ type: 2 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            memberUserList.value = rows || []

        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
const codeText = ref('获取校验码')
const isCode = ref(false)
const time = ref(Date.now() + 1000 * 60 * 2)
const withOdeText = ref('获取校验码')
const withIsCode = ref(false)
const withTime = ref(Date.now() + 1000 * 60 * 2)
// 获取短信领用码
const getCode = (e) => {
    refForm.value.validateField(['imgCode', 'Mobile'], valid => {
        if (!valid) {
            return
        }
        let paraData = {}
        if (e == 1) {
            paraData = {
                id: formData.value.Id,
                mobile: formData.value.Mobile,
                messageType: 1,
            }
        } else {
            paraData = {
                id: formData.value.Id,
                mobile: formData.value.WithMobile,
                messageType: 2,
            }
        }
        DcApplyConfirmDetailResendCode(paraData).then(res => {
            if (res.data.flag == 1) {
                const { other } = res.data.data
                if (e == 1) {
                    time.value = Date.now() + 1000 * 120
                    isCode.value = true
                } else {
                    withTime.value = Date.now() + 1000 * 120
                    withIsCode.value = true
                }
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    });
}
// 校验码倒计时结束
const finishChange = (e) => {
    if (e == 1) {
        // console.log('倒计时结束')
        codeText.value = '重新获取'
        isCode.value = false
    } else {
        withOdeText.value = '重新获取'
        withIsCode.value = false
    }

}
// 计算合并信息
const spanArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'UserName')
})
// 计算合并信息
const spanBatchNoArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'BatchNo')
})
// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    // 只对第一列（UserName）进行合并
    if (columnIndex === 0) {
        const _row = spanArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
    if (columnIndex === 1) {
        const _row = spanBatchNoArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDate) return false;
    return time < new Date(filters.value.BeginDate + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    待发放危化品 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 点击【发放】进行危化品发放； </li>
                    <li> 已发放危化品进入【已发放危化品】栏中。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :span-method="objectSpanMethod"
            header-cell-class-name="headerClassName">
            <el-table-column prop="UserName" label="领用人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="BatchNo" label="申领批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="领用数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ApplyDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.ApplyDate ? row.ApplyDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Mobile" label="手机号码" min-width="120" align="center"></el-table-column>
            <el-table-column prop="UseTime" label="使用时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.UseTime ? row.UseTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="用途" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="WithUserName" label="同领用人" min-width="110" align="center">
                <template #default="{ row }">
                    {{ row.WithUserName || '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="危化品发放" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">发放</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="危化品发放">
            <template #content>
                <el-form style="min-width: 400px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>

                    <el-form-item label="危化品信息 ：">
                        <span style="color: #999999;"> {{ formData.Name }} {{ formData.Model }} {{ formData.Brand }}
                            {{ formData.Num }}{{ formData.UnitName }}</span>
                    </el-form-item>
                    <el-form-item label="实际领用数量：" prop="Num">
                        <el-input v-model="formData.Num" @input="limitInput($event, 'Num')"
                            style="width: 240px"></el-input>
                        <span class="marginLeft"> 单位： (<span>{{ formData.UnitName }}</span>) </span>
                    </el-form-item>
                    <el-form-item label="领用人手机号码：">
                        <el-input v-model="formData.Mobile" style="width: 240px" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="领用码" prop="CheckCode">
                        <el-input v-model="formData.CheckCode" @input="integerLimitInput($event, 'CheckCode')"
                            auto-complete="off" placeholder="请输入领用码" style="width: 240px;"></el-input>
                        <el-button class="marginLeft" type="primary" plain :disabled="isCode" @click="getCode(1)"><span
                                v-if="!isCode">
                                {{ codeText }}</span>
                            <el-countdown v-else format="ss" :value="time" @finish="finishChange(1)" suffix="秒后重新获取"
                                :value-style="{ color: '#606266', fontSize: '14px' }" />
                        </el-button>
                    </el-form-item>
                    <el-form-item label="同领用人手机号码：" v-if="formData.ApplyMode == 2">
                        <el-input v-model="formData.WithMobile" style="width: 240px" readonly></el-input>
                    </el-form-item>
                    <el-form-item label="领用码" prop="WithCheckCode" v-if="formData.ApplyMode == 2">
                        <el-input v-model="formData.WithCheckCode" @input="integerLimitInput($event, 'WithCheckCode')"
                            auto-complete="off" placeholder="请输入领用码" style="width: 240px;"></el-input>
                        <el-button class="marginLeft" type="primary" plain :disabled="withIsCode"
                            @click="getCode(2)"><span v-if="!withIsCode">
                                {{ withOdeText }}</span>
                            <el-countdown v-else format="ss" :value="withTime" @finish="finishChange(2)" suffix="秒后重新获取"
                                :value-style="{ color: '#606266', fontSize: '14px' }" />
                        </el-button>
                    </el-form-item>
                    <el-form-item label="同发放人：" v-if="bconfigSet == 2" prop="WithUserId">
                        <el-select v-model="formData.WithUserId" filterable style="width: 240px">
                            <el-option v-for="item in memberUserList" :key="item.MemberUserId"
                                :label="item.MemberUserName" :value="item.MemberUserId" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 发放危化品 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
:deep(.el-statistic__suffix) {
    font-size: 14px;
}

.marginLeft {
    margin-left: 10px;
}
</style>