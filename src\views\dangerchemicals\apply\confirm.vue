<script setup>
defineOptions({
    name: 'dangerchemicalsapplyconfirm'
});
import {
    View, Select
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    DcapplyView, DcApplyAudit, DcApplyAuditListFind, DcApplySchoolMaterialFind, DcApplyAdjust, DcApplyConfirmDetailDelete, DcApplyAdjustFind, DcApplyConfirm
} from '@/api/dangerchemicals.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore, limit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'

const route = useRoute()
const userStore = useUserStore()
const purchase = ref({})
const auditStatuz = ref('30')
const isConfirmDetail = ref(false)
const activeNames = ref(['information', 'audit'])
const formData = ref({ Remark: '同意' })
const refForm = ref()
const ruleForm = {
    Remark: [
        { required: true, message: '请填写意见', trigger: 'change' },
    ]
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleViewData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)

    nextTick(() => {
        if (!route.query.isTagRouter) {
            isConfirmDetail.value = false
            HandleViewData()
        }
    })
})
const HandleSubmit = () => {
    let data = {
        id: route.query.id,
        num: formData.value.NUm,
        remark: formData.value.Remark,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcApplyConfirm(data).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '提交成功')
                let tagsList = userStore.tagsList
                tagsList = tagsList.filter(t => t.path != route.path)
                userStore.setTagsList(tagsList)
                router.push({ path: '/dangerchemicals/apply/auditlist@p=30' })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
const spApplyNum = ref(0)
// 节点容器样式
const spSplitNum = computed(() => {
    let arr = selectRows.value.filter(item => item.ApplyNum).map(item => item.ApplyNum)
    let sum = arr.reduce((acc, cur) => Number(acc) + Number(cur), 0);
    return sum || 0
})
// 详情
const HandleViewData = () => {
    DcapplyView({ id: route.query.id, viewType: 20 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            purchase.value = rows;
            formData.value.num = Math.round(rows.Num - rows.ConfirmedNum)
            if (rows.SchoolMaterialBrandId && rows.StockNum < (rows.Num - rows.ConfirmedNum)) {
                DcApplyAdjustFindUser(rows.Id, true)
            }
            spApplyNum.value = formData.value.num
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入框限制：输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}

const tableDialogVisible = ref(false)
const tableDialogData = ref([])

// 查看危化品目录
const HandleDetail = (userId) => {
    let data = {
        StatuzOr: true,
        ApplyType: 1,
        UserId: userId,
        pageIndex: 1,
        pageSize: 100000,
        sortModel: [{ SortCode: "BatchNo", SortType: "ASC" }]
    }
    DcApplyAuditListFind(data).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableDialogData.value = rows.data;
            tableDialogVisible.value = true

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const tableEditDialogVisible = ref(false)
const tableEditDialogData = ref([])
const selectRows = ref([])
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
//输入框限制：输入两位小数
const limitMumInput = (val, row, index, name) => {
    tableEditDialogData.value[index][name] = limit(val);
}
// 调整
const HandleAdjust = () => {
    let data = {
        IsMayUse: 1,
        SchoolCatalogId: purchase.value.SchoolCatalogId,
        pageIndex: 0,
        pageSize: 100000,
        sortModel: [{ SortCode: "SchoolMaterialModelId", SortType: "asc" }, { SortCode: "ValidDate", SortType: "asc" }]
    }
    DcApplySchoolMaterialFind(data).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableEditDialogData.value = rows.data;
            tableEditDialogVisible.value = true
            tableEditDialogData.value.forEach(item => {
                item.ApplyNum = ''
            })

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const tableData = ref([])
const DcApplyAdjustFindUser = (id, isShow) => {
    let data = {
        IsConfirmed: false,
        ApplyId: id,
        pageIndex: 0,
        pageSize: 100000,
        sortModel: [{ SortCode: "Id", SortType: "asc" }]
    }

    DcApplyAdjustFind(data).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value.StockNum = rows.data
            if (isShow && rows.data.length > 0) {
                isConfirmDetail.value = true
            } else {
                isConfirmDetail.value = false
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const HandleDel = (row) => {
    ElMessageBox.confirm('您确认要删除该数据吗？')
        .then(() => {
            DcApplyConfirmDetailDelete({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    DcApplyAdjustFindUser()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 危化品数量调整
const HandleAdjustSubmit = () => {
    console.log("spSplitNum", spSplitNum, spSplitNum.value)
    let find = selectRows.value.find(item => !item.ApplyNum)
    if (find) {
        ElMessage.error('已选择的危化品请填写大于0的领用数量')
        return
    }
    if (spApplyNum.value < spSplitNum.value) {
        ElMessage.error('配货数量不得大于申请数量')
        return
    }

    let list = selectRows.value.map(item => {
        return {
            ApplyId: purchase.value.Id,
            Num: item.ApplyNum,
            SchoolMaterialId: item.Id
        }
    })


    DcApplyAdjust(list).then((res) => {
        if (res.data.flag == 1) {
            DcApplyAdjustFindUser()
            ElMessage.success(res.data.msg || '删除成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
</script>
<template>
    <div class="demo-collapse">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="领用信息" name="information">
                <template #title>
                    <div style="padding-left: 10px;">
                        领用信息
                    </div>
                </template>
                <div class="content">
                    <div><span class="content_label">领用人：</span>{{ purchase.UserName }}
                        <el-button type="primary" :icon="View" size="small"
                            @click="HandleDetail(purchase.UserId)">查看危化品目录</el-button>
                    </div>
                    <div><span class="content_label">申领批次：</span>{{ purchase.BatchNo }}</div>
                    <div><span class="content_label">危化品名称：</span>{{ purchase.Name }}</div>
                    <div><span class="content_label">数量：</span>{{ purchase.Num }} &nbsp;&nbsp;({{ purchase.UnitName }})
                    </div>
                    <div><span class="content_label">规格属性：</span>{{ purchase.Model ? purchase.Model : '--' }}</div>

                    <div
                        v-if="purchase.SchoolMaterialBrandId && purchase.StockNum < (purchase.Num - purchase.ConfirmedNum)">
                        <div style="width: 500px;"><span class="content_label">品牌：</span>{{ purchase.Brand }}
                            <!-- <span style=" padding-left: 30px;"> {{ purchase.StockNum }}</span> -->
                            <span style="color: #F56C6C;padding-left: 30px;">
                                库存数量缺： {{ Math.round(purchase.Num - purchase.ConfirmedNum - purchase.StockNum) }}
                            </span>
                        </div>
                    </div>
                    <div v-else>
                        <div style="width: 500px;"><span class="content_label">品牌：</span>
                            {{ purchase.Brand ? purchase.Brand : '--' }}
                            <span style="color: #999;padding-left: 30px;">
                                {{ purchase.SchoolMaterialBrandId || purchase.StockNum < (purchase.Num -
                                    purchase.ConfirmedNum) ? "" : "（品牌由系统根据有效期及数量自动分配）" }} </span>
                        </div>
                    </div>
                    <div class="content_label_div"><span class="content_label">使用时间：</span>
                        {{ purchase.UseTime ? purchase.UseTime.substring(0, 16) : '--' }}</div>
                    <div class="content_label_div"><span class="content_label">用途：</span>{{ purchase.Remark ?
                        purchase.Remark : '无' }}</div>
                </div>
            </el-collapse-item>
            <el-collapse-item title="领用审核" name="audit">
                <template #title>
                    <div style="padding-left: 10px;">
                        领用确认
                    </div>
                </template>
                <el-form style="min-width: 120px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <el-form-item label="确认意见：" prop="Remark">
                        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="formData.Remark"
                            style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item label="确认数量：" prop="NUm">
                        <el-input v-model="formData.NUm" @input="limitInput($event, 'NUm')"
                            style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item label="配货："
                        v-if="purchase.SchoolMaterialBrandId && purchase.StockNum < (purchase.Num - purchase.ConfirmedNum)">
                        <el-button type="primary" size="small" @click="HandleAdjust">调整</el-button>
                    </el-form-item>
                </el-form>

                <el-table v-if="isConfirmDetail" ref="refTable" :data="tableData" border stripe
                    header-cell-class-name="headerClassName">
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                        <template #default="{ row }">
                            {{ row.Brand || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="Num" label="领用数量" min-width="120" align="right"></el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
                    <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                        <template #default="{ row }">
                            {{ row.CollarRegDate ? row.CollarRegDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="ValidDate" label="有效期至" min-width="120" align="center">
                        <template #default="{ row }">
                            {{ row.ValidDate ? row.ValidDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="100" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div style="padding-left: 120px;">
                    <el-button type="primary" :icon="Select" @click="HandleSubmit"> 提交 </el-button>
                </div>
            </el-collapse-item>
        </el-collapse>


        <app-box v-model="tableDialogVisible" :width="660" :lazy="true" title="申领明细">
            <template #content>
                <div>
                    <el-table ref="refTableData" :data="tableDialogData" border stripe max-height="360px"
                        header-cell-class-name=" headerClassName">
                        <el-table-column prop="BatchNo" label="申请批次" min-width="140" align="center"></el-table-column>
                        <el-table-column prop="Name" label="危化品名称" min-width="140"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="Statuz" label="状态" min-width="100" align="center"></el-table-column>
                        <el-table-column prop="Num" label="数量" min-width="120" align="right"></el-table-column>
                        <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
                        <template #empty>
                            <el-empty description="没有数据"></el-empty>
                        </template>
                    </el-table>
                </div>
            </template>
        </app-box>
        <app-box v-model="tableEditDialogVisible" :width="680" :lazy="true" title="配货">
            <template #content>
                <div>
                    <el-table ref="refTableData" :data="tableEditDialogData" border stripe max-height="360px"
                        @selection-change="HandleSelectChange" header-cell-class-name=" headerClassName">
                        <el-table-column type="selection" width="50"></el-table-column>
                        <el-table-column prop="Model" label="规格属性" min-width="160"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                            <template #default="{ row }">
                                {{ row.Brand || '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="StockNum" label="库存数量" min-width="120" align="right"></el-table-column>
                        <el-table-column prop="ApplyNum" label="领用数量" min-width="120" align="right">
                            <template #default="{ row, $index }">
                                <el-input v-model="row.ApplyNum"
                                    @input="limitMumInput($event, row, $index, 'ApplyNum')"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
                        <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                            <template #default="{ row }">
                                {{ row.CollarRegDate ? row.CollarRegDate.substring(0, 10) : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="ValidDate" label="有效期至" min-width="120" align="center">
                            <template #default="{ row }">
                                {{ row.ValidDate ? row.ValidDate.substring(0, 10) : '--' }}
                            </template>
                        </el-table-column>
                        <template #empty>
                            <el-empty description="没有数据"></el-empty>
                        </template>
                    </el-table>
                </div>
                <div style="margin: 5px; text-align: center; font-size: 14px; color: #ff0000">
                    <span> 申请数量：{{ spApplyNum }}{{ purchase.UnitName }}</span>，
                    <span>已配货数量：{{ spSplitNum }}{{ purchase.UnitName }}</span>
                </div>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="tableEditDialogVisible = false">取消</el-button>
                    <el-button type="primary" :disabled="selectRows.length == 0" @click="HandleAdjustSubmit"> 确认调整
                    </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-wrap: wrap;
    color: #333;
    font-size: 14px;
    width: 1000px;
    padding-left: 30px;

    &>div {
        width: 460px;
        padding: 5px 10px;
    }

    .content_label_div {
        width: 600px;
    }

    .content_label {
        display: inline-block;
        width: 120px;
        text-align: right;
        font-weight: bold;
    }

}

:deep(.el-collapse-item__header) {

    font-size: 14px;
    color: #0000ee;
}
</style>