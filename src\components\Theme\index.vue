<script setup>
import { ref, computed, onMounted } from 'vue'

import {
    Sunny
} from '@element-plus/icons-vue'
import { useChangeColor } from '@/utils/theme';
import { Local } from '@/utils/storage';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '@/stores';
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { getLightColor, getDarkColor } = useChangeColor();
// 获取布局配置信息
const getThemeConfig = computed(() => {
    return themeConfig.value;
});

const isDrawer = ref(false)

onMounted(() => {
    setTimeout(() => {
        // 默认样式
        onColorPickerChange();
        // // 灰色模式
        // if (getThemeConfig.value.isGrayscale) onAddFilterChange('grayscale');
        // // 色弱模式
        // if (getThemeConfig.value.isInvert) onAddFilterChange('invert');
        // 深色模式
        if (getThemeConfig.value.isIsDark) onAddDarkChange();
        // // 初始化菜单样式等
        // initSetStyle();
    }, 100);

})
const onLayoutSetingClick = () => {
    isDrawer.value = true
}
const onDrawerClose = () => {
    isDrawer.value = false
}
// 设置主题
const onColorPickerChange = () => {
    if (!getThemeConfig.value.primary) return ElMessage.warning('全局主题 primary 颜色值不能为空');
    // 颜色加深
    document.documentElement.style.setProperty('--el-color-primary-dark-2', `${getDarkColor(getThemeConfig.value.primary, 0.1)}`);
    document.documentElement.style.setProperty('--el-color-primary', getThemeConfig.value.primary);
    // 颜色变浅
    for (let i = 1; i <= 9; i++) {
        document.documentElement.style.setProperty(`--el-color-primary-light-${i}`, `${getLightColor(getThemeConfig.value.primary, i / 10)}`);
    }
    setDispatchThemeConfig();
}
// 切换深色模式
const onAddDarkChange = () => {
    console.log("getThemeConfig.value.isIsDark", getThemeConfig.value.isIsDark, "themeConfig.value", themeConfig.value)
    const body = document.documentElement;
    if (getThemeConfig.value.isIsDark) body.setAttribute('data-theme', 'dark');
    else body.setAttribute('data-theme', '');
    setLocalThemeConfig();
};


// 2、菜单 / 顶栏
const onBgColorPickerChange = (bg) => {
    // topBar   topBarColor  menuBar   menuBarColor
    document.documentElement.style.setProperty(`--next-bg-${bg}`, themeConfig.value[bg]);
    if (bg === 'menuBar') {
        document.documentElement.style.setProperty(`--next-bg-menuBar-light-1`, getLightColor(getThemeConfig.value.menuBar, 0.05));
    }
    setLocalThemeConfig();
    onColorPickerChange();
};



// 触发 store 布局配置更新
const setDispatchThemeConfig = () => {
    setLocalThemeConfig();
    setLocalThemeConfigStyle();
};
// 存储布局配置全局主题样式（html根标签）
const setLocalThemeConfigStyle = () => {
    // Local.set('themeConfigStyle', document.documentElement.style.cssText);

    let str = document.documentElement.style.cssText
    let str1 = str.split('--body-height: auto;').join('');//去除body高度影响
    let cssText = str1.split('--body-height: 100%;').join('');//去除body高度影响
    Local.set('themeConfigStyle', cssText);
};
// 存储布局配置
const setLocalThemeConfig = () => {
    Local.remove('themeConfig');
    Local.set('themeConfig', getThemeConfig.value);
};


// 一键恢复默认
const onResetConfigClick = () => {
    Local.remove('themeConfigStyle');
    Local.remove('themeConfig');
    window.location.reload();
    // @ts-ignore
    // Local.set('version', __NEXT_VERSION__);
};


</script>


<template>
    <div class="seting">
        <div  @click="onLayoutSetingClick">
            <!-- <el-icon>
                <Sunny />
            </el-icon> -->
            <el-tooltip content="布局配置" placement="bottom" :auto-close="3000">
                <SvgIcon name="zhuti" class="svg-icon" color="var(--next-bg-topBarColor)" ></SvgIcon>
            </el-tooltip>
           
        </div>

        <el-drawer title="布局配置" v-model="isDrawer" direction="rtl" destroy-on-close size="260px" :z-index="999"
            @close="onDrawerClose">
            <el-scrollbar class="setingBar">
                <!-- 全局主题 -->
                <el-divider content-position="left">主题色</el-divider>
                <div class="setingBar-flex">
                    <div class="setingBar-flex-label">主题色</div>
                    <div class="setingBar-flex-value">
                        <el-color-picker v-model="getThemeConfig.primary" size="default" @change="onColorPickerChange">
                        </el-color-picker>
                    </div>
                </div>
                <!-- <div class="setingBar-flex mt15">
					<div class="setingBar-flex-label">深色模式</div>
					<div class="setingBar-flex-value">
						<el-switch v-model="getThemeConfig.isIsDark" size="small" @change="onAddDarkChange"></el-switch>
					</div>
				</div> -->

                <!-- 顶栏设置 -->
                <el-divider content-position="left">顶栏设置</el-divider>
                <div class="setingBar-flex">
                    <div class="setingBar-flex-label">顶栏背景</div>
                    <div class="setingBar-flex-value">
                        <el-color-picker v-model="getThemeConfig.topBar" size="default"
                            @change="onBgColorPickerChange('topBar')"> </el-color-picker>
                    </div>
                </div>
                <div class="setingBar-flex">
                    <div class="setingBar-flex-label">顶栏默认字体颜色 </div>
                    <div class="setingBar-flex-value">
                        <el-color-picker v-model="getThemeConfig.topBarColor" size="default"
                            @change="onBgColorPickerChange('topBarColor')"> </el-color-picker>
                    </div>
                </div>

                <!-- 菜单设置 -->
                <el-divider content-position="left">菜单设置</el-divider>
                <div class="setingBar-flex">
                    <div class="setingBar-flex-label">菜单背景</div>
                    <div class="setingBar-flex-value">
                        <el-color-picker v-model="getThemeConfig.menuBar" size="default"
                            @change="onBgColorPickerChange('menuBar')"> </el-color-picker>
                    </div>
                </div>
                <div class="setingBar-flex">
                    <div class="setingBar-flex-label">菜单默认字体颜色</div>
                    <div class="setingBar-flex-value">
                        <el-color-picker v-model="getThemeConfig.menuBarColor" size="default"
                            @change="onBgColorPickerChange('menuBarColor')"> </el-color-picker>
                    </div>
                </div>
                <div class="setingBar-flex">
                    <div class="setingBar-flex-label">菜单高亮背景色</div>
                    <div class="setingBar-flex-value">
                        <el-color-picker v-model="getThemeConfig.menuBarActiveColor" size="default" show-alpha
                            @change="onBgColorPickerChange('menuBarActiveColor')" />
                    </div>
                </div>
                <div class="copy-config">
                    <!-- <el-alert :title="$t('message.layout.tipText')" type="warning" :closable="false"> </el-alert> -->
                    <el-button size="default" class="copy-config-btn-reset" type="info" @click="onResetConfigClick">
                        <!-- <el-icon class="mr5">
							<ele-RefreshRight />
						</el-icon> -->
                        恢复默认
                    </el-button>
                </div>

            </el-scrollbar>
        </el-drawer>
    </div>
</template>

<style scoped lang="scss">
:deep(.el-drawer__header) {

    margin-bottom: 10px !important;
    padding: 0 !important;
}

:deep(.el-drawer__body) {

    padding: 0 !important;
}

.setingBar {
    // height: calc(100vh - 50px);
    padding: 0 15px;

    :deep(.el-scrollbar__view) {
        overflow-x: hidden !important;
    }

    .setingBar-flex {
        display: flex;
        align-items: center;
        height: 32px;
        margin-bottom: 5px;

        &-label {
            flex: 1;
            color: var(--el-text-color-primary);
        }
    }

    .setingBar-flex-label {
        text-align: left;
    }

    .layout-drawer-content-flex {
        overflow: hidden;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        margin: 0 -5px;

        .layout-drawer-content-item {
            width: 50%;
            height: 70px;
            cursor: pointer;
            border: 1px solid transparent;
            position: relative;
            padding: 5px;

            .el-container {
                height: 100%;

                .el-aside-dark {
                    background-color: var(--next-color-seting-header);
                }

                .el-aside {
                    background-color: var(--next-color-seting-aside);
                }

                .el-header {
                    background-color: var(--next-color-seting-header);
                }

                .el-main {
                    background-color: var(--next-color-seting-main);
                }
            }

            .el-circular {
                border-radius: 2px;
                overflow: hidden;
                border: 1px solid transparent;
                transition: all 0.3s ease-in-out;
            }

            .drawer-layout-active {
                border: 1px solid;
                border-color: var(--el-color-primary);
            }

            .layout-tips-warp,
            .layout-tips-warp-active {
                transition: all 0.3s ease-in-out;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                border: 1px solid;
                border-color: var(--el-color-primary-light-5);
                border-radius: 100%;
                padding: 4px;

                .layout-tips-box {
                    transition: inherit;
                    width: 30px;
                    height: 30px;
                    z-index: 9;
                    border: 1px solid;
                    border-color: var(--el-color-primary-light-5);
                    border-radius: 100%;

                    .layout-tips-txt {
                        transition: inherit;
                        position: relative;
                        top: 5px;
                        font-size: 12px;
                        line-height: 1;
                        letter-spacing: 2px;
                        white-space: nowrap;
                        color: var(--el-color-primary-light-5);
                        text-align: center;
                        transform: rotate(30deg);
                        left: -1px;
                        background-color: var(--next-color-seting-main);
                        width: 32px;
                        height: 17px;
                        line-height: 17px;
                    }
                }
            }

            .layout-tips-warp-active {
                border: 1px solid;
                border-color: var(--el-color-primary);

                .layout-tips-box {
                    border: 1px solid;
                    border-color: var(--el-color-primary);

                    .layout-tips-txt {
                        color: var(--el-color-primary) !important;
                        background-color: var(--next-color-seting-main) !important;
                    }
                }
            }

            &:hover {
                .el-circular {
                    transition: all 0.3s ease-in-out;
                    border: 1px solid;
                    border-color: var(--el-color-primary);
                }

                .layout-tips-warp {
                    transition: all 0.3s ease-in-out;
                    border-color: var(--el-color-primary);

                    .layout-tips-box {
                        transition: inherit;
                        border-color: var(--el-color-primary);

                        .layout-tips-txt {
                            transition: inherit;
                            color: var(--el-color-primary) !important;
                            background-color: var(--next-color-seting-main) !important;
                        }
                    }
                }
            }
        }
    }

    .copy-config {
        margin: 10px 0;

        .copy-config-btn {
            width: 100%;
            margin-top: 15px;
        }

        .copy-config-btn-reset {
            width: 100%;
            margin: 10px 0 0;
        }
    }
}
</style>
