<script setup>
defineOptions({
    name: 'processsourceoffunds'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd, DocumentChecked
} from '@element-plus/icons-vue'
import {
    FindChooseColumnPageList, SetSourceFundField, FindPageColumnconfigList, ListColumnSet,
    ListColumnGetByid, ListColumnInsertUpdate, FindChooseFundpageList, ChooseFundSave, ListColumnBatchUpdate
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { tagsListStore, integerLimit } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10, ShowAll: false })
const tableFilters = ref({ pageIndex: 1, pageSize: 10, isFirst: true })
const treasuryFilters = ref({ pageIndex: 1, pageSize: 10, isFirst: true })
const FieldTypeList = ref([{ value: '1', label: '表格列' }, { value: '2', label: '查询条件' }])
const yesNoList = ref([{ value: '1', label: '是' }, { value: '2', label: '否' }])
const tableData = ref([])
const tableTotal = ref(0)
const tableDialogData = ref([])
const tableDialogTotal = ref(0)
const treasuryDialogData = ref([])
const treasuryDialogTotal = ref(0)
const refTable = ref()
const refTreasuryTable = ref()
const refTableData = ref()
const selectRows = ref([])//选中的行
const fieldTypeList = ref([{ value: 1, label: '表格列' }, { value: 2, label: '查询条件' }])
const titleStyleList = ref([{ value: 'left', label: '居左' }, { value: 'center', label: '居中' }, { value: 'right', label: '居右' }])
const dateDisplayList = ref([
    { value: 10, label: '年-月-日' },
    { value: 4, label: '年份' },
    { value: 7, label: '年-月' },
    { value: 13, label: '年-月-日 时' },
    { value: 16, label: '年-月-日 时:分' },
    { value: 19, label: '年-月-日 时:分:秒' },
])
const useMenuTypeList = ref([])
const nodeList = ref([])
const isEditor = ref(false)
const configType = ref(1)
const dialogVisible = ref(false)
const tableDialogVisible = ref(false)
const treasuryDialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    FieldType: [
        { required: true, message: '请选择查询条件数据类型', trigger: 'change' },
    ],
    IsDateRange: [
        { required: true, message: '请选择是否为日期区间查询', trigger: 'change' },
    ],
    ColumnFieldType: [
        { required: true, message: '请选择列展示数据类型', trigger: 'change' },
    ],
    FieldCode: [
        { required: true, message: '请输入字段Code', trigger: 'blur' },
    ],
    FieldName: [
        { required: true, message: '请输入字段名称', trigger: 'blur' },
    ],
    ListFieldType: [
        { required: true, message: '请选择是否是查询条件', trigger: 'change' },
    ],
    CalculateFun: [
        { required: true, message: '请选择绑定事件名', trigger: 'change' },
    ],
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        filters.value.ProcessId = route.query.ProcessId
        tableFilters.value.ModuleId = route.query.ModuleId
        HandleTableData(true);
    }
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    isEditor.value = false
    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.ProcessId = route.query.ProcessId
            tableFilters.value.ModuleId = route.query.ModuleId
            HandleTableData(true);
        }
    })
})
// 列表多选
const HandleSelectChange = (selection) => {
    console.log(selection)
    selectRows.value = selection
}

// 修改
const HandleEdit = (row) => {
    ListColumnGetByidUser(row.Id)
    dialogVisible.value = true
}

// 启用禁用
const HandleEnable = (row) => {
    let msg = ''
    if (row.Statuz == 1) {
        msg = '确定要禁用该流程吗?'
    } else {
        msg = '确定要启用该流程吗?'
    }
    ElMessageBox.confirm(msg)
        .then(() => {
            ListColumnSet({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleSearch()
                    ElMessage.success(res.data.msg || '设置成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
}

//创建/修改选用组织  提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        ListColumnInsertUpdateUser()
    })
}
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    FindPageColumnconfigList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
            if (isFirst) {
                // titleStyleList.value = other.listTitleStyle || [];//内容对其方式
                useMenuTypeList.value = other.listUseMenuType || [];//列类型
                useMenuTypeList.value = useMenuTypeList.value.filter(t => t.Value != 5);// 过滤掉按钮
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    filters.value.ShowAll = false
    nextTick(() => {
        HandleTableData()
    })
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 下拉框选中搜索
const filtersChange = () => {
    HandleTableData()
}
// 选择列弹窗
const HandleTreasury = (e) => {
    FindChooseFundpageListUser()
    treasuryDialogVisible.value = true
}

//选择列列表
const FindChooseFundpageListUser = () => {
    FindChooseFundpageList(treasuryFilters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            treasuryDialogData.value = rows.data;
            treasuryDialogTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 选择列分页
const handleTreasuryPage = (val) => {
    FindChooseFundpageListUser()
}
//选择列  提交
const HandleTreasurySubmit = () => {
    ChooseFundSaveUser()
}
// 选择列提交提交
const ChooseFundSaveUser = () => {
    //根据数据集合提取需要的属性
    let ListColumn = selectRows.value.map(({ Id }) => ({ Id }));
    let formData = {
        ModuleId: route.query.ModuleId,//模块Id
        ProcessId: route.query.ProcessId,//流程Id
        ModeType: 3,
        ListFieldType: configType.value,//配置类型，1：表格列 2：查询条件
        ListColumn: ListColumn
    }
    ChooseFundSave(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '提交成功')
            treasuryDialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const pageColumnConfigId = ref()//需要绑定的Id（列表项Id）
const processFieldId = ref()//选择被绑定的Id
const radio = ref(false)//选择被绑定的Id
const checkId = ref('')//选择被绑定的Id
// 绑定字段
const HandleBind = (row) => {
    console.log('row', row);
    radio.value = false
    tableDialogVisible.value = true
    pageColumnConfigId.value = row.Id
    checkId.value = row.ProcessFieldId
    FindChooseColumnPageListUser()
}

//绑定字段列表
const FindChooseColumnPageListUser = () => {
    FindChooseColumnPageList(tableFilters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableDialogData.value = rows.data;
            if (checkId.value) {
                nextTick(() => {
                    radio.value = checkId.value
                    processFieldId.value = checkId.value
                    let row = tableDialogData.value.filter(item => item.Id == checkId.value)[0]
                    refTableData.value.setCurrentRow(row)
                })
            }
            tableDialogTotal.value = rows.dataCount
            nodeList.value = other.listNode || [];//节点名称
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 绑定字段列表搜索
const HandleDialogSearch = () => {
    tableFilters.value.pageIndex = 1
    FindChooseColumnPageListUser()
}
// 绑定字段列表重置
const HandleDialogReset = () => {
    tableFilters.value.pageIndex = 1
    tableFilters.value.ProcessNodeId = undefined
    tableFilters.value.Key = undefined
    FindChooseColumnPageListUser()
}
// 绑定字段列表分页
const handleTablePage = (val) => {
    FindChooseColumnPageListUser()
}
// 绑定字段列表下拉框选中搜索
const filtersTableChange = () => {
    FindChooseColumnPageListUser()
}

// 表格单选
const HandleRadioChange = (row) => {
    // console.log('row', row);
    processFieldId.value = row.Id
}
// 表格单选
const handleCurrentChange = (row) => {
    console.log('row', row);
    if (row) {
        radio.value = row.Id
        processFieldId.value = row.Id
    }
}

//绑定字段列表提交
const HandleTableSubmit = () => {
    SetSourceFundField({ pageColumnConfigId: pageColumnConfigId.value, processFieldId: processFieldId.value }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '提交成功')
            tableDialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 添加/修改提交
const ListColumnInsertUpdateUser = () => {
    let formData = {
        Id: dialogData.value.Id,//字段Id
        FieldName: dialogData.value.FieldName,//字段名称
        FieldType: dialogData.value.FieldType,//查询条件数据类型
        ColumnFieldType: dialogData.value.ColumnFieldType,//列展示数据类型
        Width: dialogData.value.Width,//宽度
        ContentStyle: dialogData.value.ContentStyle,//内容对齐方式
        IsNeedSum: dialogData.value.IsNeedSum,//底部是否显示汇总
        ListFieldType: Number(dialogData.value.ListFieldType),//配置类型： 1：列 2：查询条件
        IsDateRange: dialogData.value.IsDateRange,//是否为日期区间查询： 1：是 2：否
        ConditionWord: dialogData.value.ConditionWord,//查询条件默认文字
        Sort: dialogData.value.Sort || 0,//排序值
        DateDisplay: dialogData.value.DateDisplay,//日期格式
    }
    ListColumnInsertUpdate(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const ListColumnGetByidUser = (id) => {
    ListColumnGetByid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows
            dialogData.value.IsNeedSum = String(rows.IsNeedSum)
            dialogData.value.ListFieldType = String(rows.ListFieldType)
            if (rows.ColumnFieldType == 4) {
                dialogData.value.DateDisplay = rows.DateDisplay || 10
                if (dialogData.value.IsDateRange == 0) dialogData.value.IsDateRange == 1
            } else {
                dialogData.value.DateDisplay = undefined
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 列表上修改数据##################################### 
//输入框限制：输入整数
const integerLimitTableInput = (val, row, index, name) => {
    tableData.value[index][name] = integerLimit(val);
}
// 批量保存
const HandleSave = (row) => {
    let arr = tableData.value.filter(item => item.ColumnFieldType != 5 && item.FieldCode)
    let list = arr.map(({ Id, FieldName, ColumnFieldType, Width, ContentStyle, Sort }) => ({ Id, FieldName, ColumnFieldType, Width, ContentStyle, Sort }));

    ListColumnBatchUpdate(list).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            // HandleTableData()
            isEditor.value = false
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleTreasury">选择列</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem flexOperation">
                        <el-input v-model.trim="filters.Key" clearable placeholder="字段Code/字段名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem" style="margin-left: 10px;margin-right: 10px !important;">
                        <el-checkbox v-model="filters.ShowAll" label="全部" @change="filtersChange">
                        </el-checkbox>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem" label="快速编辑：">
                        <el-switch v-model="isEditor" />
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" v-if="isEditor" :icon="DocumentChecked"
                            @click="HandleSave(row)">批量保存</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Code" label="资金库字段名称" min-width="140" align="center"></el-table-column>
            <el-table-column prop="ShowName" label="资金库字段显示名称" min-width="160"></el-table-column>
            <el-table-column prop="FieldCode" label="字段Code" min-width="140" align="center"></el-table-column>
            <el-table-column prop="FieldName" label="字段名称" min-width="160"></el-table-column>
            <el-table-column prop="StrFieldType" label="列展示类型" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="!isEditor || row.ColumnFieldType == 5 || !row.FieldCode || row.ListFieldType == 2">
                        {{ row.ListFieldType == 2 ? '--' : row.ColumnFieldType == 1 ? '数字' : row.ColumnFieldType == 2 ?
                            '金额' : row.ColumnFieldType == 3
                                ? '文本' : row.ColumnFieldType == 4 ? '日期' : '按钮' }}
                    </span>
                    <el-select v-else v-model="row.ColumnFieldType">
                        <el-option v-for="item in useMenuTypeList" :key="item.Value" :label="item.Description"
                            :value="item.Value" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="ContentStyle" label="内容对其方式" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="!isEditor || row.ColumnFieldType == 5 || !row.FieldCode || row.ListFieldType == 2">
                        {{ row.ListFieldType == 2 ? '--' : row.ContentStyle == 'left' ? "居左" : row.ContentStyle ==
                            'center' ? "居中" : "居右" }}
                    </span>
                    <el-select v-else v-model="row.ContentStyle">
                        <el-option v-for="item in titleStyleList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="ListFieldType" label="配置类型" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.ListFieldType == 1 ? "表格列" : "查询条件" }}
                </template>
            </el-table-column>
            <el-table-column prop="Width" label="宽度" min-width="90" align="center">
                <template #default="{ row, $index }">
                    <span v-if="!isEditor || row.ColumnFieldType == 5 || !row.FieldCode">{{ row.Width }}</span>
                    <el-input v-else v-model="row.Width"
                        @input="integerLimitTableInput($event, row, $index, 'Width')"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="Sort" label="排序值" min-width="90" align="center">
                <template #default="{ row, $index }">
                    <span v-if="!isEditor || row.ColumnFieldType == 5 || !row.FieldCode">{{ row.Sort }}</span>
                    <el-input v-else type="number" v-model="row.Sort"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="80" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.Statuz == 1 ? 'success' : 'danger'" disable-transitions>
                        {{ row.Statuz != 0 ? "启用" : "禁用" }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="绑定字段" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link v-if="row.Statuz == 1" @click="HandleBind(row)">绑定</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link v-if="row.Statuz == 1" @click="HandleEnable(row)">禁用</el-button>
                    <el-button type="primary" link v-else @click="HandleEnable(row)">启用</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <!-- 选择列 -->
        <app-box v-model="treasuryDialogVisible" :width="960" :lazy="true" title="选择列">
            <template #content>
                <div style="height: 550px;">
                    <el-row class="navFlexBox">
                        <el-col>
                            <el-form @submit.prevent :inline="true" class="flexBox">
                                <el-form-item class="flexItem" label="配置类型：">
                                    <el-radio-group v-model="configType">
                                        <el-radio v-for="item in fieldTypeList" :key="item.value" :value="item.value">
                                            {{ item.label }}</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                    <el-table ref="refTreasuryTable" :data="treasuryDialogData" height="480" highlight-current-row
                        border stripe @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
                        <el-table-column type="selection" width="50"></el-table-column>
                        <el-table-column prop="Code" label="资金库字段" min-width="140"></el-table-column>
                        <el-table-column prop="ShowName" label="资金库字段显示名称" min-width="160"></el-table-column>
                        <el-table-column prop="ShowType" label="显示类型" min-width="120">
                            <template #default="{ row }">
                                {{ row.ShowType == 1 ? '数字' : row.ShowType == 2 ? '金额' : row.ShowType == 3 ? '文本' : '日期'
                                }}
                            </template>
                        </el-table-column>
                        <template #empty>
                            <el-empty description="没有数据"></el-empty>
                        </template>
                    </el-table>
                    <table-page :total="treasuryDialogTotal" v-model:pageIndex="treasuryFilters.pageIndex"
                        v-model:pageSize="treasuryFilters.pageSize" @handleChange="handleTreasuryPage" />
                </div>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="treasuryDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleTreasurySubmit" :disabled="selectRows.length == 0"> 提交
                    </el-button>
                </span>
            </template>
        </app-box>
        <!-- 绑定字段列表 -->
        <app-box v-model="tableDialogVisible" :width="960" :lazy="true" title="选择列">
            <template #content>
                <div style="height: 550px;">
                    <el-row class="navFlexBox" style="margin-top: 0;">
                        <el-col>
                            <el-form @submit.prevent :inline="true" :model="tableFilters" class="flexBox">
                                <el-form-item label="" class="flexItem">
                                    <el-select v-model="tableFilters.ProcessNodeId" clearable placeholder="节点名称"
                                        @change="filtersTableChange" style="width: 160px">
                                        <el-option v-for="item in nodeList" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="" class="flexItem">
                                    <el-input v-model.trim="tableFilters.Key" clearable placeholder="字段Code/字段名称"
                                        style="width: 240px"></el-input>
                                </el-form-item>
                                <el-form-item class="flexItem">
                                    <el-button type="primary" :icon="Search" @click="HandleDialogSearch">搜索</el-button>
                                    <el-button :icon="Refresh" @click="HandleDialogReset">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                    <el-table ref="refTableData" :data="tableDialogData" height="480" highlight-current-row border
                        stripe @current-change="handleCurrentChange" header-cell-class-name="headerClassName">
                        <el-table-column label="" width="50" align="center">
                            <template #default="{ row }">
                                <el-radio v-model="radio" :value="row.Id" @change="HandleRadioChange(row)"></el-radio>
                            </template>
                        </el-table-column>
                        <el-table-column prop="FieldCode" label="字段Code" min-width="120"></el-table-column>
                        <el-table-column prop="FieldName" label="字段名称" min-width="120"></el-table-column>
                        <el-table-column prop="NodeName" label="节点名称" min-width="120"></el-table-column>
                        <el-table-column prop="NodeShowName" label="节点显示名称" min-width="120"></el-table-column>
                        <template #empty>
                            <el-empty description="没有数据"></el-empty>
                        </template>
                    </el-table>
                    <table-page :total="tableDialogTotal" v-model:pageIndex="tableFilters.pageIndex"
                        v-model:pageSize="tableFilters.pageSize" @handleChange="handleTablePage" />
                </div>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="tableDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleTableSubmit" :disabled="processFieldId == '0'"> 提交
                    </el-button>
                </span>
            </template>
        </app-box>
        <!-- 修改列 -->
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="修改列">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                    status-icon>
                    <el-form-item label="查询条件数据类型：" prop="FieldType" v-if="dialogData.ListFieldType == 2">
                        <el-select v-model="dialogData.FieldType" style="width: 80%">
                            <el-option v-for="item in useMenuTypeList" :key="item.Value" :label="item.Description"
                                :value="item.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="列展示数据类型：" prop="ColumnFieldType" v-if="dialogData.ListFieldType == 1">
                        <el-select v-model="dialogData.ColumnFieldType" style="width: 80%">
                            <el-option v-for="item in useMenuTypeList" :key="item.Value" :label="item.Description"
                                :value="item.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="字段Code：">
                        <el-input v-model="dialogData.FieldCode" disabled style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="字段名称：">
                        <el-input v-model="dialogData.FieldName" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="宽度：">
                        <el-input v-model="dialogData.Width" @input="integerLimitInput($event, 'Width')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="内容对齐方式：" v-if="dialogData.ListFieldType == '1'">
                        <el-radio-group v-model="dialogData.ContentStyle" style="width: 80%">
                            <el-radio v-for="item in titleStyleList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="配置类型：" prop="ListFieldType">
                        <el-radio-group v-model="dialogData.ListFieldType" style="width: 80%">
                            <el-radio v-for="item in FieldTypeList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否为时间区间查询：" prop="IsDateRange"
                        v-if="dialogData.ListFieldType == 2 && dialogData.FieldType == 4">
                        <el-radio-group v-model="dialogData.IsDateRange" style="width: 80%">
                            <el-radio :value="1"> 是</el-radio>
                            <el-radio :value="2"> 否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="日期格式：" v-if="dialogData.ColumnFieldType == 4">
                        <el-select v-model="dialogData.DateDisplay" style="width: 80%">
                            <el-option v-for="item in dateDisplayList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="查询条件默认文字：" v-if="dialogData.ListFieldType == '2'">
                        <el-input v-model="dialogData.ConditionWord" placeholder="不填写默认字段名称"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="底部是否显示汇总：" v-if="dialogData.ListFieldType == '1'">
                        <el-radio-group v-model="dialogData.IsNeedSum" style="width: 80%">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item> -->
                    <el-form-item label="排序值：">
                        <el-input type="number" v-model="dialogData.Sort" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.el-table {
    :deep(.el-radio) {
        height: 20px;
    }
}
</style>