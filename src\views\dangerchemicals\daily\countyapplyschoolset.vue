<script setup>
defineOptions({
    name: 'dangerchemicalsdailycountyapplyschoolset'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { DcGovernDeclareUnitConfigFind, DcGovernDeclareUnitConfigSet, DcGovernDeclareUnitConfigBatchSet } from '@/api/daily.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页  
import AppBox from "@/components/Approve/AppBox.vue";
// import {  } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const unitType = ref(userStore.userInfo.UnitType)
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData();
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const HandleTableData = () => {
    DcGovernDeclareUnitConfigFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data;
            tableData.value = rows.data
            tableTotal.value = rows.dataCount

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.SchoolName = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)

//验证
const ruleForm = {
    Statuz: [
        { required: true, message: '请选择是否存在危化品管理', trigger: 'change' },
    ]
}

const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 批量设置
const HandleBatchSet = () => {
    dialogData.value = {}
    dialogVisible.value = true
}

// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        let data = {
            SchoolIds: selectRows.value.map(item => item.Id).join(','),
            isNeedReporting: dialogData.value.Statuz
        }
        DcGovernDeclareUnitConfigBatchSet(data).then(res => {
            if (res.data.flag == 1) {
                dialogVisible.value = false
                ElMessage.success(res.data.msg || '设置成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

// 直接设置
const HandleStatuzSet = (row, statuz) => {
    let data = {
        id: row.Id,
        isNeedReporting: statuz
    }
    DcGovernDeclareUnitConfigSet(data).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="unitType == 2 || dangerChemicalsLevel == unitType">
                        <el-button type="success" :icon="Setting" :disabled="selectRows.length == 0"
                            @click="HandleBatchSet">批量设置</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="unitType == 2 || dangerChemicalsLevel == unitType"></div>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.SchoolName" placeholder="学校名称" style="width: 180px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="SchoolName" label="" width="300" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Statuz" label="是否存在危化品管理" width="160" align="center">
                <template #default="{ row }">
                    <el-radio-group v-model="row.Statuz">
                        <el-radio class="radio" :value="1" @click="HandleStatuzSet(row, 1)">是</el-radio>
                        <el-radio class="radio" :value="0" @click="HandleStatuzSet(row, 0)">否</el-radio>
                    </el-radio-group>
                </template>
            </el-table-column>
            <el-table-column prop="SchoolName111" label=""></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="设置是否存在危化品管理">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px"
                    status-icon>
                    <el-form-item label="是否存在危化品管理：" prop="Statuz">
                        <el-radio-group v-model="dialogData.Statuz">
                            <el-radio :value="1" label="是"> </el-radio>
                            <el-radio :value="0" label="否"> </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>

</template>
<style lang="scss" scoped></style>