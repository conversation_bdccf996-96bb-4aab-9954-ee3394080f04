<script setup>
defineOptions({
    name: 'workflowformdraggable'
});
import { ref, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import AppFormDraggable from "@/components/Approve/AppFormDraggable/AppFormDraggable.vue";
import { tagsListStore } from "@/utils/index.js";
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const nodeId = ref(route.query.id)
const userComponents = ref([]);

onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    nodeId.value = route.query.id
})
</script>
<template>
    <app-form-draggable :nodeId="nodeId" :userComponents="userComponents"></app-form-draggable>
</template>
<style lang="scss" scoped></style>