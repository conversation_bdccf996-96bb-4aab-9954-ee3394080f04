import request from '@/utils/request.js'

// 采购申请- 采购申请列表（学校、区县同用）
export const GetUniformBuyList = (params) => {
  return request.post('/api/hyun/xuniformbuy/getuniformbuylist', params)
}
// 采购申请-保存修改采购申请
export const UniformBuySave = (params) => {
  return request.post('/api/hyun/xuniformbuy/uniformbuysave', params)
}
// 采购申请-根据Id获取采购申请数据信息
export const UniformBuyGetById = (params) => {
  return request.get('/api/hyun/xuniformbuy/uniformbuygetbyid', { params: params })
}
// 采购申请-提交采购申请
export const UniformBuySubmit = (params) => {
  return request.get('/api/hyun/xuniformbuy/uniformbuysubmit', { params: params })
}
// 采购申请-采购申请删除
export const UniformBuyDeleteById = (params) => {
  return request.delete('/api/hyun/xuniformbuy/uniformbuydeletebyid', { params: params })
}
// 采购申请-采购申请审核
export const UniformBuyAudit = (params) => {
  return request.post('/api/hyun/xuniformbuy/uniformbuyaudit', params)
}
// 采购申请-采购申请撤销、退回
export const UniformBuyRevoke = (params) => {
  return request.post('/api/hyun/xuniformbuy/uniformbuyrevoke', params)
}
// 采购申请-校服采购删除附件
export const BuyDelattachmentbyid = (params) => {
  return request.delete('/api/hyun/xuniformbuy/delattachmentbyid', { params: params })
}

//招标结果招标结果删除附件
export const BiddingDelattachmentbyid = (params) => {
  return request.delete('/api/hyun/xuniformbidding/delattachmentbyid', { params: params })
}
//招标结果-招标结果列表
export const GetUniformBiddingList = (params) => {
  return request.post('/api/hyun/xuniformbidding/getuniformbiddinglist', params)
}
//招标结果-保存修改招标结果
export const UniformBiddingSave = (params) => {
  return request.post('/api/hyun/xuniformbidding/uniformbiddingsave', params)
}
//招标结果-根据Id获取招标结果数据信息
export const UniformBiddingGetById = (params) => {
  return request.get('/api/hyun/xuniformbidding/uniformbiddinggetbyid', { params: params })
}
//招标结果-提交招标结果
export const UniformBiddingSubmit = (params) => {
  return request.get('/api/hyun/xuniformbidding/uniformbiddingsubmit', { params: params })
}
//招标结果-招标结果删除
export const UniformBiddingDeleteById = (params) => {
  return request.delete('/api/hyun/xuniformbidding/uniformbiddingdeletebyid', { params: params })
}
//招标结果-招标结果审核
export const UniformBiddingAudit = (params) => {
  return request.post('/api/hyun/xuniformbidding/uniformbiddingaudit', params)
}
//招标结果-招标结果撤销退回
export const UniformBiddingRevoke = (params) => {
  return request.post('/api/hyun/xuniformbidding/uniformbiddingrevoke', params)
}

// 获取合同履约列表
export const PurchaseGetpaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getpaged', params)
}
// 合同履约获取详情
export const PurchaseGeteditbyid = (params) => {
  return request.get('/api/hyun/xuniformpurchase/geteditbyid', { params: params })
}
// 合同履约-添加
export const PurchaseSaveadd = (params) => {
  return request.post('/api/hyun/xuniformpurchase/saveadd', params)
}
// 合同履约-修改
export const PurchaseSaveedit = (params) => {
  return request.post('/api/hyun/xuniformpurchase/saveedit', params)
}
// 合同履约删除
export const PurchaseDeletebyid = (params) => {
  return request.delete('/api/hyun/xuniformpurchase/deletebyid', { params: params })
}
// 合同履约-备案提交
export const PurchaseSavesubmit = (params) => {
  return request.post('/api/hyun/xuniformpurchase/savesubmit', params)
}
// 合同履约-区县审核
export const PurchaseConfirmfiling = (params) => {
  return request.put('/api/hyun/xuniformpurchase/confirmfiling', null, { params: params })
}
// 合同履约-区县退回
export const PurchaseFilingbackout = (params) => {
  return request.put('/api/hyun/xuniformpurchase/filingbackout', null, { params: params })
}
// 合同履约-区县撤回
export const PurchaseFilingrevoked = (params) => {
  return request.put('/api/hyun/xuniformpurchase/filingrevoked', null, { params: params })
}
// 合同履约-删除附件
export const Delattachmentbyid = (params) => {
  return request.post('/api/hyun/xuniformpurchase/delattachmentbyid', null, { params: params })
}

// 合同履约-校服清单- 列表
export const GetPurchaseListPaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getpurchaselistpaged', params)
}
// 合同履约-校服清单-根据Id获取对象
export const GetPurchaseListById = (params) => {
  return request.get('/api/hyun/xuniformlist/getpurchaselistbyid', params)
}
// 合同履约-校服清单-保存
export const PurchaseListEdit = (params) => {
  return request.post('/api/hyun/xuniformpurchase/savepurchaselistedit', params)
}
// 合同履约-校服清单-删除
export const DeletePurchaseListById = (params) => {
  return request.post('/api/hyun/xuniformpurchase/deletepurchaselistbyid', null, { params: params })
}
// 获取校服录入列表
export const ListGetpaged = (params) => {
  return request.post('/api/hyun/xuniformlist/getpaged', params)
}
// 校服录入获取详情
export const ListGeteditbyid = (params) => {
  return request.get('/api/hyun/xuniformlist/geteditbyid', { params: params })
}
// 校服录入-添加
export const ListSaveadd = (params) => {
  return request.post('/api/hyun/xuniformlist/add', params)
}
// 校服录入-复制
export const ListSavecopy = (params) => {
  return request.put('/api/hyun/xuniformlist/savecopy', null, { params: params })
}
// 校服录入-修改
export const ListSaveedit = (params) => {
  return request.post('/api/hyun/xuniformlist/edit', params)
}
// 校服录入-列表删除
export const ListDeletebyid = (params) => {
  return request.delete('/api/hyun/xuniformlist/deletebyid', { params: params })
}
// 校服录入- 设置状态
export const ListSetsattuz = (params) => {
  return request.put('/api/hyun/xuniformlist/setsattuz', null, { params: params })
}
// 校服录入-附件删除
export const ListDelattachmentbyid = (params) => {
  return request.put('/api/hyun/xuniformlist/delattachmentbyid', null, { params: params })
}
// 校服录入-尺码删除
export const ListDelsizebyid = (params) => {
  return request.put('/api/hyun/xuniformlist/delsizebyid', null, { params: params })
}
// 校服录入-批量审核
export const ListSubmit = (params) => {
  return request.put('/api/hyun/xuniformlist/submit', null, { params: params })
}
// 获取已提交列表
export const ShelfGetpaged = (params) => {
  return request.post('/api/hyun/xuniformshelf/getpaged', params)
}
// 已提交列表 -删除
export const ShelfDeletebyid = (params) => {
  return request.delete('/api/hyun/xuniformshelf/deletebyid', { params: params })
}
// 已提交列表 -调整排序
export const ShelfSetsortbyid = (params) => {
  return request.put('/api/hyun/xuniformshelf/setsortbyid', null, { params: params })
}
// 校服预览详情
export const ListGetbyid = (params) => {
  return request.get('/api/hyun/xuniformlist/getbyid', { params: params })
}
// 校服预览详情
export const ShelfGetbyid = (params) => {
  return request.get('/api/hyun/xuniformshelf/getbyid', { params: params })
}
// 已提交列表- 设置状态
export const ShelfSetstatuzbyid = (params) => {
  return request.put('/api/hyun/xuniformshelf/setstatuzbyid', null, { params: params })
}
// 获取待审核列表
export const ShelfGetwaitauditpaged = (params) => {
  return request.post('/api/hyun/xuniformshelf/getwaitauditpaged', params)
}
// 待审核列表 -审核
export const ShelfAudit = (params) => {
  return request.put('/api/hyun/xuniformshelf/audit', null, { params: params })
}
// 待审核列表 -获取批次
export const PurchaseGetpurchaseno = (params) => {
  return request.get('/api/hyun/xuniformpurchase/getpurchaseno', { params: params })
}
// 获取已审核列表
export const ShelfGetauditedpaged = (params) => {
  return request.post('/api/hyun/xuniformshelf/getauditedpaged', params)
}
// 已审核列表 -撤回
export const ShelfRevokedbyid = (params) => {
  return request.put('/api/hyun/xuniformshelf/revokedbyid', null, { params: params })
}
// 校服征订-生成征订单 ：征订单管理列表
export const purchaseGeteditorderpaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/geteditorderpaged', params)
}
// 校服征订-获取征订单信息
export const PurchaseGetcreateorder = (params) => {
  return request.get('/api/hyun/xuniformpurchase/getcreateorder', { params: params })
}
// 校服征订-保存\修改征订单信息
export const PurchaseSaveeditorder = (params) => {
  return request.post('/api/hyun/xuniformpurchase/saveeditorder', params)
}
// 校服征订-查看征订单
export const PurchaseGetorderpaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getorderpaged', params)
}
// 校服征订-查看征订单 -征订单明细
export const PurchaseGetstudentpaged = (params) => {
  return request.post('/api/hyun/xuniformparentpurchase/getstudentpaged', params)
}
// 校服征订-查看征订单 （班主任）
export const PurchaseGetorderteacherpaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getorderteacherpaged', params)
}
// 校服征订-查看征订单 -征订单明细（班主任）
export const PurchaseGetteacherstudentpaged = (params) => {
  return request.post('/api/hyun/xuniformparentpurchase/getteacherstudentpaged', params)
}
// 校服调换
// 获取调换列表
export const purchaseGetswappaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getswappaged', params)
}
// 调换列表 -调换发起
export const PurchaseLaunch = (params) => {
  return request.post('/api/hyun/xuniformpurchase/launch', params)
}
// 获取查看调换单列表 （学校）
export const PurchaseGetexchangeorder = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getexchangeorder', params)
}
// 管理调换单列表（供应商）
export const PurchaseGetmanagerorder = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getmanagerorder', params)
}
// 管理调换单列表-确认调换（供应商）
export const PurchaseConfirm = (params) => {
  return request.get('/api/hyun/xuniformpurchase/confirm', { params: params })
}
// 查看调换单-调换明细（学校、企业）
export const PurchaseGetswapdetail = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getswapdetail', params)
}
// 调换明细-按汇总查看(企业)
export const PurchaseGetsummaryorder = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getsummaryorder', params)
}
// 班主任校服调换列表
export const PurchaseGetteacherorder = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getteacherorder', params)
}
// 调换明细(班主任)
export const PurchaseGetswapdetailbyteacher = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getswapdetailbyteacher', params)
}
// 校服资助
// 校服资助列表：学校
export const PurchaseGetschoolsponsorpaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getschoolsponsorpaged', params)
}
// 校服资助列表：区、市汇总
export const PurchaseGetsponsorpaged = (params) => {
  return request.post('/api/hyun/xuniformpurchase/getsponsorpaged', params)
}
// 校服资助- 创建资助信息
export const PurchaseSponsoradd = (params) => {
  return request.post('/api/hyun/xuniformpurchase/sponsoradd', params)
}
// 校服资助=修改资助信息
export const PurchaseSponsoredit = (params) => {
  return request.post('/api/hyun/xuniformpurchase/sponsoredit', params)
}
// 校服资助-资助详情:学校
export const PurchaseGetsponsorbyid = (params) => {
  return request.get('/api/hyun/xuniformpurchase/getsponsorbyid', { params: params })
}
// 校服资助-资助详情:区、市汇总
export const PurchaseGetsponsordetailpaged = (params) => {
  return request.get('/api/hyun/xuniformpurchase/getsponsordetailpaged', { params: params })
}
// 校服资助-列表项删除
export const PurchaseDelsponsorbyid = (params) => {
  return request.delete('/api/hyun/xuniformpurchase/delsponsorbyid', { params: params })
}
// 校服资助-附件删除
export const PurchaseDelfilebyid = (params) => {
  return request.delete('/api/hyun/xuniformpurchase/delfilebyid', { params: params })
}
// 选用组织
// 组建选用组织-列表
export const OrganizationGetpaged = (params) => {
  return request.post('/api/hyun/xuniformorganization/getpaged', params)
}
// 选用组织-组织管理修改
export const OrganizationSaveedit = (params) => {
  return request.post('/api/hyun/xuniformorganization/saveedit', params)
}
// 选用组织-备案提交
export const OrganizationSavefiling = (params) => {
  return request.put('/api/hyun/xuniformorganization/savefiling', null, { params: params })
}
// 选用组织-详情
export const OrganizationGeteditbyid = (params) => {
  return request.get('/api/hyun/xuniformorganization/geteditbyid', { params: params })
}
// 选用组织-删除附件
export const OrganizationDelattachmentbyid = (params) => {
  return request.put('/api/hyun/xuniformorganization/delattachmentbyid', null, { params: params })
}
// 选用组织-审核
export const OrganizationConfirmfiling = (params) => {
  return request.put('/api/hyun/xuniformorganization/confirmfiling', null, { params: params })
}
// 选用组织-退回
export const OrganizationFilingbackout = (params) => {
  return request.put('/api/hyun/xuniformorganization/filingbackout', null, { params: params })
}
// 校服评价
// 评价管理-列表
export const EvaluateGetevaluatepaged = (params) => {
  return request.post('/api/hyun/xuniformevaluate/getevaluatepaged', params)
}
// 评价管理-创建/修改
export const EvaluateLaunch = (params) => {
  return request.post('/api/hyun/xuniformevaluate/launch', params)
}
// 查看评价-列表
export const EvaluateGetevaluatelist = (params) => {
  return request.post('/api/hyun/xuniformevaluate/getevaluatelist', params)
}
// 查看评价-评价明细
export const EvaluateGetevaluatedetaillist = (params) => {
  return request.post('/api/hyun/xuniformevaluate/getevaluatedetaillist', params)
}
// 查看评价-评价明细
export const EvaluateGetbyId = (params) => {
  return request.get('/api/hyun/xuniformevaluate/getbyid', { params: params })
}
// 导出
// 校服采购-校服征订-征订单明细导出
export const PurchaseExportstudent = (params) => {
  return request.post('/api/hyun/xuniformparentpurchase/exportstudent', params)
}
// 校服调换-调换明细导出
export const PurchaseExportswapdetail = (params) => {
  return request.post('/api/hyun/xuniformpurchase/exportswapdetail', params)
}
// 校服调换-调换明细导出(班主任)
export const PurchaseExportbyteacher = (params) => {
  return request.post('/api/hyun/xuniformpurchase/exportbyteacher', params)
}
// 校服调换-调换汇总导出
export const PurchaseExportsummaryorder = (params) => {
  return request.post('/api/hyun/xuniformpurchase/exportsummaryorder', params)
}
// 校服评价-评价明细导出
export const ValuateExportdetail = (params) => {
  return request.post('/api/hyun/xuniformevaluate/exportdetail', params)
}
