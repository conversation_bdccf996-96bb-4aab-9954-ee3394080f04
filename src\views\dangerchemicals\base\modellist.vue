<script setup>
defineOptions({
    name: 'dangerchemicalsbasemodellist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Tools, FolderAdd, Download
} from '@element-plus/icons-vue'
import {
    DictionaryFind, DcbaseCatalogCmboget, DcbaseModelextensionFind, DcbaseModelextensiongetByid, DcbaseModelextensionSave, DcdcbaseModelextensionconfigSave
} from '@/api/dangerchemicals.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { limit, integerLimit } from "@/utils/index.js";
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Code", SortType: "ASC" }] })
const bogetList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const baseNameList = ref([])
const writeSourceFundStatuzList = ref([])
const schoolStagezList = ref([])
const dialogVisible = ref(false)
const dialogData = ref({})
const editId = ref()
const refForm = ref()
const ruleForm = {
    Model: [
        { required: true, message: '请输入规格、属性', trigger: 'change' },
    ],
    Limited: [
        { required: true, message: '请输入超量预警值', trigger: 'change' },
    ],
    ValidityValue: [
        { required: true, message: '请输入超期预警值', trigger: 'change' },
    ],
    MeasuredValue: [
        { required: true, message: '请输入计量值', trigger: 'change' },
    ],
    UseLife: [
        { required: true, message: '请输入使用年限(月)', trigger: 'change' },
    ],
    SchoolStagez: [
        { required: true, message: '请选择适用学段', trigger: 'change' },
    ],
    Statuz: [
        { required: true, message: '请选择状态', trigger: 'change' },
    ],
    IsPoison: [
        { required: true, message: '请选择是否易制毒', trigger: 'change' },
    ],
    IsDetonate: [
        { required: true, message: '请选择是否易制爆', trigger: 'change' },
    ],
    IsNeedApproval: [
        { required: true, message: '请选择采购前是否需公安审批', trigger: 'change' },
    ],
    IsNeedReport: [
        { required: true, message: '请选择采购后是否需公安报备', trigger: 'change' },
    ],
    IsSecuritySupervise: [
        { required: true, message: '请选择是否需要公安监管', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', }
])
const yesNoList = ref([{ value: '1', label: '是' }, { value: '0', label: '否' }])
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        // HandleTableData(true);
    }
    HandleTableData()
    DictionaryFindUser()
    DcbaseCatalogCmbogetUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            // HandleTableData(true);
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 新增规格
const HandleAddSpec = () => {
    hType.value = 100
    dialogData.value = {}
    dialogVisible.value = true
}
// 批量设置
const HandleSetAll = (row, num) => {
    hType.value = num
    dialogData.value = {}
    dialogVisible.value = true
}
// 修改
const HandleEdit = (row) => {
    hType.value = 1000
    dialogVisible.value = true
    DcbaseModelextensiongetByidUser(row.Id)
}


//创建/修改选用组织  提交
const HandleSubmit = (hType) => {
    if (hType == 100) {
        // 新增规格 
        let formData = {
            BaseCatalogId: dialogData.value.BaseCatalogId,
            Name: dialogData.value.Model
        }
        DcbaseModelextensionSaveUser(formData)
    } else if (hType == 1000) {
        let SchoolStagez = ''
        if (dialogData.value.SchoolStagez) {
            SchoolStagez = dialogData.value.SchoolStagez.join(",")
        }

        let formData = {
            BaseCatalogId: dialogData.value.BaseCatalogId,
            Name: dialogData.value.Model,
            Id: dialogData.value.Id,
            EduCode: dialogData.value.EduCode,
            Cas: dialogData.value.Cas,
            Limited: dialogData.value.Limited,
            SchoolStagez: SchoolStagez,
            IsNeedApproval: dialogData.value.IsNeedApproval,
            IsNeedReport: dialogData.value.IsNeedReport,
            IsDetonate: dialogData.value.IsDetonate,
            IsPoison: dialogData.value.IsPoison,
            ValidityValue: dialogData.value.ValidityValue,
            Statuz: dialogData.value.Statuz,
            UseLife: dialogData.value.UseLife,
            Remark: dialogData.value.Remark,
            IsBurn: dialogData.value.IsBurn,
            IsBlast: dialogData.value.IsBlast,
            IsToxic: dialogData.value.IsToxic,
            IsHyperToxic: dialogData.value.IsHyperToxic,
            IsCorrode: dialogData.value.IsCorrode,
            IsOther: dialogData.value.IsOther,
            IsSecuritySupervise: dialogData.value.IsSecuritySupervise,
            MeasuredValue: dialogData.value.MeasuredValue
        }
        DcbaseModelextensionSaveUser(formData)
        // 修改
    } else {
        // 批量设置
        let setValue = ''
        switch (hType) {
            case 1:
                setValue = dialogData.value.Statuz
                break;
            case 2:
                setValue = dialogData.value.Limited
                break;
            case 3:
                setValue = dialogData.value.ValidityValue
                break;
            case 4:
                let SchoolStagez = ''
                if (dialogData.value.SchoolStagez) {
                    SchoolStagez = dialogData.value.SchoolStagez.join(",")
                }
                setValue = SchoolStagez
                break;
            case 5:
                setValue = dialogData.value.IsPoison
                break;
            case 6:
                setValue = dialogData.value.IsDetonate
                break;
            case 7:
                setValue = dialogData.value.IsNeedApproval
                break;
            case 8:
                setValue = dialogData.value.IsNeedReport
                break;
            case 10:
                setValue = dialogData.value.UseLife
                break;
            case 12:
                setValue = dialogData.value.IsSecuritySupervise
                break;
            case 13:
                setValue = dialogData.value.MeasuredValue
                break;
        }
        let formData = {
            arrId: selectRows.value.map(item => item.Id).join(','),
            type: hType,
            setValue: setValue
        }
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            DcdcbaseModelextensionconfigSave(formData).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '设置成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })

    }

}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const filtersKey = ref('')
const filtersValue = ref('Name')
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.TwoCatalogId = undefined
    filters.value.IsNeedApproval = undefined
    filters.value.IsNeedReport = undefined
    filters.value.IsPoison = undefined
    filters.value.IsDetonate = undefined
    filters.value.Statuz = undefined
    filtersKey.value = ''
    filtersValue.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

const hType = ref(0)
const filtersChange = () => {
    HandleTableData()
}

// 公安监管(自动)
const HandleSupervision = () => {
    ElMessageBox.confirm('请确保平台已与溯源系统对接，否则执行此操作将影响后续业务流程。您确认执行吗？', '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {

    }).catch(() => {
    })
}
// 下载中爆危化品库
const HandleDown = () => {
}

// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }

    DcbaseModelextensionFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取学段
const DictionaryFindUser = () => {
    let paraData = {
        TypeCode: "1101",
        pageIndex: 1,
        pageSize: 100,
        sortModel: [{ SortCode: "Sequence", SortType: "ASC" }]
    }

    DictionaryFind(paraData).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            schoolStagezList.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取危化品分类一二级
const DcbaseCatalogCmbogetUser = () => {
    DcbaseCatalogCmboget({ depth: 0, pid: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bogetList.value = rows.data[0]?.children || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选择危化品分类
const pnameChange = (e) => {
    DcbaseCatalogCmboget({ depth: 2, pid: e }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            baseNameList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const DcbaseModelextensiongetByidUser = (id) => {
    DcbaseModelextensiongetByid({ id: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dialogData.value = rows.data[0] || {}
            dialogData.value.SchoolStagez = dialogData.value.SchoolStagez?.split(',')
            dialogData.value.IsNeedApproval = String(dialogData.value.IsNeedApproval)
            dialogData.value.IsNeedReport = String(dialogData.value.IsNeedReport)
            dialogData.value.IsSecuritySupervise = String(dialogData.value.IsSecuritySupervise)
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
// 添加/修改 保存
const DcbaseModelextensionSaveUser = (data) => {
    DcbaseModelextensionSave(data).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
//输入两位小数
const limitInput = (val, name) => {
    dialogData.value[name] = limit(val);
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in bogetList" :key="item.id" :label="item.text" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsNeedApproval" clearable placeholder="是否需要公安审批"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in yesNoList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsNeedReport" clearable placeholder="是否需要公安报备"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in yesNoList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsPoison" clearable placeholder="是否易制毒" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yesNoList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsDetonate" clearable placeholder="是否易制爆" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yesNoList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="是否启用" @change="filtersChange"
                            style="width: 160px">
                            <el-option :value="1" label="是" />
                            <el-option :value="2" label="否" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" size="small" @click="HandleAddSpec">新增规格</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 1)">状态</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 2)">超量预警值</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 3)">超期预警值</el-button>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 10)">使用年限</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 4)">使用学段</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 5)">易制毒</el-button>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 6)">易制爆</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 7)">公安审批</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 8)">公安报备</el-button>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 13)">计量值</el-button>
                        <el-button type="success" :icon="Tools" size="small" :disabled="selectRows.length == 0"
                            @click="HandleSetAll(row, 12)">公安监管(手动)</el-button>
                        <!-- <el-button @click="HandleSupervision">公安监管(自动)</el-button> -->
                        <el-button type="success" :icon="Download" size="small" @click="HandleDown">下载中爆危化品库</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="Pname" label="危化品分类" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格、属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Statuz" label="是否启用" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.Statuz == 1">是</span>
                    <span v-else style="color: #F56C6C;">否</span>
                </template>
            </el-table-column>
            <el-table-column prop="Limited" label="超量预警值" min-width="100" align="center"> </el-table-column>
            <el-table-column prop="ValidityValue" label="超期预警值(天)" min-width="130" align="center"> </el-table-column>
            <el-table-column prop="MeasuredValue" label="计算值" min-width="100" align="center"> </el-table-column>
            <!-- <el-table-column prop="SchoolStagez" label="适用学段" min-width="100" align="center"> </el-table-column> -->
            <el-table-column prop="IsPoison" label="易制毒" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsPoison == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsDetonate" label="易制爆" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsDetonate == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsBurn" label="易燃" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsBurn == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsBlast" label="易爆" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsBlast == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsToxic" label="有毒" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsToxic == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsHyperToxic" label="剧毒" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsHyperToxic == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsCorrode" label="腐蚀" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsCorrode == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsOther" label="其它" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsOther == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsNeedApproval" label="采购前需公安审批" min-width="140" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsNeedApproval == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsNeedReport" label="采购后需公安报备" min-width="140" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsNeedReport == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="IsSecuritySupervise" label="需要公安监管" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsSecuritySupervise == 1" style="color: #F56C6C;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="EduCode" label="教育部分类代码" min-width="160" align="center"> </el-table-column> -->
            <!-- <el-table-column prop="CAS" label="CAS号" min-width="120" align="center"> </el-table-column> -->
            <el-table-column prop="Remark" label="备注" min-width="120" show-overflow-tooltip> </el-table-column>
            <el-table-column label="操作" fixed="right" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="hType == 4 || hType == 1000 ? 880 : 680" :lazy="true"
            :title="editId ? '修改模块' : '添加模块'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                    status-icon>
                    <el-form-item label="危化品名称：" v-if="hType == 1000">
                        <span>{{ dialogData.Name }}</span>
                    </el-form-item>

                    <el-form-item label="危化品分类：" prop="Pname" v-if="hType == 100">
                        <el-select v-model="dialogData.Pname" @change="pnameChange" style="width: 80%">
                            <el-option v-for="item in bogetList" :key="item.id" :label="item.text" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <!-- filterable allow-create default-first-option -->
                    <el-form-item label="危化品名称：" prop="BaseCatalogId" v-if="hType == 100">
                        <el-select v-model="dialogData.BaseCatalogId" style="width: 80%">
                            <el-option v-for="item in baseNameList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="规格、属性：" prop="Model" v-if="hType == 100 || hType == 1000">
                        <el-input v-model="dialogData.Model" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="计量单位：" v-if="hType == 1000">
                        <span>{{ dialogData.UnitsMeasurement }}</span>
                    </el-form-item>

                    <el-form-item label="是否启用：" v-if="hType == 1000">
                        <el-radio-group v-model="dialogData.Statuz">
                            <el-radio :value="1" label="是"> </el-radio>
                            <el-radio :value="2" label="否"> </el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="状态：" prop="Statuz" v-if="hType == 1">
                        <el-radio-group v-model="dialogData.Statuz">
                            <el-radio :value="1" label="启用"> </el-radio>
                            <el-radio :value="2" label="禁用"> </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="超量预警值：" prop="Limited" v-if="hType == 2 || hType == 1000">
                        <el-input v-model="dialogData.Limited" @input="limitInput($event, 'Limited')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="超期预警值：" prop="ValidityValue" v-if="hType == 3 || hType == 1000">
                        <el-input v-model="dialogData.ValidityValue" @input="limitInput($event, 'ValidityValue')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="使用年限(月)：" prop="UseLife" v-if="hType == 10 || hType == 1000">
                        <el-input v-model="dialogData.UseLife" @input="integerLimitInput($event, 'UseLife')"
                            style="width: 80%"></el-input>
                    </el-form-item>

                    <el-form-item label="计量值：" prop="MeasuredValue" v-if="hType == 13 || hType == 1000">
                        <el-input v-model="dialogData.MeasuredValue" @input="limitInput($event, 'MeasuredValue')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="适用学段：" prop="SchoolStagez" v-if="hType == 4 || hType == 1000">
                        <el-checkbox-group v-model="dialogData.SchoolStagez">
                            <el-checkbox v-for="item in schoolStagezList" :key="item.DicValue" :label="item.DicName"
                                :value="item.DicValue">
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="属性：" v-if="hType == 1000">
                        <el-checkbox v-model="dialogData.IsPoison" label="易制毒" :true-value="1" :false-value="0">
                        </el-checkbox>
                        <el-checkbox v-model="dialogData.IsDetonate" label="易制爆" :true-value="1" :false-value="0">
                        </el-checkbox>
                        <el-checkbox v-model="dialogData.IsBurn" label="易燃" :true-value="1" :false-value="0">
                        </el-checkbox>
                        <el-checkbox v-model="dialogData.IsBlast" label="易爆" :true-value="1" :false-value="0">
                        </el-checkbox>
                        <el-checkbox v-model="dialogData.IsToxic" label="有毒" :true-value="1" :false-value="0">
                        </el-checkbox>
                        <el-checkbox v-model="dialogData.IsHyperToxic" label="剧毒" :true-value="1" :false-value="0">
                        </el-checkbox>
                        <el-checkbox v-model="dialogData.IsCorrode" label="腐蚀" :true-value="1" :false-value="0">
                        </el-checkbox>
                        <el-checkbox v-model="dialogData.IsOther" label="其它" :true-value="1" :false-value="0">
                        </el-checkbox>
                    </el-form-item>
                    <el-form-item label="易制毒：" prop="IsPoison" v-if="hType == 5">
                        <el-radio-group v-model="dialogData.IsPoison">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="易制爆：" prop="IsDetonate" v-if="hType == 6">
                        <el-radio-group v-model="dialogData.IsDetonate">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="采购前需公安审批：" prop="IsNeedApproval" v-if="hType == 7 || hType == 1000">
                        <el-radio-group v-model="dialogData.IsNeedApproval">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="采购后需公安报备：" prop="IsNeedReport" v-if="hType == 8 || hType == 1000">
                        <el-radio-group v-model="dialogData.IsNeedReport">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="需要公安监管：" prop="IsSecuritySupervise" v-if="hType == 12 || hType == 1000">
                        <el-radio-group v-model="dialogData.IsSecuritySupervise">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="教育部代码：" v-if="hType == 1000">
                        <el-input v-model="dialogData.EduCode" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="CAS号：" v-if="hType == 1000">
                        <el-input v-model="dialogData.Cas" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="备注：" v-if="hType == 1000">
                        <el-input type="textarea" v-model="dialogData.Remark" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit(hType)"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
:deep(.el-checkbox) {
    width: 12%;
}
</style>