<script setup>
defineOptions({
    name: 'dangerchemicalsdepositaddresslist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    FolderAdd, QuestionFilled, Search, Refresh, UploadFilled
} from '@element-plus/icons-vue'
import {
    Getpagedbytype, AttachmentUpload
} from '@/api/user.js'
import {
    DcDepositAddressDelete, DcDepositAddressGetById, DcDepositAddressInsertUpdate, DcDepositAddressListFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    Address: [
        { required: true, message: '请输入存放地点', trigger: 'change' },
    ],
    Space: [
        { required: true, message: '请输入房间面积(平米)', trigger: 'change' },
    ]
}
const dangerChemicalsLevel = ref('')
const unitType = ref(userStore.userInfo.UnitType)
const uploadFileData = ref([])
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    GetpagedbytypeUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 添加
const HandleAdd = (row, e) => {
    dialogVisible.value = true
    nextTick(() => {
        dialogData.value = {}
        refForm.value.resetFields()
        uploadFileData.value.forEach(item => {
            item.fileLChildList = []
        })
    })
}
// 修改
const HandleEdit = (row, e) => {
    DcDepositAddressGetById({ id: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows
            dialogVisible.value = true
            uploadFileData.value.forEach(item => {
                item.fileLChildList = other || []
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要删除此存放地点吗?')
        .then(() => {
            DcDepositAddressDelete({ Id: row.Id }).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })

}
// 提交
const HandleSubmit = () => {
    let paraData = {
        Id: dialogData.value.Id || "0",
        Space: dialogData.value.Space,
        Address: dialogData.value.Address,
        Space: dialogData.value.Space,
        LiablePerson: dialogData.value.LiablePerson,
        Remark: dialogData.value.Remark,
        AttachmentIdList: uploadFileData.value[0].fileLChildList.map(t => t.Id)
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcDepositAddressInsertUpdate(paraData).then(res => {
            if (res.data.flag == 1) {
                dialogVisible.value = false
                ElMessage.success(res.data.msg || '保存成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.MemberUserName = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    DcDepositAddressListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 401 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
            console.log('uploadFileData.value', uploadFileData.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    console.log("ary", ary, "name", name)
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])

            // 获取上传后附件Id
            fileIdList.value.push(rows[0])
            console.log("fileIdList.value", fileIdList.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//   删除附件
const delFile = (item, index) => {
    console.log("item", item, "index", index)
    console.log(uploadFileData.value)
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)

}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }

}
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    存放地点列表 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 为了保证正常入库，须在此处设置危化品存放地点！</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Address" placeholder="存放地点" style="width: 280px"
                            class="input-with-select"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="id" default-expand-all
            header-cell-class-name="headerClassName">
            <el-table-column prop="Address" label="存放地点" min-width="180"></el-table-column>
            <el-table-column prop="Space" label="房间面积(平米)" min-width="100" align="center"></el-table-column>
            <el-table-column prop="LiablePerson" label="责任人" min-width="100" align="center"></el-table-column>
            <el-table-column prop="imgShow" label="场景图片" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center" v-if="unitType == 3">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="dialogData.Id ? '修改存放地点' : '添加存放地点'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="140px"
                    status-icon>
                    <el-form-item label="存放地点：" prop="Address">
                        <template #label>
                            <el-tooltip class="item" effect="light" placement="top">
                                <template #content>
                                    <div id="idmsg" style="color: #999">
                                        存放地点编写规则：楼宇名称+房间名称+储存柜；<br />（1）没有储存柜，如：实验楼304室；<br />（2）有一个储存柜，如：实验楼304室储存柜；<br />（3）有多个储存柜，如：实验楼304室1号储存柜。
                                    </div>
                                </template>
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> 存放地点： </span>
                        </template>
                        <el-input v-model="dialogData.Address" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="房间面积(平米)：" prop="Space">
                        <el-input v-model="dialogData.Space" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="责任人：">
                        <el-input v-model="dialogData.LiablePerson" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                            :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                            :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                            <el-button type="success" size="small" :icon="UploadFilled"
                                @click="MaxFileNumberClick(item)">上传</el-button>
                        </el-upload>
                        <div class="fileFlex">
                            <div v-for="(itemChild, indexChild) in item.fileLChildList" :key="itemChild.Id">
                                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                                    <Delete />
                                </el-icon>
                                <span style="cursor: pointer;"
                                    @click="fileListDownload(itemChild, item.fileLChildList)">
                                    {{ itemChild.Title }}{{ itemChild.Ext }}
                                </span>

                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="备注：">
                        <el-input v-model="dialogData.Remark" type="textarea" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
        <!-- 图片预览 -->
        <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    </div>
</template>