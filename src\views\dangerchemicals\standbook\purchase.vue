<script setup>
defineOptions({
    name: 'dangerchemicalsstandbookpurchase'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcStandbookPurchaseFind
} from '@/api/dangerchemicals.js'
import { ExcelDownload } from "@/utils/index.js"
import {
    ExportDcStandbookPurchaseFind
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "RegDate", SortType: "DESC" }] })
const date = ref()
//加载数据
onMounted(() => {

    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.month = undefined
    date.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const monthChange = (val) => {
    console.log(val)
    if (!val) {
        filters.value.Year = undefined;
        filters.value.Month = undefined;
    } else {
        const [year, month] = val.split('-');
        filters.value.Year = parseInt(year, 10);
        filters.value.Month = parseInt(month, 10);
    }
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    DcStandbookPurchaseFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const HandlePrint = (e) => {
    router.push({
        path: "./purchaseprint",
        query: {
            Year: filters.value.Year,
            Month: filters.value.Month,
            date: date.value
        }
    })
}
//导出
const HandleExport = () => {
    ExportDcStandbookPurchaseFind(filters.value).then(res => {
        ExcelDownload(res)
    });
} 
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    操作提示 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 打印与导出请先选择出库时间（月份）； </li>
                    <li> 有数据才可进行打印或导出。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="HandlePrint"
                            :disabled="!date || tableData.length == 0">打印</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport"
                            :disabled="!date || tableData.length == 0">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="date" type="month" format="YYYY-MM" value-format="YYYY-MM" clearable
                            placeholder="出库时间" @change="monthChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" id="printArea" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Num" label="入库数量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CompanyName" label="供应商" min-width="140" show-overflow-tooltip> </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>