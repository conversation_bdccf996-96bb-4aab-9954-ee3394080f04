<script setup>
defineOptions({
    name: 'dangerchemicalsapplygrantuserset'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    FolderAdd, Setting, Search, Refresh
} from '@element-plus/icons-vue'
import {
    UserFindMyUnitUserNameByRoleId, DcApplyGrantUserAdd, DcApplyGrantUserDelete, DcApplyGrantUserFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { pageQuery } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const memberUserList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const dangerChemicalsLevel = ref('')
const unitType = ref(userStore.userInfo.UnitType)
const routerObject = ref({})
const ruleForm = {
    MemberUserId: [
        { required: true, message: '请选择人员', trigger: 'change' },
    ],
}
//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象 
    filters.value.UserType = routerObject.value.t || 1
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    userfindmyunitusernamebyroleidUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 添加
const HandleAdd = (row, e) => {
    dialogData.value = {}
    dialogVisible.value = true
}
// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要删除此供应商信息吗?')
        .then(() => {
            DcApplyGrantUserDelete({ Id: row.Id }).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })

}
// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;

        dialogData.value.UserType = routerObject.value.t
        // dialogData.value.Id = 0
        DcApplyGrantUserAdd(dialogData.value).then(res => {
            if (res.data.flag == 1) {
                // dialogVisible.value = false
                ElMessage.success(res.data.msg || '保存成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.MemberUserName = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    DcApplyGrantUserFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取供应商单位
const userfindmyunitusernamebyroleidUser = () => {
    let rids = 352
    if (routerObject.value.t == 1) {
        rids = 352
    } else {
        rids = 354
    }
    UserFindMyUnitUserNameByRoleId({ rids: rids }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            memberUserList.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
} 
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    {{ routerObject.t == 1 ? '领用人列表' : '发放人列表' }} &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li v-if="routerObject.t == 1"> 只有双人领用时，才需要配置同领用人！</li>
                    <li v-else> 只有双人发放时，才需要配置同发放人！</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.MemberUserName" placeholder="领用人" style="width: 180px"
                            class="input-with-select"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="id" default-expand-all
            header-cell-class-name="headerClassName">
            <el-table-column prop="MemberUserName" :label="routerObject.t == 1 ? '领用人' : '发放人'"
                min-width="180"></el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center" v-if="unitType == 3">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="设置状态">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="140px"
                    status-icon>
                    <el-form-item :label="routerObject.t == 1 ? '领用人 :' : '发放人 :'" prop="MemberUserId">
                        <el-select v-model="dialogData.MemberUserId" filterable style="width: 80%">
                            <el-option v-for="item in memberUserList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="备注：">
                        <el-input v-model="dialogData.Remark" type="textarea" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>