<script setup>
import { onMounted, ref, watch, getCurrentInstance } from 'vue'
import { getGetTreeTable, removeDepartment, editDepartment, addDepartment, getDepartmentTree } from '@/api/dep.js'

import { ElMessageBox, ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const currentRow = ref({})
const selectRows = ref([])
const filters = ref({ page: 1, size: 10, key: '' })
const menuTrees = ref([])
const HandleSelectChange = (selection) => {
  selectRows.value = selection
}

const dialogCheck = (selection, row) => {
  currentRow.value = null;
  proxy.$refs.refTable.clearSelection();
  if (selection.length === 0) {
    return;
  }
  if (row) {
    selectCurrentRow(row);
  }
}
const selectCurrentRow = (val) => {
  if (val) {
    currentRow.value = val;
    proxy.$refs.refTable.clearSelection();
    proxy.$refs.refTable.toggleRowSelection(val, true);
  }
}

const load = (tree, treeNode, resolve) => {
  let para = {
    page: filters.value.page,
    f: tree.Id,
    key: filters.value.key,
  };



  getGetTreeTable(para).then((res) => {
    resolve(res.data.response);
  });
}

const HandleClearTable = () => {
  //当前表格数据
  // tableData.value = []
  //统计总数(不要手动重置 否则会出现翻页问题 找了好半天)
  // tableTotal.value = 0
  //当前选中行
  currentRow.value = null
  //当前选择多行
  selectRows.value = []
}
// 翻页
watch(() => filters.value.page, () => {
  HandleSearch()
})
watch(() => filters.value.size, () => {
  filters.value.page = 1
  HandleSearch()
})



//加载数据
const getDepartmentTreeUser = () => {
  selectRows.value = []
  getGetTreeTable(filters.value).then(res => {
    tableData.value = res.data.response
    // tableTotal.value = res.data.response.dataCount
  })
}
onMounted(() => {
  HandleSearch()

})



//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
  Name: [
    { required: true, message: '部门名称不能为空', trigger: 'change' },
  ],
  Leader: [
    { required: true, message: '负责人不能为空', trigger: 'change' },
  ]
}
//新增
const HandleAdd = () => {
  formData.value = { Pid: '0', CodeRelationship: '0', Status: true }
  dialogVisible.value = true
  getDepartmentTree({ pid: 0 }).then((res) => {
    menuTrees.value = [res.data.response];
    console.log("menuTrees", menuTrees.value)
  });
}
//编辑
const HandleEdit = (row) => {
  if (!row) {
    ElMessage.error('请选择要操作的数据!')
    return;
  }
  formData.value = JSON.parse(JSON.stringify(row))
  console.log('formData', formData.value)
  dialogVisible.value = true
  getDepartmentTree({ pid: 0 }).then((res) => {
    menuTrees.value = [res.data.response];
  });
}
//删除
const HandleDel = (row) => {
  if (!row) {
    ElMessage.error('请选择要操作的数据!')
    return;
  }
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      removeDepartment({ id: row.Id }).then((res) => {
        HandleSearch()
        ElMessage.success('删除成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })



}

//提交
const HandleSubmit = () => {
  console.log('formData', formData.value)
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    // formData.value.PidArr = [formData.value.PidArrId]
    // if (typeof (formData.value.PidArr) == 'string') {
    //   formData.value.Pid = formData.value.PidArr
    //   formData.value.PidArr = undefined
    // }

    if (formData.value.Id) {
      //编辑
      editDepartment(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '添加成功')
      })
    } else {

      //新增
      addDepartment(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '编辑成功')
      })
    }


  })

}

//搜索
const HandleSearch = (page) => {

  if (page) filters.value.page = page

  getDepartmentTreeUser()
}

</script>
<template>
  <!-- 搜索 -->
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
        <el-form-item label="关键词" class="flexItem" label-width="90">
          <el-input class="flexContent" v-model="filters.key" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" plain @click="HandleSearch(1)">查询</el-button>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" plain @click="HandleAdd">添加</el-button>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" plain @click="HandleEdit(currentRow)">修改</el-button>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="danger" plain @click="HandleDel(currentRow)">删除</el-button>
        </el-form-item>
      </el-form>

    </el-col>
  </el-row>
  <!-- 内容 -->
  <el-table ref="refTable" :data="tableData" highlight-current-row @select="dialogCheck" @row-click="selectCurrentRow"
    @selection-change="HandleSelectChange" border lazy :load="load" row-key="Id" class="custom-tbl"
    header-cell-class-name="headerClassName">
    <el-table-column type="selection" width="50"></el-table-column>
    <el-table-column prop="Name" label="部门名称" min-width="200"></el-table-column>
    <el-table-column prop="Id" label="部门ID" min-width="200"></el-table-column>
    <!-- <el-table-column prop="CodeRelationship" label="上级" min-width="80"></el-table-column> -->
    <el-table-column prop="Leader" label="负责人" min-width="100" align="center"></el-table-column>
    <el-table-column prop="OrderSort" label="排序" min-width="80" align="center"></el-table-column>
    <el-table-column prop="Status" label="是否有效" min-width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="row.Status ? 'success' : 'danger'" disable-transitions>
          {{ !row.Status ? "否" : "是" }}</el-tag>
      </template>
    </el-table-column>

    <el-table-column prop="CreateTime" label="创建时间" min-width="120" align="center">
      <template #default="{ row }">
        {{ row.CreateTime ? row.CreateTime.substring(0, 10) : '--' }}
      </template>
    </el-table-column>
    <el-table-column prop="ModifyTime" label="更新时间" min-width="120" align="center">
      <template #default="{ row }">
        {{ row.ModifyTime ? row.ModifyTime.substring(0, 10) : '--' }}
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>
  <!-- 分页 -->
  <!-- <el-row>
    <el-col class="flexBox">
      <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
        :total="tableTotal" v-model:current-page="filters.page" v-model:page-size="filters.size" />
    </el-col>
  </el-row> -->
  <!-- 弹窗 -->
  <el-dialog v-model="dialogVisible" :title="formData.Id ? '编辑' : '添加'" width="560px">
    <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="80px" status-icon>

      <el-form-item label="部门名称" prop="Name">
        <el-input v-model="formData.Name" auto-complete="off"></el-input>
      </el-form-item>
      <!-- <el-form-item label="上级关系">
        <el-tooltip placement="top">
          <template #content> 以','号结尾，方便下属部门统一查询</template>
          <el-input v-model="formData.CodeRelationship" disabled auto-complete="off"></el-input>
        </el-tooltip>
      </el-form-item> -->
      <el-form-item label="负责人">
        <el-input v-model="formData.Leader" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="排序">
        <el-input v-model="formData.OrderSort" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="是否有效" width sortable>
        <el-switch v-model="formData.Status"></el-switch>
      </el-form-item>
      <el-form-item label="父级部门">
        <el-tree-select v-model="formData.Pid" :data="menuTrees" filterable clearable :check-strictly="true" />
      </el-form-item>

    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}

.custom-tbl :deep(th .el-checkbox) {
  display: none;
}
</style>