<script setup>
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Select, UploadFilled, Position,Tools, Search, Refresh, FolderAdd,
} from '@element-plus/icons-vue'
import {
    Getpagedbytype, AttachmentUpload ,DHelpDocById,DHelpDocSave,DHelpItemFind,DHelpItemDeleteByIds,DHelpItemSave} from '@/api/user.js'
import {
    getPermissionTree
} from '@/api/assign.js'
import { fileDownload, dateDay, tagsListStore } from "@/utils/index.js";
import { ElMessageBox, ElMessage } from 'element-plus'
import Editor from '@/components/Editor/index.vue'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { limit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ SysMenuId: 0})
const dialogVisible = ref(false)
const formData = ref({})
const formItemData = ref({})
const refForm = ref()
const isSet = ref(false)
const menuTrees = ref([])
const ruleForm = {
    SysMenuId: [
        { required: true, message: '请选择页面名称', trigger: 'change' },
    ],
    Statuz: [
        { required: true, message: '请选择状态', trigger: 'change' },
    ]
}

const ruleItemForm = {
    ItemPath: [
        { required: true, message: '请上传图片或视频', trigger: 'change' },
    ]
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    GetPermissionTree()
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 帮助文档，点击菜单加载
const HandleEdit = (id) => {
    // isSet.value = false
    DHelpDocById({ id: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows
            formData.value.Remark = formData.value.Remark|| ''
        console.log("----2025-09-15 11:21:56----:", rows);
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交保存
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        let data = {
            Id: formData.value.Id,
            SysMenuId: formData.value.SysMenuId,
            Remark: formData.value.Remark,
            Statuz: formData.value.Statuz, 
        }
        DHelpDocSave(data).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '保存成功')
                dialogVisible.value = false
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

// 列表
const HandleTableData = () => {
    filters.value.SysMenuId = SysMenuId.value
    DHelpItemFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows 
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取菜单列表(不含接口按钮)
const GetPermissionTree = () => {
    getPermissionTree({ needbtn: false }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            menuTrees.value = rows.children;
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
 
 
//分项删除
const HandleItemDelete = (row) => {
  if (!row) {
    ElMessage.error('请选择要操作的数据!')
    return;
  }
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      DHelpItemDeleteByIds({ id: row.Id }).then((res) => {
        HandleTableData()
        ElMessage.success('删除成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//设置状态-启用
const HandleItemEnable = (row) => {
  if (!row) {
    ElMessage.error('请选择要操作的数据!')
    return;
  }
  ElMessageBox.confirm('确定启用吗?')
    .then(() => {
      DHelpItemSave({ Id: row.Id ,OptType:1}).then((res) => {
        HandleTableData()
        ElMessage.success('启用成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
//设置状态-禁用
const HandleItemDisable = (row) => {
  if (!row) {
    ElMessage.error('请选择要操作的数据!')
    return;
  }
  ElMessageBox.confirm('确定禁用吗?')
    .then(() => {
      DHelpItemSave({ Id: row.Id ,OptType:2 }).then((res) => {
        HandleTableData()
        ElMessage.success('禁用成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
//保存分项内容
//新增帮助分项信息
const HandleItemAdd = () => {
    if(SysMenuId.value <= 0){
        ElMessage.error("请先选择需要添加帮助的菜单。")
    }else{
        formItemData.value = ref({ItemContent:'',ShowOrder:'',FileType:1,ItemPath:''})
        formItemData.value.FileType=1;
        dialogVisible.value = true
    }
}

const HandleItemSave = (row) => {
   let dataForm = {
        Id: 0,
        OptType: 0,
        SysMenuId: SysMenuId.value,
        FileType:formItemData.value.FileType,
        ItemPath:formItemData.value.ItemPath, 
        ItemContent:formItemData.value.ItemContent, 
        ShowOrder:formItemData.value.ShowOrder
    }
    if(!(SysMenuId.value && SysMenuId.value > 0)){
        ElMessage.error("请先选择需要添加帮助的菜单。")
        return false;
    }
    if(!(formItemData.value.ItemPath && formItemData.value.ItemPath.length> 0)){
        ElMessage.error("请上传图片或视频。")
        return false;
    }
    
    DHelpItemSave(dataForm).then(res => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '保存成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const SysMenuId = ref(0)
const SysMenuName = ref('')
//菜单点击事件
const handleNodeClick = (item) => {
    SysMenuId.value = item.value;
    SysMenuName.value = item.label;
    HandleEdit(item.value);
    HandleTableData()
}
 
const editorChange = (getText, getHtml) => {
    // console.log("getText", getText, "getHtml", getHtml)
}

//附件上传
const uploadFileData = ref([
    { FileCategory: 301001, fileLChildList: [], categoryList: [], IsFilled: 1, FileSize: 10, Memo: "文件小于5M，支持pdf和图片文件", Name: "路径：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" }
])
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)

// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data

            console.log("----2025-09-15 14:36:50-path---:", rows[0]);
            formItemData.value.ItemPath = rows[0].Path
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
  
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }
}

</script>
<template>
    <div class="viewContainer">
        <div style="display: flex;">
            <div class="section_left">
                <el-tree :data="menuTrees" :expand-on-click-node="false"
                    :props="{ children: 'children', label: 'label' }" @node-click="handleNodeClick" />
            </div>
            <div class="viewContainer" style="width: 100%;">
                <el-form style="width: 900px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
                    label-width="180px" status-icon>
                    <el-form-item label="菜单名称" prop="Title">
                        <el-input v-model="SysMenuName" auto-complete="off" disabled></el-input>
                    </el-form-item>
                    <!-- 富文本 -->
                    <el-form-item label="内容：">
                        <Editor v-model:get-html="formData.Remark" :defaultHtml="formData.Remark" @editorChange="editorChange">
                        </Editor>
                    </el-form-item>
                    <el-form-item label="状态：" prop="Statuz">
                        <el-radio-group v-model="formData.Statuz">
                            <el-radio :value="1" label="启用"> </el-radio>
                            <el-radio :value="2" label="禁用"> </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" :icon="Select" @click="HandleSubmit()">保存</el-button>
                    </el-form-item>
                </el-form>
 
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-button type="success" :icon="FolderAdd" @click="HandleItemAdd">新增</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                    @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
           
                    <el-table-column prop="ItemContent" label="内容" min-width="160" show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="ItemPath" label="图片/视频查看" min-width="140" align="center"></el-table-column>
                    <el-table-column prop="FileType" label="类型" fixed="right" width="90" align="center">
                        <template #default="{ row }">
                            {{row.FileType == 1 ? "图片": row.FileType == 2? "视频":"--"}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="ShowOrder" label="排序" fixed="right" width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Statuz" label="状态" fixed="right" width="100" align="center">
                        <template #default="{ row }">
                            {{ row.Statuz == 1 ? '启用' : '禁用' }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" width="140" align="center">
                        <template #default="{ row }">
                            <el-button v-if="row.Statuz==1" type="primary" link @click="HandleItemDisable(row)">禁用</el-button>
                            <el-button v-else type="primary" link @click="HandleItemEnable(row)">启用</el-button>
                            <el-button type="primary" link @click="HandleItemDelete(row)">删除</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
            </div>
        </div>
        <app-box v-model="dialogVisible" :lazy="true" title="添加帮助条目">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="formItemData" :rules="ruleItemForm" label-width="120px" status-icon> 
                    <!-- 富文本 -->
                    <el-form-item label="内容：">
                        <Editor v-model:get-html="formItemData.ItemContent" :defaultHtml="formItemData.ItemContent" @editorChange="editorChange">
                        </Editor>
                    </el-form-item> 
                    <el-form-item label="类型" prop="FileType">
                        <el-radio-group v-model="formItemData.FileType">
                            <el-radio :value="1" label="图片"> </el-radio>
                            <el-radio :value="2" label="视频"> </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="item.FileCategory">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            {{ item.Name }}
                        </template>
                        <div style="display: flex;align-items: center;width: 100%;">
                            <el-input v-model="formItemData.ItemPath" auto-complete="off" disabled placeholder="请上传图片或视频"></el-input>
                            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                                :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                                :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                                <el-button type="success" :icon="UploadFilled" style="margin: 0 10px;">上传</el-button>
                            </el-upload>
                        </div>
                    </el-form-item>
                    <el-form-item label="排序值" prop="ShowOrder">
                        <el-input v-model="formItemData.ShowOrder" auto-complete="off" placeholder="请输入排序值"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleItemSave" v-if="!isSet"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style></style>
<style lang="scss" scoped>
.section_left {
    width: 200px;
    margin-top: 10px;
    flex-shrink: 0;
}

:deep(.el-tree-node:focus>.el-tree-node__content) {

    background-color: #409eff1A;

    .el-text {
        color: #409eff;
    }
}
</style>