<script setup>
defineOptions({
    name: 'nodelinkagecfgset'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Checked
} from '@element-plus/icons-vue'
import {
    GetLinkageHostDetailList, GetDetailSetList, GetDetaibyParentId, PostSaveLinkageDetail
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const refTable = ref()
const parentData = ref([])//父级数据
const childData = ref([])//子级数据
const parentName = ref('')//父级字段名称
const childName = ref('')//子级字段名称

//加载数据
onMounted(() => {
    radio.value = false
    if (route.query.isTagRouter) {
        GetLinkageHostDetailListUser();
        GetDetailSetListUser();
    }
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    radio.value = false
    nextTick(() => {
        if (!route.query.isTagRouter) {
            GetLinkageHostDetailListUser();
            GetDetailSetListUser();
        }
    })
})

//获取配置列表信息
const GetDetailSetListUser = () => {
    GetDetailSetList({ id: route.query.id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows || [];
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
//获取联动父子联动数据及关联关系
const GetLinkageHostDetailListUser = () => {
    GetLinkageHostDetailList({ id: route.query.id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            parentData.value = other.listParent || [];//父级数据
            childData.value = other.listChild || [];//子级数据
            parentName.value = other.parentName;//父级字段名称
            childName.value = other.childName;//子级字段名称
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

const radio = ref(false)//选择被绑定的Id
const tableRef = ref(null); // 表格实例 
const selectedRows = ref([]); // 子级数据
// 父级数据单选
const HandleRadioChange = (row) => {
    radio.value = row.value
    GetDetaibyParentIdUser(row.value)
}
// 父级数据单选
const handleCurrentChange = (row) => {
    if (row) {
        radio.value = row.value
        GetDetaibyParentIdUser(row.value)
    }
}

// 子级数据：行点击事件
const handleRowClick = (row) => {
    if (tableRef.value) {
        tableRef.value.toggleRowSelection(row); // 切换选中状态 
    }
};
// 子级数据：选中项变化事件
const HandleSelectChange = (val) => {
    selectedRows.value = val; // 更新选中数据 
};
// 修改  
const HandleEdit = (row) => {
    radio.value = row.ParentId
    GetDetaibyParentIdUser(row.ParentId)
}
//获取配置列表信息
const GetDetaibyParentIdUser = (parentId) => {
    GetDetaibyParentId({ id: route.query.id, parentId: parentId }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            let dicIdList = []
            // 取childData内的value与rowdeDicId相同数据
            dicIdList = childData.value.filter(t => rows.some(s => s.DicId == t.value))
            // console.log("dicIdList", dicIdList)
            childData.value.forEach(row => {
                tableRef.value.toggleRowSelection(row, false)
            })
            nextTick(() => {
                dicIdList.forEach(row => {
                    tableRef.value.toggleRowSelection(row, true)
                })
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 保存联动关系数据信息
const HandleSave = () => {
    if (!radio.value) {
        ElMessage.error("请选择父级数据");
        return
    }
    if (selectedRows.value.length == 0) {
        ElMessage.error("请选择子级数据");
        return
    }
    // console.log("selectedRows.value", selectedRows.value)
    const formData = {
        LinkAgeHostId: route.query.id,
        ParentDicId: radio.value,
        ListChildDicId: selectedRows.value.map(t => t.value)
    }
    // console.log("保存联动关系数据信息", formData)
    PostSaveLinkageDetail(formData).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            GetDetailSetListUser()
        } else {
            ElMessage.error(res.data.msg)
        }
    });

};

</script>
<template>
    <div class="viewContainer">
        <div style="display: flex;">
            <div style="width: 400px;height: 280px;">
                <el-table :data="parentData" style="width: 100%" height="280" highlight-current-row
                    @current-change="handleCurrentChange">
                    <el-table-column width="55" align="center">
                        <template #default="{ row }">
                            <el-radio v-model="radio" :value="row.value" @change="HandleRadioChange(row)"></el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column :label="'父级数据: ' + parentName" prop="label"> </el-table-column>
                </el-table>
            </div>
            <el-divider direction="vertical" style=" height:280px;" />
            <div style="width: 560px;height: 280px;">
                <el-table ref="tableRef" :data="childData" style="width: 100%;" height="280" @row-click="handleRowClick"
                    @selection-change="HandleSelectChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column :label="'子级数据:' + childName" prop="label"> </el-table-column>
                </el-table>
            </div>
        </div>
        <div style="margin: 10px 0 0 300px ;"><el-button type="success" :icon="Checked"
                @click="HandleSave(row)">保存</el-button>
        </div>
        <el-divider style="margin: 10px 0;" />
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe height="400"
            header-cell-class-name="headerClassName">
            <el-table-column prop="ParentName" label="名称" min-width="120"></el-table-column>
            <el-table-column prop="StrChildNames" label="下级数据" min-width="300"></el-table-column>
            <el-table-column label="操作" fixed="right" width="160" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>

    </div>
</template>
<style lang="scss" scoped>
:deep(.el-radio) {
    height: 20px;
}

:deep(.el-radio__inner) {
    /* 移除圆角 */
    // border-radius: 0 !important;

}
</style>