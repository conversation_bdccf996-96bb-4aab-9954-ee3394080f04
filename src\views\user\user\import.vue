<script setup>
import { UploadFilled } from '@element-plus/icons-vue'
import { onMounted, ref, watch } from 'vue'
import { useUserStore } from '@/stores';

import {
    Uploaduserfile
} from '@/api/user.js'
import { ElMessageBox, ElMessage, genFileId } from 'element-plus'
// 表格初始化
const userStore = useUserStore()
const succeedFilters = ref({ pageIndex: 1, pageSize: 10 })
const failFilters = ref({ pageIndex: 1, pageSize: 10 })
const hUnitType = ref(userStore.userInfo.UnitType)

// 翻页
watch(() => succeedFilters.value.pageIndex, () => {
    SucceedHandleSearch()
})
watch(() => succeedFilters.value.pageSize, () => {
    succeedFilters.value.pageIndex = 1
    SucceedHandleSearch()
})
// 翻页
watch(() => failFilters.value.pageIndex, () => {
    FailHandleSearch()
})
watch(() => failFilters.value.pageSize, () => {
    failFilters.value.pageIndex = 1
    FailHandleSearch()
})

//加载数据
onMounted(() => { })

//获取列表 
const SucceedHandleSearch = (page) => {
    if (page) filters.value.pageIndex = page
    succeedTableData.value = jsonSucceedData.value.slice((succeedFilters.value.pageIndex - 1) * succeedFilters.value.pageSize, succeedFilters.value.pageIndex * succeedFilters.value.pageSize)
}
//获取列表 
const FailHandleSearch = (page) => {
    if (page) failFilters.value.pageIndex = page
    failTableData.value = jsonFailData.value.slice((failFilters.value.pageIndex - 1) * failFilters.value.pageSize, failFilters.value.pageIndex * failFilters.value.pageSize)
}

const fileFile = ref()
const uploadRef = ref()
const succeedTableData = ref([])
const failTableData = ref([])
const jsonSucceedData = ref([])
const jsonFailData = ref([])
const succeedTableTotal = ref(0)
const faliTableTotal = ref(0)
// 导入前校验
const beforeAvatarUpload = (file) => {
    fileFile.value = file
    const extension = file.name.split('.')[1] === 'xls'
    const extension2 = file.name.split('.')[1] === 'xlsx'
    if (!extension && !extension2) {
        ElMessage({
            message: '上传模板只能是 xls、xlsx格式!',
            type: 'error'
        })
    }
    return extension || extension2
}
const httpRequest = () => {
    Uploaduserfile({ file: fileFile.value, roleId: 10, subdir: "User", moduleType: 7 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            const { footer } = res.data.data
            jsonSucceedData.value = JSON.parse(JSON.stringify(rows))
            jsonFailData.value = JSON.parse(JSON.stringify(footer))

            succeedTableData.value = rows
            failTableData.value = footer
            succeedTableTotal.value = rows.length
            faliTableTotal.value = footer.length
            SucceedHandleSearch()
        } else {
            ElMessage.error(res.data.msg)
        }

    })
}
//  重新上传并覆盖原来的文件
const handleExceed = (files) => {
    uploadRef.value.clearFiles()
    const file = files[0]
    file.uid = genFileId()
    uploadRef.value.handleStart(file)
    fileFile.value = file
    httpRequest()
}
</script>
<template>
    <div> 本单位用户导入模板下载:暂无模版下载方法
    </div>
    <el-upload ref="uploadRef" class="upload-demo" drag :limit="1" accept=".xlsx,.xls"
        :before-upload="beforeAvatarUpload" :on-exceed="handleExceed" :http-request="httpRequest">
        <el-icon class="el-icon--upload" style="color: #409EFF;"><upload-filled /></el-icon>
        <div class="el-upload__text">
            点击或拖转上传文件(xls、xlsx格式)
        </div>
    </el-upload>

    <!-- 上传成功表格 -->
    <div v-if="succeedTableData.length" style="margin: 20px 0;padding-bottom: 20px; border-bottom: 1px dotted #9e9e9e;">
        <div class="tableDeader">单位导入成功信息</div>
        <el-table :data="succeedTableData" highlight-current-row border stripe>
            <el-table-column prop="UnitName" label="单位名称" min-width="200" align="center"></el-table-column>
            <el-table-column prop="Name" label="姓名" min-width="160" align="center"></el-table-column>
            <el-table-column prop="Mobile" label="手机号码" min-width="120" align="center"></el-table-column>
            <el-table-column prop="AcctName" label="登录账号" min-width="180" align="center"></el-table-column>
            <el-table-column prop="Sex" label="性别" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Qq" label="QQ" min-width="160" align="center"></el-table-column>
            <el-table-column prop="Memo" label="备注" min-width="240" show-overflow-tooltip align="center">
                <template #default="{ row }">
                    <div style="color: #F56C6C;">
                        {{ row.Memo }}
                    </div>
                </template>

            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <!-- 分页 -->
        <el-row style="margin: 10px 0;">
            <el-col class="flexBox">
                <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
                    :total="succeedTableTotal" v-model:current-page="succeedFilters.pageIndex"
                    v-model:page-size="succeedFilters.pageSize" />
            </el-col>
        </el-row>
    </div>

    <!-- 上传失败表格 -->
    <div v-if="failTableData.length > 0">
        <div class="tableDeader">单位导入失败信息</div>
        <el-table :data="failTableData" highlight-current-row border stripe>
            <el-table-column prop="UnitName" label="单位名称" min-width="200" align="center"></el-table-column>
            <el-table-column prop="Name" label="姓名" min-width="160" align="center"></el-table-column>
            <el-table-column prop="Mobile" label="手机号码" min-width="120" align="center"></el-table-column>
            <el-table-column prop="AcctName" label="登录账号" min-width="180" align="center"></el-table-column>
            <el-table-column prop="Sex" label="性别" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Qq" label="QQ" min-width="160" align="center"></el-table-column>
            <el-table-column prop="Memo" label="备注" min-width="240" show-overflow-tooltip align="center">
                <template #default="{ row }">
                    <div style="color: #F56C6C;"> {{ row.Memo }} </div>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <!-- 分页 -->
        <el-row style="margin: 10px 0;">
            <el-col class="flexBox">
                <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
                    :total="faliTableTotal" v-model:current-page="failFilters.pageIndex"
                    v-model:page-size="failFilters.pageSize" />
            </el-col>
        </el-row>
    </div>


</template>
<style lang="scss" scoped>
.tableDeader {
    margin: 10px;
    font-size: 16px;
    font-weight: bold;
    color: #337ecc;

}

.taskNameConent {
    width: 100%;
    /* 具体宽度，例如 200px 或 100% */
    ;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

:deep(.el-upload-list__item-file-name) {
    color: #409EFF;
}
</style>