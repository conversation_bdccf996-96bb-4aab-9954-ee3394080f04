import request from '@/utils/homerequest.js'
// 首页---主页校服展示
export const HomeGetlistnum = (params) => {
  return request.post('/api/hyun/xuniformhome/getlistnum', params)
}
// 校服展示列表-根据查询条件获取数据集合
export const HomeGetpagelist = (params) => {
  return request.post('/api/hyun/xuniformhome/getpagelist', params)
}
// 获取首页轮播图
export const Getinformationbycatetype = (params) => {
  return request.get('/api/hyun/uniformarticle/getinformationbycatetype', { params: params })
}
// 获取底部资讯分类信息
export const ArticleGetbottomcatetype = (params) => {
  return request.get('/api/hyun/uniformarticle/getbottomcatetype', { params: params })
}
// 项目配置信息
export const AnonGetconfigbymodule = (params) => {
  return request.post('/api/hyun/anon/getconfigbymodule', null, { params: params })
}
// 获取资讯首页信息
export const ArticleGetinformation = (params) => {
  return request.get('/api/hyun/uniformarticle/getinformation', { params: params })
}
// 根据资讯分类Id查询资讯信息
export const ArticleGetinformationbycid = (params) => {
  return request.get('/api/hyun/uniformarticle/getinformationbycid', { params: params })
}
// 根据资讯Id查询资讯详情信息
export const ArticleGetinformationdetail = (params) => {
  return request.get('/api/hyun/uniformarticle/getinformationdetail', { params: params })
}
// 获取运营商资讯分类信息及默认显示信息
export const ArticleGetoperator = (params) => {
  return request.get('/api/hyun/uniformarticle/getoperator', { params: params })
}
// 运营商资讯更加分类Id查询资讯信息
export const ArticleGetoperatorlist = (params) => {
  return request.get('/api/hyun/uniformarticle/getoperatorlist', { params: params })
}
// 获取首页资讯分类对应资讯信息
export const GetArticleList = (params) => {
  return request.get('/api/hyun/uniformarticle/getarticlelist', { params: params })
}
