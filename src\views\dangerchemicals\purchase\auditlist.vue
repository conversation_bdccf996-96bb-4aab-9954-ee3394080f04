<script setup>
defineOptions({
    name: 'dangerchemicalsauditlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    DcPurchaseAuditFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { pageQuery } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: route.query.p, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const EndDate = ref()
const summation = ref({})
const routerObject = ref({})//成页面携带的参数对象 
const options = ref([
    { value: 'BatchNo', label: '采购批次', },
    { value: 'UserName', label: '申请人', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象 
    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象 

    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    filters.value.Statuz = routerObject.value.p
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcPurchaseAuditFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (rows.Statistics) {
                summation.value = res.data.data.rows.Statistics[0]
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.RegDatege = undefined
    filters.value.RegDatele = undefined
    EndDate.value = undefined
    filters.value.Statuz = undefined
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}

//查看危化品明细
const HandleDetailView = (row) => {
    router.push({
        path: "/dangerchemicals/purchase/detaillist", query: {
            Id: row.Id,
            batchNo: row.BatchNo
        }
    })
}

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = '总计：';
            return;
        }
        else if (index == 2) {
            sums[index] = summation.value.Amount ? '￥' + Number(summation.value.Amount).toFixed(2) : '--'
            return;
        }
    });
    return sums;
}

//审核
const Audit = (id) => {
    router.push({
        path: "/dangerchemicals/purchase/audit", query: {
            Id: id,
            p: routerObject.value.p
        }
    })
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="UserName" label="申请人" min-width="160"></el-table-column>
            <el-table-column prop="BatchNo" label="采购批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Amount" label="参考金额" min-width="160" align="right">
                <template #default="{ row }">
                    {{ row.Amount ? '￥' + Number(row.Amount).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DeviceDetail" label="危化品明细" min-width="80" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetailView(row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 10" type="primary" link @click="Audit(row.Id)">审核</el-button>
                    <el-button v-if="row.Statuz == 20" type="primary" link @click="Audit(row.Id)">审批</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>