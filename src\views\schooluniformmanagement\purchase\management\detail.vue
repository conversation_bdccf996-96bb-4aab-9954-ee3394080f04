<script setup>
defineOptions({
    name: 'managementdetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    Getpagedbytype,
} from '@/api/user.js'
import {
    PurchaseGeteditbyid
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
const route = useRoute()
const formData = ref({})
const refForm = ref()
const uploadFileData = ref([])
const IsRenewalStatuzList = ref([])
const SupplierListList = ref([])
const contractMainBodyList = ref([])
const payMethodList = ref([])
const supplierLocationList = ref([])
const purchaseId = ref()
const BuyList = ref([])
const StageList = ref([])

onMounted(() => {
    GetpagedbytypeUser()
    if (route.query.id) {
        purchaseId.value = route.query.id
    }
    nextTick(() => {
        if (route.query.isTagRouter) {
            PurchaseGeteditbyidUser(purchaseId.value)
        }
    })
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)

    if (route.query.id) {
        purchaseId.value = route.query.id
    }
    nextTick(() => {
        if (!route.query.isTagRouter) {
            PurchaseGeteditbyidUser(purchaseId.value)
        }
    })
})
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    let paraData = {}
    paraData = { moduleType: 102 }
    Getpagedbytype(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.categoryList = []
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const PurchaseGeteditbyidUser = (id) => {
    PurchaseGeteditbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            uploadFileData.value.forEach(item => {
                item.categoryList = []
            })
            formData.value = rows
            if (rows.IsContractRenewal === 0) {
                formData.value.IsContractRenewal = undefined
            } else {
                formData.value.IsContractRenewal = String(rows.IsContractRenewal)
            }
            if (rows.ContractMainBodyId === 0) {
                formData.value.ContractMainBodyId = undefined
            } else {
                formData.value.ContractMainBodyId = String(rows.ContractMainBodyId)
            }
            if (rows.SupplierLocationId === 0) {
                formData.value.SupplierLocationId = undefined
            } else {
                formData.value.SupplierLocationId = String(rows.SupplierLocationId)
            }
            let categoryList = other.AttachmentList || [];//附件集合
            if (categoryList.length > 0) {
                // 遍历数组 categoryList 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 uploadFileData中具有相同 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
            IsRenewalStatuzList.value = other.IsRenewalStatuz || [];//是否为续签合同
            SupplierListList.value = other.SupplierList || [];//供应商
            contractMainBodyList.value = other.ContractMainBody || [];//合同签订主体
            payMethodList.value = other.PayMethod || [];//费用支付方式
            supplierLocationList.value = other.SupplierLocation || [];//供应商属地
            StageList.value = other.StageList || [];//学段
            BuyList.value = other.BuyList || [];//采购批次
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}

</script>
<template>
    <el-form style="width: 100%;min-width: 560px;" :inline="true" class="mobile-box" @submit.prevent ref="refForm"
        :model="formData" label-width="180px" status-icon>
        <el-form-item label="采购批次：" class="formItem">
            <el-select v-model="formData.UniformBuyId" disabled class="item_content">
                <el-option v-for="item in BuyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item class="formItem"> </el-form-item>
        <el-form-item label="年度：" class="formItem">
            <el-input v-model="formData.PurchaseYear" disabled></el-input>
        </el-form-item>
        <el-form-item label="合同批次：" class="formItem">
            <el-input v-model="formData.PurchaseNo" disabled></el-input>
        </el-form-item>
        <el-form-item label="续签合同：" class="formItem">
            <el-radio-group v-model="formData.IsContractRenewal" disabled>
                <el-radio v-for="item in IsRenewalStatuzList" :key="item.value" :value="item.value">
                    {{ item.label }}</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="合同签订主体：" class="formItem">
            <el-select v-model="formData.ContractMainBody" disabled class="item_content">
                <el-option v-for="item in contractMainBodyList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="订购总人数：" class="formItem">
            <el-input v-model="formData.ContractPersonNum" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="其中：" class="formItem" v-if="StageList.length > 1">
            <el-form-item v-for="item in StageList" :key="item.value" :label="item.label" label-width="60px"
                style="width: 33.3%;">
                <el-input v-model="formData[item.value]" disabled class="item_content"></el-input>
            </el-form-item>
        </el-form-item>

        <el-form-item label="合同签订日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.ContractSignDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="合同开始日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.ContractStartDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="合同终止日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.ContractEndDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="合同金额（元）：" class="formItem">
            <el-input v-model="formData.ContractAmount" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="费用支付方式：" class="formItem">
            <el-select v-model="formData.PayMethod" disabled class="item_content">
                <el-option v-for="item in payMethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="供应商：" class="formItem">
            <el-select v-model="formData.SupplierId" disabled>
                <el-option v-for="item in SupplierListList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="供应商归属地：" class="formItem">
            <el-select v-model="formData.SupplierLocationId" disabled class="item_content">
                <el-option v-for="item in supplierLocationList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="供货期（天）：" class="formItem">
            <el-input v-model="formData.GoodsDeadline" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="质保期（月）：" class="formItem">
            <el-input v-model="formData.WarrantyMonth" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="送货日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.DeliveryDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="验收日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.AcceptanceDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="供应商送检日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.SupplierSendTestDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="学校送检日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.SchoolSendTestDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%;">
            <el-input type="textarea" v-model="formData.Memo" disabled :maxlength="300" show-word-limit
                :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
        </el-form-item>
        <el-form-item v-for="(item, index) in uploadFileData" :key="index" :label="item.Name + '：'"
            style="width: 100%;">
            <div class="fileFlex">
                <div v-for="(itemCate, indexCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                    <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                        {{ itemCate.Title }}{{ itemCate.Ext }}
                    </span>
                </div>
            </div>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-right: 0;
}

.item_content {
    // width: 60% !important;
}

:deep(.el-date-editor.el-input) {
    width: 100% !important;
}

.formItem {
    width: 50%;
}

@media (max-width: 768px) {
    .formItem {
        width: 100%;
    }
}
</style>