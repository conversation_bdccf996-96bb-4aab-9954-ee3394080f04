import axios from 'axios'
import { ElLoading, ElMessage } from 'element-plus'
const baseURL = ''
//数据请求用
const instance = axios.create({
  baseURL,
  timeout: 600000
})

var baseURLPath = ''
instance.interceptors.request.use(
  (config) => {
    baseURLPath = config.url
    return config
  },
  (error) => {
    Promise.reject(error)
  }
)

instance.interceptors.response.use(
  async (res) => {
    // console.log("res", res)
    if (res.data.flag >= 0 || res.data.success) {
      // console.log("res.data", res.data)
      return res
    } else {
      ElMessage.error(res.data.msg || '请求出错')
      return Promise.reject(res.data)
    }
  },
  async (err) => {
    // console.log("请求失败err", err)
    // 错误默认情况
    if (err.response && err.response.data) {
      ElMessage.error(err.response.data.msg)
    } else {
      ElMessage.error('服务异常')
    }
    // router.push('/login')
    return Promise.reject(err)
  }
)

export default instance
export { baseURL }
