<script setup>
defineOptions({
    name: 'teacheraffairstudentmanage'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd, Download, Upload, UploadFilled
} from '@element-plus/icons-vue'
import {
    PstudentGetpagedbyteacher, PstudentTeacheraddstu, PstudentTeachereditstu, PstudentTeacherimportstu, PstudentTeacherdelstu
} from '@/api/user.js'
import {
    Exportteacherstu
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { fileDownload, integerLimit, ExcelDownload } from "@/utils/index.js";
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const isAdd = ref(false)
const filePath = ref('')
// const oldIDCard = ref('')
// const idCardRegex = /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(10|12))(([0-2][1-9])|10|20|30|31)\d{3}[\dXx]$/
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    StudentNo: [
        { required: true, message: '请输入学生学号', trigger: 'change' },
    ],
    StudentName: [
        { required: true, message: '请输入学生姓名', trigger: 'change' },
    ],
    Sex: [
        { required: true, message: '请选择学生性别', trigger: 'change' },
    ],
    // IDCard: [
    //     { required: true, message: '请输入学生身份证号', trigger: 'change' },
    //     {
    //         validator: (rule, value, callback) => {
    //             if (!value) {
    //                 return callback(new Error('请输入身份证号'));
    //             }
    //             if (oldIDCard.value != dialogData.value.IDCard && !idCardRegex.test(value)) {
    //                 callback(new Error('身份证号格式不正确'));
    //             } else {
    //                 callback();
    //             }
    //         },
    //         trigger: 'blur'
    //     }
    // ],
}
// const idType = ref('mainland')  //身份证类型
// const idNumberValidators = ref({
//     mainland: (rule, value, callback) => {
//         // 中国大陆身份证校验规则（简单示例，实际应更严格）
//         const mainlandRegex = /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(10|12))(([0-2][1-9])|10|20|30|31)\d{3}[\dXx]$/;
//         if (!mainlandRegex.test(value)) {
//             callback(new Error('请输入有效的中国大陆身份证号码'));
//         } else {
//             callback();
//         }
//     },
//     hongkong: (rule, value, callback) => {
//         // 香港身份证校验规则（简单示例，不包含校验码验证）
//         const hongkongRegex = /^([A-Z]|[1-9])\d{3}(\([A-Z0-9]\)|)\d{2}$|^[A-Z]{2}\d{6}(\([A-Z]\))?$/;
//         if (!hongkongRegex.test(value)) {
//             callback(new Error('请输入有效的香港身份证号码'));
//         } else {
//             callback();
//         }
//     },
//     macao: (rule, value, callback) => {
//         // 澳门身份证校验规则（简单示例，不包含校验码验证）
//         const macaoRegex = /^[MF](?:[0-9]{6}(?:[A-Z0-9X])?|[0-9]{7})$|^[C](?:[0-9]{6}(?:[A-Z0-9])?)$/;
//         if (!macaoRegex.test(value)) {
//             callback(new Error('请输入有效的澳门身份证号码'));
//         } else {
//             callback();
//         }
//     },
// })

onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
//添加
const HandleAdd = () => {
    isAdd.value = true
    dialogVisible.value = true
    nextTick(() => {
        dialogData.value = {}
        // idType.value = 'mainland'
        refForm.value.resetFields()

    })
}

//导出
const HandleExport = () => {
    Exportteacherstu(filters.value).then(res => {
        // console.log(res)
        ExcelDownload(res)
    });
}

// 修改
const HandleEdit = (row) => {
    isAdd.value = false
    dialogData.value.Id = row.Id
    // dialogData.value.IDCard = row.IDCard6
    dialogData.value.StudentName = row.StudentName
    dialogData.value.StudentNo = row.StudentNo
    dialogData.value.Sex = row.Sex
    dialogVisible.value = true
    // oldIDCard.value = row.IDCard6
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定删除学生信息吗?')
        .then(() => {
            PstudentTeacherdelstu({ id: row.Id }).then((res) => {
                HandleTableData()
                ElMessage.success('删除成功')
            })
        })
        .catch((err) => {
            console.info(err)
        })
}

//学生信息提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (isAdd.value) {
            PstudentTeacheraddstuUser()
        } else {
            PstudentTeachereditstuUser()
        }
    })
}
// 添加
const PstudentTeacheraddstuUser = () => {
    PstudentTeacheraddstu(dialogData.value).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '添加成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 修改
const PstudentTeachereditstuUser = () => {
    PstudentTeachereditstu(dialogData.value).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
// 导入学生
const HandleStudentUpload = () => {
    uploadVisible.value = true
}
// 模板下载
const HandleDownload = () => {
    fileDownload(filePath.value);
}


//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst

    PstudentGetpagedbyteacher(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            if (isFirst) {
                filePath.value = other.filePath;
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const fileFile = ref()
const uploadRef = ref()
// 导入前校验
const beforeAvatarUpload = (file) => {
    fileFile.value = file
    const extension = file.name.split('.')[1] === 'xls'
    const extension2 = file.name.split('.')[1] === 'xlsx'
    if (!extension && !extension2) {
        ElMessage({
            message: '上传模板只能是 xls、xlsx格式!',
            type: 'error'
        })
    }
    return extension || extension2
}
const httpRequest = () => {
    PstudentTeacherimportstu([fileFile.value]).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '导入成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem flexOperation">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                        <el-button type="success" :icon="Upload" @click="HandleStudentUpload">导入学生</el-button>
                        <el-button type="success" :icon="Upload" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="学号/姓名"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="font-size: 14px;color: #606266;margin-bottom: 10px;" v-if="tableData.length > 0">班级：
            <span style="color: #999;">{{ tableData[0].GradeName }}{{ tableData[0].ClassName }}</span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="StudentNo" label="学号" min-width="120" align="center"></el-table-column>
            <el-table-column prop="StudentName" label="姓名" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Sex" label="性别" min-width="80" align="center"></el-table-column>
            <!-- <el-table-column prop="IDCard6" label="身份证号" min-width="120" align="center"></el-table-column> -->
            <el-table-column fixed="right" label="操作" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <el-dialog v-model="dialogVisible" :title="isAdd ? '添加学生' : '修改学生信息'" draggable width="680px"
            :close-on-click-modal="false">
            <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                status-icon>
                <el-form-item label="姓名：" prop="StudentName">
                    <el-input v-model="dialogData.StudentName" placeholder="学生姓名" style="width: 60%;"></el-input>
                </el-form-item>
                <el-form-item label="学号：" prop="StudentNo">
                    <el-input v-model="dialogData.StudentNo" @input="integerLimitInput($event, 'StudentNo')"
                        placeholder="入学年代+班级号+序号，如：20250621" style="width: 60%;"></el-input>
                </el-form-item>
                <el-form-item label="性别：" prop="Sex">
                    <el-radio-group v-model="dialogData.Sex">
                        <el-radio value="男"> 男</el-radio>
                        <el-radio value="女"> 女</el-radio>
                    </el-radio-group>
                </el-form-item>
                <!-- <el-form-item label="证件类型：">
                    <el-select v-model="idType" placeholder="请选择证件类型" style="width: 60%;">
                        <el-option label="中国大陆身份证" value="mainland"></el-option>
                        <el-option label="香港身份证" value="hongkong"></el-option>
                        <el-option label="澳门身份证" value="macao"></el-option> 
                    </el-select>
                </el-form-item>
                <el-form-item label="身份证号：" prop="IDCard"
                    :rules="[{ validator: idNumberValidators[idType], trigger: 'blur' }]">
                    <el-input v-model="dialogData.IDCard" placeholder="学生身份证号" style="width: 60%;"></el-input>
                </el-form-item> -->
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 导入学生弹窗 -->
        <el-dialog v-model="uploadVisible" title="学生导入" draggable width="480px">
            <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" label-width="120px" status-icon>
                <el-form-item>
                    <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" accept=".xlsx,.xls"
                        style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload"
                        :http-request="httpRequest">
                        <el-button type="primary" :icon="UploadFilled">导入学生</el-button>
                    </el-upload>
                    <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>