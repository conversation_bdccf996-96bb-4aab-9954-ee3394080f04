<script setup>
defineOptions({
    name: 'maintainenteredit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    QuestionFilled, Select, UploadFilled, View, Position, CircleClose, Plus, Delete
} from '@element-plus/icons-vue'
import {
    Getpagedbytype,
    AttachmentUpload,
} from '@/api/user.js'
import {
    ListGeteditbyid, ListSaveadd, ListSaveedit, ListDelattachmentbyid, ListDelsizebyid
} from '@/api/purchase.js'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
import { ElMessageBox, ElMessage } from 'element-plus'
import { limit, integerLimit, foundReturn, tagsListStore, UniformtypeList, SexList } from "@/utils/index.js";
import ImgCorpper from '@/components/ImgCorpper/index.vue'
// 二维码生成下载
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
const router = useRouter()
const userStore = useUserStore()
const route = useRoute()
const formData = ref({})
const AttachmentList = ref([])
const BrandList = ref([])
const NameList = ref([])
const OriginAddressList = ref([])
const ProducerList = ref([])
const SchoolList = ref([])
const SecurityLevelList = ref([])
const UniformSizeList = ref([])
const UniformAmendSizeList = ref([])//操作的尺码列表
const UniformShowSizeList = ref([])//展示的尺码列表 
const UnitNameList = ref([])
const PurchaseDemandList = ref([])
const refForm = ref()
const purchaseId = ref()
const isDetailId = ref()
const isAdd = ref(false)
const ruleForm = {
    SchoolId: [
        { required: true, message: '请选择适用学校', trigger: 'change' },
    ],
    Uniformtype: [
        { required: true, message: '请输入种类', trigger: 'change' },
    ],
    Name: [
        { required: true, message: '请输入品名', trigger: 'change' },
    ],
    Brand: [
        { required: true, message: '请输入品牌', trigger: 'change' },
    ],
    Price: [
        { required: true, message: '请输入单价', trigger: 'change' },
    ],
    Sex: [
        { required: true, message: '请选择适合性别', trigger: 'change' },
    ],
    PurchaseDemand: [
        { required: true, message: '请选择采购要求', trigger: 'change' },
    ],
    StandardNum: [
        { required: true, message: '请输入标配数量', trigger: 'change' },
    ],
    UnitName: [
        { required: true, message: '请选择单位', trigger: 'change' },
    ],
    Producer: [
        { required: true, message: '请选择生产厂商', trigger: 'change' },
    ],
    OriginAddress: [
        { required: true, message: '请选择产地', trigger: 'change' },
    ],
    SecurityLevel: [
        { required: true, message: '请选择安全等级', trigger: 'change' },
    ],
}

onMounted(() => {
    GetpagedbytypeUser()
    if (route.query.id) {
        purchaseId.value = route.query.id
        isDetailId.value = route.query.id
        formData.value.Id = route.query.id
        if (route.query.id == '0') {
            purchaseId.value = 0
            isDetailId.value = 0
            formData.value.Id = 0
            isAdd.value = true
            nextTick(() => {
                refForm.value.resetFields()
            })
        } else {
            isAdd.value = false
        }
    }

    if (route.query.isTagRouter) {
        fileIdList.value = []
        ListGeteditbyidUser(purchaseId.value)
    }
})
onActivated(() => {
    // 修改tag标签名称########################
    if (route.query.title) {
        // 修改pinia数据
        userStore.$patch(state => {
            state.pageTitleObj.maintainTitle = route.query.title
        })
    }
    // pageTitleObj
    let tagsList = userStore.tagsList
    // 使用forEach遍历数组  修改tag标签名称
    tagsList.forEach(item => {
        if (item.path == route.path) {
            // 如果path匹配，则修改title  
            item.title = userStore.pageTitleObj.maintainTitle;
        }
    });
    userStore.setTagsList(tagsList)
    // ########################
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)

    if (route.query.id) {
        purchaseId.value = route.query.id
        isDetailId.value = route.query.id
        formData.value.Id = route.query.id
        if (route.query.id == '0') {
            purchaseId.value = 0
            isDetailId.value = 0
            formData.value.Id = 0
            isAdd.value = true
            nextTick(() => {
                refForm.value.resetFields()

            })
        } else {
            isAdd.value = false
        }
    }

    nextTick(() => {
        if (!route.query.isTagRouter) {
            fileIdList.value = []
            ListGeteditbyidUser(purchaseId.value)
        }
    })
})
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 103 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//输入保留两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}

//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
// 信息详情
const ListGeteditbyidUser = (id) => {
    ListGeteditbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            AttachmentList.value = other.AttachmentList || [];//附件
            BrandList.value = other.BrandList || [];//品牌
            NameList.value = other.NameList || [];//品名
            OriginAddressList.value = other.OriginAddressList || [];//产地
            ProducerList.value = other.ProducerList || [];//生产厂商
            SchoolList.value = other.SchoolList || [];//适用学校
            SecurityLevelList.value = other.SecurityLevelList || [];//安全等级
            UniformSizeList.value = other.UniformSizeList || [];//已添加尺码
            UniformShowSizeList.value = other.UniformSizeList || [];//展示的尺码
            UniformAmendSizeList.value = other.UniformSizeList || [];//操作尺码 
            UnitNameList.value = other.UnitNameList || [];//单位
            PurchaseDemandList.value = other.PurchaseDemandList || [];//采购要求
            uploadFileData.value.forEach(item => {
                item.fileLChildList = []
                item.categoryList = []
            })
            if (rows) {
                formData.value.Brand = rows.Brand;
                formData.value.Name = rows.Name;
                formData.value.OriginAddress = rows.OriginAddress;
                formData.value.Producer = rows.Producer;
                formData.value.SchoolId = rows.SchoolId;
                formData.value.SecurityLevel = rows.SecurityLevel;
                formData.value.Uniformtype = rows.Uniformtype;
                formData.value.UnitName = rows.UnitName;
                formData.value.Parameter = rows.Parameter;//具体参数
                formData.value.Sex = rows.Sex;//适合性别
                formData.value.StandardNum = rows.StandardNum;//标配数量
                formData.value.Price = rows.Price;//单价
                formData.value.PurchaseDemand = rows.PurchaseDemand;//单价
            } else {
                formData.value = {}
            }

            let categoryList = other.AttachmentList || []
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            } else {
                uploadFileData.value.forEach(item => {
                    item.categoryList = []
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//提交
const HandleSubmit = (num) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) {
            const firstErrorProp = Object.keys(fields)[0];
            if (firstErrorProp) {
                if (['SchoolId', 'Uniformtype', 'Name', 'Brand', 'Price', 'Sex'].includes(firstErrorProp)) {
                    activeName.value = 'essentialTab'
                } else {
                    activeName.value = 'otherTab'
                }
            }
            return;
        };
        // 判断必传的附件是否已传
        let found = foundReturn(uploadFileData.value)
        if (found) {
            activeName.value = 'fileTab'
            return
        }
        if (UniformShowSizeList.value.length == 0) {
            activeName.value = 'essentialTab'
            ElMessage.error("请添加尺码")
            return
        }
        ElMessageBox.confirm('确定提交吗?')
            .then(() => {
                formData.value.AttachmentIdList = fileIdList.value.map(t => t.Id)
                formData.value.SizeList = UniformShowSizeList.value.filter(t => !t.Id)
                if (formData.value.Id) {
                    formData.value.SchemeNo = undefined
                    ListSaveeditUser()
                } else {
                    formData.value.Id = undefined
                    ListSaveaddUser()
                }
            })
            .catch((err) => {
                console.info(err)
            })
    })

}
// 添加保存
const ListSaveaddUser = (row) => {

    ListSaveadd(formData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            // fileIdList.value = [];//Id
            // 新增后生成Id
            isDetailId.value = res.data.data.rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 修改保存
const ListSaveeditUser = (row) => {
    ListSaveedit(formData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            fileIdList.value = [];//Id
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 预览
const HandlePreview = () => {
    const { href } = router.resolve({
        path: "/preview",
        query: { id: isDetailId.value, requestNum: 1 }
    });
    window.open(href, "_blank");
}

//新增尺码操作
const dialogVisible = ref(false)
const dialogData = ref({})
const refDialogForm = ref()
const ruleDialogForm = {
    statuz: [
        { required: true, message: '请选择是否通过审核', trigger: 'change' },
    ],
    explanation: [
        { required: true, message: '请输入退回原因', trigger: 'change' },
    ],
}
// 尺码弹窗
const HandleDialog = () => {
    dialogVisible.value = true
}
// 添加尺码
const HandleSizeAdd = () => {
    UniformAmendSizeList.value.push({ StandardName: '', Sort: UniformAmendSizeList.value.length + 1, Id: '' })
}
// 弹窗删除尺码
const delStandard = (item, i) => {
    if (item.Id) {
        ListDelsizebyid({ id: formData.value.Id, sizeid: item.Id }).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '删除成功')
                UniformShowSizeList.value.splice(i, 1)
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        UniformAmendSizeList.value.splice(i, 1)
    }
    UniformAmendSizeList.value = UniformShowSizeList.value
}
// 展示删除尺码
const delShowStandard = (item, i) => {
    if (item.Id) {
        ListDelsizebyid({ id: formData.value.Id, sizeid: item.Id }).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '删除成功')
                UniformShowSizeList.value.splice(i, 1)
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        UniformShowSizeList.value.splice(i, 1)
    }
    UniformAmendSizeList.value = UniformShowSizeList.value
}
// 尺码名称
const handleInput = (index, value) => {
    UniformAmendSizeList.value[index].StandardName = value;
}
// 尺码排序
const handleSort = (index, value) => {
    UniformAmendSizeList.value[index].Sort = value;
}
// 保存尺码
const HandleSizeSubmit = () => {
    UniformShowSizeList.value = UniformAmendSizeList.value.filter(t => t.StandardName)
    dialogVisible.value = false
}

// /////    附件处理      /////
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const uploadFileData = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.categoryList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: item.FileCategory }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    ListDelattachmentbyid({ id: formData.value.Id, attid: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const dialogVisibleCorpper = ref(false)
const corpperItem = ref({})
const corpperIndex = ref(0)
// 选择图片截取
const btnCorpper = (item, index) => {
    // console.log("item", item)
    let length = item.categoryList.length + item.fileLChildList.length
    if (!item.MaxFileNumber || item.MaxFileNumber > length) {
        dialogVisibleCorpper.value = true
    } else {
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
    }
    corpperItem.value = item
    corpperIndex.value = index
}

// 组件提交事件
const confirm = (file) => {
    console.log("file", file)
    AttachmentUpload({ file: file, filecategory: corpperItem.value.FileCategory }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[corpperIndex.value].fileLChildList.push(rows[0])
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
        let item = uploadFileData.value[corpperIndex.value]
        let length = item.categoryList.length + item.fileLChildList.length
        if (item.MaxFileNumber && item.MaxFileNumber == length) {
            dialogVisibleCorpper.value = false
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList, index) => {
    let path = e.Path;
    let list = [...uploadFileData.value[index].fileLChildList, ...uploadFileData.value[index].categoryList]
    viewPhotoList.value = list.map(t => t.Path)
    showViewer.value = true;
    viewPhotoList.value.forEach((item, i) => {
        if (path == item) {
            imgSrcIndex.value = i;
        }
    });

    // //大图预览从点击的那张开始
    let tempImgList = [...viewPhotoList.value];
    let temp = [];
    for (let i = 0; i < imgSrcIndex.value; i++) {
        temp.push(tempImgList.shift());
    }
    viewPhotoList.value = tempImgList.concat(temp);
}

const activeName = ref('essentialTab')
// 手机预览 二维码弹窗
const dialogPreviewVisible = ref(false)
const qrPreviewText = ref('')
// 预览
const HandlePreviewMobile = (num) => {
    dialogPreviewVisible.value = true
    let url = window.location.protocol + '//' + window.location.host
    qrPreviewText.value = url + "/ui/#" + "/pages/purchase/orderDetail?id=" + isDetailId.value;
}

</script>
<template>
    <el-form style="width: 1000px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
        label-width="180px" status-icon>
        <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="基本信息" name="essentialTab">
                <el-form-item prop="SchoolId">
                    <template #label>
                        <el-tooltip class="item" effect="dark" content="如下拉框没有你所需的学校名称，请通知学校注册。" placement="top">
                            <div>
                                <el-icon color="#E6A23C" class="tipIcon">
                                    <QuestionFilled />
                                </el-icon>
                            </div>
                        </el-tooltip>
                        <span> 适用学校： </span>
                    </template>
                    <el-select v-model="formData.SchoolId" placeholder="请选择适用学校" style="width: 50%;">
                        <el-option v-for="item in SchoolList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="种类：" prop="Uniformtype">
                    <el-select v-model="formData.Uniformtype" placeholder="请选择种类" style="width: 50%;">
                        <el-option v-for="item in UniformtypeList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="品名：" prop="Name">
                    <el-select v-model="formData.Name" filterable allow-create default-first-option
                        :reserve-keyword="false" placeholder="请输入品名。如:T恤" style="width: 50%;">
                        <el-option v-for="item in NameList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="品牌：" prop="Brand">
                    <el-select v-model="formData.Brand" filterable allow-create default-first-option
                        :reserve-keyword="false" placeholder="请输入品牌" style="width: 50%;">
                        <el-option v-for="item in BrandList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="具体参数：" prop="Parameter">
                    <el-input v-model="formData.Parameter" maxlength="100" style="width: 50%"
                        placeholder="如：面料、成分等，限100字" show-word-limit autosize type="textarea" />
                </el-form-item>
                <el-form-item label="单价（元）：" prop="Price">
                    <el-input v-model="formData.Price" auto-complete="off" placeholder="根据合同填写"
                        @input="limitInput($event, 'Price')" style="width: 50%;"></el-input>
                </el-form-item>
                <el-form-item label="适合性别：" prop="Sex">
                    <el-select v-model="formData.Sex" placeholder="请选择适合性别" style="width: 50%;">
                        <el-option v-for="item in SexList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="尺码：">
                    <div>
                        <el-button type="success" size="small" :icon="Plus" @click="HandleDialog">添加</el-button>
                        <div style="display: flex;flex-wrap: wrap;">
                            <div class="sizeStandard" v-for="(item, index) in UniformShowSizeList" :key="index">
                                {{ item.StandardName }}
                                <el-icon class="sizeIcon" color="#e3393c" size="16"
                                    @click="delShowStandard(item, index)">
                                    <CircleClose />
                                </el-icon>
                            </div>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="采购要求" prop="PurchaseDemand">
                    <el-radio-group v-model="formData.PurchaseDemand">
                        <el-radio v-for="item in PurchaseDemandList" :key="item.value" :value="item.value">{{ item.label
                            }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.PurchaseDemand == '502001'" label="标配数量：" prop="StandardNum">
                    <el-input v-model="formData.StandardNum" auto-complete="off" placeholder="是指每个学生标准配置的数量"
                        @input="integerLimitInput($event, 'StandardNum')" style="width: 50%;"></el-input>
                </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="产品信息" name="otherTab">
                <el-form-item label="单位：" prop="UnitName">
                    <el-select v-model="formData.UnitName" filterable placeholder="请选择单位" style="width: 50%;">
                        <el-option v-for="item in UnitNameList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="生产厂商：" prop="Producer">
                    <el-select v-model="formData.Producer" filterable allow-create default-first-option
                        :reserve-keyword="false" placeholder="请输入生产厂商。生产厂商全称" style="width: 50%;">
                        <el-option v-for="item in ProducerList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="产地：" prop="OriginAddress">
                    <el-select v-model="formData.OriginAddress" filterable allow-create default-first-option
                        :reserve-keyword="false" placeholder="请输入产地。实际生产地，如：南京" style="width: 50%;">
                        <el-option v-for="item in OriginAddressList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="安全等级：" prop="SecurityLevel">
                    <el-select v-model="formData.SecurityLevel" filterable allow-create default-first-option
                        :reserve-keyword="false" placeholder="请输入安全等级" style="width: 50%;">
                        <el-option v-for="item in SecurityLevelList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="附件信息" name="fileTab">
                <!-- 附件上传 -->
                <div style="margin-top: 10px;"></div>
                <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                    <template #label>
                        <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                        <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                            <div>
                                <el-icon color="#E6A23C" class="tipIcon">
                                    <QuestionFilled />
                                </el-icon>
                            </div>
                        </el-tooltip>
                        <span> {{ item.Name }}： </span>
                    </template>
                    <div style="width: 100px; height: 100%;">
                        <div v-if="item.FileCategory === '103003'">
                            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                                accept=".jpg,.jpeg,.png" :before-upload="beforeAvatarUpload.bind(null, item)"
                                :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                                <el-button type="success" size="small" :icon="UploadFilled"
                                    @click="MaxFileNumberClick(item)">上传</el-button>
                            </el-upload>
                        </div>
                        <div v-else>
                            <el-button type="success" size="small" :icon="UploadFilled"
                                @click="btnCorpper(item, index)">选择图片</el-button>
                        </div>
                    </div>
                    <div style="display: flex;">
                        <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" class="Imgposition">
                            <span style="cursor: pointer;"
                                @click="fileListDownload(itemCate, item.categoryList, index)">
                                <el-image v-if="itemCate.Path" class="img" :src="itemCate.Path" />
                            </span>
                            <el-icon color="#F56C6C" class="iconColor" @click="delCateFile(itemCate, index)">
                                <Delete />
                            </el-icon>
                        </div>
                        <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id" class="Imgposition">
                            <span style="cursor: pointer;"
                                @click="fileListDownload(itemChild, item.fileLChildList, index)">
                                <el-image v-if="itemChild.Path" class="img" :src="itemChild.Path" />
                            </span>
                            <el-icon color="#F56C6C" class="iconColor" @click="delFile(itemChild, index)">
                                <Delete />
                            </el-icon>
                        </div>
                    </div>
                </el-form-item>
            </el-tab-pane>
        </el-tabs>
        <el-form-item>
            <el-button type="primary" :icon="Select" @click="HandleSubmit(1)">保存</el-button>
            <el-button type="success" v-if="isDetailId" :icon="View" @click="HandlePreview">电脑预览</el-button>
            <el-button type="primary" v-if="isDetailId" :icon="Position" @click="HandlePreviewMobile">手机预览</el-button>
        </el-form-item>
    </el-form>
    <!-- 图片截取 -->
    <div class="avatar-box">
        <ImgCorpper v-model:dialogVisibleCorpper="dialogVisibleCorpper" @confirm="confirm" />
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    <!-- 弹窗 -->
    <el-dialog v-model="dialogVisible" title="添加尺码" draggable width="600px" :close-on-click-modal="false">

        <el-form @submit.prevent ref="refDialogForm" :model="dialogData" :rules="ruleDialogForm" status-icon>
            <el-button type="success" size="small" :icon="Plus" @click="HandleSizeAdd">新增尺码</el-button>
            <div class="dialogAddInput" v-for="(item, index) in UniformAmendSizeList" :key="index">
                <el-form-item label="尺码" style="width: 40%;">
                    <el-input v-model="item.StandardName" size="small" @input="handleInput(index, $event)"></el-input>
                </el-form-item>
                <el-form-item label="排序" style="width: 40%;">
                    <el-input v-model="item.Sort" size="small" @input="handleSort(index, $event)"></el-input>
                </el-form-item>
                <el-form-item style="width: 10%;">
                    <el-button type="danger" plain size="small" :icon="Delete"
                        @click="delStandard(item, index)">删除</el-button>
                </el-form-item>
            </div>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="HandleSizeSubmit"> 保存 </el-button>
            </span>
        </template>
    </el-dialog>
    <!-- 手机预览 二维码弹窗-->
    <el-dialog v-model="dialogPreviewVisible" title="手机预览二维码" width="460px">
        <div style="text-align: center;font-size: 18px;color:#999;">请使用手机扫描二维码预览</div>
        <div style="text-align: center;">
            <vue-qr :text="qrPreviewText" :size="160"></vue-qr>
        </div>
    </el-dialog>
</template>
<style lang="scss" scoped>
.sizeStandard {
    position: relative;
    color: #666666;
    font-size: 12px;
    margin: 5px;
    padding: 0 8px;
    border: 1px solid #ccc;
    cursor: pointer;
}

.sizeStandard:hover {
    border-color: #e3393c;
}

.sizeIcon {
    position: absolute;
    top: -7px;
    right: -7px;
    background-color: #ffffff;
    display: none;
}

.sizeStandard:hover .sizeIcon {
    display: block;
}

.dialogAddInput {
    display: flex;

    .el-form-item {
        margin-left: 10px;
        margin-right: 10px;
        margin-bottom: 5px;
    }
}

.avatar-box {
    width: 300px;
    display: flex;
    justify-content: center;
}

.Imgposition {
    color: #409EFF;
    position: relative;
    padding-top: 5px;
    margin: 0 10px 10px 5px;
}

.img {
    width: 50px;
    height: 50px;
    cursor: pointer;
}

.iconColor {

    cursor: pointer;
    position: absolute;
    padding: 2px;
    border-radius: 50%;
    background-color: #fff;
    border: 1px solid #F56C6C;
    top: -5px;
    right: -5px;
    display: none;
}


.Imgposition:hover .iconColor {
    display: block;
}
</style>
