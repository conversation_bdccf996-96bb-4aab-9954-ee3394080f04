<script setup>
defineOptions({
    name: 'dangerchemicalsstandbookapplyprint'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Back, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcApplyFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { fileDownload, tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 9999999, IsGrant: 1, sortModel: [{ SortCode: "GrantDate", SortType: "DESC" }] })
const attribute = ref()

const GrantDatege = ref('')
const GrantDatele = ref('')
const format = (date) => {
    if (date) {
        const [year, month, day] = date.split('-');
        return `${year}年${month}月${day}日`;
    } else {
        return ''
    }
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    GrantDatege.value = format(route.query.GrantDatege)
    if (route.query.GrantDatele) {
        let data1 = route.query.GrantDatele.substring(0, 10)
        console.log(data1)
        GrantDatele.value = format(data1)
    }
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: './apply' })
}

// 列表
const HandleTableData = () => {
    tableData.value = []
    if (route.query.attribute) {
        filters.value[route.query.attribute] = 1
    }
    filters.value.GrantDatege = route.query.GrantDatege
    filters.value.GrantDatele = route.query.GrantDatele
    DcApplyFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data || []
            // console.log(rows.data)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const handleNativePrint = () => {
    // 获取打印区域内容
    const printContent = document.getElementById('printArea').innerHTML;

    // 创建一个隐藏的iframe
    const iframe = document.createElement('iframe');
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');

    // 添加到DOM中
    document.body.appendChild(iframe);

    // 获取iframe的document对象
    const iframeWindow = iframe.contentWindow || iframe.contentDocument;
    const iframeDoc = iframeWindow.document || iframeWindow;

    // 写入打印内容
    iframeDoc.open();
    iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>危险化学品领用台账</title>
            <style>
                @page { size: landscape; margin:10mm 5mm; }
                body { font-family: 宋体; margin: 0; padding: 0;   }
                .print-table { border-collapse: collapse; width: 100%; font-size: 14px; }
                .print-table th { border: 1px solid #ddd; padding: 5px 1px; } 
                .print-table td { border: 1px solid #ddd; padding: 6px 3px; }
                thead { display: table-header-group; }
                tr { page-break-inside: avoid; }
                .print-title { font-size: 35px; margin: 10px 0; text-align: center; }
                .print-date { font-size: 22px; text-align: right; }
                .print-signleft { margin-top: 20px; margin-bottom: 10px; text-align: left; }
                .print-signcenter { border-bottom: 1px solid #000000; width: 250px; display: inline-block; margin-right: 100px; }
                .print-signright { border-bottom: 1px solid #000000; width: 250px; display: inline-block; }
            </style>
        </head>
        <body>
            ${printContent}
        </body>
        </html>
    `);
    iframeDoc.close();

    // 等待内容加载完成后打印
    iframe.onload = function () {
        setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();

            // 打印完成后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 100);
        }, 200);
    };
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="handleNativePrint">打印</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="width: 1150px;" id="printArea">
            <div class="print-title">
                <span style="font-size: 35px;">危险化学品领用台账</span>
            </div>
            <div class="print-date">
                <span>出库时间段：{{ GrantDatege }}--{{ GrantDatele }}</span>
            </div>
            <table class="print-table">
                <thead>
                    <tr>
                        <th style="width: 40px;">序号</th>
                        <th style="width: 100px;">危化品名称</th>
                        <th style="width: 120px;">规格属性</th>
                        <th style="width: 50px;">单位</th>
                        <th style="width: 70px;">申领数量</th>
                        <th style="width: 70px;">退回数量</th>
                        <th style="width: 70px;">实用数量</th>
                        <th style="width: 120px;">药品用途</th>
                        <th style="width: 100px;">出库时间</th>
                        <th style="width: 120px;">发放人</th>
                        <th style="width: 120px;">领用人</th>
                        <th style="width: 160px;">领用人签字(双人)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in tableData" :key="row.Id">
                        <td style="text-align: center;">{{ index + 1 }}</td>
                        <td>{{ row.Name }}</td>
                        <td>{{ row.Model }}</td>
                        <td style="text-align: center;">{{ row.UnitName }}</td>
                        <td style="text-align: right;">{{ row.Num }}</td>
                        <td style="text-align: right;">{{ row.BackNum }}</td>
                        <td style="text-align: right;"> {{ row.Num - row.BackNum }}</td>
                        <td>{{ row.Remark }}</td>
                        <td>{{ row.GrantDate ? row.GrantDate.substring(0, 10) : '--' }}</td>
                        <td> {{ row.GrantWithUserName ? row.GrantUserName + ',' + row.GrantWithUserName :
                            row.GrantUserName }}</td>
                        <td> {{ row.WithUserName ? row.UserName + ',' + row.WithUserName : row.UserName }}</td>
                        <td> </td>
                    </tr>
                </tbody>
            </table>
            <div class="print-signleft">
                发放人（双人签字）：
                <div class="print-signcenter" style="width: 250px;">
                    &nbsp;</div>
                领用审批人（签字）：
                <div class="print-signright" style="width: 250px;">&nbsp;
                </div>
            </div>
        </div>

    </div>
</template>
<style>
/* 打印样式 - 必须放在style标签中 */
@media print {
    .el-table {
        border: 1px solid #ebeef5;

    }

    .el-table th,
    .el-table td {
        border: 1px solid #ebeef5 !important;
    }

    .el-table__header-wrapper {
        display: table-header-group !important;
    }

    .el-table__body-wrapper tr {
        page-break-inside: avoid !important;
    }

    .el-pagination {
        display: none !important;
    }


}

.print-title {
    font-size: 35px;
    font-family: 宋体;
    margin: 10px 0;
    text-align: center;
}

.print-date {
    font-size: 22px;
    font-family: 宋体;
    text-align: right;
}

.print-signleft {
    margin-top: 20px;
    font-family: 宋体;
    margin-bottom: 10px;
    text-align: left;
}

.print-signcenter {
    border-bottom: 1px solid #000000;
    display: inline-block;
    margin-right: 100px;
}

.print-signright {
    border-bottom: 1px solid #000000;
    display: inline-block;
}
</style>
<style lang="scss" scoped>
table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

table th {
    border: 1px solid #ddd;
    padding: 10px 1px;
}

table td {
    border: 1px solid #ddd;
    padding: 6px 3px;
}
</style>