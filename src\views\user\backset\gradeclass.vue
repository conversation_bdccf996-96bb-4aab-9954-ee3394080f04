<script setup>
defineOptions({
    name: 'backsetgradeclass'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd, Download, Upload, Finished, UploadFilled
} from '@element-plus/icons-vue'
import {
    PclassinfoGetpaged, PclassinfoAdd, PclassinfoEdit, PclassinfoDeletebyid, PclassinfoImport, PstudentImport, UploadPostexecl, PclassinfoGradeupgrade
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { fileDownload, integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const isAdd = ref(false)
const ClassList = ref([])
const GradeList = ref([])
const GradeClassList = ref([])
const StudentFilePath = ref('')
const ClassFilePath = ref('')
//新增&编辑操作
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    StartYear: [
        { required: true, message: '请输入入学年份', trigger: 'change' },
    ],
    GradeId: [
        { required: true, message: '请选择年级', trigger: 'change' },
    ],
    ClassName: [
        { required: true, message: '请选择班级', trigger: 'change' },
    ],
    TeacherName: [
        { required: true, message: '请输入班主任姓名', trigger: 'change' },
    ],
    TeacherMobile: [
        { required: true, message: '请输入班主任手机号码', trigger: 'change' },
        {
            pattern: /^1[0-9]{10}$/,
            message: '请输入正确11位手机号码',
            trigger: 'blur'
        }
    ],
}

onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
//添加
const HandleAdd = () => {
    isAdd.value = true
    dialogVisible.value = true
    nextTick(() => {
        dialogData.value = {}
        refForm.value.resetFields()

    })
}
const isStudent = ref(false)
const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
const uploadRuleForm = {
    GradeName: [
        { required: true, message: '请选择年级班级', trigger: 'change' },
    ]
}
// 导入班级
const HandleClassUpload = () => {
    isStudent.value = false
    uploadVisible.value = true
}
// 导入学生
const HandleStudentUpload = () => {
    isStudent.value = true
    uploadVisible.value = true
}
// 修改
const HandleEdit = (row) => {
    isAdd.value = false
    dialogData.value.Id = row.Id
    dialogData.value.StartYear = row.StartYear
    dialogData.value.GradeId = String(row.GradeId)
    dialogData.value.ClassName = row.ClassName
    dialogData.value.TeacherName = row.TeacherName
    dialogData.value.TeacherMobile = row.TeacherMobile
    dialogVisible.value = true
}
// 查看
const HandleDetail = (row) => {
    router.push({ path: "./student", query: { id: row.Id } })
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定删除班级信息吗?')
        .then(() => {
            PclassinfoDeletebyid({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData(true)
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 年级升级
const HandleGradeup = (row) => {
    ElMessageBox.confirm('确定要进行年级升级吗（操作后无法撤销）?')
        .then(() => {
            PclassinfoGradeupgrade().then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData(true)
                    ElMessage.success(res.data.msg || '年级升级成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
//班级信息提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (isAdd.value) {
            // 年级名称
            let arr = GradeList.value.filter(t => t.value == dialogData.value.GradeId)
            dialogData.value.GradeName = arr[0].label
            PclassinfoAddUser()
        } else {
            PclassinfoEditUser()
        }
    })
}
// 添加
const PclassinfoAddUser = () => {
    PclassinfoAdd(dialogData.value).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '添加成功')
            dialogVisible.value = false
            HandleTableData(true)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 修改
const PclassinfoEditUser = () => {
    let paramy = {
        Id: dialogData.value.Id,
        TeacherName: dialogData.value.TeacherName,
        TeacherMobile: dialogData.value.TeacherMobile,
    }
    PclassinfoEdit(paramy).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 模板下载
const HandleDownload = () => {
    if (isStudent.value) {
        fileDownload(StudentFilePath.value);
    } else {
        fileDownload(ClassFilePath.value);
    }
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    PclassinfoGetpaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            if (isFirst) {
                StudentFilePath.value = other.StudentFilePath;
                ClassFilePath.value = other.ClassFilePath;
                GradeList.value = other.GradeList || [];
                ClassList.value = other.ClassList || [];
                GradeClassList.value = other.GradeClassList || [];
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.GradeId = undefined
    filters.value.ClassName = undefined
    filters.value.Key = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
const fileFile = ref()
const uploadRef = ref()

// 导入前校验
const beforeAvatarUpload = (file) => {
    fileFile.value = file
    const extension = file.name.split('.')[1] === 'xls'
    const extension2 = file.name.split('.')[1] === 'xlsx'
    if (!extension && !extension2) {
        ElMessage({
            message: '上传模板只能是 xls、xlsx格式!',
            type: 'error'
        })
    }
    return extension || extension2
}
const httpRequest = () => {
    if (isStudent.value) {
        UploadPostexecl([fileFile.value]).then(res => {
            if (res.data.flag == 1) {
                const { rows } = res.data.data

                PstudentImport({ FilePath: rows, UniformClassId: uploadData.value.UniformClassId[1] }).then(res1 => {
                    if (res1.data.flag == 1) {
                        ElMessage.success(res1.data.msg || '导入成功')
                        setTimeout(function () {
                            HandleTableData()
                            uploadVisible.value = false
                        }, 1000);
                    } else {
                        ElMessage({
                            showClose: true,
                            message: res1.data.msg,
                            type: 'error',
                            duration: 5000
                        })
                    }
                })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        PclassinfoImport([fileFile.value]).then(res => {
            if (res.data.flag == 1) {
                // const { rows } = res.data.data
                ElMessage.success(res.data.msg || '导入成功')
                HandleTableData(true)
                uploadVisible.value = false
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem flexOperation">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加班级</el-button>
                        <el-button type="success" :icon="Upload" @click="HandleClassUpload">导入班级</el-button>
                        <el-button type="success" :icon="Upload" @click="HandleStudentUpload">导入学生</el-button>
                        <el-button type="success" :icon="Finished" @click="HandleGradeup">年级升级</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.GradeId" clearable placeholder="年级" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in GradeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.ClassName" clearable placeholder="班级" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in ClassList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="班主任"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="SchoolStageName" label="学段" min-width="100" align="center"></el-table-column>
            <el-table-column prop="StartYear" label="入学年份" min-width="100" align="center"></el-table-column>
            <el-table-column prop="GradeName" label="年级" min-width="120" align="center"></el-table-column>
            <el-table-column prop="ClassName" label="班级" min-width="120" align="center"></el-table-column>
            <el-table-column prop="TeacherName" label="班主任" min-width="120" align="center"></el-table-column>
            <el-table-column prop="TeacherMobile" label="班主任手机号" min-width="140" align="center"></el-table-column>
            <el-table-column prop="StudentNum" label="学生数" min-width="80" align="center"></el-table-column>
            <el-table-column label="学生名单" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">管理</el-button>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <el-dialog v-model="dialogVisible" :title="isAdd ? '添加班级' : '修改班级信息'" draggable width="680px"
            :close-on-click-modal="false">
            <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                status-icon>
                <el-form-item label="年级：" prop="GradeId">
                    <el-select v-model="dialogData.GradeId" :disabled="!isAdd" style="width: 60%;">
                        <el-option class="flexItem" v-for="item in GradeList" :value="item.value" :label="item.label"
                            :key="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="班级：" prop="ClassName">
                    <el-select v-model="dialogData.ClassName" :disabled="!isAdd" style="width: 60%;">
                        <el-option class="flexItem" v-for="item in ClassList" :value="item.value" :label="item.label"
                            :key="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="班主任：" prop="TeacherName">
                    <el-input v-model="dialogData.TeacherName" style="width: 60%;"></el-input>
                </el-form-item>
                <el-form-item label="班主任手机：" prop="TeacherMobile">
                    <el-input v-model="dialogData.TeacherMobile" style="width: 60%;"
                        @input="integerLimitInput($event, 'TeacherMobile')"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 导入班级、学生弹窗 -->
        <el-dialog v-model="uploadVisible" :title="isStudent ? '学生导入' : '班级导入'" width="480px">
            <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" :rules="uploadRuleForm" label-width="120px"
                status-icon>
                <el-form-item label="年级班级：" prop="GradeId" v-if="isStudent">
                    <el-cascader placeholder="请选择年级班级" :options="GradeClassList" filterable
                        v-model="uploadData.UniformClassId"
                        :props="{ value: 'Id', label: 'Name', children: 'Children' }" style="width: 80%;"></el-cascader>
                </el-form-item>
                <el-form-item>
                    <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" accept=".xlsx,.xls"
                        style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload"
                        :http-request="httpRequest">
                        <el-button type="primary" :icon="UploadFilled">{{ isStudent ? '导入学生' : '导入班级' }} </el-button>
                    </el-upload>
                    <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>