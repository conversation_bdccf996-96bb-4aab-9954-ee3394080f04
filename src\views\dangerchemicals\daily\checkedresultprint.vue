<script setup>
defineOptions({
    name: 'dangerchemicalsdailycheckedresultprint'
});
import { onMounted, ref, nextTick, onActivated, watch } from 'vue'
import {
    Refresh, Back, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    Areagetbyid
} from '@/api/user.js'
import {
    GovernTaskUnitListPrint
} from '@/api/daily.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getYearMonthDay, tagsListStore, methodMul, methodDiv } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const AreaName = ref('')
const filters = ref({})
//加载数据
onMounted(() => {

    if (route.query.isTagRouter) {
        AreagetbyidUser()
        HandleTableData()
    }

})
const dtBegin = ref('')
const dtEnd = ref('')
const format = (date) => {
    if (date) {
        const [year, month, day] = date.split('-');
        return `${year}年${month}月${day}日`;
    } else {
        return ''

    }

}
onActivated(() => {
    dtBegin.value = format(route.query.dtBegin)
    dtEnd.value = format(route.query.dtEnd)
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            AreagetbyidUser()
            HandleTableData()
        }
    })
})
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })
}
const header = ref({})
const footerDate = ref([])
// 列表
const HandleTableData = () => {
    GovernTaskUnitListPrint({ GovernTaskId: route.query.GovernTaskId }).then(res => {
        if (res.data.flag == 1) {
            const { rows, headers, footer } = res.data.data
            tableData.value = rows.data || []
            console.log(rows.data)
            header.value = headers || {}
            footerDate.value = footer || []
            console.log(footerDate.value, tableData.value)
            tableData.value.forEach((item, index) => {
                if (item.ProblemNum != 0) {
                    let s1 = methodMul(item.RectifyProblemNum, 100);
                    item.problemRate = methodDiv(s1, item.ProblemNum).toFixed(2) + "%";
                }
                if (item.DangerNum != 0) {
                    var s1 = methodMul(item.RectifyDangerNum, 100);
                    item.dangerRate = methodDiv(s1, item.DangerNum).toFixed(2) + "%";
                }
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const AreagetbyidUser = () => {
    Areagetbyid({ id: userStore.userInfo.AreaId }).then((res) => {
        AreaName.value = res.data.data.rows.Name
    })
}



const handleNativePrint = () => {
    // 获取打印区域内容
    const printContent = document.getElementById('printArea').innerHTML;

    // 创建一个隐藏的iframe
    const iframe = document.createElement('iframe');
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');

    // 添加到DOM中
    document.body.appendChild(iframe);

    // 获取iframe的document对象
    const iframeWindow = iframe.contentWindow || iframe.contentDocument;
    const iframeDoc = iframeWindow.document || iframeWindow;

    // 写入打印内容
    iframeDoc.open();
    iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>危险化学品领用台账</title>
            <style>
                @page { size: auto; margin:10mm 5mm; }
                body { font-family: 宋体; margin: 0; padding: 0;   }
                .tsbleTwo { page-break-before: always; }
                .print-table { border-collapse: collapse; width: 100%; font-size: 14px; }
                .print-table th { border: 1px solid #ddd; padding: 0px 1px; } 
                .print-table td { border: 1px solid #ddd; padding: 6px 3px; }
                thead { display: table-header-group; }
                tr { page-break-inside: avoid; }
                .print-title { font-size: 35px; margin: 10px 0; text-align: center; }
                .print-date { font-size: 15px; font-family: 宋体;  margin: 30px 0 20px 0;}
                .print-signright { margin-top: 20px; margin-bottom: 10px; text-align: right; }
                .print-signcenter { border-bottom: 1px solid #000000; width: 150px; display: inline-block; margin-right: 20px; } 
                .tdCenter { text-align: center; }
            </style>
        </head>
        <body>
            ${printContent}
        </body>
        </html>
    `);
    iframeDoc.close();

    // 等待内容加载完成后打印
    iframe.onload = function () {
        setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();

            // 打印完成后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 100);
        }, 200);
    };
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="handleNativePrint">打印</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="width: 880px;" id="printArea">
            <div class="tsbleOne" v-if="tableData.length > 0">
                <div class="print-title">
                    <span style="font-size: 35px;">{{ AreaName }}学校危险化学品安全综合治理检查记录表</span>
                </div>
                <div class="print-date" style="display: flex;">
                    <div style="width: 25%;display: flex;">
                        <div style="width: 75px;flex-shrink: 0;">填报单位：</div>
                        <div>{{ header.UnitName }}</div>
                    </div>
                    <div style="width: 25%;">填报人：{{ header.UserName }}</div>
                    <div style="width: 25%;">联系电话：{{ header.UserPhoneNumber }}</div>
                    <div style="width: 25%;">填报时间：{{ getYearMonthDay(new Date()) }}</div>
                </div>
                <table class="print-table">
                    <thead>
                        <tr>
                            <th width='60' height='44' class='tdCenter' rowspan='2'
                                v-if="userStore.userInfo.UnitType == 1">
                                区县名称
                            </th>
                            <th width='120' height='44' class='tdCenter' rowspan='2'
                                v-if="userStore.userInfo.UnitType == 1">学校名称
                            </th>
                            <th width='180' height='44' class='tdCenter' rowspan='2' v-else>学校名称</th>
                            <th width='330' height='44' class='tdCenter' colspan='3'>参加检查人</th>
                            <th width='90' height='44' class='tdCenter' rowspan='2'>检查时间</th>
                            <th width='70' height='44' class='tdCenter' rowspan='2'>发现问<br />题(个)</th>
                            <th width='70' height='44' class='tdCenter' rowspan='2'>已整改<br />问题(个)</th>
                            <th width='70' height='44' class='tdCenter' rowspan='2'>发现隐<br />患(个)</th>
                            <th width='70' height='44' class='tdCenter' rowspan='2'>已整改<br />隐患(个)</th>
                        </tr>
                        <tr>
                            <th width='110' height='44' class='tdCenter'>领导</th>
                            <th width='110' height='44' class='tdCenter'>专家</th>
                            <th width='110' height='44' class='tdCenter'>工作人员</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for="(row, index) in tableData" :key="index">
                            <tr>
                                <td style="text-align: center;" v-if="userStore.userInfo.UnitType == 1">
                                    {{ row.AreaName }} </td>
                                <td>{{ row.SchoolName }}</td>
                                <td class='tdCenter'>{{ row.Leaderusers }}</td>
                                <td class='tdCenter'>{{ row.ExpertUsers }}</td>
                                <td class='tdCenter'>{{ row.WorkUsers }}</td>
                                <td class='tdCenter'>{{ row.CheckingDate ? row.CheckingDate.substring(0, 10) : '' }}
                                </td>
                                <td class='tdCenter'>{{ row.ProblemNum }}</td>
                                <td class='tdCenter'>{{ row.RectifyProblemNum }}</td>
                                <td class='tdCenter'>{{ row.DangerNum }}</td>
                                <td class='tdCenter'>{{ row.RectifyDangerNum }}</td>
                            </tr>
                        </template>
                        <!-- 添加合计行 -->
                        <tr v-if="tableData.length > 0">
                            <td v-if="userStore.userInfo.UnitType == 1"></td>
                            <td class='tdCenter'>总计：</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class='tdCenter'> {{tableData.reduce((sum, row) => sum + row.ProblemNum, 0)}}</td>
                            <td class='tdCenter'> {{tableData.reduce((sum, row) => sum + row.RectifyProblemNum, 0)}}
                            </td>
                            <td class='tdCenter'> {{tableData.reduce((sum, row) => sum + row.DangerNum, 0)}}</td>
                            <td class='tdCenter'> {{tableData.reduce((sum, row) => sum + row.RectifyDangerNum, 0)}}</td>

                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="tsbleTwo" v-if="footerDate.length > 0">
                <div class="print-title">
                    <span style="font-size: 35px;">{{ AreaName }}问题与隐患清单检查记录表</span>
                </div>
                <div class="print-date" style="display: flex;">
                    <div style="width: 25%;display: flex;">
                        <div style="width: 75px;flex-shrink: 0;">填报单位：</div>
                        <div>{{ header.UnitName }}</div>
                    </div>
                    <div style="width: 25%;">填报人：{{ header.UserName }}</div>
                    <div style="width: 25%;">联系电话：{{ header.UserPhoneNumber }}</div>
                    <div style="width: 25%;">填报时间：{{ getYearMonthDay(new Date()) }}</div>
                </div>
                <table class="print-table">
                    <thead>
                        <tr>
                            <th width='60' height='44' class='tdCenter' v-if="userStore.userInfo.UnitType == 1">区县名称
                            </th>
                            <th width='120' height='44' class='tdCenter' v-if="userStore.userInfo.UnitType == 1">学校名称
                            </th>
                            <th width='180' height='44' class='tdCenter' v-else>学校名称</th>
                            <th width='50' height='44' class='tdCenter'>类别</th>
                            <th width='140' height='44' class='tdCenter'>问题隐患清单</th>
                            <th width='50' height='44' class='tdCenter'>性质</th>
                            <th width='50' height='44' class='tdCenter'>危险等级</th>
                            <th width='90' height='44' class='tdCenter'>检查日期</th>
                            <th width='90' height='44' class='tdCenter'>整改期限</th>
                            <th width='90' height='44' class='tdCenter'>整改日期</th>
                            <th width='80' height='44' class='tdCenter'>整改状态</th>
                            <th width='100' height='44' class='tdCenter'>整改措施</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for="(row, index) in footerDate" :key="index">
                            <tr>
                                <td style="text-align: center;" v-if="userStore.userInfo.UnitType == 1">
                                    {{ row.AreaName }} </td>
                                <td>{{ row.SchoolName }}</td>
                                <td class='tdCenter'>{{ row.CategoryName }}</td>
                                <td>{{ row.Name }}</td>
                                <td class='tdCenter'>{{ row.Nature == 1 ? '问题' : '隐患' }}</td>
                                <td class='tdCenter'>{{ row.Grade == 1 ? '一般' : row.Grade == 2 ? '较大' : '重大' }}</td>
                                <td class='tdCenter'>{{ row.CheckingDate ? row.CheckingDate.substring(0, 10) : '' }}
                                </td>
                                <td class='tdCenter'>{{ row.RectifyLimit ? row.RectifyLimit.substring(0, 10) : '' }}
                                </td>
                                <td class='tdCenter'>{{ row.RegDate ? row.RegDate.substring(0, 10) : '' }}</td>
                                <td class='tdCenter'>{{ row.Statuz == 1 ? '已整改' : '待整改' }}</td>
                                <td>{{ row.Measures ? row.Measures : '' }}</td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</template>
<style>
/* 打印样式 - 确保合并行不被分割 */
@media print {
    .tsbleTwo {
        page-break-before: always;
    }

    /* 强制表格在分页时保持行完整 */
    table.print-table {
        page-break-inside: auto;
    }

    /* 确保合并行不会被分割到不同页 */
    tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    /* 表头每页重复 */
    thead {
        display: table-header-group;
    }

    /* 合并单元格的边框处理 */
    td[rowspan] {
        border-bottom: 1px solid #000 !important;
    }

    /* 确保最后一行边框完整 */
    tr:last-child td {
        border-bottom: 1px solid #000 !important;
    }
}

.print-title {
    font-size: 35px;
    font-family: 宋体;
    margin: 10px 0;
    text-align: center;
}

.print-date {
    font-size: 15px;
    font-family: 宋体;
    margin: 30px 0 20px 0;
}

/* 屏幕显示样式（保持不变） */
.print-table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

.print-table th,
.print-table td {
    border: 1px solid #ddd;
    padding: 3px;
}
</style>
<style lang="scss" scoped>
table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

table th {
    border: 1px solid #ddd;
    padding: 0 1px;
}

table td {
    border: 1px solid #ddd;
    padding: 6px 3px;
}

.print-signright {
    margin-top: 20px;
    font-family: 宋体;
    margin-bottom: 10px;
    text-align: right;
}

.print-signcenter {
    border-bottom: 1px solid #000000;
    display: inline-block;
    margin-right: 20px;
}

.tdCenter {
    text-align: center;
}
</style>