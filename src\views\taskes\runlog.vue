<script setup>
defineOptions({
    name: 'dangerchemicalstrainsafeeducationlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { TasksLogGetPaged} from '@/api/tasks.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, tagsListStore } from "@/utils/index.js";//上传附件
import { ElMessageBox, ElMessage } from 'element-plus';
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const SchoolId = ref(0)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })

//加载数据
onMounted(() => {
    // 存在参数并且由切换tag标签时刷新浏览器
    if ((route.query.isTagRouter)) {
        filters.value.JobId = route.query.id
        HandleTableData(true);
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.JobId = route.query.id
            HandleTableData(true);
        }
    })
})


// 列表
const HandleTableData = () => {
    TasksLogGetPaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
} // 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.Name = undefined;
    filters.value.JobGroup = undefined;
    filters.value.RunResult = undefined;
    filters.value.RunTimege = undefined;
    filters.value.RunTimele = undefined;
    RunTimele.value = '';
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const regRunTimegeChange = (val) => {
    if (!val) filters.value.BeginDatege = undefined
    HandleTableData()
}
const RunTimele = ref("");
const regRunTimeleChange = (val) => {
    if (val) {
        filters.value.RunTimele = val + " 23:59:59"
    } else {
        filters.value.RunTimele = undefined
    }
    HandleTableData()
}
 // 格式化日期时间
const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');
};

// 限制开始日期不能晚于结束日期
const disabledRunTimege = (time) => {
    if (!RunTimele.value) return false;
    return time >= new Date(RunTimele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledRunTimele = (time) => {
    if (!filters.value.RunTimege) return false;
    return time < new Date(filters.value.RunTimege + ' 00:00:00');
};

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="任务名称" style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.JobGroup" placeholder="任务分组" style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.RunResult" placeholder="执行结果" clearable  style="width: 180px">
                            <el-option label="成功" value="true"></el-option>
                            <el-option label="失败" value="false"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RunTimege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledRunTimege" clearable placeholder="开始启动时间"
                            @change="regRunTimegeChange" style="width: 140px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="RunTimele" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledRunTimele" clearable placeholder="截止启动时间"
                            @change="regRunTimeleChange" style="width: 140px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
                        <el-table-column type="expand">
                            <template #default="props">
                                <div class="expand-content">
                                    <div class="expand-section" v-if="props.row.RunPars">
                                        <div class="expand-label">执行参数:</div>
                                        <div class="expand-value">{{ props.row.RunPars }}</div>
                                    </div>
                                    <div class="expand-section" v-if="props.row.ErrMessage">
                                        <div class="expand-label">异常信息:</div>
                                        <div class="expand-value">{{ props.row.ErrMessage }}</div>
                                    </div>
                                    <div class="expand-section" v-if="props.row.ErrStackTrace">
                                        <div class="expand-label">异常堆栈:</div>
                                        <div class="expand-value">{{ props.row.ErrStackTrace }}</div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="Name" label="任务名称" min-width="180" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="JobGroup" label="任务分组" min-width="120" show-overflow-tooltip></el-table-column>
                        
                        <el-table-column prop="RunResult" label="执行结果" width="100" align="center">
                            <template #default="{ row }">
                                <span :class="['status-badge', row.RunResult ? 'status-success' : 'status-failure']">
                                    {{ row.RunResult ? '成功' : '失败' }}
                                </span>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="TotalTime" label="任务耗时(ms)" width="120" align="center">
                            <template #default="{ row }">
                                <el-tag :type="row.TotalTime > 1000 ? 'warning' : 'success'">
                                    {{ row.TotalTime.toFixed(2) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="RunTime" label="运行时间" width="160" sortable>
                            <template #default="{ row }">
                                <span class="time-column">{{ formatDateTime(row.RunTime) }}</span>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="EndTime" label="结束时间" width="160">
                            <template #default="{ row }">
                                <span class="time-column">{{ formatDateTime(row.EndTime) }}</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column prop="CreateBy" label="执行者" width="120" show-overflow-tooltip></el-table-column> -->
                        
                        <el-table-column prop="CreateTime" label="记录时间" width="160" sortable>
                            <template #default="{ row }">
                                <span class="time-column">{{ formatDateTime(row.CreateTime) }}</span>
                            </template>
                        </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped>
 .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #f0f9eb;
            color: #67c23a;
        }
        
        .status-inactive {
            background: #fef0f0;
            color: #f56c6c;
        }

</style>