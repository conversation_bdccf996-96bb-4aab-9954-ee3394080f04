<script setup>
defineOptions({
    name: 'dangerchemicalstrainsafeeducationlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { TaskQzGetPaged, TaskQzGetById ,TaskQzAddSave ,TaskQzEditSave, TaskQzDelete} from '@/api/tasks.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, tagsListStore } from "@/utils/index.js";//上传附件
import { ElMessageBox, ElMessage } from 'element-plus';
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const SchoolId = ref(0)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })

//加载数据
onMounted(() => {
    SchoolId.value = route.query.SchoolId || 0
    if (route.query.isTagRouter) {
        HandleTableData();
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    SchoolId.value = route.query.SchoolId || 0
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData();
        }
    })
})


// 列表
const HandleTableData = () => {
    TaskQzGetPaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
} // 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.Name = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)
const isAdd = ref(true)

//验证
const ruleForm = {
   Name: [
        { required: true, message: '请输入任务名称', trigger: 'blur' }
    ],
    JobGroup: [
        { required: true, message: '请输入任务分组', trigger: 'blur' }
    ],
    TriggerType: [
        { required: true, message: '请选择触发器类型', trigger: 'change' }
    ],
    Cron: [
        { 
            required: true, 
            validator: (rule, value, callback) => {
                if (dialogData.TriggerType === 1 && !value) {
                    callback(new Error('请输入Cron表达式'));
                } else {
                    callback();
                }
            }, 
            trigger: 'blur' 
        }
    ],
    IntervalSecond: [
        { 
            required: true, 
            validator: (rule, value, callback) => {
                if (dialogData.TriggerType === 0 && (!value || value < 1)) {
                    callback(new Error('请输入有效的循环间隔时间（至少1秒）'));
                } else {
                    callback();
                }
            }, 
            trigger: 'blur' 
        }
    ],
    AssemblyName: [
        { required: true, message: '请输入程序集名称', trigger: 'blur' }
    ],
    ClassName: [
        { required: true, message: '请输入任务类名', trigger: 'blur' }
    ],
    BeginTime: [
        { 
            required: true, 
            message: '请选择开始时间',  trigger: 'change'  }
    ],
    CycleRunTimes: [
        { 
            type: 'number', 
            min: 0, 
            message: '循环执行次数不能为负数', 
            trigger: 'blur' 
        }
    ],
    JobParams: [
        { 
            validator: (rule, value, callback) => {
                if (value) {
                    try {
                        JSON.parse(value);
                        callback();
                    } catch (e) {
                        callback(new Error('任务参数必须是有效的JSON格式'));
                    }
                } else {
                    callback();
                }
            }, 
            trigger: 'blur' 
        }
    ],
    IsStart: [
        { required: true, message: '请选择任务状态', trigger: 'change' }
    ]
}

// 添加
const HandleAdd = (row, e) => {
    num.value = e
    dialogVisible.value = true;
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {Name:"",IsStart:false};
    })
}
const num = ref(0)
// 修改弹出窗体
const HandleEdit = (row, e) => {
    num.value = e
    isAdd.value = false
    dialogData.value = row
    dialogVisible.value = true
}

// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要删除该数据信息吗?')
        .then(() => {
            TaskQzDelete({ Id: row.Id }).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })

}
// 提交
const HandleSubmit = () => {
    if(dialogData.value.Id && Number(dialogData.value.Id)> 0){
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            TaskQzEditSave(dialogData.value).then(res => {
                if (res.data.flag == 1) {
                    dialogVisible.value = false
                    dialogVisible.value = false
                    ElMessage.success(res.data.msg || '保存成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    } else {
         refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            TaskQzAddSave(dialogData.value).then(res => {
                if (res.data.flag == 1) {
                    dialogVisible.value = false
                    dialogVisible.value = false
                    ElMessage.success(res.data.msg || '保存成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    }
}

//查看
const HandleLogView = (row) => {
  router.push({ path: "./runlog", query: { id: row.Id,name:row.Name } })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item v-if="SchoolId == 0" class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="任务名称" style="width: 180px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="任务名称" min-width="180"></el-table-column>
            <el-table-column prop="JobGroup" label="任务分组" min-width="180"></el-table-column>
            <el-table-column prop="Cron" label="运行时间表达式" min-width="180"></el-table-column>

            <el-table-column prop="RunTimes" label="累计运行(次)" min-width="110"></el-table-column>
            <el-table-column prop="IntervalSecond" label="循环周期(秒)" min-width="100"></el-table-column>
            <el-table-column prop="IntervalSecond" label="循环周期(秒)" min-width="100"></el-table-column>
            <el-table-column prop="CycleRunTimes" label="循环执行次数" min-width="100"></el-table-column>
            <el-table-column prop="CycleHasRunTimes" label="已循环次数" min-width="100"></el-table-column>
            <el-table-column prop="AssemblyName" label="程序集名称" min-width="180"></el-table-column>
            <el-table-column prop="ClassName" label="任务所在类" min-width="120"></el-table-column>
            <el-table-column prop="Remark" label="任务描述" min-width="100" align="center"></el-table-column>
           
           <el-table-column prop="BeginTime" label="开始时间" min-width="160" align="center">
                <template #default="{ row }">
                    {{ row.BeginTime ? row.BeginTime : '--' }}
                    <!-- row.EffectiveDate.substring(0, 10) -->
                </template>
            </el-table-column>
            <el-table-column prop="EndTime" label="结束时间" min-width="160" align="center">
                <template #default="{ row }">
                    {{ row.EndTime ? row.EndTime : '--' }}
                    <!-- row.EffectiveDate.substring(0, 10) -->
                </template>
            </el-table-column>
            <el-table-column prop="IsStart" label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <span :class="['status-badge', row.IsStart ? 'status-active' : 'status-inactive']">
                        {{ row.IsStart ? '运行中' : '已停止' }}
                    </span>
                </template>
            </el-table-column>
 
            <el-table-column prop="Triggers" label="状态-内存" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegTime ? row.RegTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                    <el-button type="primary" link @click="HandleLogView(row)">日志</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="dialogData.Id > 0 ? '修改任务信息' : '添加任务信息'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px"
                    status-icon>
                    <el-form-item label="任务名称" prop="Name">
                        <el-input v-model="dialogData.Name" placeholder="请输入任务名称" style="width: 80%"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="任务分组" prop="JobGroup">
                        <el-input v-model="dialogData.JobGroup" placeholder="请输入任务分组" style="width: 80%"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="触发器类型" prop="TriggerType">
                        <div class="trigger-type-group">
                            <el-radio-group v-model="dialogData.TriggerType">
                                <el-radio :label="0">Simple</el-radio>
                                <el-radio :label="1">Cron</el-radio>
                            </el-radio-group>
                            <span class="trigger-help">
                                {{ dialogData.TriggerType === 0 ? '简单定时器' : 'Cron表达式定时器' }}
                            </span>
                        </div>
                    </el-form-item>
                    
                    <el-form-item v-if="dialogData.TriggerType === 1" label="Cron表达式" prop="Cron">
                        <el-input v-model="dialogData.Cron" placeholder="例如：0 0 2 * * ?" style="width: 80%">
                            <!-- <template #append>
                                <el-button @click="showCronHelp = true">表达式帮助</el-button>
                            </template> -->
                        </el-input>
                    </el-form-item>
                    
                    <el-form-item v-else label="循环间隔(秒)" prop="IntervalSecond">
                        <el-input-number v-model="dialogData.IntervalSecond" :min="1" :max="86400"></el-input-number>
                    </el-form-item>
                    
                    <el-form-item label="程序集名称" prop="AssemblyName">
                        <el-input v-model="dialogData.AssemblyName" placeholder="请输入程序集名称" style="width: 80%"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="任务类名" prop="ClassName">
                        <el-input v-model="dialogData.ClassName" placeholder="请输入完整的类名" style="width: 80%"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="开始时间" prop="BeginTime">
                        <el-date-picker
                            v-model="dialogData.BeginTime"
                            type="datetime"
                            placeholder="选择开始时间"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            style="width: 80%"
                        />
                    </el-form-item>
                    
                    <el-form-item label="结束时间" prop="EndTime">
                        <el-date-picker
                            v-model="dialogData.EndTime"
                            type="datetime"
                            placeholder="选择结束时间（可选）"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            style="width: 80%"
                        />
                    </el-form-item>
                    
                    <el-form-item label="循环执行次数" prop="CycleRunTimes">
                        <el-input-number v-model="dialogData.CycleRunTimes" :min="0"></el-input-number>
                        <span class="trigger-help">0表示无限循环</span>
                    </el-form-item>
                    
                    <el-form-item label="任务参数" prop="JobParams">
                        <el-input
                            v-model="dialogData.JobParams"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入JSON格式的任务参数"
                            style="width: 80%"
                        />
                    </el-form-item>
                    
                    <el-form-item label="任务描述" prop="Remark" class="full-width">
                        <el-input
                            v-model="dialogData.Remark"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入任务描述"
                            style="width: 80%"
                        />
                    </el-form-item>
                    <!-- <el-form-item label="任务状态 :" prop="IsStart">
                        <el-radio-group v-model="dialogData.IsStart">
                            <el-radio :value="1" label="启动"> </el-radio>
                            <el-radio :value="0" label="停止"> </el-radio>
                        </el-radio-group>
                    </el-form-item> -->

                    <el-form-item label="任务状态" prop="IsStart" class="full-width">
                        <el-switch
                            v-model="dialogData.IsStart"
                            active-text="启动"
                            inactive-text="停止"
                        />
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button v-if="SchoolId == 0" type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
 .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #f0f9eb;
            color: #67c23a;
        }
        
        .status-inactive {
            background: #fef0f0;
            color: #f56c6c;
        }

</style>