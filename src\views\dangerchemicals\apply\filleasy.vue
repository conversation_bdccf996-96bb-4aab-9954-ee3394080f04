<script setup>
defineOptions({
    name: 'dangerchemicalsapplyfilleasy'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Plus
} from '@element-plus/icons-vue'
import {
    BconfigsetGet, BconfigSetgetPunit, DcApplyEasyListFind, DcApplyEasyApply, DcapplyGrantUserComboGet, DccatalogGetClassTwo
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit } from "@/utils/index.js";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const isSendMessage = ref(false)
const selectRows = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const memberUserList = ref([])//同领用人
const filters = ref({ pageIndex: 1, pageSize: 10, StockNumgt: 0, sortModel: [{ SortCode: "TwoCatalogId", SortType: "ASC" }, { SortCode: "SchoolCatalogId", SortType: "ASC" }] })
const bconfigSet = ref(0)
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    Num: [
        { required: true, message: '请输入领用数量', trigger: 'change' },
    ],
    Remark: [
        { required: true, message: '请输入用途', trigger: 'change' },
    ],
    UseTime: [
        { required: true, message: '请选择使用时间', trigger: 'change' },
    ],
    WithUserId: [
        { required: true, message: '请选择同领用人', trigger: 'change' },
    ]
}
const unitType = ref(userStore.userInfo.UnitType)
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
    BconfigSetgetPunitUser()
    BconfigsetGetUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 审核
const HandleEdit = (row) => {
    formData.value = row
    formData.value.Remark = '做实验'
    formData.value.Num = ''
    formData.value.WithUserId = undefined
    dialogVisible.value = true
}

// 通过入库
const HandleSubmit = () => {
    let isNeedSendMessage = 0
    if (formData.value.IsNeedSendMessage) {
        isNeedSendMessage = 1
    }
    let paraData = {
        IsNeedSendMessage: isNeedSendMessage,
        Num: formData.value.Num,
        Remark: formData.value.Remark,
        SchoolCatalogId: formData.value.SchoolCatalogId,
        SchoolMaterialBrandId: formData.value.SchoolMaterialBrandId,
        SchoolMaterialModelId: formData.value.SchoolMaterialModelId,
        UseTime: formData.value.UseTime,
        WithUserId: formData.value.WithUserId,
    }
    console.log("paraData", paraData)
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcApplyEasyApply(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '领用成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.TwoCatalogId = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcApplyEasyListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 根据TypeCode 配置信息
const BconfigsetGetUser = () => {
    BconfigsetGet({ moduleCode: 9, typeCode: 'WHPSFXYFFQR' }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (rows == 1) {
                isSendMessage.value = true
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 根据TypeCode获取上级单位配置信息
const BconfigSetgetPunitUser = () => {
    BconfigSetgetPunit({ moduleCode: 9, typeCode: 'WHPLYMS' }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bconfigSet.value = rows
            if (rows == 2 || rows == 3) {
                DcapplyGrantUserComboGetUser()
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}

// 获取同领用人、发放人
const DcapplyGrantUserComboGetUser = () => {
    DcapplyGrantUserComboGet({ type: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            memberUserList.value = rows || []
            if (memberUserList.value.length > 0) {
                const propertyNames = Object.keys(memberUserList.value[0]);
                if (propertyNames[0] === 'MEMBERUSERID') {
                    memberUserList.value = memberUserList.value.map(item => {
                        return {
                            MemberUserId: item.MEMBERUSERID,
                            MemberUserName: item.MEMBERUSERNAME
                        }
                    })
                }
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
//输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="StockNum" label="存量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100" align="center" v-if="unitType == 3">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">领用</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="危化品领用">
            <template #content>
                <el-form style="min-width: 120px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <el-form-item label="危化品名称：">
                        <span>{{ formData.Name }}</span>
                    </el-form-item>
                    <el-form-item label="规格属性：">
                        <span>{{ formData.Model }}</span>
                    </el-form-item>
                    <el-form-item label="品牌：">
                        <span>{{ formData.Brand }}</span>
                    </el-form-item>
                    <el-form-item label="领用数量：" prop="Num" class="boxItem">
                        <el-input v-model="formData.Num" @input="limitInput($event, 'Num')"
                            style="width: 240px"></el-input>
                        <span> 单位： (<span>{{ formData.UnitsMeasurement }}</span>) </span>
                    </el-form-item>
                    <el-form-item label="用途：" prop="Remark" class="boxItem">
                        <el-input v-model="formData.Remark" style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item label="使用时间：" prop="UseTime" class="boxItem">
                        <el-date-picker v-model="formData.UseTime" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable style="width: 240px;">
                        </el-date-picker>
                        <span style="color: #999;padding-left: 5px;"> 是指做实验的时间</span>
                    </el-form-item>
                    <el-form-item label="同领用人：" v-if="bconfigSet == 2 || bconfigSet == 3" prop="WithUserId"
                        class="boxItem">
                        <el-select v-model="formData.WithUserId" filterable style="width: 240px">
                            <el-option v-for="item in memberUserList" :key="item.MemberUserId"
                                :label="item.MemberUserName" :value="item.MemberUserId" />
                        </el-select>
                        <span style="color: #999;padding-left: 5px;"> 是指同去仓库领用危化品的人</span>
                    </el-form-item>
                    <el-form-item v-if="isSendMessage">
                        <el-checkbox v-model="formData.IsNeedSendMessage" label="发送短信通知危化品发放人" size="large" />
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
.mobile-box {
    :deep(.el-form-item) {
        margin-bottom: 8px;

    }

    :deep(.el-checkbox) {
        height: 32px;
        line-height: 32px;
    }

    .boxItem.el-form-item {
        margin-bottom: 18px;
    }
}
</style>