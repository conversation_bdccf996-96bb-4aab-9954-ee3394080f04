<script setup>
defineOptions({
    name: 'dangerchemicalsapplyaudit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DcapplyView, DcApplyAudit
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const route = useRoute()
const userStore = useUserStore()
const purchase = ref({})
const activeNames = ref(['information', 'audit'])
const formData = ref({ Statuz: '30', Remark: '同意' })
const refForm = ref()
const ruleForm = {
    Remark: [
        { required: true, message: '请填写意见', trigger: 'change' },
    ]
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleViewData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleViewData()
        }
    })
})
const HandleSubmit = () => {
    let paraData = {
        id: route.query.id,
        isWithdraw: 0,
        processNumber: route.query.p,
        remark: formData.value.Remark,
        statuz: formData.value.Statuz,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcApplyAudit(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '审核成功')
                let tagsList = userStore.tagsList
                tagsList = tagsList.filter(t => t.path != route.path)
                userStore.setTagsList(tagsList)
                router.push({ path: '/dangerchemicals/apply/auditlist@p=20' })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 详情
const HandleViewData = () => {
    DcapplyView({ id: route.query.id, viewType: 20 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            purchase.value = rows;
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="demo-collapse">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="领用信息" name="information">
                <template #title>
                    <div style="padding-left: 10px;">
                        领用信息
                    </div>
                </template>
                <div class="content">
                    <div><span class="content_label">领用人：</span>{{ purchase.UserName }}</div>
                    <div><span class="content_label">申领批次：</span>{{ purchase.BatchNo }}</div>
                    <div><span class="content_label">危化品名称：</span>{{ purchase.Name }}</div>
                    <div><span class="content_label">数量：</span>{{ purchase.Num }} &nbsp;&nbsp;({{ purchase.UnitName }})
                    </div>
                    <div><span class="content_label">规格属性：</span>{{ purchase.Model ? purchase.Model : '--' }}</div>
                    <div
                        v-if="purchase.SchoolMaterialBrandId && purchase.StockNum < (purchase.Num - purchase.ConfirmedNum)">
                        <div style="width: 500px;"><span class="content_label">品牌：</span>{{ purchase.Brand }}
                            <span style="color: #F56C6C;padding-left: 30px;">该品牌的危化品库存不足</span>
                        </div>
                    </div>
                    <div v-else>
                        <div style="width: 500px;"><span class="content_label">品牌：</span>
                            {{ purchase.Brand ? purchase.Brand : '--' }}
                            <span style="color: #999;padding-left: 30px;">
                                {{ purchase.SchoolMaterialBrandId || purchase.StockNum < (purchase.Num -
                                    purchase.ConfirmedNum) ? "" : "（品牌由系统根据有效期及数量自动分配）" }} </span>
                        </div>
                    </div>
                    <div class="content_label_div"><span class="content_label">使用时间：</span>
                        {{ purchase.UseTime ? purchase.UseTime.substring(0, 16) : '--' }}</div>
                    <div class="content_label_div"><span class="content_label">用途：</span>{{ purchase.Remark ?
                        purchase.Remark : '无' }}</div>
                </div>
            </el-collapse-item>
            <el-collapse-item title="领用审核" name="audit">
                <template #title>
                    <div style="padding-left: 10px;">
                        领用审核 <span style="color:red;font-size:14px;">（请根据用途，审核危化品的领用数量是否合理）</span>
                    </div>
                </template>
                <el-form style="min-width: 120px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <el-form-item label="审核结果：">
                        <el-radio-group v-model="formData.Statuz">
                            <el-radio value="30">通过</el-radio>
                            <el-radio value="21">不通过</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="审核意见：" :prop="formData.Statuz == 21 ? 'Remark' : ''">
                        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="formData.Remark"
                            style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                    </el-form-item>
                </el-form>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-wrap: wrap;
    color: #333;
    font-size: 14px;
    width: 760px;
    padding-left: 30px;

    &>div {
        width: 360px;
        padding: 5px 10px;
    }

    .content_label_div {
        width: 600px;
    }

    .content_label {
        display: inline-block;
        width: 120px;
        text-align: right;
        font-weight: bold;
    }

}

:deep(.el-collapse-item__header) {

    font-size: 14px;
    color: #0000ee;
}
</style>