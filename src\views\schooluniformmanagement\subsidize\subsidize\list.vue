<script setup>
defineOptions({
    name: 'subsidizelist'
});
import { onMounted, ref, computed, nextTick, onActivated } from 'vue'
import {
    QuestionFilled, Refresh, Search, UploadFilled, Delete, FolderAdd
} from '@element-plus/icons-vue'
import {
    Getpagedbytype,
    AttachmentUpload
} from '@/api/user.js'
import {
    PurchaseGetschoolsponsorpaged, PurchaseSponsoradd, PurchaseSponsoredit, PurchaseGetsponsorbyid, PurchaseDelsponsorbyid, PurchaseDelfilebyid
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { previousYearDate, foundReturn, fileDownload, integerLimit, limit, formatNumberWithCommas } from "@/utils/index.js";
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const uploadFileData = ref([])
//新增&编辑操作
const dialogVisible = ref(false)
const dialogData = ref({ Type: '1' })
const uniformList = ref([])
const editId = ref()
const isAdd = ref(false)
const refForm = ref()
const ruleForm = {
    SponsorAmount: [
        { required: true, message: '请填写资助总额', trigger: 'change' },
    ],
    SponsorUserNum: [
        { required: true, message: '请填写资助人数', trigger: 'change' },
    ],
    SponsorSourceName: [
        { required: true, message: '请填写资助来源', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    yearDateList.value = previousYearDate()
    GetpagedbytypeUser()
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    fileIdList.value = []
    PurchaseGetsponsorbyidUser(0, true)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// // 总金额
// const PersonSupportAll = computed(() => {
//     let num = 0;
//     if (dialogData.value.SponsorUserNum && dialogData.value.PersonSupport) {
//         num = Number(dialogData.value.SponsorUserNum) * Number(dialogData.value.PersonSupport)
//     }
//     return num
// })

//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
//输入保留两位小数
const limitInput = (val, name) => {
    dialogData.value[name] = limit(val);
}
// 新增
const HandleAdd = (row) => {
    isAdd.value = true
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    })

    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value.UniformPurchaseId = ''; //合同批次
        dialogData.value.SponsorAmount = '';//资助总额
        dialogData.value.SponsorUserNum = '';//资助人数
        dialogData.value.SponsorSourceName = '';//资助来源
        fileIdList.value = [];//附件ID集合
        dialogData.value.OrderNum = 0;//订单人数
    })
}
// 修改
const HandleEdit = (row) => {
    isAdd.value = false
    editId.value = row.Id
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    })
    PurchaseGetsponsorbyidUser(row.Id, false)
    dialogVisible.value = true
}
// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定删除此项校服资助信息吗?')
        .then(() => {
            PurchaseDelsponsorbyid({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleSearch()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}

//修改 提交
const HandleSubmit = () => {
    // 判断资助人数
    if (Number(dialogData.value.SponsorUserNum) > Number(dialogData.value.OrderNum)) {
        ElMessage.error('资助人数不能大于订单人数')
        return
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        // 判断必传的附件是否已传
        let found = foundReturn(uploadFileData.value)
        if (found) {
            return
        }
        if (isAdd.value) {
            PurchaseSponsoraddUser()
        } else {
            PurchaseSponsoreditUser()
        }
    })
}

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 105 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    PurchaseGetschoolsponsorpaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.PurchaseYear = undefined
    // filters.value.UniformPurchaseId = undefined
    filters.value.Key = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
const changePurchase = (e) => {
    console.log(e)
    let find = uniformList.value.find(t => t.value == e)
    if (find) dialogData.value.OrderNum = find.pid
}

// 新增
const PurchaseSponsoraddUser = () => {
    let formData = {
        UniformPurchaseId: dialogData.value.UniformPurchaseId,//合同批次
        SponsorAmount: dialogData.value.SponsorAmount,//资助总金额
        SponsorUserNum: dialogData.value.SponsorUserNum,//资助人数
        SponsorSourceName: dialogData.value.SponsorSourceName,//资助来源
        ListAttachmentId: fileIdList.value.map(t => t.Id),//附件ID集合
    }
    PurchaseSponsoradd(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '新增成功')
            dialogVisible.value = false
            fileIdList.value = [];
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 修改
const PurchaseSponsoreditUser = () => {
    let formData = {
        UniformPurchaseId: dialogData.value.UniformPurchaseId,
        Id: editId.value,
        SponsorAmount: dialogData.value.SponsorAmount,//资助总金额
        SponsorUserNum: dialogData.value.SponsorUserNum,//资助人数
        SponsorSourceName: dialogData.value.SponsorSourceName,//资助来源
        ListAttachmentId: fileIdList.value.map(t => t.Id),//附件ID集合
    }
    PurchaseSponsoredit(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '新增成功')
            dialogVisible.value = false
            fileIdList.value = [];
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const PurchaseGetsponsorbyidUser = (id, isFirst) => {
    PurchaseGetsponsorbyid({ id: id, isFirst: isFirst }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            if (isFirst) {
                if (other) {
                    uniformList.value = other.listPurchaseNo || [];//合同批次
                }
            } else {
                dialogData.value = rows
                dialogData.value.OrderNum = rows.ContractPersonNum
                if (isAdd.value) {
                    dialogData.value.SponsorAmount = ''
                    dialogData.value.SponsorUserNum = ''
                    dialogData.value.SponsorSourceName = ''
                }
                // 清除页面缓存附件存留造成的影响
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                })
                let categoryList = rows.ListAttachment || []
                if (categoryList.length > 0) {
                    // 遍历数组 b 的每个元素
                    categoryList.forEach(item => {
                        // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                        let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                        // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                        if (match) {
                            match.categoryList.push(item);
                        }
                    });
                }
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.categoryList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: item.FileCategory }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    PurchaseDelfilebyid({ id: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });

        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yearDateList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="合同批次"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="PurchaseYear" label="年度" min-width="100" align="center"></el-table-column>
            <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="SponsorUserNum" label="资助学生数" min-width="100" align="center"></el-table-column>
            <el-table-column prop="SponsorAmount" label="资助总额（元）" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.SponsorAmount ? formatNumberWithCommas(row.SponsorAmount) : '' }}</template>
            </el-table-column>
            <el-table-column prop="SponsorRatio" label="资助学生占比" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.SponsorRatio ? row.SponsorRatio : 0 }}%
                </template>
            </el-table-column>
            <el-table-column prop="SponsorSourceName" label="资助来源" min-width="140"></el-table-column>
            <el-table-column fixed="right" label="资助管理" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.SponsorUserNum > 0" type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="isAdd ? '新增资助信息' : '修改资助信息'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                    status-icon>
                    <!-- <el-form-item label="合同批次：">
                        <el-select v-model="dialogData.UniformPurchaseId" clearable filterable placeholder="合同批次"
                            @change="changePurchase" style="width: 60%;">
                            <el-option v-for="item in uniformList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item> -->

                     <el-form-item label="合同批次：">
                        <template #label>
                            <el-tooltip class="item" effect="dark"
                                content="是指合同履约的中“合同批次”；一个合同批次只能创建一次资助”，请正确选择，否则会影响资助统计数据" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> 合同批次： </span>
                        </template>
                         <el-select v-model="dialogData.UniformPurchaseId" clearable filterable placeholder="合同批次"
                            @change="changePurchase" style="width: 60%;">
                            <el-option v-for="item in uniformList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="订购总人数（人）：">
                        <span>{{ dialogData.OrderNum }}</span>
                    </el-form-item>

                    <el-form-item label="资助总额（元）：" prop="SponsorAmount">
                        <el-input v-model="dialogData.SponsorAmount" style="width: 60%;" @input="limitInput($event, 'SponsorAmount')"></el-input>
                    </el-form-item>
                    <el-form-item label="资助学生数（人）：" prop="SponsorUserNum">
                        <el-input v-model="dialogData.SponsorUserNum"
                            @input="integerLimitInput($event, 'SponsorUserNum')" style="width: 60%;"></el-input>
                    </el-form-item>
                    
                    <!-- <el-form-item label="资助总额（元）：">
                        <span>{{ PersonSupportAll }}</span>
                    </el-form-item> -->

                    <el-form-item label="资助来源：" prop="SponsorSourceName">
                        <el-input v-model="dialogData.SponsorSourceName" placeholder="资助单位名称或个人姓名"
                            style="width: 60%;"></el-input>
                    </el-form-item>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                            :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                            :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                            <el-button type="success" size="small" :icon="UploadFilled"
                                @click="MaxFileNumberClick(item)">上传</el-button>
                        </el-upload>
                        <div class="fileFlex">
                            <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                                    <Delete />
                                </el-icon>
                                <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                                    {{ itemCate.Title }}{{ itemCate.Ext }}
                                </span>
                            </div>
                            <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                                    <Delete />
                                </el-icon>
                                <span style="cursor: pointer;"
                                    @click="fileListDownload(itemChild, item.fileLChildList)">
                                    {{ itemChild.Title }}{{ itemChild.Ext }}
                                </span>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}

.detailDialog {
    :deep(.el-form-item__label) {
        color: #409EFF;

    }

    :deep(.el-form-item) {
        margin-bottom: 5px !important;
        margin-top: 5px !important;
        border-bottom: 1px solid #f5f5f5;
    }
}
</style>