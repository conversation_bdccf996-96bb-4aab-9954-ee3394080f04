<script setup>
defineOptions({
    name: 'evaluateexaminedetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Position
} from '@element-plus/icons-vue'
import {
    EvaluateGetevaluatedetaillist, ValuateExportdetail
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const ColumnList = ref([])
const UserTypeIdList = ref([])
const tipMsg = ref('')
const summation = ref({})
const PurchaseNo = ref('')

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        filters.value.UnifomEvaluateMainId = route.query.id
        HandleTableData(true);
    }
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.UnifomEvaluateMainId = route.query.id
            HandleTableData(true);
        }
    })
})

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    EvaluateGetevaluatedetaillist(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            if (isFirst) {
                ColumnList.value = other.listColumn || {};//评分列表
                UserTypeIdList.value = other.listUserTypeId || [];//分类人
                tipMsg.value = other.tipMsg || '';//合同批次
                summation.value = other.footer;//总计
            }
            if (rows.length > 0) {
                PurchaseNo.value = rows[0].PurchaseNo;
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.UserTypeId = undefined
    filters.value.Key = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
//导出
const HandleExport = () => {
    ValuateExportdetail(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let url = rows;
            let index = rows.lastIndexOf('_');
            let title = rows.substring(index + 1);
            fileDownload(url, title);
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计';
            return;
        }
        let data = ColumnList.value.find(t => t.Title == column.label)
        if (data) {
            sums[index] = summation.value[data.Name]
            return;
        }
    });
    return sums;
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.UserTypeId" clearable placeholder="评价人分类" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in UserTypeIdList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="评价人"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">
            <span style="color: #999;">{{ tipMsg }}</span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="UserTypename" label="评价人分类" min-width="110" align="center"></el-table-column>
            <el-table-column prop="UserName" label="评价人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="UserMobile" label="评价人手机号" min-width="140" align="center"></el-table-column>
            <el-table-column label="评价得分" min-width="100" align="center">
                <el-table-column prop="Score" label="综合得分" min-width="100" align="center">
                    <template #header="{ column }">
                        {{ column.label }}
                    </template>
                </el-table-column>
                <el-table-column v-for="(item, index) in ColumnList" :key="index" :label="item.Title" min-width="130"
                    align="center">
                    <template #default="{ row }">
                        {{ row[item.Name] }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column prop="EvaluateDate" label="评价时间" min-width="110" align="center">
                <template #default="{ row }">
                    {{ row.EvaluateDate ? row.EvaluateDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="建议" min-width="160" show-overflow-tooltip></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>