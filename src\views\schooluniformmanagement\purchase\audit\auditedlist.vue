<script setup>
defineOptions({
  name: 'auditauditedlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search
} from '@element-plus/icons-vue'
import {
  ShelfGetauditedpaged, ShelfRevokedbyid
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, })
const SupplierList = ref([])
const AuditStatuzList = ref([])

//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

// 品名查看
const HandleDetail = (row) => {
  // 电脑预览
  const { href } = router.resolve({
    path: "/preview",
    query: { id: row.Id, requestNum: 2 }
  });
  window.open(href, "_blank");
}
// 起止日期
const HandleDateSearch = (date) => {
  console.log("开始日期date", date)
  if (date) {
    filters.value.AuditTimeStart = date[0]
    filters.value.AuditTimeEnd = date[1]
  } else {
    filters.value.data = undefined
    filters.value.AuditTimeStart = undefined
    filters.value.AuditTimeEnd = undefined
  }
  HandleTableData()
}

//撤回
const HandleDel = (row) => {
  ElMessageBox.confirm('确定要撤回此项审核吗?')
    .then(() => {
      ShelfRevokedbyid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '撤回成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//列表
const HandleTableData = (isFirst) => {
  let formData = {
    pageIndex: filters.value.pageIndex,
    pageSize: filters.value.pageSize,
    SupplierId: filters.value.SupplierId,
    AuditStatuz: filters.value.AuditStatuz,
    AuditTimeStart: filters.value.AuditTimeStart,
    AuditTimeEnd: filters.value.AuditTimeEnd,
    Name: filters.value.Name,
  }
  if (isFirst) {
    formData.isFirst = true
  } else {
    formData.isFirst = undefined
  }

  ShelfGetauditedpaged(formData).then(res => {
    if (res.data.flag == 1) {
      // console.log("采购管理列表", res.data.data)
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        SupplierList.value = other.SupplierList || [];//供应商
        AuditStatuzList.value = other.AuditStatuzList;//审核状态
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.SupplierId = undefined
  filters.value.AuditStatuz = undefined
  filters.value.date = undefined
  filters.value.AuditTimeStart = undefined
  filters.value.AuditTimeEnd = undefined
  filters.value.Name = undefined
  HandleTableData(page)
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="审核时间：" class="flexItem">
            <el-date-picker v-model="filters.date" type="daterange" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
              range-separator="至" start-placeholder="起始日期" end-placeholder="截止日期" style="width: 260px"
              @change="HandleDateSearch" />
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SupplierId" clearable filterable placeholder="供应商" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SupplierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.AuditStatuz" clearable placeholder="审核状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in AuditStatuzList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="合同批次/种类/品名" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center">
        <template #default="{ row }">
          {{ row.PurchaseNo || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"> </el-table-column>
      <el-table-column prop="Name" label="品名" min-width="120" align="center">
        <template #default="{ row }">
          <span style="color: #66ccff;cursor: pointer;" @click="HandleDetail(row)">{{ row.Name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="Price" label="单价（元）" min-width="100" align="right"></el-table-column>
      <el-table-column prop="Sex" label="适合性别" min-width="90" align="center">
        <template #default="{ row }">
          {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : row.Sex == 3 ? '男/女' : '未知' }}
        </template>
      </el-table-column>
      <el-table-column prop="StandardNum" label="标配数量" min-width="90" align="center"></el-table-column>
      <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
      <el-table-column prop="ContractEndDate" label="合同终止时间" min-width="120" align="center">
        <template #default="{ row }">
          <span v-if="!row.ContractEndDate">--</span>
          <span v-else-if="new Date(row.ContractEndDate).setHours(23, 59, 59, 0) < new Date()" style="color: #F56C6C;">
            {{ row.ContractEndDate.substring(0, 10) }}</span>
          <span v-else>{{ row.ContractEndDate.substring(0, 10) }}</span>
        </template>

      </el-table-column>
      <el-table-column prop="SupplierName" label="供应商" min-width="180"></el-table-column>
      <el-table-column label="审核状态" min-width="100" align="center">
        <template #default="{ row }">
          <span v-if="row.AuditStatuz == 11" style="color: #F56C6C;">审核退回</span>
          <span v-else> {{ row.AuditStatuz == 10 ? '待审核' : '审核通过' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="AuditTime" label="审核时间" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.AuditTime ? row.AuditTime.substring(0, 10) : '--' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="80" align="center">
        <template #default="{ row }">
          <span v-if="row.OrderedNum > 0"></span>
          <el-button v-else type="primary" link @click="HandleDel(row)">撤回</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>