<script setup>
defineOptions({
    name: 'dangerchemicalsschoolmaterialcataloglist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Position, Edit
} from '@element-plus/icons-vue'
import {
    DcDepositAddressFind, DcMaterialAddressBatchEdit, DcSchoolMaterialStatisticsFind, DcScrapListFind, DccatalogGetClassTwo, DcCabinetAddressGet
} from '@/api/dangerchemicals.js'
import { ExcelDownload } from "@/utils/index.js"
import {
    ExportDcScrapList
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const detailTableData = ref([])
const detailTableTotal = ref(0)
const refTable = ref()
const StatuzSolicitedList = ref([])
const RegDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1, StockNumgt: 0, sortModel: [{ SortCode: "ValidDate", SortType: "ASC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const dialogVisible = ref(false)
const tableEditDialogVisible = ref(false)
const tableEditDialogData = ref([])
const formData = ref({})
const refForm = ref()
const ruleForm = {
    DepositAddressId: [
        { required: true, message: '请选择存放地点', trigger: 'blur' },
    ]
}
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', },
])
const options1 = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', },
    { value: 'Address', label: '存放地点', },
    { value: 'CabinetAddress', label: '储存柜层次', },
    { value: 'ThirdMaterialId', label: '标识码', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
const activeName = ref('collect')
const addressList = ref([])
const cabinetAddressList = ref([])
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
    AttributeUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
const summation = ref({})

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    console.log("columns", columns)
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = '总计';
            return;
        }
        if (column.property == 'StockNum') {
            sums[index] = summation.value.StockNum || '--'
        } else if (column.property == 'Amount') {
            if (summation.value.Amount) {
                sums[index] = '￥' + Number(summation.value.Amount).toFixed(2)
            } else {
                sums[index] = '--'
            }
        }
    });
    return sums;
}

//导出
const HandleExport = () => {
    ExportDcScrapList(filters.value).then(res => {
        ExcelDownload(res)
    });
}
const regDategeChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const regDateleChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}

// 按汇总查看列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    filters.value.Address = undefined;
    filters.value.CabinetAddress = undefined;
    filters.value.ThirdMaterialId = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    if (activeName.value == 'collect') {
        DcSchoolMaterialStatisticsFind(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, other } = res.data.data
                tableData.value = rows.data
                tableTotal.value = rows.dataCount
                summation.value = other[0] || {}
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        DcScrapListFind(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, total } = res.data.data
                detailTableData.value = rows.data
                detailTableTotal.value = rows.dataCount
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.RegDatege = undefined
    filters.value.RegDatele = undefined
    filters.value.TwoCatalogId = undefined
    filters.value.IsMayUse = undefined
    RegDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
//修改存放地点
const HandleSubmit = () => {
    let paraData = {
        ids: selectRows.value.map(item => item.Id).join(','),
        addressId: formData.value.DepositAddressId,
        cabinetAddress: formData.value.CabinetAddress
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcMaterialAddressBatchEdit(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '修改成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 切换tab
const handleClick = (tab) => {
    console.log(tab.props.name, tab.index)
    activeName.value = tab.props.name
    filters.value = {
        pageIndex: 1,
        pageSize: 10,
        Statuz: 1,
        StockNumgt: 0,
        sortModel: [{ SortCode: "ValidDate", SortType: "ASC" }, { SortCode: "RegDate", SortType: "DESC" }]
    }
    filtersValue.value = 'Name'
    HandleTableData()
}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const HandleDetail = (row) => {
    let data = {
        SchoolCatalogId: row.SchoolCatalogId,
        SchoolMaterialBrandId: row.SchoolMaterialBrandId,
        SchoolMaterialModelId: row.SchoolMaterialModelId,
        pageIndex: 1,
        pageSize: 1000000,
        Statuz: 1,
        StockNumgt: 0,
        sortModel: [{ SortCode: "ValidDate", SortType: "ASC" }, { SortCode: "RegDate", SortType: "DESC" }]
    }
    DcScrapListFind(data).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableEditDialogData.value = rows.data
            tableEditDialogVisible.value = true
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const AttributeUser = () => {
    // 获取存放地点
    DcDepositAddressFind({ Statuz: 1, pageIndex: 0, pageSize: 999999999 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            addressList.value = rows.data || []

        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
    // 获取储存柜层次
    DcCabinetAddressGet().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            cabinetAddressList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!RegDatele.value) return false;
    return time >= new Date(RegDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    危化品存量库列表 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 入库方式：“采购”是指从供应商采购后直接入库，“退回”是指实验使用后剩余退回入库； </li>
                    <li> 是否可用：“不可用”是指危化品待报废处置；此状态在【危化品报废】栏中设置； </li>
                    <li> 按照“存储地点”名称搜索后，点击【导出】按钮导出储存柜内的药品分布。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="按汇总查看" name="collect">
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable
                                    placeholder="入库时间" @change="regDategeChange" style="width: 180px;">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item class="flexItem" label="至">
                                <el-date-picker v-model="RegDatele" type="date" format="YYYY-MM-DD"
                                    :disabled-date="disabledEndDate" value-format="YYYY-MM-DD" clearable
                                    placeholder="入库时间" @change="regDateleChange" style="width: 180px;">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item label="" class="flexItem">
                                <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类"
                                    style="width: 160px" @change="filtersChange">
                                    <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                        :value="item.Id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                                    class="input-with-select">
                                    <template #prepend>
                                        <el-select v-model="filtersValue" style="width: 120px">
                                            <el-option v-for="item in options" :key="item.value" :label="item.label"
                                                :value="item.value" />
                                        </el-select>
                                    </template>
                                </el-input>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                    :summary-method="getSummaries" show-summary @selection-change="HandleSelectChange"
                    header-cell-class-name="headerClassName">
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="StockNum" label="存量" min-width="110" align="right">
                        <template #default="{ row }">
                            <span :style="{ fontWeight: row.Id ? 'normal' : 'bold' }">
                                {{ row.StockNum }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
                    <el-table-column prop="Amount" label="金额" min-width="160" align="right">
                        <template #default="{ row }">
                            <span :style="{ fontWeight: row.Id ? 'normal' : 'bold' }">
                                {{ row.Amount ? '￥' + Number(row.Amount).toFixed(2) : '--' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                        <template #default="{ row }">
                            {{ row.Brand || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="明细" min-width="100" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
            <el-tab-pane label="按明细查看" name="detail">
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-button type="success" :icon="Edit" :disabled="selectRows.length == 0"
                                    @click="dialogVisible = true">修改存放地点</el-button>
                                <div class="verticalDividel"></div>
                                <el-button type="success" :icon="Position" :disabled="detailTableData.length == 0"
                                    @click="HandleExport">导出</el-button>
                            </el-form-item>
                            <div class="verticalIdel"></div>
                            <el-form-item class="flexItem">
                                <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable
                                    placeholder="入库时间" @change="regDategeChange" style="width: 180px;">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item class="flexItem" label="至">
                                <el-date-picker v-model="RegDatele" type="date" format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD" :disabled-date="disabledEndDate" clearable
                                    placeholder="入库时间" @change="regDateleChange" style="width: 180px;">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item label="" class="flexItem">
                                <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类"
                                    style="width: 160px" @change="filtersChange">
                                    <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                        :value="item.Id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="" class="flexItem">
                                <el-select v-model="filters.IsMayUse" clearable placeholder="是否可用" style="width: 160px"
                                    @change="filtersChange">
                                    <el-option label="可用" value="1" />
                                    <el-option label="不可用" value="0" />
                                </el-select>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                                    class="input-with-select">
                                    <template #prepend>
                                        <el-select v-model="filtersValue" style="width: 120px">
                                            <el-option v-for="item in options1" :key="item.value" :label="item.label"
                                                :value="item.value" />
                                        </el-select>
                                    </template>
                                </el-input>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <el-table ref="refTable" :data="detailTableData" highlight-current-row border stripe
                    @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="StockNum" label="存量" min-width="110" align="right">
                        <template #default="{ row }">
                            <span :style="{ fontWeight: row.Id ? 'normal' : 'bold' }">
                                {{ row.StockNum }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
                    <el-table-column prop="Amount" label="金额" min-width="160" align="right">
                        <template #default="{ row }">
                            <span :style="{ fontWeight: row.Id ? 'normal' : 'bold' }">
                                {{ row.Amount ? '￥' + Number(row.Amount).toFixed(2) : '--' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                        <template #default="{ row }">
                            {{ row.Brand || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                        <template #default="{ row }">
                            {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="ValidDate" label="有效期至" min-width="120" align="center">
                        <template #default="{ row }">
                            {{ row.ValidDate ? row.ValidDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="Address" label="存放地点" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="CabinetAddress" label="储存柜层次" min-width="120" align="center">
                    </el-table-column>
                    <el-table-column prop="InputType" label="入库方式" min-width="110" align="center">
                        <template #default="{ row }">
                            {{ row.InputType == 1 ? '采购' : row.InputType == 1 ? '退回' : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="IsMayUse" label="是否可用" min-width="110" align="center">
                        <template #default="{ row }">
                            <span v-if="row.IsMayUse == 1" style="color:green">√</span>
                            <span v-else-if="row.IsMayUse == 0" style="color:red">×</span>
                            <span v-else></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="标识码" min-width="120" align="center" show-overflow-tooltip>
                        <template #default="{ row }">
                            {{ row.ThirdMaterialId || '--' }}
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="detailTableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
        </el-tabs>
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="危化品发放">
            <template #content>
                <el-form style="min-width: 400px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="存放地点：" prop="DepositAddressId">
                        <el-select v-model="formData.DepositAddressId" filterable style="width: 400px">
                            <el-option v-for="item in addressList" :key="item.Id" :label="item.Address"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="储存柜层次：">
                        <el-select v-model="formData.CabinetAddress" filterable allow-create default-first-option
                            style="width: 400px">
                            <el-option v-for="item in cabinetAddressList" :key="item.CabinetAddress"
                                :label="item.CabinetAddress" :value="item.CabinetAddress" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 发放危化品 </el-button>
                </span>
            </template>
        </app-box>
        <app-box v-model="tableEditDialogVisible" :width="980" :lazy="true" title="危化品库存明细">
            <template #content>
                <el-table ref="refTableData" :data="tableEditDialogData" border stripe max-height="360px"
                    header-cell-class-name=" headerClassName">
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                        <template #default="{ row }">
                            {{ row.Brand || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="StockNum" label="存量" min-width="110" align="right">
                        <template #default="{ row }">
                            <span :style="{ fontWeight: row.Id ? 'normal' : 'bold' }">
                                {{ row.StockNum }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
                    <el-table-column prop="Amount" label="金额" min-width="160" align="right">
                        <template #default="{ row }">
                            <span :style="{ fontWeight: row.Id ? 'normal' : 'bold' }">
                                {{ row.Amount ? '￥' + Number(row.Amount).toFixed(2) : '--' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="RegDate" label="入库时间" min-width="180" align="center">
                        <template #default="{ row }">
                            {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="ValidDate" label="有效期至" min-width="180" align="center">
                        <template #default="{ row }">
                            {{ row.ValidDate ? row.ValidDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="Address" label="存放地点" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="CabinetAddress" label="储存柜层次" min-width="120" align="center">
                    </el-table-column>
                    <el-table-column prop="InputType" label="入库方式" min-width="110" align="center">
                        <template #default="{ row }">
                            {{ row.InputType == 1 ? '采购' : row.InputType == 1 ? '退回' : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="IsMayUse" label="是否可用" min-width="110" align="center">
                        <template #default="{ row }">
                            <span v-if="row.IsMayUse == 1" style="color:green">√</span>
                            <span v-else-if="row.IsMayUse == 0" style="color:red">×</span>
                            <span v-else></span>
                        </template>
                    </el-table-column>
                    <el-table-column label="标识码" min-width="120" align="center" show-overflow-tooltip>
                        <template #default="{ row }">
                            {{ row.ThirdMaterialId || '--' }}
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>