<script setup>
import { ref, watch, computed, watchEffect, onMounted } from 'vue'
import {
    ArrowLeftBold, ArrowRightBold
} from '@element-plus/icons-vue'
import { useMouseInElement } from '@vueuse/core'
const imageList = ref([])
const props = defineProps({
    //总数量
    imageAllList: {
        type: Array,
        default: []
    },
})
onMounted(() => {
    if (props.imageAllList.length) {
        // 执行一些操作  
        imageList.value = props.imageAllList
    }
    // console.log("props.imageAllList", props.imageAllList)
})
// 1.小图切换大图显示
const activeIndex = ref(0)

const enterhandler = (i) => {
    activeIndex.value = i
}

// 2. 获取鼠标相对位置
const target = ref(null)
const { elementX, elementY, isOutside } = useMouseInElement(target)

// 3. 控制滑块跟随鼠标移动（监听elementX/Y变化，一旦变化 重新设置left/top）
// 滑块俩坐标
const left = ref(0)
const top = ref(0)

// 大图俩坐标
const positionX = ref(0)
const positionY = ref(0)

// 回调函数会一直执行,可以优化不执行
watch([elementX, elementY, isOutside], () => {
    // 如果鼠标没有移入到盒子里面 直接不执行后面的逻辑
    if (isOutside.value) return
    // 有效范围内控制滑块距离
    // 横向
    if (elementX.value > 100 && elementX.value < 300) {
        left.value = elementX.value - 100
    }
    // 纵向
    if (elementY.value > 100 && elementY.value < 300) {
        top.value = elementY.value - 100
    }
    // 处理边界
    if (elementX.value > 300) { left.value = 200 }
    if (elementX.value < 100) { left.value = 0 }

    if (elementY.value > 300) { top.value = 200 }
    if (elementY.value < 100) { top.value = 0 }

    // 控制大图的显示
    positionX.value = -left.value * 2
    positionY.value = -top.value * 2
})

const visibleCount = 5; // 同时显示的图片数量  
const itemWidth = 350; // 包括间隔的图片宽度  
const scrollLeft = ref(0); // 控制滚动的位置  

const allImagesVisible = computed(() => {
    return (scrollLeft.value + visibleCount * itemWidth) >= imageList.value.length * itemWidth;
});

const nextImages = () => {
    if (!allImagesVisible.value) {
        scrollLeft.value += itemWidth;
    }
}

const prevImages = () => {
    if (scrollLeft.value > 0) {
        scrollLeft.value -= itemWidth;
    }
}

// 监听images变化，重新计算scrollLeft（可选，如果images是动态变化的）  
watchEffect(() => {
    if (imageList.value.length <= visibleCount) {
        scrollLeft.value = 0;
    }
});


</script>


<template>
    <div class="goods-image">
        <!-- 左侧大图-->
        <div class="middle" ref="target">
            <!-- 大图展示 -->
            <img :src="imageAllList[activeIndex].Path" alt="" />
            <!-- 大图中的蒙层小滑块 -->
            <div class="layer" v-show="!isOutside" :style="{ left: `${left}px`, top: `${top}px` }"></div>
        </div>
        <!-- 小图列表 -->
        <div style="display: flex;margin-top: 10px;justify-content: space-between;">
            <div class="middleBtn">
                <el-button @click="prevImages" :disabled="scrollLeft <= 0" :icon="ArrowLeftBold"></el-button>
            </div>
            <div class="small">
                <!-- 可以使用 @mouseenter 事件监听器来监听鼠标移入（hover）元素的事件。 -->


                <div class="image-wrapper" :style="{ transform: `translateX(-${scrollLeft}px)` }">
                    <div class="image-item" v-for="(img, i) in imageAllList" :key="i" @mouseenter="enterhandler(i)"
                        :class="{ active: activeIndex === i }">
                        <!-- 小图展示 -->
                        <img :src="img.Path" alt="" />
                    </div>
                </div>

            </div>
            <div class="middleBtn">
                <el-button @click="nextImages" :disabled="allImagesVisible" :icon="ArrowRightBold"></el-button>
            </div>
        </div>
        <!-- 放大镜大图 -->
        <div class="large" :style="[
                {
                    backgroundImage: `url(${imageAllList[activeIndex].Path})`,
                    backgroundPositionX: `${positionX}px`,
                    backgroundPositionY: `${positionY}px`,
                    backgroundColor: 'rgba(255,255,255,1)',
                    backgroundSize: '200% 200%',
                },
            ]" v-show="!isOutside">


        </div>
    </div>
</template>

<style scoped lang="scss">
.goods-image {
    // width: 600px;
    //   height: 400px;
    position: relative;

    //   display: flex;
    //   margin: 200px 600px;



    // 左侧大图
    .middle {
        width: 400px;
        height: 400px;
        border: 1px solid #e4e4e4;

        img {
            width: 100%;
            height: 100%;
        }

        // 大图中的蒙层小滑块
        .layer {
            width: 200px;
            height: 200px;
            background: rgba(0, 0, 0, 0.2);
            // 绝对定位 然后跟随鼠标控制left和top属性就可以让滑块移动起来
            position: absolute;
            left: 0;
            top: 0;
        }
    }

    // 列表小图夫容器
    .small {
        width: 340px;
        height: 100px;
        // display: flex;
        // overflow: hidden;
        // // flex-direction: column; /* 设置主轴为垂直方向 */
        // justify-content: space-between;
        // /* 在主轴上均匀分布 */
        // align-items: center;


        overflow: hidden;
        position: relative;
        // width: 400px;
        /* 假设每张图片100px宽，加上一些间隔 */
        height: auto;
        white-space: nowrap;


        /* 在交叉轴上居中 */
        // margin-left: 12px;
        .image-item {
            width: 56px;
            height: 56px;
            margin: 0 6px;
            cursor: pointer; // 鼠标悬停时显示为手型指示符的样式

            &:hover,
            &.active {
                border: 2px solid greenyellow;
            }

            // 列表小图
            img {
                width: 56px;
                height: 56px;
            }
        }
    }

    // 放大镜大图
    .large {
        position: absolute;
        top: 0;
        left: 400px;
        width: 400px;
        height: 400px;
        z-index: 999;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    }
}

.image-carousel {
    overflow: hidden;
    position: relative;
    width: 400px;
    /* 假设每张图片100px宽，加上一些间隔 */
    height: auto;
    white-space: nowrap;
}

.image-wrapper {
    display: flex;
    transition: transform 0.5s ease;
    /* 平滑滚动效果 */
}

.middleBtn {
    // width: 25px;


    .el-button {
        width: 22px;
        height: 58px;
        flex-shrink: 0;
        border: none;
    }
}
</style>
