<script setup>
defineOptions({
    name: 'dangerchemicalsdailycheckresultlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Select, FolderAdd, DocumentCopy
} from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
    GovernTaskUnitListFind, GovernTaskSchoolListFind, GovernTaskSchoolBatchAdd, GovernTaskSchoolDelete, GovernTaskFinish, PrintCheck
} from '@/api/daily.js'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
const taskId = ref(0)
const unitType = ref(0)
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 0, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const summation = ref({})
const purchaseStatuzArray = ref([
    { id: 0, Name: '未填写' },
    { id: 1, Name: '已填写' }
])
const name = ref('')
const selectRows = ref([])//选中的行
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})

// 列表
const HandleTableData = () => {
    GovernTaskUnitListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (rows.Other) {
                formData.value.name = rows.Other.GovernTask.Name
                taskId.value = rows.Other.GovernTask.Id
                unitType.value = rows.Other.GovernTask.UnitIdType
            }
            if (rows.Statistics) {
                summation.value = rows.Statistics
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.TaskUnitStatuz = undefined
    filters.value.SchoolName = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

const filtersChange = () => { HandleTableData() }

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        if (unitType.value == 1) {
            if (index == 3) {
                sums[index] = summation.value.ProblemNum
            }
            if (index == 4) {
                sums[index] = summation.value.DangerNum
            }
            if (index == 5) {
                sums[index] = summation.value.CheckingUserNum
            }
        } else {
            if (index == 2) {
                sums[index] = summation.value.ProblemNum
            }
            if (index == 3) {
                sums[index] = summation.value.DangerNum
            }
            if (index == 4) {
                sums[index] = summation.value.CheckingUserNum
            }
        }
    });
    return sums;
}
//=====================选择列表====================//
const selectTableData = ref([])
const selectTableTotal = ref(0)
const selectRefTable = ref()
const selectFiltersKey = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
//列表
const selectHandleTableData = () => {
    selectFiltersKey.value.GovernTaskId = taskId.value
    GovernTaskSchoolListFind(selectFiltersKey.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            selectTableData.value = rows.data
            selectTableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const selectHandleSearch = () => {
    filters.value.pageIndex = 1
    selectHandleTableData()
}

// 重置
const selectHandleReset = () => {
    selectFiltersKey.value.pageIndex = 1
    selectFiltersKey.value = {}
    selectFiltersKey.value.Name = undefined
    selectHandleTableData()
}

// 分页
const selectHandlePage = (val) => {
    selectHandleTableData()
}
//================================================//
const dialogVisible = ref(false)
// 添加账户
const HandleAdd = () => {

    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        dialogVisible.value = true
        selectHandleTableData()
    })


}

// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

//添加学校
const AddSchool = (schoolId) => {
    let schoolIds = [];
    schoolIds.push(schoolId);
    let formData = {
        ListSchoolId: schoolIds,
        Name: formData.value.name
    }
    GovernTaskSchoolBatchAdd(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '添加成功')
            dialogVisible.value = false

            taskId.value = res.data.data.rows
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//批量添加学校
const HandleBatchAdd = () => {
    //根据数据集合提取需要的属性
    let schoolIds = selectRows.value.map(item => item.SchoolId);
    let formData = {
        ListSchoolId: schoolIds,
        Name: formData.value.name
    }
    GovernTaskSchoolBatchAdd(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '批量添加成功')
            dialogVisible.value = false
            taskId.value = res.data.rows
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//删除学校
const HandleDel = (schoolId) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false
    }).then(() => {
        //这里需要进行业务判断，是否可以删除
        let formData = {
            TaskId: taskId.value,
            SchoolId: schoolId
        }
        GovernTaskSchoolDelete(formData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '删除成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
        nextTick(function () {
            //jsPlumbObj.value.removeAllEndpoints(node.id);
        })
    }).catch(() => {

    })
}

//修改检查信息
const BtnEdit = (param) => {
    router.push({
        path: "/dangerchemicals/daily/checkresultedit", query: {
            taskId: param.GovernTaskId,
            schoolId: param.SchoolId,
            governTaskUnitId: param.GovernTaskUnitId,
        }
    })
}

//结束本次检查
const HandleEndSubmit = () => {

    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;


        if (taskId.value == 0) {
            ElMessage.error('存在未填写完成的项，请全部添加完成后在结束');
            return false;
        }

        ElMessageBox.confirm('确定要结束本次检查吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            closeOnClickModal: false
        }).then(() => {
            //这里需要进行业务判断，是否可以删除
            let formData = {
                taskId: taskId.value,
                name: formData.value.name
            }

            GovernTaskFinish(formData).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '执行成功')
                    HandleTableData()
                    formData.value.name = ''
                } else {
                    ElMessage.error(res.data.msg)
                }
            })

            nextTick(function () {
                //jsPlumbObj.value.removeAllEndpoints(node.id);
            })
        }).catch(() => {

        })

    })
}
// 打印
const HandlePrint = (e) => {
    PrintCheck().then((res) => {
        if (res.data.flag == 1) {
            router.push({
                path: "./offlinecheckprint",
                query: {
                    path: './checkresultlist',
                }
            })
        } else {
            ElMessage.error('必须设置单位信息才能打印，请先至“危化品治理设置”中点击“单位信息设置”！')
        }
    })
}
const refForm = ref()
const formData = ref({ name: '' })
const ruleForm = {
    name: [
        { required: true, message: '请填写检查任务名称', trigger: 'change' },
    ]
}
</script>
<template>

    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    填写检查结果 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>第一步：填写“检查任务名称”，添加学校名称；</li>
                    <li>第二步：填写检查信息；</li>
                    <li>第三步：点击【结束本次检查】，完成后请到【检查记录表】中查看。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="FolderAdd" @click="HandleAdd">添加学校</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.TaskUnitStatuz" clearable placeholder="状态" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in purchaseStatuzArray" :key="item.id" :label="item.Name"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.SchoolName" placeholder="学校名称" style="width: 180px"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Select" @click="HandleEndSubmit">结束本次检查</el-button>
                        <el-button type="primary" :icon="DocumentCopy" @click="HandlePrint">打印线下检查表</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>

        <el-form style="min-width: 500px;" class="mobile-box" ref="refForm" :model="formData" :rules="ruleForm"
            inline-message status-icon>
            <el-form-item label="检查任务名称：" prop="name">
                <el-input v-model="formData.name" style="width: 240px" placeholder="检查任务名称" />
            </el-form-item>
        </el-form>


        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column v-if="unitType == 1" prop="AreaName" label="区县名称" min-width="140"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="SchoolName" label="学校名称" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="CheckingDate" label="检查时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.CheckingDate ? row.CheckingDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ProblemNum" label="发现问题" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.ProblemNum > -1 ? row.ProblemNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DangerNum" label="发现隐患" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.DangerNum > -1 ? row.DangerNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CheckingUserNum" label="检查人员数" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.CheckingUserNum > -1 ? row.CheckingUserNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="TaskUnitStatuz" label="填写状态" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.TaskUnitStatuz == 1 ? '已填写' : '未填写' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="BtnEdit(row)">修改检查信息</el-button>
                    <el-button type="primary" link @click="HandleDel(row.SchoolId)">删除学校</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
    <!-- 学校列表弹窗 -->
    <app-box v-model="dialogVisible" :height="600" :width="800" :lazy="true" title="添加学校">
        <template #content>
            <div class="viewContainer">
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="selectFiltersKey" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-input v-model.trim="selectFiltersKey.Name" placeholder="学校名称" style="width: 180px">
                                </el-input>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="selectHandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="selectHandleReset">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <el-table ref="selectRefTable" :data="selectTableData" highlight-current-row border stripe
                    header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column v-if="unitType == 1" prop="CountyName" label="区县名称" min-width="14"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="SchoolName" label="学校名称" min-width="160"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column fixed="right" label="操作" min-width="120" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="AddSchool(row.SchoolId)">添加</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="selectTableTotal" v-model:pageIndex="selectFiltersKey.pageIndex"
                    v-model:pageSize="selectFiltersKey.pageSize" @handleChange="selectHandlePage" />
            </div>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="HandleBatchAdd" :disabled="selectRows.length == 0"> 批量添加 </el-button>
            </span>
        </template>
    </app-box>
</template>
<style lang="scss" scoped></style>