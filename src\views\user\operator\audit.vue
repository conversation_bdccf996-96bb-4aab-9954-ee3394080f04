<script setup>
import { onMounted, ref } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    PsupplierschoolauditGetpaged, PsupplierschoolauditGetbyid, PsupplierschoolauditAuditunit, Getpagedbytype
} from '@/api/user.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { fileDownload } from "@/utils/index.js";
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const AreaList = ref([])
const areaValue = ref([])
const uploadFileData = ref([])
const dialogVisible = ref(false)
const dialogData = ref({ Type: '1' })
const refForm = ref()
const ruleForm = {
    AuthStatuz: [
        { required: true, message: '请选择审核结果', trigger: 'change' },
    ],
    Reason: [
        { required: true, message: '请输入审核不通过原因', trigger: 'change' },
    ],
    IsCountyManager: [
        { required: true, message: '请选择区县是否管理学校', trigger: 'change' },
    ],
    Pid: [
        { required: true, message: '请选择主管单位', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    HandleTableData(true)
})

// 审核
const HandleExamine = (row) => {
    GetpagedbytypeUser()
    PsupplierschoolauditGetbyidUser(row.Id)
    dialogVisible.value = true
}
// 提交审核
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        PsupplierschoolauditAuditunitUser()
    })
}
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 106 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.categoryList = []
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//列表
const HandleTableData = (isFirst) => {
    if (isFirst) {
        filters.value.pageIndex = 1
        filters.value.Key = undefined
    }
    filters.value.isFirst = isFirst
    PsupplierschoolauditGetpaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = (page) => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 获取审核信息
const PsupplierschoolauditGetbyidUser = (id) => {
    PsupplierschoolauditGetbyid({ id: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows
            dialogData.value.AuthStatuz = undefined
            let categoryList = rows.ListAttachment || []
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
            AreaList.value = other.listArea || [];//省、市、区
            let provinceId = rows.ProvinceId || undefined;
            let cityId = rows.CityId || undefined;
            let countyId = rows.CountyId || undefined;
            areaValue.value = [provinceId, cityId, countyId];//省、市、区

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 提交审核
const PsupplierschoolauditAuditunitUser = () => {
    let params = {
        Id: dialogData.value.Id,
        AuthStatuz: dialogData.value.AuthStatuz,
        Reason: dialogData.value.Reason,
    }
    if (dialogData.value.AuthStatuz == 1) {
        params.Reason = undefined
    }


    PsupplierschoolauditAuditunit(params).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '提交审核成功')
            HandleTableData()
            dialogVisible.value = false
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="单位名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column type="index" width="50" />
            <el-table-column prop="UnitTypeName" label="单位类型" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Name" label="单位名称" min-width="160"></el-table-column>
            <el-table-column prop="CreateTime" label="注册时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.CreateTime ? row.CreateTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleExamine(row)">审核</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <!-- 分页 -->
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <!-- 弹窗 -->
        <el-dialog v-model="dialogVisible" title="校服审核" draggable width="760px" :close-on-click-modal="false">
            <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="160px"
                status-icon>
                <div class="dialogFlexBox">
                    <el-form-item label="单位名称">
                        <el-input v-model="dialogData.Name" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="组织机构代码：">
                        <el-input v-model="dialogData.SocialCreditCode" disabled></el-input>
                    </el-form-item>
                </div>
                <div class="dialogFlexBox">
                    <el-form-item label="所在地区：">
                        <el-cascader :options="AreaList" disabled v-model="areaValue"
                            :props="{ value: 'Id', label: 'Name', children: 'Children' }"></el-cascader>
                    </el-form-item>
                    <el-form-item label="详细地址：">
                        <el-input v-model="dialogData.Address" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="联系人：">
                        <el-input v-model="dialogData.ContactUser" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="联系电话：">
                        <el-input v-model="dialogData.Tel" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="电商地址：">
                        <el-input v-model="dialogData.Url" disabled></el-input>
                    </el-form-item>
                </div>
                <el-form-item label="公司简介：">
                    <el-input v-model="dialogData.Introduction" disabled show-word-limit
                        :autosize="{ minRows: 2, maxRows: 6 }" type="textarea"></el-input>
                </el-form-item>
                <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                    <template #label>
                        <span>
                            {{ item.Name }}:
                        </span>
                    </template>
                    <div class="fileFlex">
                        <div v-for="(itemCate, indexCate) in item.categoryList" :key="itemCate.Id">
                            <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                                {{ itemCate.Title }}{{ itemCate.Ext }}
                            </span>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="审核结果：" prop="AuthStatuz">
                    <el-radio-group v-model="dialogData.AuthStatuz">
                        <el-radio :value="1">审核通过</el-radio>
                        <el-radio :value="2">审核不通过</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="dialogData.AuthStatuz == 2" label="审核意见：" prop="Reason">
                    <el-input type="textarea" v-model="dialogData.Reason" :autosize="{ minRows: 2, maxRows: 6 }"
                        placeholder="请输入审核意见"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit">
                        提交
                    </el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 图片预览 -->
        <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}

.dialogFlexBox {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
        width: 50%;
    }
}
</style>