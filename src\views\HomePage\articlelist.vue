<script setup>
import { ElMessage } from 'element-plus'
import { ref, onMounted, onUnmounted } from 'vue'
import {
    ArticleGetoperator, ArticleGetinformationbycid
} from '@/api/home.js'
import HomeHeader from '@/views/HomePage/HomeHeader.vue';
import Footer from '@/views/HomePage/homefooter.vue';
import { fileDownload } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const userStore = useUserStore()
const route = useRoute()
const IsMany = ref(0)
const menuIndex = ref('')
const defaultActive = ref('')
const menuData = ref([])
const articleCid = ref(0)
const articleList = ref([])
const articleData = ref({})
const ArticleTitle = ref(true)
const articleFooterList = ref([])
const defaultSet = ref({})
const msg = ref('')
onMounted(() => {
    console.log("route.query", route)
    document.documentElement.style.setProperty('--body-background-color', '#f2f2f2');
    document.documentElement.style.setProperty('--body--webkit-scrollbar', '0px');
    articleCid.value = route.query.Id || 0
    if (route.query.code) {
        ArticleGetoperatorUser({ code: route.query.code })
    } else {
        ArticleGetoperatorUser({ cid: articleCid.value })
    }
    IsMany.value = route.query.IsMany
    menuIndex.value = route.query.Id
    defaultActive.value = route.query.Id

    articleFooterList.value = userStore.articleFooter
    defaultSet.value = userStore.defaultSet
})
onUnmounted(() => {
    // 移除样式
    document.documentElement.style.setProperty('--body-background-color', '#ffffff');
    document.documentElement.style.setProperty('--body--webkit-scrollbar', '6px');
});

// 查看资讯详情
const detailClick = (item) => {
    const { href } = router.resolve({
        path: "/articledetail",
        query: { Id: item.Id }
    });
    window.open(href, "_blank");
}

const nameClick = (item, index) => {
    // console.log(item)
    IsMany.value = item.IsMany
    menuIndex.value = item.Id
    articleData.value = {}
    articleList.value = []
    ArticleGetinformationbycidUser(item.Id)
}
//首次获取资讯分类
const ArticleGetoperatorUser = (parData) => {
    menuData.value = []
    ArticleGetoperator(parData).then(res => {
        // console.log("资讯列表", res)
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            menuData.value = rows || []
            if (menuData.value.length > 0) {
                if (!IsMany.value) IsMany.value = menuData.value[0].IsMany
                if (!menuIndex.value) menuIndex.value = menuData.value[0].Id
                if (!defaultActive.value) defaultActive.value = menuData.value[0].Id
            }
            if (IsMany.value == 1) {
                articleList.value = other || []
            } else {
                if (other && other.length > 0) {
                    articleData.value = other[0]
                }
            }
            if (other && other.length > 0) {
                ArticleTitle.value = true
                msg.value = ''
            } else {
                ArticleTitle.value = false
                msg.value = '暂无资讯'
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
//根据资讯分类Id查询资讯信息  
const ArticleGetinformationbycidUser = (cid) => {
    ArticleGetinformationbycid({ cid: cid }).then(res => {
        // console.log("资讯列表", res)
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            articleList.value = rows || []
            if (rows && rows.length > 0) {
                articleData.value = rows[0]
                articleData.value.BeginTime = rows[0].BeginTime.substring(0, 10)
                ArticleTitle.value = true
                msg.value = ''
            } else {
                ArticleTitle.value = false
                msg.value = '暂无资讯'
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

const showViewer = ref(false)
const viewPhotoList = ref([])
// 附件图片预览与文件下载
const fileListDownload = () => {
    let path = articleData.value.Attachment
    let index = path.lastIndexOf(".")
    let Ext = path.substring(index)
    viewPhotoList.value = [path]
    if (Ext == ".png" || Ext == ".jpg" || Ext == ".jpeg") {
        showViewer.value = true;
    } else {
        let title = articleData.value.Title + Ext
        fileDownload(path, title)
    }
}
// 获取子组件数据
const updateData = (data) => {
    // console.log("updateData", data)
    articleCid.value = data.Id
    if (data.code) {
        ArticleGetoperatorUser({ code: data.code })
    } else {
        ArticleGetoperatorUser({ cid: data.Id })
    }

    IsMany.value = data.IsMany
    menuIndex.value = data.Id
    defaultActive.value = data.Id
}
</script>
<template>
    <header class="headerNav">
        <HomeHeader @sendData="updateData"></HomeHeader>
    </header>
    <section class="section">
        <div class="section_left">
            <el-menu :default-active="defaultActive">
                <el-menu-item v-for="(item, index) in menuData" :key="item.Id" :index="item.Id"
                    @click="nameClick(item, index)" :class="{ active: menuIndex == item.Id }">
                    <span :class="{ activeName: menuIndex == item.Id }">{{ item.Name }}</span>
                </el-menu-item>
            </el-menu>
        </div>
        <div class="section_right">
            <div v-if="IsMany == 2">
                <div v-if="ArticleTitle">
                    <div>
                        <div class="Title">
                            <h1>{{ articleData.Title }}</h1>
                            <span>{{ articleData.BeginTime }} </span>
                        </div>
                        <div v-if="articleData.ImageUrl" style="margin: 10px auto;text-align: center;">
                            <img :src="articleData.ImageUrl" style="max-width:600px;max-height:480px;" />
                        </div>
                        <div class="item-txt-remark" v-html="articleData.Remark"></div>
                    </div>
                    <div class="fileDiv" v-if="articleData.Attachment">
                        附件：<span class="fileSpan" @click="fileListDownload">下载</span>
                    </div>
                </div>
                <div v-else class="emptyMsg">
                    <span> {{ msg }} </span>
                </div>
                <!-- 图片预览 -->
                <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
            </div>
            <div v-if="IsMany == 1">
                <div class="articleContent" v-if="ArticleTitle">
                    <ul>
                        <li v-for="item in articleList" :key="item.Id" @click="detailClick(item)">
                            <span class="liTitle"> {{ item.Title }}</span>
                            <span> {{ item.BeginTime.substring(0, 10) }}</span>
                        </li>
                    </ul>
                </div>
                <div v-else class="emptyMsg">
                    <span> {{ msg }} </span>
                </div>
            </div>
        </div>
    </section>
    <div style="height: 100px;"></div>
    <footer class="footer">
        <Footer @footerData="updateData"></Footer>
    </footer>
</template>

<style lang="scss" scoped>
.headerNav {
    margin-bottom: 100px;
}

.imgNav {
    width: 1200px;
    margin: 10px auto;
    // margin-top: 100px;
}

.section {
    width: 1200px;
    margin: 10px auto;
    display: flex;
    justify-content: space-between;

    .section_left {
        width: 200px;

        .el-menu-item {
            justify-content: center;
        }
    }

    .section_right {
        width: 950px;
        background-color: #fff;

        .Title {
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 1px dashed #e6e6e6;

            h1 {
                font-size: 24px;
                text-align: center;
                margin-bottom: 10px;
            }

            span {
                font-size: 14px;
                color: #333;
            }
        }

        .item-txt-remark {
            padding: 20px 50px;
        }
    }
}



:deep(.el-menu-item) {
    border-bottom: 1px solid #f2f2f2;

}

.el-menu-item.is-active {
    color: #303133;
}

.active {
    border-left: 5px solid var(--el-color-primary);
}

.activeName {
    color: var(--el-color-primary);
}

.articleContent {
    font-size: 16px;
    color: #333;

    ul {
        padding-inline-start: 0px;

        li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 30px;
            border-bottom: 1px dashed #e6e6e6;

            &:hover {
                cursor: pointer;
                color: var(--el-color-primary);
                text-decoration: underline;
                /* 鼠标悬停时添加下划线 */
            }

            .liTitle {
                max-width: 80%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

        }
    }
}

.footer {
    background-color: #545c64;
    color: #fff !important;
    padding: 10px 0;
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 99;
}

.fileDiv {
    text-align: center;
    font-size: 18px;
    padding-bottom: 20px;

    .fileSpan {
        color: #3c9cff;
        cursor: pointer;
    }
}

:deep(table) {
    border-collapse: collapse !important;
    border: 1px solid #e5e5e5 !important;
    /* 设置表格的边框为单线，颜色为黑色 */
}

:deep(th),
:deep(td) {
    border: 1px solid #e5e5e5 !important;
    /* 设置单元格的边框为单线，颜色为黑色 */
    text-align: center;
    // height: 20px;
    height: 20px;
}
</style>
