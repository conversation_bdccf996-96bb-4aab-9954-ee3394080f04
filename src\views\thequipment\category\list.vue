<script setup>
// defineOptions({
//     name: 'dangerchemicalstrainsafeeducationlist'
// });
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh, Upload, Download } from '@element-plus/icons-vue'
import { ThEquipmentCategoryGetPaged, ThEquipmentCategoryById, ThEquipmentCategoryInsertUpdate, ThEquipmentCategorySetStatuz, ThEquipmentCategoryDelById, ThEquipmentCategory, ThEquipmentCategoryUploadFile } from '@/api/thequipment.js'
import { Getdictionarycombox, AttachmentUpload, UploadPostexecl } from '@/api/user.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, tagsListStore } from "@/utils/index.js";//上传附件
import { ElMessageBox, ElMessage } from 'element-plus';
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';

const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Sort", SortType: "ASC" }] })
const categoryListData = ref([])
const schoolstageListData = ref([])

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {

    }
    HandleTableData(true);
    LoadSchoolStageData();
    // LoadCategoryListData();

})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// 加载学段集合
const LoadSchoolStageData = () => {
    Getdictionarycombox({ TypeCode: "100000" }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (rows) {
                rows.unshift({ DicValue: 0, DicName: "全部" });
            }
            schoolstageListData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// // 加载分类集合
// const LoadCategoryListData = () => {
//     ThEquipmentCategory(null).then((res) => {
//         if (res.data.flag == 1) {
//             const { rows } = res.data.data
//             categoryListData.value = rows
//         } else {
//             ElMessage.error(res.data.msg)
//         }
//     })
// }

// 列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    ThEquipmentCategoryGetPaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (isFirst) {
                filePath.value = other.filePath;
                categoryListData.value = other.CategoryList;
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData(false)
} // 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.Name = undefined;
    filters.value.CategoryName = undefined;
    filters.value.SchoolStageName = undefined;
    HandleTableData(false)
}
// 分页
const handlePage = (val) => {
    HandleTableData(false)
}

const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData(false)
}

const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)
const isAdd = ref(true)

//验证
const ruleForm = {
    CategoryName: [
        { required: true, message: '装备分类', trigger: 'change' },
    ],
    EquipmentName: [
        { required: true, message: '装备名称', trigger: 'change' },
    ],
    SchoolStageIdArr: [
        { required: true, message: '适用学段', trigger: 'change' },
    ],
    UnitName: [
        { required: true, message: '单位', trigger: 'change' },
    ]
}

//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
// 添加
const HandleAdd = (row, e) => {
    num.value = e
    dialogVisible.value = true;
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value.SchoolStageIdArr = []
    })
}
const num = ref(0)
// 修改弹出窗体
const HandleEdit = (row, e) => {
    num.value = e
    isAdd.value = false
    dialogVisible.value = true
    ThEquipmentCategoryById({ Id: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows
            if (rows.SchoolStageName) {
                dialogData.value.SchoolStageIdArr = rows.SchoolStageName.split(',');
            } else {
                dialogData.value.SchoolStageIdArr = [];
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要删除该数据信息吗?')
        .then(() => {
            ThEquipmentCategoryDelById({ Id: row.Id }).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}

// 设置状态 
const setTableDataStatuz = (e, data) => {
    console.log("----2025-08-01 17:48:06--data--:", e);
    console.log("----2025-08-01 17:48:06--data--:", data);
    ThEquipmentCategorySetStatuz({ Id: data.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交
const HandleSubmit = () => {
    dialogData.value.SchoolStageName = dialogData.value.SchoolStageIdArr.join(',');
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;

        ThEquipmentCategoryInsertUpdate(dialogData.value).then(res => {
            if (res.data.flag == 1) {
                dialogVisible.value = false
                dialogVisible.value = false
                ElMessage.success(res.data.msg || '保存成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })

    })
}

//=======================导入============================
const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
const filePath = ref('')
// 导入学生
const HandleCategoryUpload = () => {
    uploadVisible.value = true
}


const fileFile = ref()
const uploadRef = ref()
// 导入前校验
const beforeAvatarUpload = (file) => {
    fileFile.value = file
    const extension = file.name.split('.')[1] === 'xls'
    const extension2 = file.name.split('.')[1] === 'xlsx'
    if (!extension && !extension2) {
        ElMessage({
            message: '上传模板只能是 xls、xlsx格式!',
            type: 'error'
        })
    }
    return extension || extension2
}


const httpRequest = () => {
    UploadPostexecl([fileFile.value]).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            ThEquipmentCategoryUploadFile({ FilePath: rows }).then(res1 => {
                if (res1.data.flag == 1) {
                    ElMessage.success(res1.data.msg || '导入成功')
                    setTimeout(function () {
                        HandleTableData(true);
                        uploadVisible.value = false
                    }, 1000);
                } else {
                    ElMessage({
                        showClose: true,
                        message: res1.data.msg,
                        type: 'error',
                        duration: 5000
                    })
                }
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 模板下载
const HandleDownload = () => {
    fileDownload(filePath.value);
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Upload" @click="HandleCategoryUpload">导入</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.CategoryName" clearable placeholder="装备分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in categoryListData" :key="item.CategoryName"
                                :label="item.CategoryName" :value="item.CategoryName" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SchoolStageName" clearable placeholder="适用学段" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in schoolstageListData" :key="item.DicName" :label="item.DicName"
                                :value="item.DicName" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="装备名称" style="width: 180px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="CategoryName" label="装备分类" min-width="100" align="center"></el-table-column>
            <el-table-column prop="EquipmentName" label="装备名称" min-width="180"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="60" align="center"></el-table-column>
            <el-table-column prop="SchoolStageName" label="适用学段" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="2" inline-prompt active-text="启"
                        inactive-text="禁" style="--el-switch-off-color: #ff4949"
                        @change="setTableDataStatuz($event, row)" />
                </template>
            </el-table-column>
            <el-table-column prop="Memo" label="备注" min-width="180"></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="dialogData.Id > 0 ? '修改' : '添加'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px"
                    status-icon>
                    <el-form-item label="装备分类：" prop="CategoryName">
                        <el-input v-model="dialogData.CategoryName" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="装备名称" prop="EquipmentName">
                        <el-input v-model="dialogData.EquipmentName" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="单位" prop="UnitName">
                        <el-input v-model="dialogData.UnitName" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="适用学段：" prop="SchoolStageIdArr">
                        <el-select v-model="dialogData.SchoolStageIdArr" filterable multiple clearable
                            style="width: 80%;">
                            <el-option v-for="item1 in schoolstageListData" :key="item1.DicName" :label="item1.DicName"
                                :value="item1.DicName"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序">
                        <el-input v-model="dialogData.Sort" style="width: 80%" auto-complete="off"
                            @input="integerLimitInput($event, 'Sort')"></el-input>
                    </el-form-item>
                    <el-form-item label="备注:" prop="Memo">
                        <el-input type="textarea" v-model="dialogData.Memo" style="width: 80%"
                            :autosize="{ minRows: 2, maxRows: 6 }" placeholder="备注"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>

        <!-- 导入分类弹窗 -->
        <el-dialog v-model="uploadVisible" title="导入装备" width="480px" :close-on-click-modal="false">
            <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" label-width="120px" status-icon>
                <el-form-item>
                    <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                        style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload"
                        :http-request="httpRequest">
                        <el-button type="primary" :icon="UploadFilled">导入装备</el-button>
                    </el-upload>
                    <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped></style>