<script setup>
defineOptions({
    name: 'dangerchemicalsapplygranteasy'
});
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcApplyAuditListFind, DcApplyEasyGrant, DcSchoolMaterialModelGetById
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, integerLimit, ConsolidatedColumn } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const EndDate = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 30, sortModel: [{ SortCode: "BatchNo", SortType: "DESC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    Num: [
        { required: true, message: '请填写实际领用数量', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'BatchNo', label: '申领批次', },
    { value: 'UserName', label: '领用人', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const oldId = ref('')
// 发放
const HandleEdit = (row) => {
    dialogVisible.value = true
    formData.value = row
    formData.value.IsNeedSendMessage = 0
}
//发放提交
const HandleSubmit = () => {
    console.log(formData.value.IsNeedSendMessage)
    let isNeedSendMessage = 0
    if (formData.value.IsNeedSendMessage) {
        isNeedSendMessage = 1
    }
    let paraData = {
        id: formData.value.Id,
        isNeedSendMessage: isNeedSendMessage,
        num: formData.value.Num,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcApplyEasyGrant(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '发放成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.BeginDate = undefined
    filters.value.EndDate = undefined
    EndDate.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.BeginDate = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.EndDate = val + " 23:59:59"
    } else {
        filters.value.EndDate = undefined
    }
    HandleTableData()
}
//输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}

// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcApplyAuditListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 计算合并信息
const spanArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'UserName')
})
// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    // 只对第一列（UserName）进行合并
    if (columnIndex === 0) {
        const _row = spanArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDate) return false;
    return time < new Date(filters.value.BeginDate + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :span-method="objectSpanMethod"
            header-cell-class-name="headerClassName">
            <el-table-column prop="UserName" label="领用人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="BatchNo" label="申领批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ApplyNum" label="申请数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="StockNum" label="库存数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="BalanceNum" label="库存差额" min-width="120" align="right">
                <template #default="{ row }">
                    <span v-if="row.BalanceNum < 0" style="color: #F56C6C">{{ row.BalanceNum }}</span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ApplyDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.ApplyDate ? row.ApplyDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UseTime" label="使用时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.UseTime ? row.UseTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="用途" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="WithUserName" label="同领用人" min-width="110" align="center">
                <template #default="{ row }">
                    {{ row.WithUserName || '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id" type="primary" link @click="HandleEdit(row)">发放</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="危化品发放">
            <template #content>
                <el-form style="min-width: 400px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="危化品名称：">
                        <span>{{ formData.Name }}</span>
                    </el-form-item>
                    <el-form-item label="规格属性：">
                        <span>{{ formData.Model }}</span>
                    </el-form-item>
                    <el-form-item label="品牌：">
                        <span>{{ formData.Brand }}</span>
                    </el-form-item>
                    <el-form-item label="实际发放数量：" prop="Num" class="boxItem">
                        <el-input v-model="formData.Num" @input="limitInput($event, 'Num')"
                            style="width: 240px"></el-input>
                        <span style="color: #999;margin-left: 20px;"> 单位： (<span>{{ formData.UnitName }}</span>) </span>
                    </el-form-item>
                    <el-form-item>
                        <el-checkbox v-model="formData.IsNeedSendMessage" label="发送短信通知危化品发放人" size="large" />
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 发放危化品 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
:deep(.el-statistic__suffix) {
    font-size: 14px;
}

.marginLeft {
    margin-left: 10px;
}

.mobile-box {
    :deep(.el-form-item) {
        margin-bottom: 5px;
    }

    .boxItem.el-form-item {
        margin-bottom: 18px;
    }
}
</style>