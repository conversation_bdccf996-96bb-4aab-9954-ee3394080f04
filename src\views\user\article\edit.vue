<script setup>
defineOptions({
    name: 'articleedit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    Select, UploadFilled, Position
} from '@element-plus/icons-vue'
import {
    Getpagedbytype, AttachmentUpload, ArticleGetbyarticleid, ArticleAddarticle, ArticleEditarticle
} from '@/api/user.js'

import { ElMessageBox, ElMessage } from 'element-plus'
import { fileDownload, dateDay, tagsListStore } from "@/utils/index.js";
import Editor from '@/components/Editor/index.vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
const route = useRoute()
const router = useRouter()
const formData = ref({})
const refForm = ref()
const uploadFileData = ref([])
const CategoryList = ref([])
const Statuz = ref(0)//当前状态 0:待提交 1:已保存 2:已发布
const ruleForm = {
    Cid: [
        { required: true, message: '请选择分类', trigger: 'change' },
    ],
    Title: [
        { required: true, message: '请输入标题', trigger: 'change' },
    ],
    BeginTime: [
        { required: true, message: '请选择发布时间', trigger: 'change' },
    ],
}

onMounted(() => {
    // 存在参数并且由切换tag标签时刷新浏览器
    if (route.query.id) {
        formData.value.Id = route.query.id
    } else {
        formData.value.Id = 0
    }
    if ((route.query.isTagRouter)) {
        ArticleGetbyarticleidUser(route.query.id)
    }

})
onActivated(() => {
    GetpagedbytypeUser()
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    formData.value.BeginTime = dateDay(new Date())
    if (route.query.id) {
        formData.value.Id = route.query.id
    } else {
        formData.value.Id = 0
    }
    console.log('route.query.id', route.query.id)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            ArticleGetbyarticleidUser(formData.value.Id)
        }
    })
})
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 201 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const ArticleGetbyarticleidUser = (id) => {
    ArticleGetbyarticleid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            if (other) {
                CategoryList.value = other.CategoryList;//分类
            }
            if (rows) {
                formData.value.Cid = rows.Cid;//资讯分类
                formData.value.Id = rows.Id;
                formData.value.Title = rows.Title;//标题
                formData.value.ShortTitle = rows.ShortTitle;//资讯短标题
                formData.value.BeginTime = rows.BeginTime;//发布时间
                formData.value.Sort = rows.Sort;//排序
                if (uploadFileData.value.length > 0) {
                    uploadFileData.value[0].urlValue = rows.ImageUrl;//封面图片
                    uploadFileData.value[1].urlValue = rows.Attachment;//附件
                }
                formData.value.ImageUrl = rows.ImageUrl;//封面图片
                formData.value.Attachment = rows.Attachment;//附件
                formData.value.Remark = rows.Remark;//内容
                Statuz.value = rows.Statuz//当前状态 0:待提交 1:已保存 2:已发布
            } else {
                formData.value = { Sort: 0 }
                if (uploadFileData.value.length > 0) {
                    uploadFileData.value[0].urlValue = '';//封面图片
                    uploadFileData.value[1].urlValue = '';//附件
                }
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 判断必传的附件(资讯赋值)是否已传
const foundReturn = (Arr) => {
    let found = false;
    for (let i = 0; i < Arr.length; i++) {
        if (Arr[i].IsFilled == 1) {
            if (!Arr[i].urlValue) {
                ElMessage.error(`请上传【${Arr[i].Name}】`)
                found = true; // 设置标志为true
                break;
            }
        }
    }
    return found
}
//提交
const HandleSubmit = (num) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        // 判断必传的附件是否已传
        let found = foundReturn(uploadFileData.value)
        if (found) {
            return
        }
        if (num == 1) {
            ElMessageBox.confirm('确定提交保存吗?')
                .then(() => {
                    formData.value.Statuz = 0
                    if (formData.value.Id) {
                        ArticleEditarticleUser()
                    } else {
                        formData.value.Id = undefined
                        ArticleAddarticleUser()
                    }
                })
                .catch((err) => {
                    console.info(err)
                })
        } else if (num == 2) {
            ElMessageBox.confirm('确定提交发布吗?')
                .then(() => {
                    formData.value.Statuz = 2
                    if (formData.value.Id) {
                        ArticleEditarticleUser()
                    } else {
                        formData.value.Id = undefined
                        ArticleAddarticleUser()
                    }
                })
                .catch((err) => {
                    console.info(err)
                })
        }
    })
}
const editorChange = (getText, getHtml) => {
    // console.log("getText", getText, "getHtml", getHtml)
}
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            console.log("rows", rows, rows[0].Path)
            uploadFileData.value[index].urlValue = rows[0].Path
            if (index === 0) {
                formData.value.ImageUrl = rows[0].Path;//封面图片
            } else if (index === 1) {
                formData.value.Attachment = rows[0].Path;//附件
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选用方案添加保存
const ArticleAddarticleUser = (row) => {
    ArticleAddarticle(formData.value).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            ElMessage.success(res.data.msg || '保存成功')
            router.push({ path: "./list" })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选用方案修改保存
const ArticleEditarticleUser = (row) => {
    ArticleEditarticle(formData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            router.push({ path: "./list" })

            let tagsList = userStore.tagsList
            tagsList = tagsList.filter(t => t.path != route.path)
            userStore.setTagsList(tagsList)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    console.log(e)
    // lastIndexOf
    let path = e.urlValue
    let index = path.lastIndexOf(".")
    let Ext = path.substring(index)
    viewPhotoList.value = [path]
    if (Ext == ".png" || Ext == ".jpg" || Ext == ".jpeg") {
        showViewer.value = true;
    } else {
        let title = e.Name + Ext
        fileDownload(path, title)
    }
}
</script>
<template>
    <el-form style="width: 900px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
        label-width="180px" status-icon>
        <el-form-item label="资讯分类：" prop="Cid">
            <el-select v-model="formData.Cid" placeholder="请选择分类">
                <el-option v-for="item in CategoryList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="标题：" prop="Title">
            <el-input v-model="formData.Title" auto-complete="off" placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item label="资讯短标题：" v-if="hUnitType == 0">
            <el-input v-model="formData.ShortTitle" auto-complete="off" placeholder="请输入资讯短标题"></el-input>
        </el-form-item>
        <el-form-item label="发布时间：" prop="BeginTime">
            <el-date-picker type="datetime" placeholder="选择日期" v-model="formData.BeginTime" format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm" style="width: 100%;"></el-date-picker>
        </el-form-item>
        <el-form-item label="排序：">
            <el-input type="number" v-model="formData.Sort"></el-input>
        </el-form-item>
        <el-form-item v-for="(item, index) in uploadFileData" :key="item.FileCategory">
            <template #label>
                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                {{ item.Name }}：
            </template>
            <div style="display: flex;align-items: center;width: 100%;">
                <el-input v-model="item.urlValue" auto-complete="off" disabled
                    :placeholder="`请上传${item.Name}`"></el-input>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                    :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                    :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                    <el-button type="success" :icon="UploadFilled" style="margin: 0 10px;">上传</el-button>
                </el-upload>
                <el-button :disabled="!item.urlValue" type="primary"
                    @click="fileListDownload(item, item.FileList)">查看</el-button>
            </div>
        </el-form-item>
        <!-- 富文本 -->
        <el-form-item label="内容：">
            <Editor v-model:get-html="formData.Remark" :defaultHtml="formData.Remark" @editorChange="editorChange">
            </Editor>
        </el-form-item>
        <el-form-item>
            <el-button v-if="Statuz != 2" type="primary" :icon="Select" @click="HandleSubmit(1)">保存</el-button>
            <el-button type="primary" :icon="Position" @click="HandleSubmit(2)">发布</el-button>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
.editorBox {
    padding: 40px;
    width: 700px;
    background-color: #fff;
    margin: 50px auto auto;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .04), 0 4px 10px rgba(0, 0, 0, .08);
}

:deep(.el-input-group__append) {
    background-color: #ecf5ff;

    button {
        border-color: #409eff;
        color: #409eff;
    }

}

:deep(.el-input-group__append:hover) {
    background-color: #409eff;
    color: #fff;
}
</style>