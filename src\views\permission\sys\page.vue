<script setup>
import { onMounted, ref, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Syspagefind,
  Syspageinsertupdate,
  Syspagegetbyid,
  Syspagedelbatch
} from '@/api/user.js'


// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const pageData = ref([])
const currentRow = ref({})
const selectRows = ref([])
const value = ref([])
const filtersKey = ref({ key: '', value: 'Url' })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "SubOrder", SortType: "ASC" }] })
const options = ref([
  { value: 'Url', label: '路径', },
  { value: 'Name', label: '页面名称', },
  { value: 'Code', label: '页面编码', }
])




const modules = ref([])
const menuTrees = ref([])
const HandleSelectChange = (selection) => {
  selectRows.value = selection
}
const HandleClickRow = (val) => {
  currentRow.value = val
}
const HandleClearTable = () => {
  //当前表格数据
  // tableData.value = []
  //统计总数(不要手动重置 否则会出现翻页问题 找了好半天)
  // tableTotal.value = 0
  //当前选中行
  currentRow.value = null
  //当前选择多行
  selectRows.value = []
}
// 翻页
watch(() => filters.value.pageIndex, () => {
  HandleSearch()
})
watch(() => filters.value.pageSize, () => {
  filters.value.pageIndex = 1
  HandleSearch()
})

//加载数据
onMounted(() => {
  HandleSearch()
  SyspagefindUser()
})

//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
  Name: [
    { required: true, message: '菜单名称不能为空', trigger: 'change' },
  ],

  Url: [
    { required: true, message: '菜单路径不能为空', trigger: 'change' },
  ]
}

//新增
const HandleAdd = () => {
  formData.value = { PageType: 0, IsShow: 1 }
  dialogVisible.value = true
}
//编辑
const HandleEdit = (row) => {

  dialogVisible.value = true
  formData.value.Id = row.Id
  Syspagegetbyid({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      formData.value.Pid = rows.Pid
      formData.value.Name = rows.Name
      formData.value.Url = rows.Url
      formData.value.SubOrder = rows.SubOrder
      formData.value.Code = rows.Code
      formData.value.PageType = Number(rows.PageType)
      formData.value.IsShow = Number(rows.IsShow)
      formData.value.SmallIcon = rows.SmallIcon
      formData.value.Memo = rows.Memo
    } else {
      ElMessage.error(res.data.msg)
    }

  })
}
//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      Syspagedelbatch({ ids: row.Id }).then((res) => {
        HandleSearch()
        ElMessage.success('删除成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })



}
//批量删除
const HandleBatchDel = (row) => {
  if (!row || row.length == 0) {
    ElMessage.error('请选择要操作的数据!')
    return;
  }
  ElMessageBox.confirm(`确认要删除[${row.map(t => t.Id).join(',')}]吗?`)
    .then(() => {
      let ids = row.map(t => t.Id).join(',')
      Syspagedelbatch({ ids: ids }).then((res) => {
        HandleSearch()
        ElMessage.success('删除成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })

}
//提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    if (formData.value.Id) {
      //编辑
      Syspageinsertupdate(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '修改成功')
      })
    } else {
      //新增
      Syspageinsertupdate(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '添加成功')
      })
    }
  })
}

//搜索
const HandleSearch = (page) => {

  if (page) filters.value.pageIndex = page
  filters.value.Url = undefined;
  filters.value.Name = undefined;
  filters.value.Code = undefined;
  // 按类型搜索
  if (filtersKey.value.key) {
    filters.value[filtersKey.value.value] = filtersKey.value.key;
  } else {
    filters.value[filtersKey.value.value] = undefined;

  }
  HandleClearTable()

  Syspagefind(filters.value).then(res => {
    if (res.data.flag == 1) {
      tableData.value = res.data.data.rows;
      tableTotal.value = Number(res.data.data.total);
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 获取一级、二级菜单
const SyspagefindUser = () => {
  let pageFormData = { pageIndex: 1, pageSize: 10000, Depathz: "0,1", sortModel: [{ SortCode: "SubOrder", SortType: "ASC" }] }
  Syspagefind(pageFormData).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data

      // 重构一级二级菜单
      // 一级菜单
      let arr = rows.filter(t => !t._parentId)
      arr.unshift({ Name: "顶级菜单", Id: "0", Depth: -1 });
      // 更新arr并返回新数组
      function mergeArrays(arr1, arr2) {
        return arr1.map(item => ({
          ...item,
          children: arr2.filter(child => child._parentId === item.Id).map(child => ({ ...child }))
        }));
      }
      // 使用函数更新数组
      pageData.value = mergeArrays(arr, rows);

    } else {
      ElMessage.error(res.data.msg)
    }
  });
}



// 重置
const HandleReset = (page) => {
  filtersKey.value.key = ''
  HandleSearch(page)
}

</script>
<template>
  <!-- 搜索 -->
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
        <el-form-item class="flexItem">
          <el-button type="primary" plain @click="HandleAdd">添加</el-button>
          <el-button type="danger" plain @click="HandleBatchDel(selectRows)">批量删除</el-button>
        </el-form-item>
        <div class="verticalIdel"></div>
        <el-form-item label="" class="flexItem" label-width="60">
          <el-input v-model.trim="filtersKey.key" placeholder="请输入搜索关键词" style="width: 240px" class="input-with-select">
            <template #prepend>
              <el-select v-model="filtersKey.value" style="width: 80px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" plain @click="HandleSearch(1)">查询</el-button>
          <el-button type="primary" plain @click="HandleReset(1)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
  <!-- 内容 -->
  <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange"
    @row-click="HandleClickRow" border stripe row-key="Id">
    <el-table-column type="selection" width="50"></el-table-column>
    <el-table-column prop="Id" label="编号" min-width="60" align="center"></el-table-column>
    <el-table-column prop="Name" label="名称" min-width="140" align="center"></el-table-column>
    <el-table-column prop="Url" label="路径" min-width="300" align="center"></el-table-column>
    <el-table-column prop="SubOrder" label="排序" min-width="100" align="center"></el-table-column>
    <el-table-column prop="SmallIcon" label="图标" min-width="120" align="center"></el-table-column>
    <el-table-column prop="IsShow" label="是否显示" min-width="60" align="center">
      <template #default="{ row }">
        <el-tag :type="row.IsShow ? 'success' : 'danger'" disable-transitions>{{ !row.IsShow ? "否" : "是" }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="Memo" label="备注" min-width="180" show-overflow-tooltip align="center"> </el-table-column>
    <el-table-column prop="opt" label="操作" min-width="120" align="center">
      <template #default="{ row }">
        <el-button type="primary" link @click="HandleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>

  <!-- 分页 -->
  <el-row>
    <el-col class="flexBox">
      <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
        :total="tableTotal" v-model:current-page="filters.pageIndex" v-model:page-size="filters.pageSize" />
    </el-col>
  </el-row>
  <!-- 弹窗 -->
  <el-dialog v-model="dialogVisible" :title="formData.Id ? '修改页面' : '添加页面'" width="750px">

    <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="120px" status-icon>
      <el-form-item label="所属上级:">

        <el-cascader placeholder="请选择，支持搜索功能" v-model="formData.Pid" :options="pageData" filterable
          :props="{ checkStrictly: true, expandTrigger: 'hover', value: 'Id', label: 'Name' }"></el-cascader>
      </el-form-item>
      <el-form-item label="菜单名称" class="flexItem" prop="Name">
        <el-input v-model="formData.Name" auto-complete="off" placeholder="中文，最好不超过6个字"></el-input>
      </el-form-item>
      <el-form-item label="菜单路径:" prop="Url">
        <el-input v-model="formData.Url" auto-complete="off" placeholder='相对于根目录的路径，路径中斜杠使用"/"，但开头不需要斜杠"/"'></el-input>
      </el-form-item>
      <el-form-item label="排序:" prop="SubOrder">
        <el-input v-model="formData.SubOrder" auto-complete="off" placeholder="只能输入数字，排序顺序由小到大"></el-input>
      </el-form-item>
      <el-form-item label="页面编码:">
        <el-input v-model="formData.Code" auto-complete="off" placeholder="	四位编码"></el-input>
      </el-form-item>
      <el-form-item label="页面类型:">
        <el-radio-group v-model="formData.PageType">
          <el-radio class="radio" :value="0">共用</el-radio>
          <el-radio class="radio" :value="1">管理员专用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否显示:">
        <el-radio-group v-model="formData.IsShow">
          <el-radio class="radio" :value="1">是</el-radio>
          <el-radio class="radio" :value="0">否</el-radio>
        </el-radio-group>

      </el-form-item>
      <el-form-item label="小图标:">
        <el-input v-model="formData.SmallIcon" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="描述:">
        <el-input type="textarea" v-model="formData.Memo"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}

.taskNameConent {
  width: 100%;
  /* 具体宽度，例如 200px 或 100% */
  ;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>