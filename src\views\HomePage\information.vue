<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import {
    ArticleGetinformation, ArticleGetinformationbycid
} from '@/api/home.js'
import { ElMessage } from 'element-plus'
import {
    Expand, MoreFilled
} from '@element-plus/icons-vue'
import HomeHeader from '@/views/HomePage/HomeHeader.vue';
import Footer from '@/views/HomePage/homefooter.vue';
import router from '@/router'
const activeName = ref('0')
const tabId = ref('')
const tabList = ref([])
const informationListt = ref([])
const articleList = ref([])
const ArticleTitle = ref(true)
const msg = ref('')
//加载数据
onMounted(() => {
    document.documentElement.style.setProperty('--body-height', 'auto');
    ArticleGetinformationUser()
})
onUnmounted(() => {
    // 移除样式
    document.documentElement.style.setProperty('--body-height', '100%');
});
const handleClick = (tab, e) => {
    console.log(tab.paneName, e)
    tabId.value = tab.paneName
    if (tab.paneName == '0') {
        ArticleGetinformationUser()
    } else {
        ArticleGetinformationbycidUser(tab.paneName)
    }
}
// 查看资讯详情
const detailClick = (item) => {
    const { href } = router.resolve({
        path: "/articledetail",
        query: { Id: item.Id }
    });
    window.open(href, "_blank");
}
// 查看资讯列表
const listClick = (item) => {
    router.push({ path: '/articlelist', query: { Id: item.Id, IsMany: item.IsMany } })
}
// 查看更多
const moreFilled = (item) => {
    router.push('/articlelist')
}
//获取资讯分类及资讯首页资讯
const ArticleGetinformationUser = () => {
    ArticleGetinformation().then(res => {
        // console.log("资讯列表", res)
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tabList.value = other || []
            if (other && other.length > 4) {
                tabList.value = other.slice(0, 4)
            }
            informationListt.value = rows || []

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
//根据资讯分类Id查询资讯信息  
const ArticleGetinformationbycidUser = (cid) => {
    ArticleGetinformationbycid({ cid: cid }).then(res => {
        // console.log("资讯列表", res)
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            articleList.value = rows || []
            if (rows && rows.length > 0) {
                ArticleTitle.value = true
                msg.value = ''
            } else {
                ArticleTitle.value = false
                msg.value = '暂无资讯'
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
</script>
<template>
    <header class="headerNav">
        <HomeHeader></HomeHeader>
    </header>
    <section class="section" style="position: relative;">
        <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="资讯首页" name="0">
                <div class="flex-warp" v-if="informationListt.length > 0">
                    <el-row :gutter="10">
                        <el-col :span="6" v-for="(v, k) in informationListt" :key="k" @click="detailClick(v)"
                            style="margin-bottom: 20px;">
                            <div class="flex-warp-item">
                                <div class="flex-warp-item-box">
                                    <div class="item-img">
                                        <img :src="v.ImageUrl" />
                                    </div>
                                    <div class="item-txt">
                                        <div class="item-txt-title">{{ v.Title }}</div>
                                        <div class="item-txt-time">{{ v.BeginTime.substring(0, 10) }} </div>
                                        <div class="item-txt-remark">{{ v.Remark }} </div>
                                    </div>
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div v-else class="emptyMsg">
                    <span> {{ msg }} </span>
                </div>
            </el-tab-pane>
            <el-tab-pane v-for="item in tabList" :key="item.Id" :name="item.Id">
                <template #label>
                    <span class="custom-tabs-label">
                        <span>{{ item.Name }}</span>
                        <el-icon style="margin-left: 3px;" v-if="tabId == item.Id" @click="listClick(item)">
                            <Expand />
                        </el-icon>
                    </span>
                </template>
                <div class="articleContent" v-if="ArticleTitle">
                    <ul>
                        <li v-for="item in articleList" :key="item.Id" @click="detailClick(item)">
                            <span class="liTitle"> {{ item.Title }}</span>
                            <span> {{ item.BeginTime.substring(0, 10) }}</span>
                        </li>
                    </ul>
                </div>
                <div v-else class="emptyMsg">
                    <span> {{ msg }} </span>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div style="position: absolute;top: 5px;right: 0;cursor: pointer;" @click="moreFilled">
            <el-icon color="#409eff" size="28">
                <MoreFilled />
            </el-icon>
        </div>

    </section>
    <footer class="footer">
        <Footer></Footer>
    </footer>
</template>
<style lang="scss" scoped>
.headerNav {
    margin-bottom: 100px;
}

.imgNav {
    width: 1200px;
    margin: 10px auto;
}

.section {
    width: 1200px;
    margin: 10px auto;
    margin-bottom: 80px;
}

.footer {
    background-color: #545c64;
    color: #fff !important;
    padding: 10px 0;
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 99;
}

.flex-warp {
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;

    .el-row {
        width: 100%;
        margin-left: 0 !important;
        margin-top: 5px;
    }

    .flex-warp-item {

        height: 320px;
        background-color: #f9f9f9;
        padding: 0;
        border: 1px solid #CDD0D6;

        .flex-warp-item-box {
            border: 1px solid var(--next-border-color-light);
            width: 100%;
            height: 100%;
            border-radius: 2px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;

            &:hover {
                cursor: pointer;
                border: 1px solid var(--el-color-primary);
                transition: all 0.3s ease;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);

                .item-txt-title {
                    color: var(--el-color-primary) !important;
                    transition: all 0.3s ease;
                }

                .item-img {
                    img {
                        transition: all 0.3s ease;
                        transform: translateZ(0) scale(1.05);
                    }
                }
            }

            .item-img {
                width: 100%;
                height: 215px;
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    transition: all 0.3s ease;
                    max-width: 100%;
                    max-height: 100%;
                }
            }

            .item-txt {
                flex: 1;
                padding: 10px;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                .item-txt-title {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    display: -webkit-box;
                    transition: all 0.3s ease;
                    font-size: 14px;
                    color: #000;

                    &:hover {
                        color: var(--el-color-primary);
                        text-decoration: underline;
                        transition: all 0.3s ease;
                    }
                }

                .item-txt-time {
                    color: #666;
                    padding-top: 5px;
                    padding-bottom: 8px;
                    font-size: 12px;
                }

                .item-txt-remark {
                    font-size: 12px;
                    color: #333;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}

.demo-tabs>.el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
}

:deep(.el-tabs__item) {
    padding: 0px 50px !important;
}

.articleContent {
    font-size: 16px;
    color: #333;

    ul {
        padding-inline-start: 0px;

        li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 30px;
            border-bottom: 1px dashed #e6e6e6;

            &:hover {
                cursor: pointer;
                color: var(--el-color-primary);
                text-decoration: underline;
            }

            .liTitle {
                max-width: 80%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
