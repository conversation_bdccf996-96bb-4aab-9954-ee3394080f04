<script setup>
import { onMounted, ref } from 'vue'
import {
    Getpaged, XuniformEdit, XuniformGetbyid
} from '@/api/user.js'
import { ElMessage } from 'element-plus'
const TargetTableData = ref([])
const dialogVisible = ref(false)
const dialogData = ref({ Code: "3100" })
const refForm = ref()
const ruleForm = {
    Title: [
        { required: true, message: '请输入指标名称', trigger: 'change' },
    ],
    Explanation: [
        { required: true, message: '请输入指标说明', trigger: 'change' },
    ],
}
//加载数据
onMounted(() => {
    TargetGetpagedUser()
})
// 修改
const HandleEdit = (row) => {
    XuniformGetbyidUser(row.Id)
    dialogVisible.value = true
}
// 确认 提交指标信息
const HandleSubmit = (id) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        XuniformEditUser()
    })
}
// 修改
const XuniformEditUser = () => {
    XuniformEdit(dialogData.value).then((res) => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            TargetGetpagedUser()
            ElMessage.success(res.data.msg || '修改成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const XuniformGetbyidUser = (id) => {
    XuniformGetbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // console.log("详情", rows)
            dialogData.value.Id = rows.Id
            dialogData.value.Title = rows.Title
            dialogData.value.Explanation = rows.Explanation
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 指标名称和分值
const TargetGetpagedUser = () => {
    Getpaged({ code: 3100 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            TargetTableData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <div style="min-width: 800px;max-width: 1200px;">
            <el-table :data="TargetTableData" highlight-current-row border stripe
                header-cell-class-name="headerClassName">
                <el-table-column prop="Title" label="指标名称" min-width="160"></el-table-column>
                <el-table-column prop="Explanation" label="指标说明" min-width="300"></el-table-column>
                <el-table-column fixed="right" label="操作" min-width="120" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-dialog v-model="dialogVisible" title="修改指标信息" width="600px" draggable>
                <div class="dialog-content">
                    <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="100px"
                        status-icon>
                        <el-form-item label="指标名称：" prop="Title">
                            <el-input v-model="dialogData.Title" placeholder="指标名称"></el-input>
                        </el-form-item>
                        <el-form-item label="指标说明：" prop="Explanation">
                            <el-input type="textarea" v-model="dialogData.Explanation"
                                :autosize="{ minRows: 2, maxRows: 6 }" placeholder="指标说明"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    </div>
</template>
<style lang="scss" scoped></style>