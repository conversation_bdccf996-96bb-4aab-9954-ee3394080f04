<script setup>
import { onMounted, ref, onUnmounted } from 'vue'
import {
    Getpurchasemethod
} from '@/api/selection.js'
import {
    ListGetbyid, ShelfGetbyid
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import HomeHeader from '@/views/HomePage/HomeHeader.vue';
import Footer from '@/views/HomePage/footer.vue';
import { useRoute, useRouter } from 'vue-router'
import UseImage from '@/components/UseImage/index.vue'
// 初始化
const router = useRouter()
const route = useRoute()
const checkList = ref([])
const id = ref('')
// 103001:校服主图  103002：款式图  103003：尺码表  103004：详情图
const imageList = ref([])
const imageDetailList = ref([])
const descripData = ref({})
//加载数据
onMounted(() => {
    document.documentElement.style.setProperty('--body-background-color', '#f2f2f2');

    id.value = route.query.id
    if (route.query.num == 1) {
        GetpurchasemethodUser()
    } else {
        if (route.query.requestNum == 1) {
            ListGetbyidUser(route.query.id)
        } else {
            ShelfGetbyidUser(route.query.id)
        }
    }
})
onUnmounted(() => {
    // 移除样式
    document.documentElement.style.setProperty('--body-background-color', '#ffffff');
});
const GetpurchasemethodUser = () => {
    Getpurchasemethod().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            console.log("校服采购方式", rows)
            checkList.value = rows

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 已提交之后的- 校服预览详情 
const ShelfGetbyidUser = (id) => {
    ShelfGetbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            descripData.value = rows;
            let list = other.AttachmentList || [];
            imageList.value = list.filter(t => t.FileCategory == 103002)
            imageDetailList.value = list.filter(t => t.FileCategory == 103004)

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 校服录入- 校服预览详情 
const ListGetbyidUser = (id) => {
    ListGetbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            descripData.value = rows;
            let list = other.AttachmentList || [];
            imageList.value = list.filter(t => t.FileCategory == 103002)
            imageDetailList.value = list.filter(t => t.FileCategory == 103004)

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>

    <header class="headerNav">
        <HomeHeader></HomeHeader>
    </header>
    <section class="section">
        <div class="previewMaintainPC" >
            <div class="header">
                <div class="headerLeft" v-if="imageList.length > 0">
                    <UseImage :imageAllList="imageList"></UseImage>
                </div>

                <div class="headerRight">
                    <el-descriptions :column="1">
                        <el-descriptions-item label="适用学校：">{{ descripData.SchoolName }}</el-descriptions-item>
                        <el-descriptions-item label="种类：">{{ descripData.Uniformtype }}</el-descriptions-item>
                        <el-descriptions-item label="品名：">{{ descripData.Name }}</el-descriptions-item>
                        <el-descriptions-item label="品牌：">{{ descripData.Brand }}</el-descriptions-item>
                        <el-descriptions-item label="具体参数：">
                            <span class="Parameter">{{ descripData.Parameter }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item v-if="descripData.IsShowPrice != 0" label="单价（元）：">
                            {{ descripData.Price }}</el-descriptions-item>
                        <el-descriptions-item label="适合性别：">
                            {{ descripData.Sex == 1 ? '男' : descripData.Sex == 2 ? '女' : descripData.Sex == 3 ? '男/女' : '未知' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="生产厂商：">{{ descripData.Producer }}</el-descriptions-item>
                        <el-descriptions-item label="产地：">{{ descripData.OriginAddress }}</el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>
            <div class="contentNav">
                <span>商品详情</span>
            </div>
            <div class="content">
                <div class="img">
                    <img v-for="(img, index) in imageDetailList" :key="img.Id" :src="img.Path" alt="">
                    <div style="height: 80px;"></div>
                </div>
            </div>

        </div>

    </section>

    <footer class="footer">
        <Footer></Footer>
    </footer>
</template>
<style lang="scss" scoped>
.headerNav {
    margin-bottom: 50px;
}

.section {
    padding: 20px;
    margin-bottom: 80px;
}



.previewMaintainPC {
    width: 900px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px 10px;

    .header {
        display: flex;

        .headerRight {
            height: 340px;
            padding: 30px 0;
        }

        :deep(.el-descriptions__cell) {
            padding: 8px 30px 5px 80px;
        }

        :deep(.el-descriptions__label) {
            display: inline-block;
            width: 100px;
            vertical-align: top;
        }

        :deep(.el-descriptions__content) {
            display: inline-block;
            width: 260px;
            color: #66ccff;
        }

    }

    .contentNav {
        background-color: #f7f7f7;
        border: 1px solid #eee;
        border-bottom: 1px solid #e4393c;
        margin: 10px 0;

        span {
            display: inline-block;
            background-color: #e4393c;
            color: #fff;
            cursor: default;
            padding: 10px 25px;
            font-size: 14px;
            font-family: "microsoft yahei";
        }
    }

    .content {
        .img {
            width: 900px;
            height: 900px;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}


.footer {
    background-color: #545c64;
    color: #fff !important;
    padding: 10px 0;
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 99;
}

.Parameter {

    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
