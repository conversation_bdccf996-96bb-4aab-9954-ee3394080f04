<script setup>
defineOptions({
    name: 'userdepartmentusermanager'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Back, DocumentChecked
} from '@element-plus/icons-vue'
import {
    UserInDepartListFind, UserInDepartBatchDel, GetAllDepartment, DepartmentUserUpdate
} from '@/api/user.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { limit, integerLimit, tagsListStore, formatDate } from '@/utils/index.js';
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const managerList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const selectRows = ref([])//选中的行  
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    DepartmentId: [
        { required: true, message: '请选择部门', trigger: 'change' },
    ]
}
const options = ref([
    { value: 'UserName', label: '姓名', },
    { value: 'UserAccount', label: '登录账号', },
])
const filtersKey = ref('')
const filtersValue = ref('UserName')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        GetAllDepartmentUser()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            GetAllDepartmentUser()
        }
    })
})
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
const ids = ref('')
// 批量修改
const HandleAllEdit = (e) => {
    ids.value = selectRows.value.map(item => item.UserId).join(',')
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {}
    })
}
// 部门管理 修改 
const HandleEdit = (row) => {
    ids.value = row.UserId
    dialogVisible.value = true
}

//修改 提交
const HandleSubmit = (id, DepartId) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        let formatDate = {
            id: ids.value,
            newDepartId: dialogData.value.DepartmentId,
            odDepartId: route.query.id,
        }
        DepartmentUserUpdate(formatDate).then(res => {
            if (res.data.flag == 1) {
                dialogVisible.value = false
                HandleTableData()
                ElMessage.success(res.data.msg || '操作成功')
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

// 删除 
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要移出此用户吗?',
        '移出确认', { type: 'warning', })
        .then(() => {
            UserInDepartBatchDel({ ids: row.Id, departId: route.query.id }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}

//列表
const HandleTableData = () => {
    filters.value.UserName = undefined;
    filters.value.UserAccount = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    filters.value.DepartmentId = route.query.id
    UserInDepartListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
//获取部门
const GetAllDepartmentUser = () => {
    GetAllDepartment(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            managerList.value = rows;
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    HandleTableData()

}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: './manager' })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-form-item class="flexItem">
                            <el-button :icon="Back" @click="HandleBack">返回</el-button>
                        </el-form-item>
                        <div class="verticalIdel"></div>
                        <el-form-item class="flexItem">
                            <el-button type="success" :icon="FolderAdd" @click="HandleAllEdit">批量修改</el-button>
                        </el-form-item>
                        <div class="verticalIdel"></div>
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="font-size: 14px;color: #606266;margin-bottom: 10px;" v-if="tableData.length > 0">部门名称：
            <span style="color: #999;"> {{ route.query.Name }} </span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="UserName" label="姓名" min-width="160"></el-table-column>
            <el-table-column prop="UserAccount" label="登录账号" min-width="160"></el-table-column>
            <el-table-column prop="Sex" label="性别" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Sex == 0 ? '女' : '男' }}
                </template>
            </el-table-column>
            <el-table-column prop="Mobile" label="手机号码" min-width="160" align="center"></el-table-column>
            <el-table-column label="备注" min-width="160">
                <template #default="{ row }">
                    <span style="color: #F56C6C;"> {{ row.Statuz == 0 ? '该账号已经禁用' : '' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="部门管理" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <!-- 新增、修改部门 -->
        <app-box v-model="dialogVisible" :width="480" :lazy="true" :title="dialogData.Id == '0' ? '添加部门信息' : '修改部门信息'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="100px"
                    status-icon>
                    <el-form-item label="部门名称：" prop="Name">
                        <el-select v-model="dialogData.DepartmentId">
                            <el-option v-for="item in managerList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>