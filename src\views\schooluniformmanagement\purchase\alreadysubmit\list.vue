<script setup>
defineOptions({
    name: 'alreadysubmitlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Sort, QuestionFilled
} from '@element-plus/icons-vue'
import {
    <PERSON><PERSON>Getpaged, <PERSON><PERSON>D<PERSON><PERSON>by<PERSON>, She<PERSON><PERSON>ets<PERSON>byid, ShelfSetstatuzbyid
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const selectRows = ref([])
const SchoolList = ref([])
const StatuzUseList = ref([])
const filters = ref({ pageIndex: 1, pageSize: 10 })
const dialogVisible = ref(false)
const dialogTableData = ref([])
const IsShowContractEnd = ref()
const dialogVisibleExplain = ref(false)
const AuditExplain = ref('')

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
const HandleExplain = (row) => {
    AuditExplain.value = row.AuditExplain
    dialogVisibleExplain.value = true

}
// 查看
const HandleDetail = (row) => {
    // 电脑预览
    const { href } = router.resolve({
        path: "/preview",
        query: { id: row.Id, requestNum: 2 }
    });
    window.open(href, "_blank");
}
// 调整排序
const HandleAudit = () => {
    let isPurchaseNo = selectRows.value.find(t => !t.PurchaseNo)
    // 判断在selectRows.value存在不同的SchoolId或不相同的purchaseNo
    let isSchoolId = selectRows.value.find(t => t.SchoolId !== selectRows.value[0].SchoolId || t.PurchaseNo !== selectRows.value[0].PurchaseNo)
    if (isPurchaseNo) {
        ElMessage.error('有合同批次后才能排序')
        return
    }
    if (isSchoolId) {
        ElMessage.error('只有同一学校且同一合同批次的才能一起调整排序')
        return
    }
    dialogVisible.value = true
    dialogTableData.value = selectRows.value
}

// 搜索多选框
const HandleCheckboxChange = (val) => {
    if (val) {
        IsShowContractEnd.value = 1
    } else {
        IsShowContractEnd.value = undefined
    }
    HandleTableData()
}
// 修改状态
const HandleSwitchChange = (e, row) => {

    ShelfSetstatuzbyid({ id: row.Id, statuz: e }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '状态修改成功成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 起止日期
const HandleDateSearch = (date) => {
    // console.log("开始日期date", date)
    if (date) {
        filters.value.AuditTimeStart = date[0]
        filters.value.AuditTimeEnd = date[1]
    } else {
        filters.value.data = undefined
        filters.value.AuditTimeStart = undefined
        filters.value.AuditTimeEnd = undefined
    }
    HandleTableData()
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定删除吗?')
        .then(() => {
            ShelfDeletebyid({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
//列表
const HandleTableData = (isFirst) => {
    let formData = {
        pageIndex: filters.value.pageIndex,
        pageSize: filters.value.pageSize,
        AuditStatuz: filters.value.AuditStatuz,
        SchoolId: filters.value.SchoolId,
        AuditTimeStart: filters.value.AuditTimeStart,
        AuditTimeEnd: filters.value.AuditTimeEnd,
        IsShowContractEnd: IsShowContractEnd.value,
        Name: filters.value.Name,
    }
    if (isFirst) {
        formData.isFirst = true
    } else {
        formData.isFirst = undefined
    }

    ShelfGetpaged(formData).then(res => {
        if (res.data.flag == 1) {
            // console.log("采购管理列表", res.data.data)
            const { rows, total, other } = res.data.data
            tableData.value = rows || [];
            tableTotal.value = Number(total)
            if (isFirst) {
                StatuzUseList.value = other.StatuzUseList || [];//使用状态
                SchoolList.value = other.SchoolList || []//学校名称
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()

}
// 重置
const HandleReset = (page) => {
    filters.value.pageIndex = 1
    filters.value.AuditStatuz = undefined
    filters.value.SchoolId = undefined
    filters.value.date = undefined
    filters.value.AuditTimeStart = undefined
    filters.value.AuditTimeEnd = undefined
    IsShowContractEnd.value = undefined
    filters.value.Name = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
const focusedIndex = ref(-1); // 用来存储当前聚焦的输入框的行索引  

// 表格输入框聚焦事件
const handleFocus = (index, isFocused) => {
    focusedIndex.value = isFocused ? index : -1;
}
// 表格输入框赋值  ：排序
const handleInput = (index, value, id) => {
    tableData.value[index].inputValue = value;
    ShelfSetsortbyid({ id: id, sort: value }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '排序修改成功')
            HandleTableData()

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Sort" :disabled="selectRows.length == 0"
                            @click="HandleAudit">调整排序</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="审核时间：" class="flexItem">
                        <el-date-picker v-model="filters.date" type="daterange" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable range-separator="至" start-placeholder="起始日期"
                            end-placeholder="截止日期" style="width: 260px" @change="HandleDateSearch" />
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="适用学校"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.AuditStatuz" clearable placeholder="审核状态" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in StatuzUseList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="合同批次/种类/品名"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem" style="margin-left: 10px;margin-right: 10px !important;">
                        <el-checkbox v-model="filters.IsShowContractEnd" label="不显示合同终止的" size="large"
                            @change="HandleCheckboxChange" />
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" border
            stripe header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="SchoolName" label="适用学校" min-width="160"></el-table-column>
            <el-table-column prop="PurchaseNo" label="合同批次" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.PurchaseNo ? row.PurchaseNo : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Uniformtype" label="种类" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Name" label="品名" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Price" label="单价（元）" min-width="100" align="right"></el-table-column>
            <el-table-column prop="Sex" label="适合性别" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : row.Sex == 3 ? '男/女' : '未知' }}
                </template>
            </el-table-column>
            <el-table-column prop="StandardNum" label="标配数量" min-width="100" align="center"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
            <el-table-column prop="ContractEndDate" label="合同终止时间" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="!row.ContractEndDate">--</span>
                    <span v-else-if="new Date(row.ContractEndDate).setHours(23, 59, 59, 0) < new Date()"
                        style="color: #F56C6C;">
                        {{ row.ContractEndDate.substring(0, 10) }}</span>
                    <span v-else>{{ row.ContractEndDate.substring(0, 10) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="审核状态" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.AuditStatuz == 11" style="color: #F56C6C;cursor: pointer;"
                        @click="HandleExplain(row)">审核退回
                        <el-icon color="#F56C6C">
                            <QuestionFilled />
                        </el-icon>
                    </span>
                    <span v-else> {{ row.AuditStatuz == 10 ? '待审核' : '审核通过' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="AuditTime" label="审核时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.AuditTime ? row.AuditTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Sort" label="排序" min-width="80" align="center"></el-table-column>
            <el-table-column label="可征订状态" min-width="100" align="center">
                <template #default="{ row }">

                    <el-switch v-if="row.AuditStatuz == 20" v-model="row.UseStatuz" :active-value="1"
                        :inactive-value="2" style="--el-switch-off-color: #ff4949" inline-prompt active-text="启"
                        inactive-text="禁" @change="HandleSwitchChange($event, row)" />
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                    <el-button v-if="row.AuditStatuz == 11" type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>

            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <el-dialog v-model="dialogVisible" draggable title="调整排序" width="900px">
            <div class="dialog-content">
                <el-table :data="dialogTableData" highlight-current-row border stripe
                    header-cell-class-name="headerClassName">
                    <el-table-column prop="SchoolName" label="适用学校" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
                    <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"></el-table-column>
                    <el-table-column prop="Name" label="品名" min-width="120" align="center"></el-table-column>
                    <el-table-column label="排序" min-width="80" align="center">
                        <template #default="scope">
                            <el-input v-model="scope.row.Sort" type="number" @focus="handleFocus(scope.$index, true)"
                                @blur="handleInput(scope.$index, scope.row.Sort, scope.row.Id)"
                                :class="{ 'input-focused': focusedIndex === scope.$index }"
                                placeholder="请输入数字"></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
        <el-dialog v-model="dialogVisibleExplain" draggable title="审核不通过退回原因" width="560px">
            <div>
                <el-input type="textarea" v-model="AuditExplain" disabled :autosize="{ minRows: 4 }"></el-input>
            </div>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}

.dialog-content {
    :deep(.el-input__wrapper) {
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-input__inner) {
        text-align: center;
    }

    .input-focused {
        border: 1px solid #409EFF;
    }
}
</style>