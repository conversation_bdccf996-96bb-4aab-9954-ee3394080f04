<script setup>
defineOptions({
    name: 'dangerchemicalsstandbookdisposallistedprint'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Back, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcWasteDisposalDetailFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { fileDownload, tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 9999999, Statuz: 3, sortModel: [{ SortCode: "Id", SortType: "asc" }] })
const attribute = ref()

const DisposalDatege = ref('')
const DisposalDatele = ref('')
const format = (date) => {
    if (date) {
        const [year, month, day] = date.split('-');
        return `${year}年${month}月${day}日`;
    } else {
        return ''
    }
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    DisposalDatege.value = format(route.query.DisposalDatege)
    DisposalDatele.value = format(route.query.DisposalDatele)
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: './disposallisted' })
}

// 列表
const HandleTableData = () => {
    tableData.value = []
    filters.value.OneClassId = route.query.OneClassId
    filters.value.BaseWasteId = route.query.BaseWasteId
    filters.value.CompanyName = route.query.CompanyName
    filters.value.DisposalDatege = route.query.DisposalDatege
    filters.value.DisposalDatele = route.query.DisposalDatele + " 23:59:59"
    DcWasteDisposalDetailFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data || []
            // console.log(rows.data)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const handleNativePrint = () => {
    // 获取打印区域内容
    const printContent = document.getElementById('printArea').innerHTML;

    // 创建一个隐藏的iframe
    const iframe = document.createElement('iframe');
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');

    // 添加到DOM中
    document.body.appendChild(iframe);

    // 获取iframe的document对象
    const iframeWindow = iframe.contentWindow || iframe.contentDocument;
    const iframeDoc = iframeWindow.document || iframeWindow;

    // 写入打印内容
    iframeDoc.open();
    iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>危险化学品处置台账</title>
            <style>
                @page { size: landscape; margin:10mm 5mm; }
                body { font-family: 宋体; margin: 0; padding: 0;   }
                .print-table { border-collapse: collapse; width: 100%; font-size: 14px; }
                .print-table th { border: 1px solid #ddd; padding: 5px 1px; } 
                .print-table td { border: 1px solid #ddd; padding: 6px 3px; }
                thead { display: table-header-group; }
                tr { page-break-inside: avoid; }
                .print-title { font-size: 35px; margin: 10px 0; text-align: center; }
                .print-date { font-size: 22px; text-align: right; }
                .print-signleft { margin-top: 20px; margin-bottom: 10px; text-align: left; }
                .print-signcenter { border-bottom: 1px solid #000000; width: 250px; display: inline-block; margin-right: 100px; }
                .print-signright { border-bottom: 1px solid #000000; width: 250px; display: inline-block; }
            </style>
        </head>
        <body>
            ${printContent}
        </body>
        </html>
    `);
    iframeDoc.close();

    // 等待内容加载完成后打印
    iframe.onload = function () {
        setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();

            // 打印完成后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 100);
        }, 200);
    };
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="handleNativePrint">打印</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="width: 1150px;" id="printArea">
            <div class="print-title">
                <span style="font-size: 35px;">危险化学品处置台账</span>
            </div>
            <div class="print-date">
                <span>统计时间段：{{ DisposalDatege }}--{{ DisposalDatele }}</span>
            </div>
            <table class="print-table">
                <thead>
                    <tr>
                        <th style="width: 40px;">序号</th>
                        <th style="width: 100px;">一级分类</th>
                        <th style="width: 140px;">二级分类</th>
                        <th style="width: 50px;">单位</th>
                        <th style="width: 70px;">数量</th>
                        <th style="width: 100px;">处置时间</th>
                        <th style="width: 140px;">处置企业</th>
                        <th style="width: 100px;">申请人</th>
                        <th style="width: 100px;">审核人</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(row, index) in tableData" :key="row.Id">
                        <td style="text-align: center;">{{ index + 1 }}</td>
                        <td>{{ row.OneClassName }}</td>
                        <td>{{ row.Name }}</td>
                        <td style="text-align: center;">{{ row.UnitsMeasurement }}</td>
                        <td style="text-align: right;">{{ row.Num }}</td>
                        <td style="text-align: center;">{{ row.DisposalDate ? row.DisposalDate.substring(0, 10) : '--'
                        }}</td>
                        <td>{{ row.CompanyName }}</td>
                        <td style="text-align: center;">{{ row.DeclareUserName }}</td>
                        <td style="text-align: center;">{{ row.AuditUserName }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
</template>
<style>
/* 打印样式 - 必须放在style标签中 */
@media print {
    .el-table {
        border: 1px solid #ebeef5;

    }

    .el-table th,
    .el-table td {
        border: 1px solid #ebeef5 !important;
    }

    .el-table__header-wrapper {
        display: table-header-group !important;
    }

    .el-table__body-wrapper tr {
        page-break-inside: avoid !important;
    }

    .el-pagination {
        display: none !important;
    }


}

.print-title {
    font-size: 35px;
    font-family: 宋体;
    margin: 10px 0;
    text-align: center;
}

.print-date {
    font-size: 22px;
    font-family: 宋体;
    text-align: right;
}

.print-signleft {
    margin-top: 20px;
    font-family: 宋体;
    margin-bottom: 10px;
    text-align: left;
}

.print-signcenter {
    border-bottom: 1px solid #000000;
    display: inline-block;
    margin-right: 100px;
}

.print-signright {
    border-bottom: 1px solid #000000;
    display: inline-block;
}
</style>
<style lang="scss" scoped>
table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

table th {
    border: 1px solid #ddd;
    padding: 10px 1px;
}

table td {
    border: 1px solid #ddd;
    padding: 6px 3px;
}
</style>