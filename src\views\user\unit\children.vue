<script setup>

import { onMounted, ref, watch } from 'vue'
import {
    Delete, UploadFilled, QuestionFilled, Search, Refresh, FolderAdd, Download
} from '@element-plus/icons-vue'
import {
    Getdictionarycombox, Unitfindchildren, Unitgetbyid, Unitinsertupdate, Unitdelbatch,
    Unitsortedit, Areagetbyid, Areagetbypid, Getareabyunitid, UploadPostexecl, SuperAdminUploadUnitFile
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit, limit, fileDownload } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const selectRows = ref([])
const schoolStageList = ref([])
const provincialDataList = ref([{ Name: "全部", Id: "0" }])
const cityDataList = ref([{ Name: "全部", Id: "0" }])
const countyDataList = ref([{ Name: "全部", Id: "0" }])
const areaForm = ref({ ProvinceIdAreaId: '0', CityAreaId: '0', CountyAreaId: '0' })
const excelUrl = ref('')
const provincialName = ref('')
const cityName = ref('')
const countyName = ref('')
const filtersKey = ref({ key: '', value: 'Name' })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Sort", SortType: "ASC" }, { SortCode: "Id", SortType: "ASC" }] })
const options = ref([
    { value: 'Name', label: '名称', },
    { value: 'Code', label: '编号', },
    { value: 'Brief', label: '简称', }
])

const HandleSelectChange = (selection) => {
    selectRows.value = selection
}




//加载数据
onMounted(() => {
    HandleTableData()
    GetdictionarycomboxUser()
    AreagetbypidUser(0, 1, false)
})
//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const isAdd = ref(true)
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '单位名称不能为空', trigger: 'blur' },
    ],
    Code: [
        { required: true, message: '单位代码不能为空', trigger: 'blur' },
    ],
    AdminMobile: [
        {
            pattern: /^1[0-9]{10}$/,
            message: '请输入11位手机号码',
            trigger: 'blur'
        }
    ],
    Mobile: [
        {
            pattern: /^1[0-9]{10}$/,
            message: '请输入11位手机号码',
            trigger: 'blur'
        }
    ],
}

// 添加账户
const HandleAdd = () => {
    isAdd.value = true
    formData.value.Id = '0'
    formData.value = {}
    dialogVisible.value = true
}
//修改
const HandleEdit = (row) => {
    isAdd.value = false
    formData.value.Id = row.Id
    Unitgetbyid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value.Id = rows.Id
            formData.value.PId = rows.PId
            formData.value.UnitType = rows.UnitType
            formData.value.IndustryId = rows.IndustryId
            formData.value.Code = rows.Code
            formData.value.Name = rows.Name
            formData.value.Brief = rows.Brief
            formData.value.OrganizationCode = rows.OrganizationCode
            formData.value.SchoolAdmin = rows.SchoolAdmin
            formData.value.AdminMobile = rows.AdminMobile
            formData.value.Legal = rows.Legal
            formData.value.Mobile = rows.Mobile
            formData.value.AreaId = rows.AreaId
            formData.value.Address = rows.Address
            formData.value.Url = rows.Url
            formData.value.SchoolNature = rows.SchoolNature
            formData.value.ClassNum = rows.ClassNum
            formData.value.TeacherNum = rows.TeacherNum
            formData.value.StudentNum = rows.StudentNum
            formData.value.FloorArea = rows.FloorArea
            formData.value.BuildArea = rows.BuildArea
            formData.value.SchoolStage = String(rows.SchoolStage)
            formData.value.Introduction = rows.Introduction
            // Areagetbyid({ id: rows.AreaId }).then((res) => {
            //   formData.value.AreaName = res.data.data.rows.FullName
            // })

            Getareabyunitid({ UnitId: rows.Id }).then(res => {
                const { rows } = res.data.data
                // console.log("所在地详情", rows)
                AreagetbypidUser(rows.ProvinceId, 2, false)
                AreagetbypidUser(rows.CityId, 3, false)
                areaForm.value.ProvinceIdAreaId = rows.ProvinceId
                areaForm.value.CityAreaId = rows.CityId
                areaForm.value.CountyAreaId = rows.CountyId
                provincialName.value = rows.ProvinceName
                cityName.value = rows.CityName
                countyName.value = rows.CountyName
                formData.value.AreaName = rows.ProvinceName + rows.CityName + rows.CountyName
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)

    })
    dialogVisible.value = true
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm(`确认删除用户[${row.Name}]吗?`)
        .then(() => {
            Unitdelbatch({ ids: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success('删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
}

//获取列表 与 搜索
const HandleTableData = (page) => {
    filters.value.Name = undefined;
    filters.value.Code = undefined;
    filters.value.Brief = undefined;
    // 按类型搜索
    if (filtersKey.value.key) {
        filters.value[filtersKey.value.value] = filtersKey.value.key;
    } else {
        filters.value[filtersKey.value.value] = undefined;
    }
    Unitfindchildren(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { other, rows, total } = res.data.data
            tableData.value = rows;
            excelUrl.value = other;
            tableTotal.value = Number(total);
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = (page) => {
    filters.value.pageIndex = 1
    filtersKey.value.key = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 批量删除
const HandleDelAll = (row) => {
    ElMessageBox.confirm(`确认删除用户[${row.map(t => t.Name).join(',')}]吗?`)
        .then(() => {
            Unitdelbatch({ ids: row.map(t => t.Id).join(',') }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success('删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
}

// 新增或修改提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) {
            return;
        }
        Unitinsertupdate(formData.value).then(res => {
            if (res.data.flag == 1) {
                HandleTableData()
                ElMessage.success('保存成功')
                dialogVisible.value = false
            } else {
                ElMessage.error(res.data.msg)
            }
        }).catch((err) => {
            console.info(err)
        })
    })
}
// 单位属性
const GetdictionarycomboxUser = () => {
    Getdictionarycombox({ typeCode: 1101 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // console.log("单位属性", rows)
            schoolStageList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取省市区 p: 1：省  2：市  3：区县   cut:是否为通过切换省市查找下一级
const AreagetbypidUser = (pid, p, cut) => {
    Areagetbypid({ pid: pid }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            rows.unshift({ Name: "全部", Id: "0" });

            if (p == 1) {
                provincialDataList.value = rows
            } else if (p == 2) {
                cityDataList.value = rows
                if (cut) {
                    let find = provincialDataList.value.find(t => t.Id == pid)
                    if (find) provincialName.value = find.Name
                    formData.value.AreaName = provincialName.value
                    areaForm.value.CityAreaId = '0'
                    areaForm.value.CountyAreaId = '0'
                }
            } else if (p == 3) {
                countyDataList.value = rows
                if (cut) {
                    let find = cityDataList.value.find(t => t.Id == pid)
                    if (find) cityName.value = find.Name
                    formData.value.AreaName = provincialName.value + ',' + cityName.value
                    areaForm.value.CountyAreaId = '0'
                }
            }
        } else {
            ElMessage.error(res.data.msg)
        }

    })
    formData.value.AreaId = pid
}

const CountyChange = (pid) => {
    let find = countyDataList.value.find(t => t.Id == pid)
    if (find) countyName.value = find.Name
    formData.value.AreaName = provincialName.value + ',' + cityName.value + ',' + countyName.value
    formData.value.AreaId = pid
}
const focusedIndex = ref(-1); // 用来存储当前聚焦的输入框的行索引  
// 表格输入框聚焦事件
const handleFocus = (index, isFocused) => {
    focusedIndex.value = isFocused ? index : -1;
}
// 表格输入框赋值  ：排序
const handleInput = (index, value, id) => {
    tableData.value[index].inputValue = value;
    Unitsortedit({ Id: id, sort: value }).then(res => {
        if (res.data.flag == 1) {
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}


const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
//  本单位用户导入
const importExecl = () => {
    uploadVisible.value = true
}
// 模板下载
const HandleDownload = () => {
    fileDownload(excelUrl.value);
}
const fileFile = ref()
const uploadRef = ref()

// 导入前校验
const beforeAvatarUpload = (file) => {
    fileFile.value = file
    const extension = file.name.split('.')[1] === 'xls'
    const extension2 = file.name.split('.')[1] === 'xlsx'
    if (!extension && !extension2) {
        ElMessage({
            message: '上传模板只能是 xls、xlsx格式!',
            type: 'error'
        })
    }
    return extension || extension2
}
const httpRequest = () => {

    UploadPostexecl([fileFile.value]).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data

            SuperAdminUploadUnitFile({ FilePath: rows }).then(res1 => {
                if (res1.data.flag == 1) {
                    ElMessage.success(res1.data.msg || '导入成功')
                    setTimeout(function () {
                        HandleTableData()
                        uploadVisible.value = false
                    }, 1000);
                } else {
                    ElMessage({
                        showClose: true,
                        message: res1.data.msg,
                        type: 'error',
                        duration: 5000
                    })

                }
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="filtersKey" class="flexBox">
                <el-form-item class="flexItem">
                    <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    <el-button type="danger" :icon="Delete" :disabled="selectRows.length == 0"
                        @click="HandleDelAll(selectRows)">批量删除</el-button>
                    <el-button type="primary" :icon="UploadFilled" @click="importExecl">下属单位导入</el-button>
                </el-form-item>
                <div class="verticalIdel"></div>
                <el-form-item label="" class="flexItem" label-width="60">
                    <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="max-width: 300px"
                        class="input-with-select">
                        <template #prepend>
                            <el-select v-model="filtersKey.value" style="width: 100px">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                    <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                </el-form-item>
            </el-form>

        </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" border
        stripe header-cell-class-name="headerClassName">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column prop="Code" label="单位代码" min-width="180" align="center"></el-table-column>
        <el-table-column prop="Name" label="单位名称" min-width="180" align="center"></el-table-column>
        <el-table-column prop="Brief" label="简称" min-width="100" align="center"></el-table-column>
        <el-table-column prop="UnitType" label="性质" min-width="80" align="center">
            <template #default="{ row }">
                {{ row.UnitType == 1 ? '市级' : row.UnitType == 2 ? '区县' : row.UnitType == 3 ? '单位'
                    : row.UnitType == 4 ? '企业' : '未知' }}
            </template>
        </el-table-column>
        <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
            <template #default="{ row }">
                <el-tag :type="row.Statuz != 0 ? 'success' : 'danger'" disable-transitions>
                    {{ row.Statuz != 0 ? "正常" : "禁用" }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column label="排序值" min-width="100" align="center">
            <template #default="scope">
                <div class="sortDiv">
                    <el-input v-model="scope.row.Sort" type="number" @focus="handleFocus(scope.$index, true)"
                        @blur="handleInput(scope.$index, scope.row.Sort, scope.row.Id)"
                        :class="{ 'input-focused': focusedIndex === scope.$index }" placeholder="请输入数字"></el-input>
                </div>
            </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="120" align="center">
            <template #default="{ row }">
                <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
            </template>
        </el-table-column>
        <template #empty>
            <el-empty description="没有数据"></el-empty>
        </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
        @handleChange="handlePage" />
    <!-- 添加修改单位用户信息 -- 弹窗 -->
    <app-box v-model="dialogVisible" :height="600" :width="940" :lazy="true" :title="isAdd ? '添加下属单位信息' : '修改下属单位信息'">
        <template #content>
            <el-form class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
                label-width="140px" status-icon>
                <fieldset>
                    <legend>单位信息</legend>
                    <el-form-item label="单位代码：" :prop="isAdd ? 'Code' : ''">
                        <el-input v-model="formData.Code" :disabled="!isAdd"></el-input>
                    </el-form-item>
                    <el-form-item label="单位名称：" prop="Name">
                        <el-input v-model="formData.Name"></el-input>
                    </el-form-item>
                    <el-form-item label="单位简称：">
                        <el-input v-model="formData.Brief" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="社会统一信用代码：">
                        <el-input v-model="formData.OrganizationCode" auto-complete="off"></el-input>
                    </el-form-item>
                    <div class="dialogFlexBox1">
                        <el-form-item label="单位超管：">
                            <el-input v-model="formData.SchoolAdmin" auto-complete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话：" prop="AdminMobile">
                            <el-input v-model="formData.AdminMobile" auto-complete="off"
                                @input="integerLimitInput($event, 'AdminMobile')"></el-input>
                        </el-form-item>
                        <el-form-item label="单位法人：">
                            <el-input v-model="formData.Legal" auto-complete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话：" prop="Mobile">
                            <el-input v-model="formData.Mobile" auto-complete="off"
                                @input="integerLimitInput($event, 'Mobile')"></el-input>
                        </el-form-item>
                    </div>
                    <div class="dialogFlexBox">
                        <el-form-item label="省市区：">
                            <el-select v-model="areaForm.ProvinceIdAreaId" style="width: 101px"
                                @change="AreagetbypidUser($event, 2, true)">
                                <el-option v-for="item in provincialDataList" :key="item.Id" :label="item.Name"
                                    :value="item.Id" />
                            </el-select>
                            <el-select v-model="areaForm.CityAreaId" style="width: 101px"
                                @change="AreagetbypidUser($event, 3, true)">
                                <el-option v-for="item in cityDataList" :key="item.Id" :label="item.Name"
                                    :value="item.Id" />
                            </el-select>
                            <el-select v-model="areaForm.CountyAreaId" style="width: 101px"
                                @change="CountyChange($event)">
                                <el-option v-for="item in countyDataList" :key="item.Id" :label="item.Name"
                                    :value="item.Id" />
                            </el-select>
                        </el-form-item>
                        <el-form-item prop="AreaName">
                            <template #label>
                                <el-tooltip class="item" effect="dark" content="请在左侧选择省市区进行修改" placement="top">
                                    <div>
                                        <el-icon color="#E6A23C">
                                            <QuestionFilled />
                                        </el-icon>
                                    </div>
                                </el-tooltip>
                                <span> 区域： </span>
                            </template>
                            <el-input v-model="formData.AreaName" auto-complete="off" readonly></el-input>
                        </el-form-item>
                    </div>
                    <el-form-item label="单位地址：">
                        <el-input v-model="formData.Address" auto-complete="off"></el-input>
                    </el-form-item>
                    <el-form-item label="单位官网：">
                        <el-input v-model="formData.Url" auto-complete="off"></el-input>
                    </el-form-item>
                </fieldset>
                <fieldset v-if="formData.UnitType == 3">
                    <legend>基础信息</legend>
                    <el-form-item label="单位性质：">
                        <el-radio-group v-model="formData.SchoolNature">
                            <el-radio class="radio" :value="1">公办</el-radio>
                            <el-radio class="radio" :value="2">民办</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <div class="dialogFlexBox">
                        <el-form-item label="单位属性：">
                            <el-select v-model="formData.SchoolStage">
                                <el-option class="flexItem" v-for="item in schoolStageList" :key="item.DicValue"
                                    :label="item.DicName" :value="item.DicValue">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="班级总数(班)：">
                            <el-input v-model="formData.ClassNum" auto-complete="off"
                                @input="integerLimitInput($event, 'ClassNum')"></el-input>
                        </el-form-item>
                        <el-form-item label="教职工数(人)：">
                            <el-input v-model="formData.TeacherNum" auto-complete="off"
                                @input="integerLimitInput($event, 'TeacherNum')"></el-input>
                        </el-form-item>
                        <el-form-item label="学生总数(人)：">
                            <el-input v-model="formData.StudentNum" auto-complete="off"
                                @input="integerLimitInput($event, 'StudentNum')"></el-input>
                        </el-form-item>
                        <el-form-item label="占地面积(㎡)：">
                            <el-input v-model="formData.FloorArea" auto-complete="off"
                                @input="limitInput($event, 'FloorArea')"></el-input>
                        </el-form-item>
                        <el-form-item label="建筑面积(㎡)：">
                            <el-input v-model="formData.BuildArea" auto-complete="off"
                                @input="limitInput($event, 'BuildArea')"></el-input>
                        </el-form-item>
                    </div>
                </fieldset>
                <fieldset>
                    <legend>简介信息</legend>
                    <el-form-item label="单位简介：">
                        <el-input type="textarea" v-model="formData.Introduction"></el-input>
                    </el-form-item>
                </fieldset>
            </el-form>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
            </span>
        </template>
    </app-box>
    <!-- 导入单位 -->
    <el-dialog v-model="uploadVisible" title="下属单位导入" width="480px">
        <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" label-width="80px" status-icon>
            <el-form-item>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" accept=".xlsx,.xls"
                    style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload"
                    :http-request="httpRequest">
                    <el-button type="primary" :icon="UploadFilled">下属单位导入 </el-button>
                </el-upload>
                <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}


.dialogFlexBox {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
        width: 50%;
        margin-bottom: 10px;
    }
}

.dialogFlexBox1 {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
        width: 50%;
    }
}

fieldset {
    // color: #333;
    border: #ccc dashed 1px;
    padding: 10px;
    margin: 10px 0;

    legend {
        font-size: 16px;
    }
}

.sortDiv {
    :deep(.el-input__wrapper) {
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-input__inner) {
        text-align: center;
    }

    .input-focused {
        border: 1px solid #409EFF;
    }

}
</style>