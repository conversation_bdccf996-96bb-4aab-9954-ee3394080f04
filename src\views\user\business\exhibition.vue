<script setup>
import { onMounted, ref } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    XuniformshelfGetpagelist, XuniformshelfSetisshow
} from '@/api/user.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, })
const CompanyList = ref([])
const SchoolList = ref([])
onMounted(() => {
    HandleTableData(true);
})
// 品名查看
const HandleDetail = (row) => {
    const { href } = router.resolve({
        path: "/preview",
        query: { id: row.Id, requestNum: 2 }
    });
    window.open(href, "_blank");
}
//列表
const HandleTableData = (isFirst) => {
    if (isFirst) {
        filters.value.pageIndex = 1
        filters.value.isFirst = true
    } else {
        filters.value.isFirst = undefined
    }
    XuniformshelfGetpagelist(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            if (isFirst) {
                CompanyList.value = other.CompanyList || [];//供应商
                SchoolList.value = other.SchoolList || [];//学校
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.SupplierId = undefined
    filters.value.SchoolId = undefined
    filters.value.date = undefined
    filters.value.Name = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 修改校服展示状态
const HandleSwitchChange = (e, row) => {
    XuniformshelfSetisshow({ id: row.Id, statuz: row.IsShow }).then((res) => {
        if (res.data.flag == 1) {
            if (e == 1) {
                ElMessage.success('启用成功')
            } else {
                ElMessage.success('禁用成功')
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SupplierId" clearable filterable placeholder="供应商"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in CompanyList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="学校/供应商"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.PurchaseNo }}
                </template>
            </el-table-column>
            <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"> </el-table-column>
            <el-table-column prop="SchoolName" label="学校" min-width="180"> </el-table-column>
            <el-table-column prop="Name" label="品名" min-width="120" align="center">
                <template #default="{ row }">
                    <span style="color: #66ccff;cursor: pointer;" @click="HandleDetail(row)">{{ row.Name }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="Price" label="单价（元）" min-width="100" align="right"></el-table-column>
            <el-table-column prop="Sex" label="适合性别" min-width="90" align="center">
                <template #default="{ row }">
                    {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : row.Sex == 3 ? '男/女' : '未知' }}
                </template>
            </el-table-column>
            <el-table-column prop="StandardNum" label="标配数量" min-width="90" align="center"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
            <el-table-column prop="SupplierName" label="供应商" min-width="180"></el-table-column>

            <el-table-column label="是否展示" min-width="100" align="center">
                <template #default="{ row }">
                    <el-switch v-model="row.IsShow" :active-value="1" :inactive-value="2" inline-prompt active-text="是"
                        inactive-text="否" style="--el-switch-off-color: #ff4949"
                        @change="HandleSwitchChange($event, row)" />
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <!-- 分页 -->
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>