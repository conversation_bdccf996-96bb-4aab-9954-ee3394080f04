<script setup>
defineOptions({
  name: 'evaluatelist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
  EvaluateGetevaluatepaged, EvaluateDelByid
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
import QRCode from 'qrcode';
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const SupplierList = ref([])

//加载数据
onMounted(() => {
  yearDateList.value = previousYearDate()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})
//新增
const HandleAdd = () => {
  router.push({ path: "./newedit", query: { id: 0, title: "创建校服评价" } })
}
// 创建/修改评价
const HandleEdit = (row, title) => {
  router.push({ path: "./newedit", query: { id: row.Id, SubscriptionDeadline: row.SubscriptionDeadline, EvaluateDeadline: row.EvaluateDeadline, title: '修改校服评价' } })
}
//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('您确认要删除该数据吗？')
    .then(() => {
      EvaluateDelByid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  EvaluateGetevaluatepaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        SupplierList.value = other.listSupplier;//调换状态
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.PurchaseYear = undefined
  filters.value.CompanyId = undefined
  filters.value.Key = undefined
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleSearch()
}
const filtersChange = () => { HandleTableData() }
//下载二维码图片
const qrCodeDataUrl = ref(null);
const generateQRCode = async (text, size) => {
  try {
    qrCodeDataUrl.value = await QRCode.toDataURL(text, { scale: size / 100 });
  } catch (error) {
    console.error('生成二维码时出错:', error);
  }
};

const downloadQRCode = (rows) => {
  let url = window.location.protocol + '//' + window.location.host
  let path = url + "/ui/#" + "/pages/evaluate/edit?id=" + rows.Id;//二维码信息
  let title = '校服评价' + rows.PurchaseNo + '.png'
  generateQRCode(path, 400).then(() => {
    // 创建一个临时的 <a> 元素来触发下载
    const a = document.createElement('a');
    a.href = qrCodeDataUrl.value;
    a.download = title;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  });
};
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.CompanyId" clearable filterable placeholder="供应商" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SupplierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Key" clearable placeholder="合同批次" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="PurchaseYear" label="年度" min-width="100" align="center"></el-table-column>
      <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
      <el-table-column prop="ContractRenewal" label="是否续签合同" min-width="120" align="center"></el-table-column>
      <el-table-column prop="ContractEndDate" label="合同终止时间" min-width="140" align="center">
        <template #default="{ row }">
          {{ row.ContractEndDate ? row.ContractEndDate.substring(0, 10) : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="SubscriptionDeadline" label="评价截止时间" min-width="140" align="center">
        <template #default="{ row }">
          <span v-if="!row.EvaluateDeadline">--</span>
          <span v-else-if="new Date(row.EvaluateDeadline).setHours(23, 59, 59, 0) < new Date()"
            style="color: #F56C6C;">{{
              row.EvaluateDeadline.substring(0, 10) }}</span>
          <span v-else>{{ row.EvaluateDeadline.substring(0, 10) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="SupplierName" label="供应商" min-width="200"></el-table-column>
      <el-table-column label="评价二维码" min-width="100" align="center">
        <template #default="{ row }">
          <el-button v-if="row.EvaluateDeadline && new Date(row.EvaluateDeadline).setHours(23, 59, 59, 0) > new Date()"
            type="primary" link @click="downloadQRCode(row)">下载</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="评价管理" min-width="120" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
          <el-button v-if="row.EvaluateNum == 0" type="primary" link @click="HandleDel(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>