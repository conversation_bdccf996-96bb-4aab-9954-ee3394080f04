<script setup>
defineOptions({
    name: 'dangerchemicalsscraplist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcMaterialSetIsMayUse, DccatalogGetClassTwo, DcMaterialsNumAuditAdd, DcSchoolMaterialGetById, DcSchoolMaterialOptFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const ValidDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1, StockNumgt: 0, sortModel: [{ SortCode: "ValidDate", SortType: "ASC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    ReturnNum: [
        { required: true, message: '请填写报废数量', trigger: 'change' },
    ],
    BackDate: [
        { required: true, message: '请选择报废时间', trigger: 'change' },
    ],
    Remark: [
        { required: true, message: '请填写报废原因', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'Name', label: '危化品名称' },
    { value: 'Model', label: '规格属性' },
    { value: 'Brand', label: '品牌' },
    { value: 'ThirdMaterialId', label: '标识码' }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

const stockNum = ref('')
// 报废
const HandleEdit = (row) => {
    dialogVisible.value = true
    stockNum.value = row.StockNum
    formData.value = row
    formData.value.BackDate = ''
    formData.value.ReturnNum = ''
    formData.value.Remark = ''
    nextTick(() => {
        refForm.value.resetFields()
    })
}

const validDateleChange = (val) => {
    if (val) {
        filters.value.ValidDatele = val + " 23:59:59"
    } else {
        filters.value.ValidDatele = undefined
    }
    HandleTableData()
}
//输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}

// 列表
const HandleTableData = () => {
    filters.value.ThirdMaterialId = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcSchoolMaterialOptFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.IsMayUse = undefined
    filters.value.TwoCatalogId = undefined
    filters.value.ValidDatele = undefined
    ValidDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 修改是否可用
const HandleSwitchChange = (e, row) => {

    DcMaterialSetIsMayUse({ id: row.Id, isMayUse: e }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success('设置成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
//报废提交
const HandleSubmit = () => {
    if (formData.value.ReturnNum > formData.value.StockNum) {
        ElMessage.error('填写的报废数量不能大于库存量')
        return
    }
    let paraData = {
        OptNum: Number(formData.value.ReturnNum),
        OptReason: formData.value.Remark,
        OptTime: formData.value.BackDate,
        OptType: 2,
        SchoolMaterialId: formData.value.Id,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcMaterialsNumAuditAdd(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '报废成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    报废危化品列表 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>当危化品发生意外（如：损坏、过期等）时，可点击【报废】；</li>
                    <li>入库方式：“采购”是指从供应商采购后直接入库，“退回”是指实验使用后剩余退回入库；</li>
                    <li>是否可用：“不可用”是指危化品待报废处置；在操作栏中可设置状态。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="ValidDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            clearable placeholder="有效期至" @change="validDateleChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsMayUse" clearable placeholder="是否可用" style="width: 160px"
                            @change="filtersChange">
                            <el-option label="可用" value="1" />
                            <el-option label="不可用" value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="StockNum" label="存量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Price" label="单价" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ValidDate" label="有效期至" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.ValidDate" :style="{ color: row.IsValid == 0 ? 'red' : '#66266' }">
                        {{ row.ValidDate.substring(0, 10) }}
                    </span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="Address" label="存放地点" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column prop="InputType" label="入库方式" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.InputType == 1 ? '采购' : row.InputType == 2 ? '退回' : '' }}
                </template>
            </el-table-column>
            <el-table-column label="是否可用" min-width="120" align="center">
                <template #default="{ row }">
                    <el-switch v-model="row.IsMayUse" :active-value="1" :inactive-value="0"
                        style="--el-switch-off-color: #ff4949" inline-prompt active-text="是" inactive-text="否"
                        @change="HandleSwitchChange($event, row)" />
                </template>
            </el-table-column>
            <el-table-column prop="ScrapAuditNum" label="报废数量" min-width="110" align="right">
                <template #default="{ row }">
                    {{ row.ScrapAuditNum == 0 ? '--' : row.ScrapAuditNum }}
                </template>
            </el-table-column>
            <el-table-column label="标识码" min-width="120" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.ThirdMaterialId || '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 1" type="primary" link @click="HandleEdit(row)">报废</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="报废">
            <template #content>
                <el-form style="min-width: 320px; " class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="危化品名称：">
                        <span>{{ formData.Name }}</span>
                    </el-form-item>
                    <el-form-item label="规格属性：">
                        <span>{{ formData.Model }}</span>
                    </el-form-item>
                    <el-form-item label="品牌：">
                        <span>{{ formData.Brand }}</span>
                    </el-form-item>
                    <el-form-item label="存量：">
                        <span>{{ formData.StockNum + formData.NoGrantNum }}</span>
                    </el-form-item>
                    <el-form-item label="单位：">
                        <span>{{ formData.UnitName }}</span>
                    </el-form-item>
                    <el-form-item label="报废数量：" prop="ReturnNum" class="boxItem">
                        <el-input v-model="formData.ReturnNum" @input="limitInput($event, 'ReturnNum')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="报废时间：" prop="BackDate" class="boxItem">
                        <el-date-picker v-model="formData.BackDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable style="width: 80%;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="报废原因：" prop="Remark" class="boxItem">
                        <el-input type="textarea" v-model="formData.Remark" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 确认 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.mobile-box {
    :deep(.el-form-item) {
        margin-bottom: 5px;
    }

    .boxItem.el-form-item {
        margin-bottom: 18px;
    }
}
</style>