<script setup>
defineOptions({
  name: 'teacheraffairorderdetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search, Position
} from '@element-plus/icons-vue'
import {
  PurchaseGetteacherstudentpaged, PurchaseExportstudent
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { fileDownload } from "@/utils/index.js";
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const StatuzList = ref([])
const tableHeader = ref({})

//加载数据
onMounted(() => { })
onActivated(() => {
  nextTick(() => {
    if (route.query.id) {
      filters.value.UniformPurchaseId = route.query.id
      filters.value.ClassInfoId = route.query.ClassInfoId
      HandleTableData(true);
    }
  })
})

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  PurchaseGetteacherstudentpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows || [];
      if (tableData.value.length > 0) {
        tableHeader.value = tableData.value[0];
      }
      tableTotal.value = Number(total)
      if (isFirst && other) {
        StatuzList.value = other.StatuzList || [];//状态
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.Statuz = undefined
  filters.value.Name = undefined
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }
//导出
const HandleExport = () => {
  PurchaseExportstudent(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      let url = rows;
      let title = tableHeader.value.SchoolName + '_' + tableHeader.value.PurchaseNo + '_' + res.data.data.headers + '.xls'
      fileDownload(url, title);
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.Statuz" clearable placeholder="状态" @change="filtersChange" style="width: 160px">
              <el-option v-for="item in StatuzList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="请输入" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">
      合同批次：<span style="color: #999;">{{ tableHeader.PurchaseNo }}</span>
    </div>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="StudentNo" label="学号" min-width="120" align="center"></el-table-column>
      <el-table-column prop="StudentName" label="学生姓名" min-width="100" align="center"></el-table-column>
      <el-table-column prop="Sex" label="性别" min-width="80" align="center"></el-table-column>
      <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"></el-table-column>
      <el-table-column prop="Name" label="品名" min-width="120" align="center"></el-table-column>
      <el-table-column prop="SizeDes" label="尺码" min-width="100" align="center"></el-table-column>
      <el-table-column prop="Num" label="征订数量" min-width="90" align="center"></el-table-column>
      <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
      <el-table-column prop="Price" label="单价（元）" min-width="100" align="right"></el-table-column>
      <el-table-column prop="Amount" label="金额（元）" min-width="150" align="right"></el-table-column>
      <el-table-column prop="Statuz" label="状态" min-width="80" align="center">
        <template #default="{ row }">
          {{ row.Statuz == 1 ? '已征订' : row.Statuz == 2 ? '未征订' : '' }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="IDCard6" label="身份证号" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.IDCard6 ? '*' + row.IDCard6 : '' }}
        </template>
      </el-table-column> -->
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>