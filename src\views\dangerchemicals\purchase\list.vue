<script setup>
defineOptions({
    name: 'dangerchemicalspurchaselist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Back, Search, Refresh, FolderAdd
} from '@element-plus/icons-vue'
import {
    DcPurchaseAdd, DcPurchaseApply, DcPurchaseListDelete, DcPurchaseListFind, DcPurchaseListFindById, DcPurchaseListUpdate,
    DcPurchaseNoPassReasonGet, DcschoolCatalogComboboxGet, DcschoolCatalogCommonuseFind, DcschoolCatalogFindCommonuseAll,
    DcSchoolMaterialModelGetByCatalogId

} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { limit, buildTree } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const schoolCatalogList = ref([])
const dangerchemicalsList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 50, Statuz: 0, IsEmptyBatchNo: true, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const whpDialogVisible = ref(false)
const isEdit = ref(false)
const formData = ref({})
const refForm = ref()
const bogetList = ref([])
const memberUserList = ref([])
const modelList = ref([])
const brandList = ref([])
const ruleForm = {
    Name: [
        { required: true, message: '请选择危化品', trigger: 'change' },
    ],
    SchoolMaterialModelId: [
        { required: true, message: '请选择规格属性', trigger: 'change' },
    ]
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        DcschoolCatalogFindCommonuseAllUser()
        DcschoolCatalogComboboxGetUser()
        if (route.query.id) {
            DcPurchaseNoPassReasonGetUser(route.query.id)
        }
    }

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            DcschoolCatalogFindCommonuseAllUser()
            DcschoolCatalogComboboxGetUser()
            if (route.query.id) {
                DcPurchaseNoPassReasonGetUser(route.query.id)
            }
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 添加危化品
const HandleAdd = (row) => {
    router.push({ path: './fill', orderId: route.query.id })
}
// 采购申请
const HandleApply = (row) => {
    let data = {
        purchaseOrderId: route.query.id,
        ids: selectRows.value.map(item => item.Id).join(',')
    }
    DcPurchaseApply(data).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '申请成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 修改
const HandleEdit = (row) => {
    isEdit.value = true
    activeName.value = 'content'
    firstName.value = ''
    dangerchemicalsList.value = []
    DcPurchaseListFindByIdUser(row.Id)
    DcSchoolMaterialModelGetByCatalogIdUser(row.SchoolCatalogId)
    dialogVisible.value = true
}

// 修改提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        let data = {
            Id: formData.value.Id,
            SchoolCatalogId: formData.value.SchoolCatalogId,
            SchoolMaterialModelId: formData.value.SchoolMaterialModelId,
            UnitName: formData.value.UnitName,
            Price: formData.value.Price,
            Remark: formData.value.Remark,
            Num: formData.value.Num,
            Name: formData.value.Name,
        }
        if (formData.value.Id) {
            DcPurchaseListUpdate(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '修改成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        } else {
            DcPurchaseAdd(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '修改成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        }
    })
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    DcPurchaseListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            tableData.value.forEach(item => {
                item.ApplyNum = ''
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const activeName = ref('content')
const firstName = ref('')
const isSearch = ref(false)
const HandleSearch1 = () => {
    isSearch.value = true
    if (!firstName.value) return
    let paraData = {
        Name: firstName.value,
        Statuz: 1,
        pageIndex: 0,
        pageSize: 100000,
        sortModel: [{ SortCode: "Code", SortType: "asc" }]
    }
    DcschoolCatalogCommonuseFind(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerchemicalsList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('您确认要删除该数据吗？')
        .then(() => {
            DcPurchaseListDelete({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path || './fill', query: { isTagRouter: true } })
}
// 获取学校物品类别选择
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let arr = rows.data || []
            bogetList.value = buildTree(arr)
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}

//输入保留两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}

// 选择危化品
const dangerchemicalsClick = (item) => {
    // console.log("item", item) 
    formData.value.Id = item.Id
    formData.value.Name = item.Name
    formData.value.UnitName = item.UnitsMeasurement
    formData.value.SchoolMaterialModelId = undefined
    whpDialogVisible.value = false
    DcSchoolMaterialModelGetByCatalogIdUser(item.Id)
}
// 选择危化品
const schoolCatalogClick = (e) => {
    isSearch.value = false
    let arr = schoolCatalogList.value.filter(item => item.Id == e)
    let name = arr[0].Name
    let index = name.lastIndexOf('> ');
    formData.value.Id = e
    formData.value.UnitName = arr[0].UnitName
    formData.value.Name = name.substring(index + 1)
    formData.value.SchoolMaterialModelId = undefined
    DcSchoolMaterialModelGetByCatalogIdUser(e)
}
// 获取规格属性
const DcSchoolMaterialModelGetByCatalogIdUser = (id) => {
    DcSchoolMaterialModelGetByCatalogId({ schoolCatalodId: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            console.log('获取规格属性', rows)
            modelList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取危化品下拉框数据
const DcschoolCatalogComboboxGetUser = () => {
    DcschoolCatalogComboboxGet().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            schoolCatalogList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取详情
const DcPurchaseListFindByIdUser = (id) => {
    DcPurchaseListFindById({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取退回原因   
const DcPurchaseNoPassReasonGetUser = (id) => {
    DcPurchaseNoPassReasonGet({ id: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value.ApprovalRemark = rows?.ApprovalRemark || ''
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    采购车 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 第一步：点击【从基础、预警中添加】； </li>
                    <li> 第二步：添加完成后点击【采购申请】。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                        <div class="verticalDividel"></div>
                        <el-button type="primary" :icon="Search" :disabled="selectRows.length == 0"
                            @click="HandleApply">采购申请</el-button>
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加危化品</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div v-if="route.query.id" style="font-size: 14px;color: #F56C6C;margin-bottom: 10px;">
            退回原因: {{ formData.ApprovalRemark }}</div>
        <div v-if="route.query.batchNo" style="font-size: 14px;color: #606266;margin-bottom: 10px;">
            采购批次：<span style="color: #999;">{{ route.query.batchNo }}</span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip> </el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="Price" label="参考单价" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Sum" label="参考金额" min-width="120" align="right">
                <template #default="{ row }">
                    <span v-if="row.Id">
                        {{ row.Num * row.Price > 0 ? '￥' + Number(row.Num * row.Price).toFixed(2) : '--' }}
                    </span>
                    <span v-else>
                        {{ row.Sum ? '￥' + Number(row.Sum).toFixed(2) : '--' }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" fixed="right" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="isEdit ? '修改危化品信息' : '危化品领用'">
            <template #content>
                <el-form style="min-width: 100px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <el-form-item label="危化品名称：">
                        <el-select v-model="formData.Name" filterable @change="schoolCatalogClick"
                            style="width: 400px;">
                            <el-option v-for="item in schoolCatalogList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                        <el-button type="success" :icon="Search" @click="whpDialogVisible = true"
                            style="margin-left: 10px;">选择</el-button>
                    </el-form-item>
                    <el-form-item label="规格属性：" prop="SchoolMaterialModelId">
                        <el-select v-model="formData.SchoolMaterialModelId" style="width: 400px;">
                            <el-option v-for="item in modelList" :key="item.Id" :label="item.Model" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="数量：">
                        <el-input-number v-model="formData.Num" :min="1" />
                        <span style="color: #F56C6C;padding: 0 10px;">({{ formData.UnitName }})</span>
                    </el-form-item>
                    <el-form-item label="参考单价(元)：" prop="Price">
                        <el-input v-model="formData.Price" @input="limitInput($event, 'Price')" style="width: 400px" />
                    </el-form-item>
                    <el-form-item label="备注：">
                        <el-input v-model="formData.Remark" type="textarea" style="width: 400px"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="danger" @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 保存 </el-button>
                </span>
            </template>
        </app-box>
        <app-box v-model="whpDialogVisible" :width="960" :lazy="true" title="请选择危化品">
            <template #content>
                <el-tabs tab-position="left" v-model="activeName" style="height: 480px" class="demo-tabs">
                    <el-tab-pane label="搜索" name="search">
                        <div>
                            <el-input v-model.trim="firstName" placeholder="危化品名称" style="width: 280px"> </el-input>
                            <el-button type="primary" :icon="Search" @click="HandleSearch1">搜索</el-button>
                        </div>
                        <div>
                            <div v-if="isSearch && firstName && dangerchemicalsList.length == 0"
                                style="padding: 10px;color: #E6A23C;">危化品不存在，请重新搜索！</div>
                            <ul class="SearchUi" v-else>
                                <li v-for="item in dangerchemicalsList" :key="item.Id"
                                    @click="dangerchemicalsClick(item)">
                                    {{ item.FirstName }} > {{ item.SecondName }} > {{ item.Name }}</li>
                            </ul>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="危化品" name="content">
                        <ul class="whpUi">
                            <li v-for="item in bogetList[0].children" :key="item.Id">
                                <div class="content_left">
                                    {{ item.Name }}
                                </div>
                                <div class="content_right">
                                    <div v-for="item1 in item.children" :key="item1.Id" style="padding: 2px 0;">
                                        <el-divider direction="vertical" />
                                        <span class="text" style="padding: 2px;" @click="dangerchemicalsClick(item1)">
                                            {{ item1.Name }}</span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                </el-tabs>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.whpUi {
    font-size: 12px;
    background-color: #f9f9f9;
    padding: 5px;
    padding-inline-start: 0px;
    margin: 0;

    li {
        list-style-type: none;
        font-size: 12px;
        display: flex;
        padding: 10px 5px;
        border: 1px solid #f9f9f9;
        border-bottom: 1px dotted #d1cfd0;

        .content_left {
            width: 140px;
            flex-shrink: 0;
            color: #723535;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .content_right {
            display: flex;
            flex-wrap: wrap;
            color: #3c3c3c;

            .text:hover {
                cursor: pointer;
                color: #ff9933 !important;
            }
        }
    }

    li:hover {
        background: #fff;
        border: 1px solid #f93 !important;
    }
}

.SearchUi {
    font-size: 14px;
    // background-color: #f9f9f9;
    padding: 5px;
    height: 500px;
    margin: 0;
    overflow-y: auto;

    li {
        list-style-type: none;
        padding: 5px;
        border-bottom: 1px dotted #d1cfd0;
    }

    li:hover {
        background: #fff;
        color: #ff9933 !important;
        border: 1px solid #f93 !important;
        cursor: pointer;
    }
}
</style>