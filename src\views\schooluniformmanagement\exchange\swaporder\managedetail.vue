<script setup>
defineOptions({
    name: 'swapordermanagedetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Position
} from '@element-plus/icons-vue'
import {
    PurchaseGetswapdetail, PurchaseGetsummaryorder, PurchaseExportswapdetail, PurchaseExportsummaryorder
} from '@/api/purchase.js'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const tabIndex = ref('0')
const filters = ref({ pageIndex: 1, pageSize: 10 })
const GradeList = ref([])
const ClassList = ref([])
const headMsg = ref('')
const aggregate = ref({})
const summation = ref({})

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        filters.value.UniformPurchaseId = route.query.id
        HandleTableData(true);
    }
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.UniformPurchaseId = route.query.id
            HandleTableData(true);
        }
    })
})
const activeName = ref('detail')
const handleClick = (tab) => {
    console.log(tab, tab.index)
    tabIndex.value = tab.index
    // 切换后重置搜索条件
    filters.value.pageIndex = 1
    filters.value.GradeId = undefined
    filters.value.ClassName = undefined
    filters.value.Key = undefined
    HandleTableData(true)
}

//列表
const HandleTableData = (isFirst) => {
    if (tabIndex.value == '0') {
        filters.value.isFirst = isFirst
        PurchaseGetswapdetail(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, total, other, footer } = res.data.data
                tableData.value = rows;
                tableTotal.value = Number(total)
                aggregate.value = footer || {}
                if (isFirst) {
                    GradeList.value = other.listGrade || [];//年级
                    ClassList.value = other.listClass || [];//班级
                    headMsg.value = other.headMsg || '';//合同批次
                    summation.value = other.footer;//总计
                }
            } else {
                ElMessage.error(res.data.msg)
            }
        });
    } else if (tabIndex.value == '1') {
        filters.value.isFirst = isFirst
        PurchaseGetsummaryorder(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, total, other, footer } = res.data.data
                tableData.value = rows;
                tableTotal.value = Number(total)
                aggregate.value = footer || {}
                if (isFirst) {
                    headMsg.value = other.headMsg || '';//合同批次
                    summation.value = other.footer;//总计
                }
            } else {
                ElMessage.error(res.data.msg)
            }
        });
    }
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = (page) => {
    filters.value.pageIndex = 1
    filters.value.GradeId = undefined
    filters.value.ClassName = undefined
    filters.value.Key = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
//导出
const HandleExport = (num) => {

    if (num == 1) {
        // 按明细
        PurchaseExportswapdetail(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows } = res.data.data
                let url = rows;
                let title = res.data.data.headers
                fileDownload(url, title);
            } else {
                ElMessage.error(res.data.msg)
            }
        });
    } else if (num == 2) {
        // 按汇总
        PurchaseExportsummaryorder(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows } = res.data.data
                let url = rows;
                let title = res.data.data.headers
                fileDownload(url, title);
            } else {
                ElMessage.error(res.data.msg)
            }
        });
    }
}

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计';
            return;
        }
        if (column.property == 'Num') {
            sums[index] = summation.value.Num;

            return;
        }
    });
    return sums;
}
</script>
<template>
    <div class="viewContainer">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="按明细查看" name="detail">
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-button type="success" :icon="Position" @click="HandleExport(1)">导出</el-button>
                            </el-form-item>
                            <div class="verticalIdel"></div>
                            <el-form-item label="" class="flexItem">
                                <el-select v-model="filters.GradeId" clearable placeholder="年级" @change="filtersChange"
                                    style="width: 160px">
                                    <el-option v-for="item in GradeList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="" class="flexItem">
                                <el-select v-model="filters.ClassName" clearable placeholder="班级"
                                    @change="filtersChange" style="width: 160px">
                                    <el-option v-for="item in ClassList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="" class="flexItem">
                                <el-input v-model.trim="filters.Key" clearable placeholder="学号/学生姓名/品名/种类"
                                    style="width: 240px"></el-input>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">
                    <span style="color: #999;">{{ headMsg }}</span>
                </div>
                <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                    :summary-method="getSummaries" show-summary header-cell-class-name="headerClassName">
                    <el-table-column prop="GradeName" label="年级" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="ClassName" label="班级" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="StudentNo" label="学号" min-width="100" align="center"></el-table-column>
                    <el-table-column prop="StudentName" label="学生姓名" min-width="100" align="center"></el-table-column>
                    <el-table-column prop="Sex" label="性别" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"></el-table-column>
                    <el-table-column prop="ProductName" label="品名" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="OldSizeDes" label="原尺码" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="NewSizeDes" label="现尺码" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="Num" label="征订数量" min-width="90" align="center"></el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
                    <!-- <el-table-column prop="IDCard6" label="身份证号" min-width="120" align="center"></el-table-column> -->
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
            <el-tab-pane label="按汇总查看" name="collect">
                <el-row class="navFlexBox">
                    <el-col>
                        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                            <el-form-item class="flexItem">
                                <el-button type="success" :icon="Position" @click="HandleExport(2)">导出</el-button>
                            </el-form-item>
                            <div class="verticalIdel"></div>
                            <el-form-item label="" class="flexItem">
                                <el-input v-model.trim="filters.Key" clearable placeholder="品名/种类"
                                    style="width: 240px"></el-input>
                            </el-form-item>
                            <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
                <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">
                    <span style="color: #999;">{{ headMsg }}</span>
                </div>
                <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">调换累计数量说明：
                    <span style="color: #999;">负数表示需要出库的，正数表示需要入库的</span>
                </div>
                <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                    header-cell-class-name="headerClassName">
                    <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"></el-table-column>
                    <el-table-column prop="ProductName" label="品名" min-width="140" align="center"></el-table-column>
                    <el-table-column prop="Sex" label="适合性别" min-width="80" align="center"></el-table-column>
                    <el-table-column prop="NewSizeDes" label="尺码" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="Num" label="调换累计数量" min-width="140" align="center"></el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<style lang="scss" scoped></style>