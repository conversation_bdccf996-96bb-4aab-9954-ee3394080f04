<script setup>
import {
  User, C<PERSON>, SwitchButton, Expand, Fold, ArrowDown
} from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import avatar from '@/assets/img/default.png'
import Footer from '@/views/HomePage/footer.vue';
import { useUserStore, useSettingStore } from '@/stores'
import { onMounted, ref, computed } from 'vue'
import router from '@/router'
import MenuItemContainer from '@/views/layout/components/MenuItemContainer.vue'
import Theme from '@/components/Theme/index.vue'
const includeList = ref([
  'projectlist',
  'projectedit',
  'solicitlist',
  'solicitopiniondetail',
  'selectionlist',
  'selectionopiniondetail',
  'applicationlist',
  'applicationedit',
  'applicationdetail',
  'biddinglist',
  'biddingedit',
  'biddingdetail',
  'managementschooluniformlist',
  'managementlist',
  'managementedit',
  'managementdetail',
  'maintainenterlist',
  'maintainenteredit',
  'alreadysubmitlist',
  'auditauditlist',
  'auditauditedlist',
  'solicitsubscriporderlist',
  'solicitsubscriporderexamine',
  'solicitsubscriporderedit',
  'solicitsubscriporderdetail',
  'organizeeditlist',
  'organizedetaillist',
  'launchlist',
  'swaporderlist',
  'swapordermanagelist',
  'swaporderdetail',
  'swapordermanagedetail',
  'subsidizelist',
  'subsidizedetail',
  'evaluateedit',
  'evaluatelist',
  'evaluateexaminelist',
  'evaluateexaminedetail',
  'teacheraffairstudentmanage',
  'teacheraffairlaunch',
  'teacheraffairlaunchdetail',
  'teacheraffairsolicitsubscrip',
  'teacheraffairorderdetail',
  'articleedit',
  'articlelist',
  'articlecategory',
  'backsetgradeclass',
  'backsetstudent',
  'modulemanagement',
  'nodemanagement',
  'workflowformdraggable',
  'nodelistconfiguration',
  'nodeconditionlist',
  'nodeconditionset',
  'nodelinkagecfglist',
  'nodelinkagecfgset',
  'dictionarymanagement',
  'processmanagement',
  'accessorymanagement',
  'treasurymanagement',
  'businesspermission',
  'workextension',
  'processsourceoffunds',
  'processbudgetlist',
  'processstatedescription',
  'processmessageconfig',
  'statisticsdetail',
  'statisticslist',
  'querystatisticslist',
  'querystatisticsmanagement',
  'ceshi',
  'nodeedit',
  'nodedetail',
  'nodeexamine',
  'nodeprocessedlist',
  'nodependinglist',
  'nodeprojectlist',
  'nodeprojectexaminelist',
  'nodeprojectprojectdetail',
  'treasurylist',
  'treasurylistitem',
  'treasurydetail',
  'userschooluser',
  'userschooldetail',
  'dangerchemicalsapplycarlist',
  'dangerchemicalsinputmaterialbyplan',
  'dangerchemicalspurchasedetailview',
  'dangerchemicalspurchasedetaillist',
  'dangerchemicalsapplydetailview',
  'dangerchemicalspurchaseaudit',
  'dangerchemicalswasterecordlist',
  'dangerchemicalspurchasefill',
  'dangerchemicalspurchaselist',
  'dangerchemicalsorderlist',
  'dangerchemicalsapplyaudit',
  'dangerchemicalspurchaseauditedlist',
  'dangerchemicalsapplyconfirm',
  'dangerchemicalsdailycheckresultlist',
  'dangerchemicalsdailycheckresultedit',
  'dangerchemicalsdailycheckedresultlist',
  'dangerchemicalsdailydcproblemrectifylist',
  'dangerchemicalsdailycheckedschoollist',
  'dangerchemicalsdailycheckresultdetail',
  'dangerchemicalswastedetaillist',
  'dangerchemicalsscrapedlist',
  'dangerchemicalscountystocknumstatistics',
  'dangerchemicalscountyclassifystatistics',
  'dangerchemicalspurchaseyearnumstatistics',
  'dangerchemicalsapplyyearnumstatistics',
  'dangerchemicalswastedisposalnumstatistics',
  'dangerchemicalscountywastestatisticslist',
  'dangerchemicalsstandbookapplyprint',
  'dangerchemicalsstandbookpurchaseprint',
  'dangerchemicalsstandbookstockprint',
  'dangerchemicalsstandbookwasteprint',
  'dangerchemicalsstandbookdisposallistedprint',
  'dangerchemicalsapplyprint',
  'dangerchemicalsschoolmaterialbacklogprint',
  'dangerchemicalsschoolstoragelogprint',
  'dangerchemicalsdailyofflinecheckprint',
  'dangerchemicalsdailyproblemdangerstatisticsprint',
  'dangerchemicalsdailycheckedresultprint',
  'dangerchemicalsdailyproblemrecordprint',
  'dangerchemicalsschoolitemstorageauditlist',
  'dangerchemicalsemergencyplanlist',
  'dangerchemicalssystemteambuild',
  'dangerchemicalstrainsafeeducationlist',
  'dangerchemicalsmsdslist',
  'dangerchemicalsdailygoverndetail',
  'dangerchemicalsdailygovernitemreport',
  'businessunitconfiguration',
  'businessclassification',
  'businessunitauthority',
  'userdepartmentusermanager'
])

const userStore = useUserStore()
const settingStore = useSettingStore()
const tagsList = ref([])
const isPass = ref(false)
// 手机适配
const showDiyElement = ref(true)
const isMobile = ref(false)

const hideDiyElement = () => {
  showDiyElement.value = false;
  checkScreenWidth()
}
const checkScreenWidth = () => {
  if (window.innerWidth <= 768) {
    settingStore.isCollapse = false
    isMobile.value = true
  } else {
    isMobile.value = false
  }
}
// 左侧菜单折叠
const clickCrumb = (flag) => {

  if (window.innerWidth <= 768) {
    showDiyElement.value = true
  }
  settingStore.isCollapse = flag
  checkScreenWidth()
}

onMounted(() => {
  tagsList.value = userStore.tagsList
  checkScreenWidth()
  showDiyElement.value = false
  window.addEventListener('resize', checkScreenWidth);

  // 判断是否只存在班主任角色，如果是，则不显示修改密码
  if (userStore.userInfo && userStore.userInfo.RoleIds) {
    if (userStore.userInfo.RoleIds.length == 1 && userStore.userInfo.RoleIds[0] == 370) {
      isPass.value = true
    } else {
      isPass.value = false
    }
  }
})

// 其他
const handleCommand = async (key) => {
  if (key === 'logout') {
    // 退出操作
    ElMessageBox.confirm('你确认要进行退出吗?', '温馨提示')
      .then(() => {
        // 清除本地的数据 (token + user信息)
        userStore.logout()
        // console.log("退出登录", key)

        router.push('/')

      })
      .catch((err) => {
        console.info(err)
      })

  } else {
    // 跳转操作
    router.push(key)
  }
}

const handleNavCommand = async (key) => {
  if (key === 'other') {
    // 关闭其他
    await ElMessageBox.confirm('你确定要关闭其他页面?', '温馨提示', {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })
    userStore.closeOtherPage()

  } else if (key === 'all') {
    // 关闭所有
    await ElMessageBox.confirm('你确定要关闭所有页面?', '温馨提示', {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })
    userStore.closeAllPage()

  }
}

// 获取导航面包屑
const getBreadcrumb = (curRoute, isDeep, breadcrumbList) => {
  // if (!isDeep && curRoute.value.path === '/') return []
  if (!breadcrumbList) breadcrumbList = []
  if (!isDeep) {

    if (curRoute.value.meta.parent) {
      getBreadcrumb(curRoute.value.meta.parent, true, breadcrumbList)
    }
    breadcrumbList.push(curRoute.value.meta.title)
  }
  else {
    // console.log("curRoute.meta.parent", curRoute)
    // console.log("curRoute.meta.parent", curRoute.meta)
    if (curRoute.meta.parent) {

      getBreadcrumb(curRoute.meta.parent, true, breadcrumbList)
    }
    breadcrumbList.push(curRoute.meta.title)
  }
  return breadcrumbList;
}
const tagPush = (tag) => {

  // router.push(tag.path)
  if (tag.query) {
    tag.query.isTagRouter = true;//有标签点击跳转的页面携带此参数，其他不用，存在页面缓存不调用接口
    router.push({ path: tag.path, query: tag.query })
  } else {
    router.push({ path: tag.path, query: { isTagRouter: true } })
  }

  // router.push({ path: tag.path, query: tag.query })
}
const tagRemove = (tag) => {
  userStore.removeOneTag(tag.path)
  tagsList.value = userStore.tagsList
}
const handlePage = async (path) => {
  router.push(path)
}
// const convertedText = computed(() => {
//   try {
//     // 将字符串用引号包裹后解析
//     return JSON.parse(`"${userStore.defaultSet['8002_DLYM_BJ']}"`);
//   } catch (error) {
//     // 如果解析失败，使用替换方法
//     return userStore.defaultSet['8002_DLYM_BJ'].replace(/\\\\n/g, '\n');
//   }
// });
</script>
<template>
  <el-container class="layout-container" style="height: 100%;">
    <el-aside v-if="!isMobile" ref="myLeft" :width="settingStore.isCollapse ? '65px' : '200px'">
      <!-- 左侧菜单 -->
      <div class="left-logo" v-if="userStore.platformType == 1">
        {{ settingStore.isCollapse ? 'XF' : userStore.defaultSet['8002_DLYM_BJ'] || '校服管理与备案平台' }}
      </div>
      <div class="left-logo" v-if="userStore.platformType == 2">
        {{ settingStore.isCollapse ? 'SHSP' : userStore.defaultSet['8002_DLYM_BJ'] || '审批配置平台' }}</div>
      <div class="left-logo" v-if="userStore.platformType == 3">
        {{ settingStore.isCollapse ? 'WHP' : userStore.defaultSet['8002_DLYM_BJ'] || '危化品平台' }}</div>
      <!-- :default-active="decodeURIComponent($route.path)" 和index唯一标识进行匹配,并禁止中文字符被编码  -->
      <el-menu :default-active="decodeURIComponent($route.path)" router :collapse="settingStore.isCollapse"
        :collapse-transition="false" unique-opened>
        <MenuItemContainer :data="userStore.menu"></MenuItemContainer>
      </el-menu>
    </el-aside>

    <el-aside v-if="isMobile && showDiyElement" class="overlay-aside">
      <!-- 左侧菜单(手机) -->

      <!-- 遮罩层 -->
      <div class="diy-element">
        <div v-if="showDiyElement" class="overlay" @click="hideDiyElement"></div>
      </div>
      <el-scrollbar class="overlay-body">
        <el-menu style="padding-bottom: 60px;" :collapse-transition="false"
          :default-active="decodeURIComponent($route.path)" router :collapse="settingStore.isCollapse" unique-opened>
          <MenuItemContainer :data="userStore.menu" :isCollapse="settingStore.isCollapse"></MenuItemContainer>
        </el-menu>
      </el-scrollbar>
    </el-aside>
    <el-container style="height: 100%;">
      <!-- 顶部 -->
      <el-header class="el-header-one">
        <div class="header-top">
          <div>
            <!-- 面包屑 -->
            <el-scrollbar>
              <div class="header-left">
                <el-icon ref="mySwitch" class="header-item header-switch" v-if="settingStore.isCollapse"
                  @click="clickCrumb(false)">
                  <Expand />
                </el-icon>
                <el-icon ref="mySwitch" class="header-item header-switch" v-if="!settingStore.isCollapse"
                  @click="clickCrumb(true)">
                  <Fold />
                </el-icon>
                <el-breadcrumb separator="/" class="header-item">
                  <el-breadcrumb-item v-for="(item, index) in getBreadcrumb(router.currentRoute)" :key="index"><span
                      class="header-nav-title">{{ item }}</span></el-breadcrumb-item>
                </el-breadcrumb>
              </div>
            </el-scrollbar>
          </div>
          <!-- 个人设置 -->
          <div class="header-right">
            <ul class="header-nav">
              <li v-if="userStore.platformType == 1" class="layout-navbars-breadcrumb-user-icon"
                @click="handlePage('/')">
                <el-tooltip content="首页" placement="bottom" :auto-close="3000">
                  <SvgIcon name="shouye" class="svg-icon" color="var(--next-bg-topBarColor)"></SvgIcon>
                </el-tooltip>
              </li>
              <li class="layout-navbars-breadcrumb-user-icon">
                <Theme></Theme>
              </li>
              <!-- <li class="layout-navbars-breadcrumb-user-icon" @click="handlePage('/information')">
                <el-tooltip content="资讯" placement="bottom" :auto-close="3000">
                  <SvgIcon name="zixun" class="svg-icon" color="var(--next-bg-topBarColor)"></SvgIcon>
                </el-tooltip>
              </li> -->
            </ul>
            <el-dropdown placement="bottom-end">
              <span class="el-dropdown__box"
                style="display: flex; align-items: center; color: var(--next-bg-topBarColor);">
                <span style="padding-right: 10px;cursor: default;">{{ userStore.userInfo.Name }}</span>
                <el-avatar :src="userStore.userInfo.HeadPortrait || avatar" />
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :icon="User" @click="handleCommand('/user/my/useredit')">个人信息</el-dropdown-item>
                  <el-dropdown-item :icon="Crop" v-if="!isPass"
                    @click="handleCommand('/user/my/changepass')">修改密码</el-dropdown-item>
                  <el-dropdown-item :icon="SwitchButton" @click="handleCommand('logout')">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>
      <el-header class="el-header-two">
        <div class="tags">
          <!-- 窗口列表 -->
          <el-scrollbar style="padding-bottom: 5px;">
            <div class="tags-left">
              <span :class="{ 'active': tag.active, 'tags-item': true }" v-for="(tag) in userStore.tagsList"
                :key="tag.path" @click="tagPush(tag)" class="tags-view-item">
                <span style="padding: 0 10px;">{{ tag.title }}</span>
                <el-icon class="el-icon-close" @click.prevent.stop="tagRemove(tag)"
                  v-if="tag.path !== '/approval/home/<USER>' && tag.path !== '/uniform/home/<USER>'">
                </el-icon>
              </span>
            </div>
          </el-scrollbar>
          <!-- 关闭按钮 -->
          <div style="margin-left: 5px;margin-top: 3px;">
            <el-dropdown @command="handleNavCommand">
              <el-button type="primary" plain><el-icon class="el-icon--right"><arrow-down /></el-icon></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="other">关闭其他</el-dropdown-item>
                  <el-dropdown-item command="all">关闭所有</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>
      <!-- 内容 -->
      <el-main style="padding: 10px;margin: 5px;border: 1px solid #f0f0f0; background-color: #ffffff;">
        <router-view v-slot="{ Component, route }">
          <keep-alive :include="includeList">
            <component :is="Component" :key="route.path" />
          </keep-alive>
        </router-view>
      </el-main>
      <el-footer>
        <Footer></Footer>
      </el-footer>
    </el-container>
  </el-container>
</template>
<style lang="scss" scoped>
// .fa {
//   vertical-align: baseline;
//   margin-right: 10px;
// }

// .el-menu--collapse i {
//   font-size: 20px !important;
// }

// .el-menu--collapse span,
// .el-menu--collapse .el-sub-menu__icon-arrow {
//   display: none !important;
// }


.layout-container {
  // color: var(--next-bg-menuBarColor);

  .el-aside {
    background-color: var(--next-bg-menuBar);


    .left-logo {
      height: 70px;
      // line-height: 70px;
      padding: 0 10px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--next-bg-menuBarColor);
      font-size: 16px;
      text-wrap: balance;
      /* 现代浏览器的文本平衡功能 */
      hyphens: auto;
    }

    .el-menu {
      border-right: none;
      --el-menu-bg-color: var(--next-bg-menuBar);
      --el-menu-text-color: var(--next-bg-menuBarColor);
      --el-menu-active-color: var(--el-color-primary);
    }


    :deep(.el-menu-item.is-active) {
      background-color: var(--next-bg-menuBarActiveColor) !important;
    }

    :deep(.el-sub-menu__title:hover) {
      background-color: var(--next-bg-menuBarActiveColor) !important;
    }

    :deep(.el-menu-item:hover) {
      background-color: var(--next-bg-menuBarActiveColor) !important;
    }

  }

  .el-header-one {
    background-color: var(--next-bg-topBar);
    border-bottom: 1px solid #f1f2f3;
    // border-left: 1px #606266 solid;
    align-items: center;
    justify-content: space-between;
  }

  .el-header-two {
    // background-color: var(--next-bg-menuBar);
    padding: 0px;
    margin: 0px;
    height: 40px;
  }

  .el-footer {
    max-width: 1100px;
    margin: 0 auto;
    font-size: 14px;
    color: #666;
  }

  .header-top {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;

    .header-left {
      display: flex;

      .header-switch {
        color: var(--next-bg-topBarColor);
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
      }

      .header-item {

        line-height: 40px;

        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;

      }
    }

  }
}


:deep(.el-scrollbar__view) {
  margin-top: 3px;
}

.tags {
  height: 40px;
  line-height: 40px;
  // border: 1px solid #f0f0f0;
  // background: #f0f0f0;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  background: var(--next-background-color-white);
  border: 1px solid var(--next-background-color-white);
}


.tags .tags-view-item {
  // border: 1px solid #d8dce5;
  // border: 1px solid var(--el-color-primary-light-9);

  // background: var(--next-background-color-white);
  display: inline-block;
  height: 32px;
  line-height: 32px;
  // padding: 0 8px;
  padding: 0 20px;
  font-size: 12px;
  // margin-left: 5px;
  // margin-top: 4px;
  margin: 0 -10px;
  cursor: pointer;



}

.tags .tags-view-item {
  // padding: 0 20px;
  -webkit-mask-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAiIGhlaWdodD0iNzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbD0ibm9uZSI+CgogPGc+CiAgPHRpdGxlPkxheWVyIDE8L3RpdGxlPgogIDxwYXRoIHRyYW5zZm9ybT0icm90YXRlKC0wLjEzMzUwNiA1MC4xMTkyIDUwKSIgaWQ9InN2Z18xIiBkPSJtMTAwLjExOTE5LDEwMGMtNTUuMjI4LDAgLTEwMCwtNDQuNzcyIC0xMDAsLTEwMGwwLDEwMGwxMDAsMHoiIG9wYWNpdHk9InVuZGVmaW5lZCIgc3Ryb2tlPSJudWxsIiBmaWxsPSIjRjhFQUU3Ii8+CiAgPHBhdGggZD0ibS0wLjYzNzY2LDcuMzEyMjhjMC4xMTkxOSwwIDAuMjE3MzcsMC4wNTc5NiAwLjQ3Njc2LDAuMTE5MTljMC4yMzIsMC4wNTQ3NyAwLjI3MzI5LDAuMDM0OTEgMC4zNTc1NywwLjExOTE5YzAuMDg0MjgsMC4wODQyOCAwLjM1NzU3LDAgMC40NzY3NiwwbDAuMTE5MTksMGwwLjIzODM4LDAiIGlkPSJzdmdfMiIgc3Ryb2tlPSJudWxsIiBmaWxsPSJub25lIi8+CiAgPHBhdGggZD0ibTI4LjkyMTM0LDY5LjA1MjQ0YzAsMC4xMTkxOSAwLDAuMjM4MzggMCwwLjM1NzU3bDAsMC4xMTkxOWwwLDAuMTE5MTkiIGlkPSJzdmdfMyIgc3Ryb2tlPSJudWxsIiBmaWxsPSJub25lIi8+CiAgPHJlY3QgaWQ9InN2Z180IiBoZWlnaHQ9IjAiIHdpZHRoPSIxLjMxMTA4IiB5PSI2LjgzNTUyIiB4PSItMC4wNDE3MSIgc3Ryb2tlPSJudWxsIiBmaWxsPSJub25lIi8+CiAgPHJlY3QgaWQ9InN2Z181IiBoZWlnaHQ9IjEuNzg3ODQiIHdpZHRoPSIwLjExOTE5IiB5PSI2OC40NTY1IiB4PSIyOC45MjEzNCIgc3Ryb2tlPSJudWxsIiBmaWxsPSJub25lIi8+CiAgPHJlY3QgaWQ9InN2Z182IiBoZWlnaHQ9IjQuODg2NzciIHdpZHRoPSIxOS4wNzAzMiIgeT0iNTEuMjkzMjEiIHg9IjM2LjY2ODY2IiBzdHJva2U9Im51bGwiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+'),
    url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzAiIGhlaWdodD0iNzAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbD0ibm9uZSI+CiA8Zz4KICA8dGl0bGU+TGF5ZXIgMTwvdGl0bGU+CiAgPHBhdGggdHJhbnNmb3JtPSJyb3RhdGUoLTg5Ljc2MjQgNy4zMzAxNCA1NS4xMjUyKSIgc3Ryb2tlPSJudWxsIiBpZD0ic3ZnXzEiIGZpbGw9IiNGOEVBRTciIGQ9Im02Mi41NzQ0OSwxMTcuNTIwODZjLTU1LjIyOCwwIC0xMDAsLTQ0Ljc3MiAtMTAwLC0xMDBsMCwxMDBsMTAwLDB6IiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgogIDxwYXRoIGQ9Im0tMC42Mzc2Niw3LjMxMjI4YzAuMTE5MTksMCAwLjIxNzM3LDAuMDU3OTYgMC40NzY3NiwwLjExOTE5YzAuMjMyLDAuMDU0NzcgMC4yNzMyOSwwLjAzNDkxIDAuMzU3NTcsMC4xMTkxOWMwLjA4NDI4LDAuMDg0MjggMC4zNTc1NywwIDAuNDc2NzYsMGwwLjExOTE5LDBsMC4yMzgzOCwwIiBpZD0ic3ZnXzIiIHN0cm9rZT0ibnVsbCIgZmlsbD0ibm9uZSIvPgogIDxwYXRoIGQ9Im0yOC45MjEzNCw2OS4wNTI0NGMwLDAuMTE5MTkgMCwwLjIzODM4IDAsMC4zNTc1N2wwLDAuMTE5MTlsMCwwLjExOTE5IiBpZD0ic3ZnXzMiIHN0cm9rZT0ibnVsbCIgZmlsbD0ibm9uZSIvPgogIDxyZWN0IGlkPSJzdmdfNCIgaGVpZ2h0PSIwIiB3aWR0aD0iMS4zMTEwOCIgeT0iNi44MzU1MiIgeD0iLTAuMDQxNzEiIHN0cm9rZT0ibnVsbCIgZmlsbD0ibm9uZSIvPgogIDxyZWN0IGlkPSJzdmdfNSIgaGVpZ2h0PSIxLjc4Nzg0IiB3aWR0aD0iMC4xMTkxOSIgeT0iNjguNDU2NSIgeD0iMjguOTIxMzQiIHN0cm9rZT0ibnVsbCIgZmlsbD0ibm9uZSIvPgogIDxyZWN0IGlkPSJzdmdfNiIgaGVpZ2h0PSI0Ljg4Njc3IiB3aWR0aD0iMTkuMDcwMzIiIHk9IjUxLjI5MzIxIiB4PSIzNi42Njg2NiIgc3Ryb2tlPSJudWxsIiBmaWxsPSJub25lIi8+CiA8L2c+Cjwvc3ZnPg=='),
    url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'><rect rx='8' width='100%' height='100%' fill='%23F8EAE7'/></svg>");
  -webkit-mask-size: 18px 30px, 20px 30px, calc(100% - 30px) calc(100% + 17px);
  -webkit-mask-position: right bottom, left bottom, center top;
  -webkit-mask-repeat: no-repeat;
}



.tags .tags-view-item.active::before {
  content: "";
  background: var(--el-color-primary);
  // color: var(--el-color-primary) !important;
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  position: relative;
  margin-right: 2px;
}

.tags .tags-view-item.active {
  color: var(--el-color-primary) !important;
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-9);
}

.tags .tags-view-item:hover {
  color: var(--el-color-primary) !important;
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-9);
}

.tags .tags-view-item .el-icon-close {
  font-size: 10px;
}

.tags .tags-view-item .el-icon-close:hover {
  background-color: #ef2b74;
  color: #fff;
  border-radius: 20px;
}

.tags {
  .tags-left {
    display: flex;

    .tags-item {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.diy-element {
  position: fixed;
  z-index: 9999;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

.overlay-aside {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 200px;
}

.overlay-body {
  position: fixed;
  width: 200px;
  z-index: 99999;
  height: 100vh;
  background-color: #2f3e52;
}

.el-aside>ul {
  height: 90%;
  overflow-y: auto;
}

.header-right {
  display: flex;

  align-items: center;

  .header-nav {
    margin: 0;
    width: 80px;
    height: 50px;
    display: flex;
    align-items: center;
    color: var(--next-bg-topBarColor);
    font-size: 14px;
    margin-right: 20px;

    li {
      // background-color: #2f3e52;
      // border-right: 1px solid #fff;
      width: 100px;
      height: 100%;
      text-align: center;
      list-style-type: none;
      line-height: 55px;
    }

    li:hover {
      background-color: rgba(0, 0, 0, 0.1);
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }


}

.el-tooltip__trigger:focus {
  outline: none; // unset 这个也行
}

.el-header.el-header-one {
  height: 50px;

  .header-top {
    padding: 0 !important;
  }

  .header-left {
    height: 50px;
    align-items: center;
  }

  .el-icon.header-item.header-switch {
    line-height: 50px;
    margin-top: 0;
  }

}

.el-scrollbar .header-nav-title {
  color: var(--next-bg-topBarColor);
}
</style>
