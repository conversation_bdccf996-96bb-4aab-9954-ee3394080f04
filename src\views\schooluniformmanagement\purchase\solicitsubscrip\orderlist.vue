<script setup>
defineOptions({
  name: 'solicitsubscriporderlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search
} from '@element-plus/icons-vue'
import {
  purchaseGeteditorderpaged
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
// 二维码下载
import QRCode from 'qrcode';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const SupplierList = ref([])
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])

//加载数据
onMounted(() => {
  yearDateList.value = previousYearDate()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

// 生成/修改征订单
const HandleEdit = (row, title) => {
  router.push({ path: "./orderedit", query: { id: row.Id, statuz: row.SubscriptionStatuz, title: title } })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  purchaseGeteditorderpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        SupplierList.value = other.SupplierList || [];//供应商
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.PurchaseYear = undefined
  filters.value.CompanyId = undefined
  filters.value.Name = undefined
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }

// //下载二维码图片
const qrCodeDataUrl = ref(null);

const generateQRCode = async (text, size) => {
  try {
    qrCodeDataUrl.value = await QRCode.toDataURL(text, { scale: size / 100 });
  } catch (error) {
    console.error('生成二维码时出错:', error);
  }
};

const downloadQRCode = (rows) => {
  let url = window.location.protocol + '//' + window.location.host
  let path = url + "/ui/#" + "/pages/purchase/order?id=" + rows.Id;//二维码信息
  let title = '校服征订' + rows.PurchaseNo + '.png'
  generateQRCode(path, 400).then(() => {
    // 创建一个临时的 <a> 元素来触发下载
    const a = document.createElement('a');
    a.href = qrCodeDataUrl.value;
    a.download = title;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  });
};
</script>
<template>
  <div class="viewContainer">
    <el-collapse>
      <el-collapse-item>
        <template #title>
          操作提示 &nbsp;
          <el-icon color="#E6A23C" :size="16">
            <QuestionFilled />
          </el-icon>
        </template>
        <ol class="rowFill">
          <li> 点击【生成】按钮，生成并发起校服征订。 </li>
        </ol>
      </el-collapse-item>
    </el-collapse>
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.CompanyId" clearable filterable placeholder="供应商" @change="filtersChange"
              style="width: 240px">
              <el-option v-for="item in SupplierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="合同批次" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="PurchaseYear" label="年度" min-width="100" align="center"></el-table-column>
      <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
      <el-table-column prop="OrderNum" label="需订购人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="OrderedNum" label="已订购人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="IsContractRenewal" label="是否续签合同" min-width="110" align="center">
        <template #default="{ row }">
          {{ row.IsContractRenewal == 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="ContractEndDate" label="合同终止时间" min-width="120" align="center">
        <template #default="{ row }">
          <span v-if="!row.ContractEndDate">--</span>
          <span v-else-if="new Date(row.ContractEndDate).setHours(23, 59, 59, 0) < new Date()"
            style="color: #F56C6C;">{{
              row.ContractEndDate.substring(0, 10) }}</span>
          <span v-else>{{ row.ContractEndDate.substring(0, 10) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="SubscriptionDeadline" label="征订截止时间" min-width="120" align="center">
        <template #default="{ row }">
          <span v-if="!row.SubscriptionDeadline">--</span>
          <span v-else-if="new Date(row.SubscriptionDeadline).setHours(23, 59, 59, 0) < new Date()"
            style="color: #F56C6C;">{{
              row.SubscriptionDeadline.substring(0, 10) }}</span>
          <span v-else>{{ row.SubscriptionDeadline.substring(0, 10) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="SupplierName" label="供应商" min-width="200"></el-table-column>
      <el-table-column label="征订二维码" min-width="100" align="center">
        <template #default="{ row }">
          <el-button
            v-if="row.SubscriptionStatuz == 10 && new Date(row.SubscriptionDeadline).setHours(23, 59, 59, 0) > new Date()"
            type="primary" link @click="downloadQRCode(row)">下载</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="征订单管理" min-width="100" align="center">
        <template #default="{ row }">
          <!-- SubscriptionStatuz  征订状态（0：填报中 10：正在征订 100：征订结束） -->
          <el-button v-if="row.SubscriptionStatuz == 0" type="primary" link
            @click="HandleEdit(row, '生成征订单')">生成</el-button>
          <el-button
            v-else-if="row.SubscriptionStatuz == 10 && new Date(row.SubscriptionDeadline).setHours(23, 59, 59, 0) > new Date()"
            type="primary" link @click="HandleEdit(row, '修改征订单')">修改</el-button>
          <el-button v-else type="primary" link @click="HandleEdit(row, '查看征订单')">查看</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>