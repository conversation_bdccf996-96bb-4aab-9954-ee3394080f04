import { createApp } from 'vue'

import App from '@/App.vue'
import router from '@/router'
// import router, { setupRouter } from '@/router'
// 引入svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon/index.vue'
import print from 'vue3-print-nb' // 打印
const app = createApp(App)

// 自定义指令：流程配置画布拖拽
app.directive('flowDrag', {
  mounted(el, binding) {
    if (!binding) {
      return
    }
    el.onmousedown = (e) => {
      // console.log('flowDrag', e.button)
      if (e.button === 2) {
        // 右键不管
        return
      }
      let disX = e.clientX
      let disY = e.clientY
      el.style.cursor = 'move'

      const onMouseMove = (e) => {
        e.preventDefault()
        const left = e.clientX - disX
        disX = e.clientX
        el.scrollLeft += -left

        const top = e.clientY - disY
        disY = e.clientY
        el.scrollTop += -top
      }

      const onMouseUp = () => {
        el.style.cursor = 'auto'
        document.removeEventListener('mousemove', onMouseMove)
        document.removeEventListener('mouseup', onMouseUp)
      }

      document.addEventListener('mousemove', onMouseMove)
      document.addEventListener('mouseup', onMouseUp)
    }
  }
})

// loadConfig() // 在应用启动时加载配置
// 引入持久化
import pinia from '@/stores'
// 使用持久化
app.use(pinia)
// 网页刷新后动态路由添加
// (此处顺序必须在app.use(router)之前 app.use(pinia)之后)
import { addDynamicRoutes } from '@/router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
if (userStore.token && userStore.menu) {
  // console.info("动态路由添加")
  addDynamicRoutes(userStore.menu)
}
// 路由
app.use(router)
app.use(print) // 打印
// // 设置 favicon 图标路径/标题

// if (userStore.platformType === 1) {
//   setFavicon('/xfptlogo.png')
// } else if (userStore.platformType === 2) {
//   setFavicon('/collaraudit.png')
// }else if (userStore.platformType === 3) {
//   setFavicon('/collaraudit.png')
// }
// // 动态设置 favicon 的函数
// function setFavicon(href) {
//   const link = document.querySelector("link[rel~='icon']")
//   if (!link) {
//     link = document.createElement('link')
//     link.rel = 'icon'
//     document.getElementsByTagName('head')[0].appendChild(link)
//   }
//   link.href = href
// }

// 引入全局样式
import '@/assets/css/main.scss'
// 引入awesome图标样式
import 'font-awesome/css/font-awesome.min.css'

// elementPlus引入
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './assets/element-icon/icon.css'
import '@/theme/index.scss'

// 使用elementPlus引入
app.use(ElementPlus)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.component('SvgIcon', SvgIcon)

app.mount('#app')
// // 3. 设置路由
// setupRouter().then(() => {
//   // 4. 挂载应用
//   app.mount('#app')
// })
