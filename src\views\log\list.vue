<script setup>
// defineOptions({
//     name: 'dangerchemicalstrainsafeeducationlist'
// });
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { HistoryTraceGetPaged, HistoryTraceGetById } from '@/api/tasks.js'
import { AttachmentUpload } from '@/api/user.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, tagsListStore } from "@/utils/index.js";//上传附件
import { ElMessageBox, ElMessage } from 'element-plus';
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({LogType:1, pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "DateTime", SortType: "DESC" }] })

const defaultTime = ref('')
const defaultendTime = ref('')
//加载数据
onMounted(() => {
    // const today = new Date();
    // const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
    // const nedOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);



    // const today = new Date();
    // defaultTime.value = new Date(today.getFullYear(), today.getMonth()-1, today.getDate(), 0, 0, 0);
    // defaultendTime.value= new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
    // filters.value.BeginTime = defaultTime.value
    // filters.value.EndTime = defaultendTime.value
    HandleTableData();
})
// onActivated(() => {
//     tagsListStore(userStore.tagsList, route)

//     nextTick(() => {
//          HandleTableData();
//     })
// })

const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}

const dialogData = ref({})
// 修改弹出窗体
const HandleEdit = (row, e) => {
    HistoryTraceGetById({ Id: row.Id,TableName:row.TableName }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows
          
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 列表
const HandleTableData = () => {
    HistoryTraceGetPaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows,total } = res.data.data
            tableData.value = rows
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
} // 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.BeginTime = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    filters.value.EndTime = new Date();
    filters.value.Message = undefined;
    // filters.value.Trainees = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!filters.value.EndTime) return false;
    return time >= new Date(filters.value.EndTime);
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginTime) return false;
    return time < new Date(filters.value.BeginTime);
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="日志类型" class="flexItem">
                        <el-radio-group v-model="filters.LogType">
                            <el-radio :value="1" label="全局信息日志"> </el-radio>
                            <el-radio :value="2" label="全局预警日志"> </el-radio>
                            <el-radio :value="3" label="全局错误日志"> </el-radio>
                            <el-radio :value="4" label="审核Sql日志"> </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginTime" type="date" format="YYYY-MM-DD" 
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="开始时间" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="filters.EndTime" type="date" format="YYYY-MM-DD" 
                            value-format="YYYY-MM-DD" :disabled-date="disabledEndDate" clearable placeholder="截止时间" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Message" placeholder="信息关键字" style="width: 180px">
                        </el-input>
                    </el-form-item>
                     <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.MessageTemplate" placeholder="信息模版关键字" style="width: 180px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="DateTime" label="时间" min-width="140"></el-table-column>
            <el-table-column prop="Level" label="级别" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Message" label="信息" min-width="140"></el-table-column>
            <el-table-column prop="MessageTemplate" label="信息模版" min-width="140"></el-table-column>
            <el-table-column fixed="right" label="详情" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box :width="680" :lazy="true" title="信息">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px" status-icon>
                    <el-form-item label="实施地点：" prop="Address">
                        <el-input :disabled="SchoolId > 0" v-model="dialogData.Address" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button v-if="SchoolId == 0" type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>