<script setup>
defineOptions({
    name: 'businessunitauthority'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import {
    GetUnitPermissionInfo, SetUnitPermission
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页   
// 初始化 
const route = useRoute()
const navTabs = ref([])
const tableData = ref([])
const refTable = ref()
const tableTotal = ref(0)
const filters = ref({ pageIndex: 1, pageSize: 10 })
const processId = ref('')//流程ID 
const activeName = ref('')
const msg = ref('')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        GetUnitPermissionInfoUser(true)
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            GetUnitPermissionInfoUser(true)
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 获取单位分组设置列表
const GetUnitPermissionInfoUser = (isFirst, process) => {
    filters.value.isFirst = isFirst
    filters.value.ProcessId = process
    GetUnitPermissionInfo(filters.value).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            if (isFirst) {
                navTabs.value = other.listProcess || []
                if (navTabs.value.length > 0) {
                    activeName.value = navTabs.value[0].Id
                    processId.value = navTabs.value[0].Id
                    msg.value = ''
                } else {
                    msg.value = '经过系统分析， 无需您配置此功能'
                }
            }
            tableData.value = rows.data || []
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    GetUnitPermissionInfoUser(undefined, processId.value)
}
// 重置
const HandleReset = () => {
    filters.value.Name = undefined
    filters.value.pageIndex = 1
    GetUnitPermissionInfoUser(undefined, processId.value)
}
// 分页
const handlePage = (val) => {
    GetUnitPermissionInfoUser(undefined, processId.value)
}

// 切换tab
const handleClick = (tab) => {
    // console.log(tab, tab.props.name, tab.index)
    filters.value.pageIndex = 1
    processId.value = navTabs.value[Number(tab.index)].Id
    GetUnitPermissionInfoUser(undefined, processId.value)
}
// 修改状态
const HandleSwitchChange = (e, row) => {
    SetUnitPermission({ ProcessId: processId.value, UnitId: row.UnitId, Statuz: e }).then((res) => {
        if (res.data.flag == 1) {
            if (e == 1) {
                ElMessage.success(res.data.msg || '启用成功')
            } else {
                ElMessage.success(res.data.msg || '禁用成功')
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
</script>
<template>
    <div v-if="navTabs.length > 0">
        <el-row>
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="单位名称" style="width: 240px"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-tabs v-model="activeName" type="border-card" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane v-for="item in navTabs" :key="item.Id" :label="item.ProcessName" :name="item.Id">
                <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
                    header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
                    <!-- <el-table-column type="selection" width="50"></el-table-column> -->
                    <el-table-column prop="UnitName" label="单位名称" width="400" show-overflow-tooltip></el-table-column>
                    <el-table-column label="状态" width="300" align="center">
                        <template #default="{ row }">
                            <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="2"
                                style="--el-switch-off-color: #ff4949" inline-prompt active-text="启" inactive-text="禁"
                                @change="HandleSwitchChange($event, row)" />
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
        </el-tabs>
    </div>
    <div v-else class="emptyMsg">
        <span> {{ msg }} </span>
    </div>
</template>
<style lang="scss" scoped></style>
