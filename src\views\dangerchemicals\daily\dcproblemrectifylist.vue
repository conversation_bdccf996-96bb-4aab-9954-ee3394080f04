<script setup>
defineOptions({
    name: 'dangerchemicalsdailydcproblemrectifylist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy
} from '@element-plus/icons-vue'
import {
    ProblemGovernRectifyListFind, DcGovernRectifyGetById, GovernDeclareDetailGetById
} from '@/api/daily.js'
import { GetDictionaryCombox } from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import AppBox from "@/components/Approve/AppBox.vue";
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const taskId = ref(0)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const dialogData = ref({})
const uploadRectifyFileData = ref([])
const uploadCheckFileData = ref([])
const measures = ref('')//整改措施
const remark = ref('')//备注
const suggest = ref('')//整改建议

//加载数据
onMounted(() => {

    taskId.value = route.query.taskId
    if (route.query.isTagRouter) {
        HandleTableData()
        DccatalogTypeGet()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    taskId.value = route.query.taskId
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            DccatalogTypeGet()
        }
    })
})

// 列表
const HandleTableData = () => {
    filters.value.GovernTaskId = taskId.value
    filters.value.TaskStatuz = 1
    ProblemGovernRectifyListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const filtersKey = ref('')

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = undefined
    filters.value.CategoryId = undefined
    filters.value.Nature = undefined
    filters.value.Grade = undefined
    filters.value.Statuz = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

const catalogList = ref([])
const natureOptions = ref([{ Id: 1, Name: '问题', }, { Id: 2, Name: '隐患', }])
const gradeOptions = ref([{ Id: 1, Name: '一般', }, { Id: 2, Name: '较大', }, { Id: 3, Name: '重大', }])
const isTallyClaimOptions = ref([{ Id: 0, Name: '待整改' }, { Id: 1, Name: '已整改' }])

const filtersChange = (row) => {
    HandleTableData()
}

// 危化品分类
const DccatalogTypeGet = () => {
    GetDictionaryCombox({ TypeCode: "10000" }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            catalogList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const rectifyVisible = ref(false)
const checkVisible = ref(false)

//整改查看
const btnRectify = (param) => {
    rectifyVisible.value = true

    DcGovernRectifyGetById({ id: param.Id, opttype: 2 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, footer } = res.data.data
            measures.value = rows.Measures
            uploadRectifyFileData.value = footer
            fileListDownload.value = footer
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//检查查看
const btnCheck = (param) => {
    checkVisible.value = true
    DcGovernRectifyGetById({ id: param.Id, opttype: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, footer } = res.data.data
            remark.value = rows.Memo
            suggest.value = rows.Suggest
            uploadCheckFileData.value = footer

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }

}
const HandlePrint = () => {
    router.push({
        path: "./problemrecordprint",
        query: {
            GovernTaskId: route.query.taskId,
            AreaId: filters.value.CountyId,
            CategoryId: filters.value.CategoryId,
            Nature: filters.value.Nature,
            Grade: filters.value.Grade,
            Statuz: filters.value.Statuz,
            SchoolName: filters.value.SchoolName,
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="HandlePrint">打印报表</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.CategoryId" clearable placeholder="类别" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in catalogList" :key="item.DicValue" :label="item.DicName"
                                :value="item.DicValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Nature" clearable placeholder="性质" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in natureOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Grade" clearable placeholder="危险等级" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in gradeOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="整改状态" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in isTallyClaimOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="问题隐患清单" style="width: 180px"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>

                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="SchoolName" label="学校名称" min-width="150" show-overflow-tooltip></el-table-column>
            <el-table-column prop="CategoryName" label="类别" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Name" label="问题隐患清单" min-width="300"></el-table-column>
            <el-table-column prop="Nature" label="性质" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.Nature == 1 ? '问题' : row.Nature == 2 ? '隐患' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Grade" label="危险等级" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Grade == 1 ? '一般' : row.Grade == 2 ? '较大' : row.Grade == 3 ? '重大' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CheckingDate" label="检查时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RectifyLimit" label="整改期限" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.RectifyDate == null"
                        :style="new Date(row.RectifyLimit) < new Date() ? { color: 'red' } : {}">
                        {{ row.RectifyLimit ? row.RectifyLimit.substring(0, 10) : '--' }}
                    </span>
                    <span v-else
                        :style="new Date(row.RectifyLimit) < new Date(row.RectifyDate) ? { color: 'red' } : {}">
                        {{ row.RectifyLimit ? row.RectifyLimit.substring(0, 10) : '--' }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="RectifyDate" label="整改日期" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RectifyDate ? row.RectifyDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="整改状态" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.Statuz == 1 ? '已整改' : '待整改' }}
                </template>
            </el-table-column>
            <el-table-column prop="IsRectify" label="存在问题隐患" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsTallyClaim != 0" style="color:red;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="opt1" fixed="right" label="整改情况" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="btnRectify(row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column prop="opt2" fixed="right" label="检查说明" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="btnCheck(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
    <!--整改情况查看-->
    <app-box v-model="rectifyVisible" :width="680" :lazy="true" title="查看">
        <template #content>
            <el-form @submit.prevent ref="refForm" :model="dialogData" label-width="200px" status-icon>

                <el-form-item label="整改措施：">
                    <span>{{ measures }}</span>
                </el-form-item>
                <el-form-item>
                    <template #label>
                        <span> 附件：</span>
                    </template>
                    <div class="fileFlex">
                        <div v-for="(itemCate) in uploadRectifyFileData" :key="itemCate.Id"
                            style="color:#409EFF ;width:200px">
                            <span style="cursor: pointer;" @click="fileListDownload(itemCate, uploadRectifyFileData)">
                                {{ itemCate.Title }}
                            </span>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="rectifyVisible = false">取消</el-button>
            </span>
        </template>
    </app-box>
    <!--检查说明查看-->
    <app-box v-model="checkVisible" :width="680" :lazy="true" title="查看">
        <template #content>
            <el-form @submit.prevent ref="refForm" :model="dialogData" label-width="200px" status-icon>
                <el-form-item label="备注：">
                    <span>{{ remark == null ? '--' : remark }}</span>
                </el-form-item>
                <el-form-item>
                    <template #label>
                        <span> 附件：</span>
                    </template>
                    <div class="fileFlex">
                        <div v-for="(itemCate) in uploadCheckFileData" :key="itemCate.Id"
                            style="color:#409EFF ;width:200px">

                            <span style="cursor: pointer;" @click="fileListDownload(itemCate, uploadCheckFileData)">
                                {{ itemCate.Title }}
                            </span>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="整改建议：">
                    <span>{{ suggest == null ? '--' : suggest }}</span>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="checkVisible = false">取消</el-button>
            </span>
        </template>
    </app-box>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>