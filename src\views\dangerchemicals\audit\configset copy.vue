<script setup>
defineOptions({
    name: 'dangerchemicalsaudit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
// import {

// } from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
// import {  } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
}) 
</script>
<template>
    <div class="viewContainer">
    </div>
</template>
<style lang="scss" scoped></style>