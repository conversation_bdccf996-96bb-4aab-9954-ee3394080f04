<script setup>
import { onMounted, ref, watch } from 'vue'
import {
    QuestionFilled, Refresh, Search, UploadFilled, EditPen
} from '@element-plus/icons-vue'
import {
    Multaccountfind,
    Multaccountdelbyid,
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { getYearMonthDay, integerLimit } from "@/utils/index.js";
import router from '@/router'
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const selectRows = ref([])
const filtersKey = ref({ key: '', date: '', value: 'LoginName' })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const options = ref([
    { value: 'LoginName', label: '账号', },
    { value: 'UserName', label: '姓名', }
])
//新增&编辑操作
const dialogVisible = ref(false)
const dialogData = ref({ Type: '1' })
const refForm = ref()
const ruleForm = {
    LoginName: [
        { required: true, message: '账号不能为空', trigger: 'change' },
    ],
    Pwd: [
        { required: true, message: '密码不能为空', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    HandleSearch()
})

//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
// 申请
const HandleApply = (row) => {
    dialogVisible.value = true
}
// 下载
const HandleDownload = (row) => {
}
// 提交

//组织修改 提交
const HandleSubmit = () => {


}
// 调换起止日期
const HandleDateSearch = (date) => {

    let date1 = new Date(date[0])
    let date2 = new Date(date[1])

    let newDate = getYearMonthDay(date1)
    let oldDate = getYearMonthDay(date2)
    console.log("开始日期date", newDate, "截止日期date", oldDate)
}

//搜索
const HandleSearch = (page) => {

    if (page) filters.value.pageIndex = page

    Multaccountfind(filters.value).then(res => {
        if (res.data.flag == 1) {
            tableData.value = res.data.data.rows;
            tableTotal.value = Number(res.data.data.total)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 重置
const HandleReset = (page) => {
    filtersKey.value.key = ''
    HandleSearch(page)
}

// 分页
const handlePage = (val) => {
    HandleSearch()
}


</script>
<template>
    <div class="viewContainer">
        <!-- 搜索 -->
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filtersKey" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="EditPen" @click="HandleApply">申请</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filtersKey.value" placeholder="请输入搜索关键词"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch(1)">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset(1)">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <!-- 内容 -->
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="LoginName" label="申请时间" min-width="100"></el-table-column>
            <el-table-column prop="RegTime" label="申请数量" min-width="180" align="center"></el-table-column>
            <el-table-column prop="RegTime" label="起始编码" min-width="180" align="center"></el-table-column>
            <el-table-column prop="RegTime" label="结束编码" min-width="120" align="center"></el-table-column>
            <el-table-column prop="RegTime" label="状态" min-width="120" align="center"></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDownload(row)">下载</el-button>
                </template>

            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <!-- 分页 -->
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />

        <!-- 弹窗 -->
        <el-dialog v-model="dialogVisible" title="申请二维码" width="680px">

            <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                status-icon>
                <el-form-item label="调换时间：" class="flexItem">
                    <el-date-picker v-model="dialogData.date" type="daterange" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" range-separator="至" start-placeholder="起始日期" end-placeholder="截止日期"
                        style="width: 260px" @change="HandleDateSearch" />
                </el-form-item>
                <el-form-item label="申请数量：" prop="EmployeeNum">
                    <el-input v-model="dialogData.EmployeeNum" auto-complete="off"
                        @input="integerLimitInput($event, 'EmployeeNum')" style="width: 50%;"></el-input>
                </el-form-item>

            </el-form>


            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit">
                        提交
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>

</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>