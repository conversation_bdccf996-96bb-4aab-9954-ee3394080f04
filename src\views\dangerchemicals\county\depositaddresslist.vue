<script setup>
defineOptions({
    name: 'dangerchemicalscountydepositaddresslist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen, Back
} from '@element-plus/icons-vue'
import {
    DcDepositAddressListFind, Punitgetschoolbycountyid
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const schoolList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })

//加载数据
onMounted(() => {
    HandleTableData()
    PunitgetschoolbycountyidUser()
    if (route.query.isTagRouter) {

    }

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {

        }
    })
})
// 列表
const HandleTableData = () => {
    DcDepositAddressListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.UnitId = undefined
    filters.value.Address = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 根据区县获取学校
const PunitgetschoolbycountyidUser = () => {
    Punitgetschoolbycountyid({ CountyId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            schoolList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)

    showViewer.value = true;
    viewPhotoList.value.forEach((item, index) => {
        if (path == item) {
            imgSrcIndex.value = index;
        }
    });
    // //大图预览从点击的那张开始
    let tempImgList = [...viewPhotoList.value];
    let temp = [];
    for (let i = 0; i < imgSrcIndex.value; i++) {
        temp.push(tempImgList.shift());
    }
    viewPhotoList.value = tempImgList.concat(temp);
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.UnitId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in schoolList" ::key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Address" placeholder="存放地点" style="width: 200px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="SchoolName" label="单位名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Address" label="存放地点" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Space" label="房间面积(平米)" min-width="120" align="center"></el-table-column>
            <el-table-column prop="LiablePerson" label="责任人" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column prop="imgShow" label="场景图片" min-width="90" align="center">
                <template #default="{ row }">
                    <span v-if="row.ListAttachment">
                        <span v-for="(item, index) in row.ListAttachment" :key="item.Id"
                            style="padding: 0 2px; color: #409eff;cursor: pointer;"
                            @click="fileListDownload(item.Path, row.ListAttachment)">
                            {{ index + 1 }}</span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="140" show-overflow-tooltip></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <!-- 图片预览 -->
        <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    </div>
</template>
<style lang="scss" scoped></style>