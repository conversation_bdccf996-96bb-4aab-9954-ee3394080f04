<script setup>
defineOptions({
  name: 'organizeeditlist'
});
import { onMounted, ref, computed, nextTick, onActivated } from 'vue'
import {
  QuestionFilled, Refresh, Search, UploadFilled, Delete, FolderAdd
} from '@element-plus/icons-vue'
import {
  Getpagedbytype, AttachmentUpload
} from '@/api/user.js'
import {
  OrganizationGetpaged, OrganizationSaveedit, OrganizationGeteditbyid, OrganizationDelattachmentbyid, OrganizationSavefiling
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { integerLimit, previousYearDate, yearDate, foundReturn, fileDownload } from "@/utils/index.js";
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const previousYearDateList = ref([])
const yearDateList = ref([])
const StatuzList = ref([])
const uploadFileData = ref([])
const dialogVisible = ref(false)
const dialogData = ref({})
const editId = ref()
const refForm = ref()
const ruleForm = {
  OrganizationYear: [
    { required: true, message: '请选择年度', trigger: 'change' },
  ],
  ParentNum: [
    { required: true, message: '请填写家长代表人数', trigger: 'change' },
  ],
  StudentNum: [
    { required: true, message: '请填写学生代表人数', trigger: 'change' },
  ],
  TeacherNum: [
    { required: true, message: '请填写教师代表人数', trigger: 'change' },
  ],
  SchoolAdminNum: [
    { required: true, message: '请填写学校管理人员人数', trigger: 'change' },
    {
      pattern: /^[1-9][0-9]{0,6}$/,
      message: '请正确填写人数，1-1000000',
      trigger: 'change'
    }
  ]
}

//加载数据
onMounted(() => {
  yearDateList.value = yearDate()
  previousYearDateList.value = previousYearDate()
  GetpagedbytypeUser()
})
onActivated(() => {
  fileIdList.value = []
  nextTick(() => {
    HandleTableData(true);
  })
})
// 总人数
const TotalNum = computed(() => {
  let num = Number(dialogData.value.ParentNum) + Number(dialogData.value.StudentNum) + Number(dialogData.value.TeacherNum) + Number(dialogData.value.SchoolAdminNum) + Number(dialogData.value.OtherNum)
  return num
})
// 家长代表（人） 比例
const ParentNumRatio = computed(() => {
  if (TotalNum.value == 0) {
    return 0
  } else {
    let num = (Number(dialogData.value.ParentNum) / Number(TotalNum.value)) * 100
    return num.toFixed(2)
  }
})
// 学生代表（人） 比例
const StudentNumRatio = computed(() => {
  if (TotalNum.value == 0) {
    return 0
  } else {
    let num = (Number(dialogData.value.StudentNum) / Number(TotalNum.value)) * 100
    return num.toFixed(2)
  }
})
// 教师代表（人） 比例
const TeacherNumRatio = computed(() => {
  if (TotalNum.value == 0) {
    return 0
  } else {
    let num = (Number(dialogData.value.TeacherNum) / Number(TotalNum.value)) * 100
    return num.toFixed(2)
  }
})
// 学校管理人员（人） 比例
const SchoolAdminNumRatio = computed(() => {
  if (TotalNum.value == 0) {
    return 0
  } else {
    let num = (Number(dialogData.value.SchoolAdminNum) / Number(TotalNum.value)) * 100
    return num.toFixed(2)
  }
})
// 其他人员（人） 比例
const OtherNumRatio = computed(() => {
  if (TotalNum.value == 0) {
    return 0
  } else {
    let num = (Number(dialogData.value.OtherNum) / Number(TotalNum.value)) * 100
    return num.toFixed(2)
  }
})
// 其中家长和学生人数占比
const ParentStudentRatio = computed(() => {
  if (dialogData.value.StudentNum == 0) {
    return 0
  } else {
    let num = ((Number(dialogData.value.ParentNum) + Number(dialogData.value.StudentNum)) / Number(TotalNum.value)) * 100
    return num.toFixed(2)
  }
})


//输入整数
const integerLimitInput = (val, name) => {
  dialogData.value[name] = integerLimit(val);
}

// 创建
const HandleAdd = (row) => {
  editId.value = undefined
  uploadFileData.value.forEach(item => {
    item.fileLChildList = []
    item.categoryList = []
  })

  dialogVisible.value = true
  nextTick(() => {
    refForm.value.resetFields()
    dialogData.value.ParentNum = undefined;//年度
    dialogData.value.ParentNum = '';//家长
    dialogData.value.StudentNum = '';//学生
    dialogData.value.TeacherNum = '';//教师
    dialogData.value.SchoolAdminNum = '';//管理人员
    dialogData.value.OtherNum = 0;//其他人员

  })
}
// 修改
const HandleEdit = (row) => {
  editId.value = row.Id
  uploadFileData.value.forEach(item => {
    item.fileLChildList = []
    item.categoryList = []
  })
  OrganizationGeteditbyidUser(row.Id)
  dialogVisible.value = true
}

//创建/修改选用组织  提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    if (ParentStudentRatio.value < 80) {
      ElMessage.error('其中家长和学生人数占比不能低于80%')
      return;
    }
    // 判断必传的附件是否已传
    let found = foundReturn(uploadFileData.value)
    if (found) {
      return
    }
    OrganizationSaveeditUser()
  })
}

//  备案||审核||退回 提交
const HandleRecordSubmit = (row) => {
  ElMessageBox.confirm('确定提交备案吗?')
    .then(() => {
      OrganizationSavefiling({ id: row.XUniformOrganizationId }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '提交成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
const detailVisible = ref(false)
const detailData = ref({})
// 查看
const HandleDetail = (row) => {
  uploadFileData.value.forEach(item => {
    item.fileLChildList = []
    item.categoryList = []
  })
  OrganizationGeteditbyidUser(row.Id)
  detailVisible.value = true
}

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
  Getpagedbytype({ moduleType: 104 }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      uploadFileData.value = rows || []
      if (uploadFileData.value.length > 0) {
        uploadFileData.value.forEach(item => {
          item.fileLChildList = []
          item.categoryList = []
          item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
        })
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  OrganizationGetpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        StatuzList.value = other.StatuzList || []//状态
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });

}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.OrganizationYear = undefined
  filters.value.Statuz = undefined
  filters.value.Name = undefined
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }

// 修改
const OrganizationSaveeditUser = () => {
  let formData = {
    Id: editId.value,
    OrganizationYear: dialogData.value.OrganizationYear,
    ParentNum: Number(dialogData.value.ParentNum),
    StudentNum: Number(dialogData.value.StudentNum),
    TeacherNum: Number(dialogData.value.TeacherNum),
    SchoolAdminNum: Number(dialogData.value.SchoolAdminNum),
    OtherNum: Number(dialogData.value.OtherNum),
    AttachmentIdList: fileIdList.value.map(t => t.Id),//附件ID集合
  }
  OrganizationSaveedit(formData).then((res) => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '修改成功')
      fileIdList.value = [];
      dialogVisible.value = false

      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 详情
const OrganizationGeteditbyidUser = (id) => {
  OrganizationGeteditbyid({ id: id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows, footer } = res.data.data
      detailData.value = rows
      dialogData.value.OrganizationYear = rows.OrganizationYear || undefined;//年度
      dialogData.value.ParentNum = rows.ParentNum || '';//家长
      dialogData.value.StudentNum = rows.StudentNum || '';//学生
      dialogData.value.TeacherNum = rows.TeacherNum || '';//教师
      dialogData.value.SchoolAdminNum = rows.SchoolAdminNum || '';//管理人员
      dialogData.value.OtherNum = rows.OtherNum;//其他人员
      uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
      })
      let categoryList = footer || []
      if (categoryList.length > 0) {
        // 遍历数组 b 的每个元素
        categoryList.forEach(item => {
          // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
          let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
          // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
          if (match) {
            match.categoryList.push(item);
          }
        });
      }

    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
  let length = item.categoryList.length + item.fileLChildList.length
  if (item.MaxFileNumber && length >= item.MaxFileNumber) {
    numberDisabled.value = true
    ElMessage.error("上传数量已达到上限,请先删除后再上传")
    return
  } else {
    numberDisabled.value = false
  }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
  fileFile.value = file
  let str = file.name.split('.')[1]
  let name = str.toLowerCase()
  let arr = item.UploadFileType.split('.')
  let ary = arr.filter(t => t != '')
  const extension = ary.includes(name)
  if (!extension) {
    ElMessage({
      message: `上传文件只能是${item.UploadFileType}格式!`,
      type: 'error'
    })
  }
  // // 校验文件大小
  let FileSize = item.FileSize
  if (item.FileSize == 0) {
    FileSize = 10
  }
  const isSize = file.size / 1024 / 1024 < FileSize;
  if (!isSize) {
    ElMessage({
      message: `文件大小不能超出${FileSize}M`,
      type: 'error'
    })
  }
  return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
  AttachmentUpload({ file: fileFile.value, filecategory: item.FileCategory }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '上传成功')
      const { rows } = res.data.data
      // 将上传的附件加入对应的模块下
      uploadFileData.value[index].fileLChildList.push(rows[0])
      // 获取上传后附件Id
      fileIdList.value.push(rows[0])
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
  uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
  fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
  OrganizationDelattachmentbyid({ id: editId.value, attid: item.Id }).then((res) => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '删除成功')
      uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
  // console.log(e)
  let path = e.Path;
  viewPhotoList.value = imgList.map(t => t.Path)
  if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
    showViewer.value = true;
    viewPhotoList.value.forEach((item, index) => {
      if (path == item) {
        imgSrcIndex.value = index;
      }
    });

    // //大图预览从点击的那张开始
    let tempImgList = [...viewPhotoList.value];
    let temp = [];
    for (let i = 0; i < imgSrcIndex.value; i++) {
      temp.push(tempImgList.shift());
    }
    viewPhotoList.value = tempImgList.concat(temp);

  } else {
    let title = e.Title + e.Ext
    fileDownload(e.Path, title)
  }

}
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.OrganizationYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in previousYearDateList" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.Statuz" clearable placeholder="备案状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>

        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="OrganizationYear" label="年度" min-width="80" align="center"></el-table-column>
      <el-table-column prop="TotalNum" label="总人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="ParentStudentRatio" label="家长和学生人数占比" min-width="140" align="center">
        <template #default="{ row }">
          {{ row.ParentStudentRatio }}%
        </template></el-table-column>
      <el-table-column prop="ParentNum" label="家长代表" min-width="90" align="center"></el-table-column>
      <el-table-column prop="StudentNum" label="学生代表" min-width="90" align="center"></el-table-column>
      <el-table-column prop="TeacherNum" label="教师代表" min-width="90" align="center"></el-table-column>
      <el-table-column prop="SchoolAdminNum" label="学校管理人员" min-width="110" align="center"></el-table-column>
      <el-table-column prop="OtherNum" label="其他人员" min-width="90" align="center"></el-table-column>
      <el-table-column prop="StatuzName" label="备案状态" min-width="90" align="center"></el-table-column>
      <el-table-column label="操作" fixed="right" min-width="90" align="center">
        <template #default="{ row }">
          <el-button v-if="row.Statuz == 0" type="primary" link @click="HandleEdit(row)">修改</el-button>
          <el-button v-else type="primary" link @click="HandleDetail(row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="选用组织备案" min-width="110" align="center">
        <template #default="{ row }">
          <el-button v-if="row.IsCountyManager == 1 && row.Statuz == 0" type="primary" link
            @click="HandleRecordSubmit(row)">提交</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
    <!-- 创建修改弹窗 -->
    <app-box v-model="dialogVisible" :width="680" :lazy="true" title="组织管理">
      <template #content>
        <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px" status-icon>
          <el-form-item label="年度：" prop="OrganizationYear">
            <el-select v-model="dialogData.OrganizationYear" style="width: 60%">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="家长代表（人）：" prop="ParentNum">
            <el-input v-model="dialogData.ParentNum" @input="integerLimitInput($event, 'ParentNum')"
              style="width: 60%;"></el-input> <span style="color: #999;padding-left: 50px;"> {{ ParentNumRatio
              }}%</span>
          </el-form-item>
          <el-form-item label="学生代表（人）：" prop="StudentNum">
            <el-input v-model="dialogData.StudentNum" @input="integerLimitInput($event, 'StudentNum')"
              style="width: 60%;"></el-input> <span style="color: #999;padding-left: 50px;">{{ StudentNumRatio
              }}%</span>
          </el-form-item>
          <el-form-item label="教师代表（人）：" prop="TeacherNum">
            <el-input v-model="dialogData.TeacherNum" @input="integerLimitInput($event, 'TeacherNum')"
              style="width: 60%;"></el-input> <span style="color: #999;padding-left: 50px;">{{ TeacherNumRatio
              }}%</span>
          </el-form-item>
          <el-form-item label="学校管理人员（人）：" prop="SchoolAdminNum">
            <el-input v-model="dialogData.SchoolAdminNum" @input="integerLimitInput($event, 'SchoolAdminNum')"
              style="width: 60%;"></el-input> <span style="color: #999;padding-left: 50px;">
              {{ SchoolAdminNumRatio }}%</span>
          </el-form-item>
          <el-form-item label="其他人员（人）：" prop="OtherNum">
            <template #label>
              <el-tooltip class="item" effect="dark" content="如聘请的专家等人员，没有就填“0”" placement="top">
                <div>
                  <el-icon color="#E6A23C" class="tipIcon">
                    <QuestionFilled />
                  </el-icon>
                </div>
              </el-tooltip>
              <span> 其他人员（人）: </span>
            </template>
            <el-input v-model="dialogData.OtherNum" @input="integerLimitInput($event, 'OtherNum')"
              style="width: 60%;"></el-input>
            <span style="color: #999;padding-left: 50px;">{{ OtherNumRatio }}%</span>
          </el-form-item>
          <el-form-item label="总人数（人）：">
            <span>{{ TotalNum }}</span>
          </el-form-item>
          <el-form-item label="其中家长和学生人数占比：">
            <span :class="ParentStudentRatio < 80 ? 'redColor' : ''" style="display: inline-block;width: 100%;">
              <span style="display: inline-block;width: 25%;">{{ ParentStudentRatio }}%</span>
              <span style="color: #999;padding-left: 50px;">要求不低于80%</span>
            </span>
          </el-form-item>
          <el-form-item v-for="(item, index) in uploadFileData" :key="index">
            <template #label>
              <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
              <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                <div>
                  <el-icon color="#E6A23C" class="tipIcon">
                    <QuestionFilled />
                  </el-icon>
                </div>
              </el-tooltip>
              <span> {{ item.Name }}： </span>
            </template>
            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" :accept="item.UploadFileTypeAccept"
              :before-upload="beforeAvatarUpload.bind(null, item)" :http-request="httpRequest.bind(null, item, index)"
              :disabled="numberDisabled">
              <el-button type="success" size="small" :icon="UploadFilled"
                @click="MaxFileNumberClick(item)">上传</el-button>
            </el-upload>
            <div class="fileFlex">
              <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                  <Delete />
                </el-icon>
                <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                  {{ itemCate.Title }}{{ itemCate.Ext }}
                </span>
              </div>
              <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                  <Delete />
                </el-icon>
                <span style="cursor: pointer;" @click="fileListDownload(itemChild, item.fileLChildList)">
                  {{ itemChild.Title }}{{ itemChild.Ext }}
                </span>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
        </span>
      </template>
    </app-box>
    <!-- 查看弹窗 -->
    <el-dialog v-model="detailVisible" title="组织管理" width="680px" draggable :close-on-click-modal="false">
      <div class="detailDialog">
        <el-form @submit.prevent :model="detailData" label-width="180px" status-icon>

          <el-form-item label="年度：">
            <span> {{ detailData.OrganizationYear }}</span>
          </el-form-item>
          <el-form-item label="家长代表（人）：">
            <span style="display: inline-block;width: 25%;">{{ detailData.ParentNum }}</span> <span
              style="color: #999;padding-left: 50px;">占比：{{ detailData.ParentNumRatio }}%</span>
          </el-form-item>
          <el-form-item label="学生代表（人）：">
            <span style="display: inline-block;width: 25%;">{{ detailData.StudentNum }}</span> <span
              style="color: #999;padding-left: 50px;">占比：{{ detailData.StudentNumRatio }}%</span>
          </el-form-item>
          <el-form-item label="教师代表（人）：">
            <span style="display: inline-block;width: 25%;">{{ detailData.TeacherNum }}</span> <span
              style="color: #999;padding-left: 50px;">占比：{{ detailData.TeacherNumRatio }}%</span>
          </el-form-item>
          <el-form-item label="学校管理人员（人）：">
            <span style="display: inline-block;width: 25%;">{{ detailData.SchoolAdminNum }}</span> <span
              style="color: #999;padding-left: 50px;">占比：{{ detailData.SchoolAdminNumRatio }}%</span>
          </el-form-item>
          <el-form-item label="其他人员（人）：">
            <span style="display: inline-block;width: 25%;">{{ detailData.OtherNum }}</span> <span
              style="color: #999;padding-left: 50px;">占比：{{ detailData.OtherNumRatio }}%</span>
          </el-form-item>
          <el-form-item label="总人数（人）：">
            <span>{{ detailData.TotalNum }}</span>
          </el-form-item>
          <el-form-item label="其中家长和学生人数占比：">
            <span style="display: inline-block;width: 25%;">{{ detailData.ParentStudentRatio }}%</span>
          </el-form-item>
          <el-form-item v-for="(item, index) in uploadFileData" :key="index">
            <template #label>
              <span> {{ item.Name }}： </span>
            </template>
            <div class="fileFlex">
              <div v-for="(itemCate, indexCate) in item.categoryList" :key="itemCate.Id">
                <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                  {{ itemCate.Title }}{{ itemCate.Ext }}
                </span>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
  </div>
</template>
<style lang="scss" scoped>
.redColor {
  color: #F56C6C;
}

.dialog-content {
  height: 500px;
  overflow: auto;
}

.detailDialog {
  :deep(.el-form-item__label) {
    color: #409EFF;
  }

  :deep(.el-form-item) {
    margin-bottom: 5px !important;
    margin-top: 5px !important;
    border-bottom: 1px solid #f5f5f5;
  }
}
</style>