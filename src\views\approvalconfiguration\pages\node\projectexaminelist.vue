<script setup>
defineOptions({
  name: 'nodeprojectexaminelist'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
  FindSuditProjectList, SaveProjectListReview, FindHistoryProjectList
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { ElMessageBox, ElMessage } from 'element-plus'
import AppBox from "@/components/Approve/AppBox.vue";
import { FolderAdd, Refresh, Search, Back } from '@element-plus/icons-vue'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, key: '' })
const tableTotal = ref(0)
const tableData = ref([])// 表格数据
const columnData = ref([])//列表头数据
const searchList = ref([])//查询条件：下拉、时间等
const formFields = ref({})
const downLoadFile = ref('')
const isEdit = ref(true)//是否可编辑
const approvalNoIdList = ref([])//审批历史记录
//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  isEdit.value = route.query.isEdit
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  if (!isEdit.value) {
    FindHistoryProjectListUser()
  }
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})
// 返回上一页
const HandleBack = () => {
  let tagsList = userStore.tagsList
  tagsList = tagsList.filter(t => t.path != route.path)
  userStore.setTagsList(tagsList)
  router.push({ path: './examine' + route.query.routerUrl, query: { id: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode } })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.ProcessId = route.query.ProcessId
  filters.value.ProcessNodeId = route.query.ProcessNodeId
  filters.value.ProjectDeclarationId = route.query.ProjectDeclarationId
  filters.value.FieldCode = route.query.FieldCode
  filters.value.isFirst = isFirst

  FindSuditProjectList(filters.value).then(res => {
    console.log('res', res)
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      tableData.value = rows.data || [];//表格数据
      tableTotal.value = rows.dataCount || 0
      // console.log('tableData.value', tableData.value)
      if (isFirst && other) {
        columnData.value = other.listColumn || [];//表头（列表字段）
        searchList.value = other.listSearch || []//查询条件
        let listAdd = other.listAdd || []//表单数据字段
        downLoadFile.value = other.downLoadFile || []//模板下载路径
        // 修改表单下拉框数据
        listAdd.forEach(item => {
          if (item.SourceValue) {
            item.SourceValue = item.SourceValue.split('|')
          }
          if (item.TypeStyle != 2) {
            item.DefaultValue = ''
          } else {
            item.DefaultValue = undefined
          }
        })
        formFields.value.formItems = listAdd
        // console.log('formFields.value.formItems', formFields.value.formItems)
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.Code = undefined
  filters.value.key = ''
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}

const focusedIndex = ref(-1); // 用来存储当前聚焦的输入框的行索引  
const examineList = ref([]);//审核不通过数据(源数据)
const projectList = ref([]);//审核不通过数据（提交数据）
const auditStatuz = ref(0);//审核结果

// 表格输入框聚焦事件
const handleFocus = (index, isFocused) => {
  focusedIndex.value = isFocused ? index : -1;
}
// 表格输入框赋值事件
const handleInput = (index, value, id) => {
  tableData.value[index].ReviewNote = value;
  console.log(tableData.value)
  let ProjectList = [{ ProjectListId: id, ReviewNote: value }]
  // console.log("ProjectList", ProjectList)
  auditStatuz.value = 0
  projectList.value = ProjectList
  SaveProjectListReviewUser()
}
//新增&编辑操作
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
  AuditStatuz: [
    { required: true, message: '请选择审核结果', trigger: 'change' },
  ],
  AuditRemark: [
    { required: true, message: '请输入审核不通过意见', trigger: 'change' },
  ],
}
// 提交审核
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    auditStatuz.value = dialogData.value.AuditStatuz
    SaveProjectListReviewUser(dialogData.value.AuditRemark)
  })
}
//审核
const HandleExamine = () => {
  // 判断表格内是否有审核不通过
  examineList.value = tableData.value.filter(item => item.ReviewNote)
  if (examineList.value.length > 0) {
    dialogData.value.AuditStatuz = 2
    projectList.value = examineList.value.map(({ Id, ReviewNote }) => ({
      ProjectListId: Id,
      ReviewNote: ReviewNote
    }));
  } else {
    dialogData.value.AuditStatuz = 1
    projectList.value = []
  }
  dialogData.value.AuditRemark = ''
  dialogVisible.value = true
}

// 项目清单保存、删除、审核处理
const SaveProjectListReviewUser = (auditRemark) => {
  let formData = {
    ProcessId: route.query.ProcessId,//流程Id
    ProjectDeclarationId: route.query.ProjectDeclarationId,//采购项目立项申报Id
    ProcessNodeId: route.query.ProcessNodeId,//字段Code编码
    FieldCode: route.query.FieldCode,//流程节点Id
    AuditStatuz: auditStatuz.value,//审核结果（AuditStatuz；0：保存,1：审核通过,2：审核不通过）
    ProjectList: projectList.value,//列表审核意见集合
    AuditRemark: auditRemark,//审核意见
  }
  // console.log("formData", formData)
  SaveProjectListReview(formData).then((res) => {
    if (res.data.flag == 1) {
      dialogVisible.value = false
      ElMessage.success(res.data.msg || '审核成功')
      if (auditStatuz.value != 0) {
        HandleBack()
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 获取项目清单审批历史
const FindHistoryProjectListUser = () => {
  let formData = {
    ProjectDeclarationId: route.query.ProjectDeclarationId,//采购项目立项申报Id
    FieldCode: route.query.FieldCode,//流程节点Id
    isFirst: true,
  }
  // console.log("formData", formData)
  FindHistoryProjectList(formData).then((res) => {
    if (res.data.flag == 1) {

    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button :icon="Back" @click="HandleBack">返回</el-button>
          </el-form-item>
          <el-form-item class="flexItem" v-if="!isEdit" label="历史审批记录: ">
            <el-select v-model="filters.ProjectApprovalNoId" style="width: 160px">
              <el-option v-for="item in approvalNoIdList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-input v-model.trim="filters.key" placeholder="请输入" style="width: 240px" class="input-with-select">
              <template #prepend>
                <el-select v-model="filters.Code" style="width: 120px">
                  <el-option v-for="item in searchList" :key="item.FieldValue" :label="item.Title"
                    :value="item.FieldValue" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
            <div v-if="isEdit" class="verticalDividel"></div>
            <el-button type="success" :icon="FolderAdd" @click="HandleExamine" v-if="isEdit">审核</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column v-for="column in columnData" :key="column.FieldValue" :prop="column.FieldValue"
        :label="column.Title" :min-width="column.Width" :align="column.ContentStyle">
        <template #default="{ row }">
          {{ row[column.FieldValue] }}
        </template>
      </el-table-column>
      <el-table-column prop="LastReviewNote" label="上一次审核意见" show-overflow-tooltip min-width="180" v-if="isEdit">
      </el-table-column>
      <el-table-column prop="ReviewNote" label="审核意见" min-width="200" v-if="isEdit">
        <template #default="scope">
          <el-input v-model="scope.row.ReviewNote" @focus="handleFocus(scope.$index, true)"
            @blur="handleInput(scope.$index, scope.row.ReviewNote, scope.row.Id)"
            :class="{ 'input-focused': focusedIndex === scope.$index }" placeholder="不通过请输入审核意见"></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="LastReviewNote" label="审核意见" show-overflow-tooltip min-width="200" v-if="!isEdit">
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
  <app-box v-model="dialogVisible" :width="680" :lazy="true" title="项目清单审核" width="600px" v-if="isEdit">
    <template #content>
      <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="120px" status-icon>
        <el-form-item label="审核结果：" prop="AuditStatuz">
          <el-radio-group v-model="dialogData.AuditStatuz">
            <el-radio :value="1" v-if="examineList.length == 0">审核通过</el-radio>
            <el-radio :value="2">审核不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见：" :prop="dialogData.AuditStatuz == 2 ? 'AuditRemark' : ''">
          <el-input type="textarea" v-model="dialogData.AuditRemark" :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入审核意见"></el-input>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
      </span>
    </template>
  </app-box>
</template>
<style lang="scss" scoped>
.el-table {
  :deep(.el-input__wrapper) {
    border: none !important;
    box-shadow: none !important;
  }

  .input-focused {
    border: 1px solid #409EFF;
  }
}
</style>
