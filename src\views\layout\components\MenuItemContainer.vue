<script setup>
import MenuItemContainer from './MenuItemContainer.vue'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
import router from '@/router'

// 传参
defineProps({
  data: {
    type: [Array],
    require: true
  }
})

const setActiveTag = (item) => {
  // console.log("item", item)
  let path = '/' + item.path
  router.push(path)
  // console.log("页面路径path", path);
  userStore.setCurrentPage(path)//存储起来用于token失效重新登录，或者返回首页后重新返回当前页面
}
</script>

<template>
  <template v-for="item in data" :key="item.id">
    <el-sub-menu v-if="item.children && item.children.find(t => !t.IsButton && !t.IsHide)" :index="item.id">
      <template #title>
        <!-- 图标 -->
        <div v-if="item.children && item.children.length > 0 && item.iconCls" style="padding-right: 3px;">
          <i :class="item.iconCls" v-if="item.iconCls.indexOf('el-icon') == 0"></i>
          <SvgIcon :name="item.iconCls" class="svg-icon" color="var(--next-bg-menuBarColor)" v-else
            style="vertical-align: middle;">
          </SvgIcon>
        </div>
        <span>{{ item.name }}</span>
      </template>
      <!-- 多级路由菜单 -->
      <MenuItemContainer :data="item.children"></MenuItemContainer>
    </el-sub-menu>
    <el-menu-item @click="setActiveTag(item)" v-else-if="!item.IsButton && !item.IsHide" :index="'/' + item.path">
      {{ item.name }}</el-menu-item>
  </template>
</template>
<style></style>