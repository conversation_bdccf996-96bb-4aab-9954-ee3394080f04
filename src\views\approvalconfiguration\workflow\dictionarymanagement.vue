<script setup>
defineOptions({
    name: 'dictionarymanagement'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd,
} from '@element-plus/icons-vue'
import {
    DictionaryinsertUpdate, DictionarysetStatuz, FindDictionaryList
} from '@/api/workflow.js'

import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({})
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const dialogVisible = ref(false)
const dicType = ref(1)//类型
const isEdit = ref(false)//是否修改
const dialogData = ref({})
const title = ref('添加字典分类')
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '请输入名称', trigger: 'change' },
    ]
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// 创建字典分类
const HandleAdd = (row) => {
    isEdit.value = false
    title.value = '创建字典分类'
    dicType.value = 1

    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            Pid: 0,
            Name: '',
            DicType: 1,
            Sort: 0,
        }
    })
}
// 添加字典值
const HandleIdAdd = (row) => {
    isEdit.value = false
    title.value = '创建字典值'
    dicType.value = 2
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            Pid: row.Id,
            Name: '',
            DicType: 2,
            Sort: 0,
            Memo: ''
        }
    })

    dialogVisible.value = true
}
// 修改字典  分类||值
const HandleEdit = (row) => {
    isEdit.value = true
    if (row.DicType == 1) {
        title.value = '修改字典分类'
    } else {
        title.value = '修改字典值'
    }
    dicType.value = row.DicType
    dialogData.value = {
        Id: row.Id,
        Name: row.Name,
        DicType: row.DicType,
        Sort: row.Sort || 0,
        Memo: row.Memo
    }
    dialogVisible.value = true
}


// 修改状态
const HandleSwitchChange = (e, row) => {
    DictionarysetStatuz({ id: row.Id }).then((res) => {
        // console.log(res)
        // if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '设置成功')
        // } else {
        //     ElMessage.error(res.data.msg)
        // }
    }).catch((err) => {
        console.info(err)
    })
}

//创建/修改选用组织  提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DictionaryinsertUpdateUser()
    })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst

    FindDictionaryList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows;
            // tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 添加/修改提交
const DictionaryinsertUpdateUser = () => {
    DictionaryinsertUpdate(dialogData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            if (isEdit.value) {
                dialogVisible.value = false
            }
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="名称" style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="Id"
            header-cell-class-name="headerClassName">
            <!-- <el-table-column prop="Id" label="Id" min-width="120" align="center"></el-table-column> -->
            <el-table-column prop="Name" label="名称" min-width="140"></el-table-column>
            <el-table-column prop="Sort" label="排序" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Memo" label="备注" min-width="180" show-overflow-tooltip></el-table-column>
            <el-table-column label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="2" inline-prompt active-text="启"
                        inactive-text="禁" style="--el-switch-off-color: #ff4949"
                        @change="HandleSwitchChange($event, row)" />
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleIdAdd(row)">添加</el-button>
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="560" :lazy="true" :title="title">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="120px"
                    status-icon>
                    <el-form-item :label="dicType == 1 ? '分类名称 :' : '名称 :'" prop="Name">
                        <el-input v-model="dialogData.Name"></el-input>
                    </el-form-item>
                    <el-form-item label="排序值：">
                        <el-input type="number" v-model="dialogData.Sort"></el-input>
                    </el-form-item>
                    <el-form-item label="备注：" v-if="dicType == 2">
                        <el-input type="textarea" v-model="dialogData.Memo" placeholder="请输入备注"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>