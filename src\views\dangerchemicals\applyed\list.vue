<script setup>
defineOptions({
    name: 'dangerchemicalsapplyedlist'
});
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    Refresh, Search, QuestionFilled, Position
} from '@element-plus/icons-vue'
import {
    DcapplyListFind, DcapplyAfterConfirm, DcapplyUpdateBackNum, DcapplyFindByid, DcapplyInsertUpdate, DcapplyApply,
    DcapplyDelete, DcApplyNoPassReasonGet, DcApplyLookAuditUser, DcApplyRevoke, DcPurchaseNoPassReasonGet,
    DcschoolCatalogBrandGetbyModelidv2, DcschoolCatalogComboboxGet, DcschoolCatalogCommonuseFind, DcschoolCatalogFindCommonuseAll,
    DcschoolCatalogModelGetv2, DcWasteBaseFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import AppBox from "@/components/Approve/AppBox.vue";
import { getApplyStatus, buildTree, limit, ConsolidatedColumn } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const dialogTableData = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, ApplyStatuzgt: 0, sortModel: [{ SortCode: "ApplyRegDate", SortType: "DESC" }] })
const formData = ref({})
const refForm = ref()
const dialogVisible = ref(false)
const whpDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const bogetList = ref([])
const whpId = ref(0)
const lookAuditList = ref([])
const modelList = ref([])
const brandList = ref([])
const schoolCatalogList = ref([])
const dangerchemicalsList = ref([])
const EndDate = ref()
const dialogNum = ref(0)
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', },
    { value: 'BatchNo', label: '申领批次', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
const ruleForm = {
    UseTime: [
        { required: true, message: '请选择使用时间', trigger: 'change' },
    ],
    SchoolMaterialModelId: [
        { required: true, message: '请选择规格属性', trigger: 'change' },
    ],
    SchoolMaterialBrandId: [
        { required: true, message: '请选择品牌', trigger: 'change' },
    ],
    Num: [
        { required: true, message: '请填写数量', trigger: 'change' },
    ],
    BackNum: [
        { required: true, message: '请填写剩余退回数量', trigger: 'change' },
    ],
    SurplusStatuz: [
        { required: true, message: '请选择药品是否有剩余', trigger: 'change' },
    ],
    WasetStatuz: [
        { required: true, message: '请选择是否产生危废物', trigger: 'change' },
    ],
    isAgree: [
        { required: true, message: '请确认药品无剩余', trigger: 'change' },
    ]
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcschoolCatalogFindCommonuseAllUser()
    DcschoolCatalogComboboxGetUser()
    DcWasteBaseFindUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcapplyListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.BeginDate = undefined
    filters.value.EndDate = undefined
    EndDate.value = undefined
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.BeginDate = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.EndDate = val + " 23:59:59"
    } else {
        filters.value.EndDate = undefined
    }
    HandleTableData()
}
const statuzDesc = ref('')
// 查看状态对应的人员信息
const HandleLookAuditUser = (row) => {
    detailDialogVisible.value = true
    statuzDesc.value = getApplyStatus(row.ApplyStatuz).substring(2) + '人';
    // console.log(statuzDesc.value)
    DcApplyLookAuditUser({ id: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            lookAuditList.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 确认
const HandleConfirm = (row) => {
    whpId.value = row.ApplyConfirmDetailId
    dialogNum.value = 1
    formData.value.Num = row.Num
    dialogTableData.value.forEach(item => {
        item.Num = ''
        item.Remark = ''
    })
    dialogVisible.value = true
}
// 修改
const HandleEditBackNum = (row) => {
    whpId.value = row.ApplyConfirmDetailId
    formData.value.Num = row.Num
    dialogNum.value = 2
    dialogVisible.value = true
}
// 修改
const HandleEdit = (row) => {
    whpId.value = row.ApplyId
    dialogNum.value = 3
    dialogVisible.value = true
    DcApplyNoPassReasonGetUser(row.ApplyId)
    DcapplyFindByidUser(row.ApplyId)
    DcschoolCatalogModelGetv2User(row.SchoolCatalogId)
    DcschoolCatalogBrandGetbyModelidv2User(row.SchoolCatalogId, row.SchoolMaterialModelId)
}
// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('您确认要删除该数据吗？')
        .then(() => {
            DcapplyDelete({ id: row.ApplyId }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 查看
const HandleDatile = (row, e) => {
    whpId.value = row.ApplyId
    if (e == 1) {
        router.push({ path: '../waste/recordlist', query: { id: row.ApplyId } })
    } else {
        router.push({ path: '../apply/detailview', query: { id: row.Id } })
    }
}
//批量撤回
const HandleDelAll = (row) => {
    ElMessageBox.confirm('您确认要撤回所选数据吗？')
        .then(() => {
            let ids = selectRows.value.map(t => t.ApplyId).join(',')
            DcApplyRevoke({ ids: ids }).then((res) => {
                HandleTableData()
                ElMessage.success(res.data.msg || '撤回成功')
            }).catch((err) => {
                console.info(err)
            })
        })
}
// 修改提交
const HandleSubmit = (e) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (dialogNum.value == 1) {
            let wasteList = []
            if (formData.value.WasetStatuz == 1) {
                wasteList = dialogTableData.value.map(item => {
                    return {
                        ObjectId: whpId.value,
                        Remark: item.Remark,
                        Num: item.Num,
                        BaseWasteId: item.Id,
                    }
                })
                wasteList = wasteList.filter(item => item.Num)
            }
            let data = {
                id: whpId.value,
                backNum: formData.value.BackNum || 0,
                surplusStatuz: formData.value.SurplusStatuz,
                wasetStatuz: formData.value.WasetStatuz,
                wasteList: wasteList,
            }
            // console.log("data", data)
            DcapplyAfterConfirm(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '确认成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        } else if (dialogNum.value == 2) {
            let data = {
                id: whpId.value,
                backNum: formData.value.BackNum || 0,
            }
            DcapplyUpdateBackNum(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '修改成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        } else if (dialogNum.value == 3) {
            let data = {
                Id: whpId.value,
                Name: formData.value.Name,
                Remark: formData.value.Remark,
                Num: formData.value.Num,
                SchoolCatalogId: formData.value.SchoolCatalogId,
                SchoolMaterialModelId: formData.value.SchoolMaterialModelId,
                SchoolMaterialBrandId: formData.value.SchoolMaterialBrandId,
                UnitName: formData.value.UnitName,
                UseTime: formData.value.UseTime,
            }
            if (e == 1) {
                DcapplyInsertUpdate(data).then(res => {
                    if (res.data.flag == 1) {
                        ElMessage.success(res.data.msg || '保存成功')
                        dialogVisible.value = false
                        HandleTableData()
                    } else {
                        ElMessage.error(res.data.msg)
                    }
                })
            } else if (e == 2) {
                DcapplyApply(data).then(res => {
                    if (res.data.flag == 1) {
                        ElMessage.success(res.data.msg || '保存成功')
                        dialogVisible.value = false
                        HandleTableData()
                    } else {
                        ElMessage.error(res.data.msg)
                    }
                })
            }
        }
    })
}

const activeName = ref('content')
const firstName = ref('')
const isSearch = ref(false)
// 危化品弹窗搜索
const HandleSearch1 = () => {
    isSearch.value = true
    if (!firstName.value) return
    let paraData = {
        Name: firstName.value,
        Statuz: 1,
        pageIndex: 0,
        pageSize: 100000,
        sortModel: [{ SortCode: "Code", SortType: "asc" }]
    }
    DcschoolCatalogCommonuseFind(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerchemicalsList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选择危化品
const dangerchemicalsClick = (item) => {
    console.log("item", item)
    formData.value.Id = item.Id
    formData.value.Name = item.Name
    formData.value.UnitName = item.UnitsMeasurement
    formData.value.SchoolMaterialModelId = undefined
    formData.value.SchoolMaterialBrandId = undefined
    whpDialogVisible.value = false
    DcschoolCatalogModelGetv2User(item.Id)
    DcschoolCatalogBrandGetbyModelidv2User(item.Id, 0)
}
// 选择危化品
const schoolCatalogClick = (e) => {
    isSearch.value = false
    let arr = schoolCatalogList.value.filter(item => item.Id == e)
    let name = arr[0].Name
    let index = name.lastIndexOf('> ');
    formData.value.Id = e
    formData.value.UnitName = arr[0].UnitName
    formData.value.Name = name.substring(index + 1)
    formData.value.SchoolMaterialModelId = undefined
    formData.value.SchoolMaterialBrandId = undefined
    // console.log("formData.value", formData.value)
    DcschoolCatalogModelGetv2User(e)
    DcschoolCatalogBrandGetbyModelidv2User(e, 0)
}
// 获取详情
const DcapplyFindByidUser = (id) => {
    DcapplyFindByid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows
            formData.value.UseTime = rows.UseTime.substring(0, 10)
            // console.log("formData.value", formData.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取学校物品类别选择
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let arr = rows.data || []
            bogetList.value = buildTree(arr)
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取危化品下拉框数据
const DcschoolCatalogComboboxGetUser = (id, modelId, brandId) => {
    DcschoolCatalogComboboxGet().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            schoolCatalogList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取规格属性
const DcschoolCatalogModelGetv2User = (id) => {
    DcschoolCatalogModelGetv2({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            modelList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取品牌
const DcschoolCatalogBrandGetbyModelidv2User = (id, modelId) => {
    DcschoolCatalogBrandGetbyModelidv2({ schoolCatalogId: id, modelId: modelId }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            brandList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取废弃物基础分类
const DcWasteBaseFindUser = () => {
    DcWasteBaseFind().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dialogTableData.value = rows || []
            dialogTableData.value.forEach(item => {
                item.Num = ''
                item.Remark = ''
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取退回原因  
const DcApplyNoPassReasonGetUser = (id) => {
    DcApplyNoPassReasonGet({ id: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value.ApprovalRemark = rows?.ApprovalRemark || ''
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入框限制：输入整数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
//输入框限制：输入整数
const limitTable = (val, row, index, name) => {
    dialogTableData.value[index][name] = limit(val);
}
// 计算合并信息
const spanArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'BatchNo')
})
// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    // 只对第一列（BatchNo）进行合并
    if (columnIndex === 1) {
        const _row = spanArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDate) return false;
    return time < new Date(filters.value.BeginDate + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    已填报危化品 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 如填报有误，请选择后点击【申领撤回】，仓库配货后就不能撤回了； </li>
                    <li> 实验完成后，须点击【确认】录入实验后信息，否则会影响你下次申领； </li>
                    <li> 危化品退回数量如有误，请点击【修改】进行重填。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Position" @click="HandleDelAll"
                            :disabled="selectRows.length == 0">批量撤回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :span-method="objectSpanMethod"
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="BatchNo" label="申领批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="领用数量" min-width="120" align="right">
                <template #default="{ row }">
                    <div v-if="row.Num != row.ApplyNum" style="display: flex;justify-content: space-between;">
                        <el-tooltip class="item" effect="dark" :content="'申领数量：' + row.ApplyNum + '配货数量：' + row.Num"
                            placement="top">
                            <el-icon class="tipIcon">
                                <QuestionFilled />
                            </el-icon>
                        </el-tooltip>
                        {{ row.Num }}
                    </div>
                    <span v-else> {{ row.Num }} </span>
                </template>
            </el-table-column>
            <el-table-column prop="BackNum" label="退回数量" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row.BackNum || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ApplyRegDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.ApplyRegDate ? row.ApplyRegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CollarRegDate" label="领用时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.CollarRegDate ? row.CollarRegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="WasetStatuz" label="产生危废物" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.WasetStatuz == 1 || row.WasetStatuz == 3" style="color: #0000ee;cursor: pointer;"
                        @click="HandleDatile(row, 1)">是</span>
                    <span v-else-if="row.WasetStatuz == 2">否</span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="WithUserName" label="同领用人" min-width="110" align="center">
                <template #default="{ row }">
                    {{ row.WithUserName || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ApplyStatuz" label="状态" min-width="140" align="center">
                <template #default="{ row }">
                    <span v-if="row.ApplyConfirmDetailId > 0">
                        {{ row.CollarStatuz == 12 ? '等待仓管发放' : row.CollarStatuz == 20 ? '已完成领用' : '' }}
                    </span>
                    <span v-else>
                        <span v-if="[10, 20, 30].includes(row.ApplyStatuz)" style="color: #0000ee; cursor: pointer"
                            @click="HandleLookAuditUser(row)">
                            {{ getApplyStatus(row.ApplyStatuz) }}
                        </span>
                        <span v-else
                            :style="{ color: [-1, , 11, 21, 31].includes(row.ApplyStatuz) ? 'red' : '#606266' }">
                            {{ getApplyStatus(row.ApplyStatuz) }}
                        </span>
                    </span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <span v-if="row.ApplyConfirmDetailId > 0 && row.CollarStatuz == 20">
                        <el-button v-if="!row.IsAfterConfirm" type="primary" link
                            @click="HandleConfirm(row)">确认</el-button>
                        <el-button v-else-if="row.SurplusStatuz == 1" type="primary" link
                            @click="HandleEditBackNum(row)">修改</el-button>
                        <span v-else>--</span>
                    </span>
                    <span v-else-if="row.ApplyStatuz % 2 == 1">
                        <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                        <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                    </span>
                    <span v-else>
                        <el-button type="primary" link @click="HandleDatile(row, 2)">查看</el-button>
                    </span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="780" :lazy="true" :title="isEdit ? '修改危化品信息' : '危化品领用'">
            <template #content>
                <el-form style="min-width: 100px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <div v-if="dialogNum == 3">
                        <el-form-item label="危化品名称：">
                            <el-select v-model="formData.Name" filterable @change="schoolCatalogClick"
                                style="width: 350px;">
                                <el-option v-for="item in schoolCatalogList" :key="item.Id" :label="item.Name"
                                    :value="item.Id" />
                            </el-select>
                            <el-button type="success" :icon="Search" @click="whpDialogVisible = true"
                                style="margin-left: 10px;">选择</el-button>
                        </el-form-item>
                        <el-form-item label="规格属性：" prop="SchoolMaterialModelId">
                            <el-select v-model="formData.SchoolMaterialModelId" style="width: 350px;">
                                <el-option v-for="item in modelList" :key="item.SchoolMaterialModelId"
                                    :label="item.Model" :value="item.SchoolMaterialModelId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="品牌：" prop="SchoolMaterialBrandId">
                            <el-select v-model="formData.SchoolMaterialBrandId" style="width: 350px">
                                <el-option v-for="item in brandList" :key="item.SchoolMaterialBrandId"
                                    :label="item.Brand" :value="item.SchoolMaterialBrandId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="数量：">
                            <el-input v-model="formData.Num" @input="limitInput($event, 'Num')" style="width: 350px;" />
                            <span style="color: #F56C6C;padding: 0 10px;">({{ formData.UnitName }})</span>
                        </el-form-item>
                        <el-form-item label="用途：">
                            <el-input v-model="formData.Remark" type="textarea" style="width: 350px"></el-input>
                        </el-form-item>
                        <el-form-item label="使用时间：">
                            <el-date-picker v-model="formData.UseTime" type="date" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" style="width: 350px;">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="退回原因：">
                            <template #label>
                                <span style="color: #F56C6C;">退回原因：</span>
                            </template>
                            <span style="color: #F56C6C;">{{ formData.ApprovalRemark }}</span>
                        </el-form-item>
                    </div>
                    <div v-else-if="dialogNum == 2">
                        <el-form-item label="剩余退回数量：" prop="BackNum">
                            <el-input v-model="formData.BackNum" @input="limitInput($event, 'BackNum')"
                                style="width: 200px;" />
                            <span style="color: #999;padding-left: 5px;"> 领用数量：{{ formData.Num }}</span>
                        </el-form-item>
                        <el-form-item>
                            <span style="color: #F56C6C;font-size: 14px;">网上提交后，请及时将剩余危化品送交给危化品保管员！</span>
                        </el-form-item>
                    </div>
                    <div v-else-if="dialogNum == 1">
                        <el-form-item label="药品是否有剩余：" prop="SurplusStatuz">
                            <el-radio-group v-model="formData.SurplusStatuz" style="width: 100%;">
                                <el-radio value="2"> 无剩余 </el-radio>
                                <el-radio value="1"> 有剩余 </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="是否产生危废物：" prop="WasetStatuz">
                            <el-radio-group v-model="formData.WasetStatuz" style="width: 100%;">
                                <el-radio value="2"> 无危废物 </el-radio>
                                <el-radio value="1"> 有危废物 </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item prop="isAgree" v-if="formData.SurplusStatuz == 2" label-width="30px">
                            <el-checkbox v-model="formData.isAgree" label="我承诺领用的药品已在实验中全部使用完！"> </el-checkbox>
                        </el-form-item>
                        <el-form-item label="剩余退回数量：" prop="BackNum" v-if="formData.SurplusStatuz == 1">
                            <el-input v-model="formData.BackNum" @input="limitInput($event, 'BackNum')"
                                style="width: 200px;" />
                            <span style="color: #999;padding-left: 5px;"> 领用数量：{{ formData.Num }}</span>
                        </el-form-item>
                        <el-form-item v-if="formData.SurplusStatuz == 1" label-width="50px">
                            <span style="color: #F56C6C;font-size: 14px;">网上提交后，请及时将剩余危化品送交给危化品保管员！</span>
                        </el-form-item>
                        <el-form-item v-if="formData.WasetStatuz == 1" label-width="10px">
                            <el-table :data="dialogTableData" highlight-current-row border stripe
                                header-cell-class-name="headerClassName">
                                <el-table-column type="index" width="50"></el-table-column>
                                <el-table-column prop="OneClassName" label="一级分类" min-width="120"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="Name" label="二级分类" min-width="140"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="UnitsMeasurement" label="单位" min-width="90"
                                    align="center"></el-table-column>
                                <el-table-column prop="Num" label="数量" min-width="120" align="center">
                                    <template #default="{ row, $index }">
                                        <el-input v-model="row.Num"
                                            @input="limitTable($event, row, $index, 'Num')"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="Remark" label="备注" min-width="200" align="center">
                                    <template #default="{ row, $index }">
                                        <el-input v-model="row.Remark"></el-input>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </div>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="danger" @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit(1)"> 保存 </el-button>
                    <el-button v-if="dialogNum == 3" type="primary" @click="HandleSubmit(2)"> 领用申请 </el-button>
                </span>
            </template>
        </app-box>
        <app-box v-model="whpDialogVisible" :width="960" :lazy="true" title="请选择危化品">
            <template #content>
                <el-tabs tab-position="left" v-model="activeName" style="height: 480px" class="demo-tabs">
                    <el-tab-pane label="搜索" name="search">
                        <div>
                            <el-input v-model.trim="firstName" placeholder="危化品名称" style="width: 280px"> </el-input>
                            <el-button type="primary" :icon="Search" @click="HandleSearch1">搜索</el-button>
                        </div>
                        <div>
                            <div v-if="isSearch && firstName && dangerchemicalsList.length == 0"
                                style="padding: 10px;color: #E6A23C;">危化品不存在，请重新搜索！</div>
                            <ul class="SearchUi" v-else>
                                <li v-for="item in dangerchemicalsList" :key="item.Id"
                                    @click="dangerchemicalsClick(item)">
                                    {{ item.FirstName }} > {{ item.SecondName }} > {{ item.Name }}</li>
                            </ul>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="危化品" name="content">
                        <ul class="whpUi">
                            <li v-for="item in bogetList[0].children" :key="item.Id">
                                <div class="content_left">
                                    {{ item.Name }}
                                </div>
                                <div class="content_right">
                                    <div v-for="item1 in item.children" :key="item1.Id" style="padding: 2px 0;">
                                        <el-divider direction="vertical" />
                                        <span class="text" style="padding: 2px;" @click="dangerchemicalsClick(item1)">
                                            {{ item1.Name }}</span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                </el-tabs>
            </template>
        </app-box>
        <app-box v-model="detailDialogVisible" :width="560" :lazy="true" :title="statuzDesc">
            <template #content>
                <div style="padding:10px 20px;">
                    <div v-for="(item, index) in lookAuditList" :key="index" class="flexBox"
                        style="padding: 5px;border-bottom: 1px #d1cfd0 dotted;">
                        <span style="width: 40%;">联系人：{{ item.Name }} </span>
                        <span style="width: 40%;">联系电话：{{ item.Mobile }} </span>
                    </div>
                </div>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.mobile-box {
    :deep(.el-form-item) {
        margin-bottom: 16px !important;
    }
}

.whpUi {
    font-size: 12px;
    background-color: #f9f9f9;
    padding: 5px;
    padding-inline-start: 0px;
    margin: 0;

    li {
        list-style-type: none;
        font-size: 12px;
        display: flex;
        padding: 10px 5px;
        border: 1px solid #f9f9f9;
        border-bottom: 1px dotted #d1cfd0;

        .content_left {
            width: 140px;
            flex-shrink: 0;
            color: #723535;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .content_right {
            display: flex;
            flex-wrap: wrap;
            color: #3c3c3c;

            .text:hover {
                cursor: pointer;
                color: #ff9933 !important;
            }
        }
    }

    li:hover {
        background: #fff;
        border: 1px solid #f93 !important;
    }
}

.SearchUi {
    font-size: 14px;
    // background-color: #f9f9f9;
    padding: 5px;
    height: 500px;
    margin: 0;
    overflow-y: auto;

    li {
        list-style-type: none;
        padding: 5px;
        border-bottom: 1px dotted #d1cfd0;
    }

    li:hover {
        background: #fff;
        color: #ff9933 !important;
        border: 1px solid #f93 !important;
        cursor: pointer;
    }
}
</style>