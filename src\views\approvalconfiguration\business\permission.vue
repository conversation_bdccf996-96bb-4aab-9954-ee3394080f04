<script setup>
defineOptions({
  name: 'businesspermission'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import { Select } from '@element-plus/icons-vue'
import {
  GetProcessAuditUserList, GetSetUserList, SetNodeAuditUser
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
// 初始化
const formData = ref({})
const route = useRoute()
const listData = ref([])
const refTable = ref()
const dialogData = ref({
  auditUserId: [],
  required: false
})
const refForm = ref()
const ruleForm = {}
const auditUserList = ref([])
const activeNames = ref([])
const yesNoList = ref([{ value: 1, label: '是' }, { value: 2, label: '否' }])
const msg = ref('')
//加载数据
onMounted(() => {

  if (route.query.isTagRouter) {
    GetProcessAuditUserListUser()
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      GetProcessAuditUserListUser()
    }
  })
})

const handleChange = (val) => {
  console.log(val)
}
const dialogVisible = ref(false)
// 设置操作人
const HandleSet = (row, item) => {
  console.log(row, item)
  if (row.UseGroupValue == '2') {
    dialogVisible.value = true
    dialogData.value.auditUserId = row.ListAuditUserId || []
    formData.value.ListUserId = row.ListAuditUserId || []
    GetSetUserListUser(row.ProcessId, row.ProcessNodeId)
    formData.value.processId = row.ProcessId
    formData.value.processNodeId = row.ProcessNodeId
    formData.value.ModuleId = item.ModuleId
  } else {
    // 跳转新页面
    router.push({ path: './classification', query: { ModuleId: item.ModuleId, ProcessId: row.ProcessId, ProcessNodeId: row.ProcessNodeId, GroupId: row.UseGroupValue } })

  }

}
// 提交设置操作人
const HandleSubmit = (row) => {
  SetNodeAuditUserUser()
  dialogVisible.value = false
}
// 选择设置操作人
const changeAuditUserId = (val) => {
  console.log(val)
  formData.value.ListUserId = val
}
// 获取权限设置列表
const GetProcessAuditUserListUser = () => {
  GetProcessAuditUserList().then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      console.log("rows", rows)
      listData.value = rows.data
      activeNames.value = listData.value.map((item) => item.ModuleId)
      if (listData.value.length == 0) {
        msg.value = '当前未开通相关业务'
      } else {
        msg.value = ''
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 获取设置的操作人数据信息
const GetSetUserListUser = (processId, processNodeId) => {

  GetSetUserList({ processId: processId, processNodeId: processNodeId }).then((res) => {
    if (res.data.flag == 1) {
      const { rows, headers } = res.data.data
      console.log("rows", rows)
      auditUserList.value = rows
      dialogData.value.IsLook = headers
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 设置节点操作人
const SetNodeAuditUserUser = () => {
  formData.value.IsLook = dialogData.value.IsLook
  SetNodeAuditUser(formData.value).then((res) => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '提交成功')
      GetProcessAuditUserListUser()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

</script>
<template>

  <div class="demo-collapse" v-if="listData.length > 0">
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item v-for="(item, index) in listData" :key="item.ModuleId" :name="item.ModuleId">
        <template #title>
          <div>{{ item.ModuleName }}</div>
        </template>
        <el-table ref="refTable" :data="item.ListAuditUser" highlight-current-row border stripe
          header-cell-class-name="headerClassName">
          <el-table-column prop="ProcessName" label="业务名称" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="ProcessNodeName" label="业务操作节点名称" min-width="140"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="RoleName" label="业务内容" min-width="200" show-overflow-tooltip align="center">
          </el-table-column>
          <el-table-column prop="AuditUserName" label="操作人姓名" min-width="180" align="center">
            <template #default="{ row }">
              <span v-if="row.UseGroupValue == '2'"> {{ row.AuditUserName }} </span>
              <span v-else><el-button type="primary" link @click="HandleSet(row, item)">查看</el-button></span>
            </template>
          </el-table-column>
          <el-table-column prop="IsLook" label="是否互看" min-width="100" align="center">
            <template #default="{ row }">
              <span v-if="row.IsLook == 1">
                <el-icon color="#67C23A"> <Select /> </el-icon>
              </span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right" align="center">
            <template #default="{ row }">
              <el-button type="primary" link @click="HandleSet(row, item)">设置</el-button>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="没有数据"></el-empty>
          </template>
        </el-table>
      </el-collapse-item>
    </el-collapse>
    <app-box v-model="dialogVisible" :width="680" :lazy="true" title="设置操作人">
      <template #content>

        <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="120px" status-icon>
          <el-form-item label="操作人员：" prop="auditUserId">
            <el-checkbox-group v-model="dialogData.auditUserId" @change="changeAuditUserId">
              <el-checkbox v-for="item in auditUserList" :key="item.UserId" :label="item.UserName" :value="item.UserId">
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="是否允许互看：" prop="required">
            <el-radio-group v-model="dialogData.IsLook">
              <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                {{ item.label }}</el-radio>
            </el-radio-group>
            <span style="padding-left: 50px;color: #F56C6C;">是指多人有权操作同一个业务节点，业务数据可互看。</span>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
        </span>
      </template>
    </app-box>
  </div>
  <div v-else class="emptyMsg">
    <span> {{ msg }} </span>
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-checkbox-group) {
  // width: 100%;
  // display: flex;
  // flex-wrap: wrap;
}

:deep(.el-checkbox) {
  // width: 20%;
  // margin-right: 0;
}
</style>
