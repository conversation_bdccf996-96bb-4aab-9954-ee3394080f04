<script setup>
defineOptions({
  name: 'articlelist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
  ArticleGetarticlepaged, ArticleDelarticle, ArticlePubarticle
} from '@/api/user.js'
import {
  ArticleGetbottomcatetype
} from '@/api/home.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, isFirst: true })

//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
      ArticleGetbottomcatetypeUser()
    }
  })
})

//新增
const HandleAdd = () => {
  router.push({ path: "./edit", query: { id: 0 } })
}
// 修改
const HandleEdit = (row) => {
  router.push({ path: "./edit", query: { id: row.Id } })
}

//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      ArticleDelarticle({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  ArticleGetarticlepaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()

}
// 重置
const HandleReset = () => {
  filters.value.Key = undefined
  filters.value.pageIndex = 1
  HandleTableData()
}
// 分页
const handlePage = () => {
  HandleTableData()
}
const handlePub = (row) => {
  let msg = ''
  if (row.Statuz == 2) {
    msg = '确定取消发布吗?'
  } else {
    msg = '确定发布吗?'
  }

  ElMessageBox.confirm(msg)
    .then(() => {
      ArticlePubarticle({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          filters.value.pageIndex = 1
          HandleTableData()
          ElMessage.success(res.data.msg || '操作成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//底部资讯分类信息
const ArticleGetbottomcatetypeUser = () => {
  ArticleGetbottomcatetype({ topCount: 3 }).then(res => {
    // console.log("资讯列表", res)
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      let articleFooterList = rows || [];//底部资讯分类信息
      userStore.setArticleFooter(articleFooterList)
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Key" clearable placeholder="标题/资讯短标题" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="Title" label="标题" min-width="200"></el-table-column>
      <el-table-column prop="ShortTitle" label="资讯短标题" min-width="200"></el-table-column>
      <el-table-column prop="CategoryName" label="资讯分类" min-width="140" align="center"></el-table-column>
      <el-table-column prop="BeginTime" label="发布时间" min-width="180" align="center"> </el-table-column>
      <el-table-column prop="Statuz" label="状态" min-width="180" align="center">
        <template #default="{ row }">
          {{ row.Statuz == 2 ? '已发布' : row.Statuz == 0 ? '已保存' : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="Sort" label="排序" min-width="80" align="center"></el-table-column>
      <el-table-column fixed="right" label="操作" min-width="190" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
          <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
          <div style="display: inline-block;width: 80px;">
            <el-button v-if="row.Statuz == 0" type="primary" link @click="handlePub(row)">发布</el-button>
            <el-button v-if="row.Statuz == 2" type="primary" link @click="handlePub(row)">取消发布</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>