<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'
import { ref } from 'vue'
import {
    ArticleGetinformationdetail
} from '@/api/home.js'
import { onMounted, onUnmounted } from 'vue';
import HomeHeader from '@/views/HomePage/HomeHeader.vue';
import Footer from '@/views/HomePage/homefooter.vue';
import { fileDownload } from "@/utils/index.js";
import { useRoute } from 'vue-router'
const route = useRoute()
const articleData = ref({ BeginTime: '' })
const fileName = ref('下载')
onMounted(() => {
    document.documentElement.style.setProperty('--body-background-color', '#f2f2f2');
    document.documentElement.style.setProperty('--body-height', 'auto');
    ArticleGetinformationdetailUser(route.query.Id)
})
onUnmounted(() => {
    // 移除样式
    document.documentElement.style.setProperty('--body-background-color', '#ffffff');
    document.documentElement.style.setProperty('--body-height', '100%');
});
//根据资讯分类Id查询资讯信息  
const ArticleGetinformationdetailUser = (id) => {
    ArticleGetinformationdetail({ id: id }).then(res => {
        // console.log("资讯详情", res)
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            articleData.value = rows
            if (rows.Attachment) {
                let path = rows.Attachment
                let index = path.lastIndexOf(".")
                let Ext = path.substring(index)
                if (Ext == ".png" || Ext == ".jpg" || Ext == ".jpeg") {
                    fileName.value = '预览'
                } else {
                    fileName.value = '下载'
                }
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
const showViewer = ref(false)
const viewPhotoList = ref([])
// 附件图片预览与文件下载
const fileListDownload = () => {
    let path = articleData.value.Attachment
    let index = path.lastIndexOf(".")
    let Ext = path.substring(index)
    viewPhotoList.value = [path]
    if (Ext == ".png" || Ext == ".jpg" || Ext == ".jpeg") {
        showViewer.value = true;
    } else {
        let title = articleData.value.Title + Ext
        fileDownload(path, title)
    }
}
</script>
<template>
    <header class="headerNav">
        <HomeHeader></HomeHeader>
    </header>
    <section class="section">
        <div>
            <div class="Title">
                <h1>{{ articleData.Title }}</h1>
                <span>{{ articleData.BeginTime.substring(0, 10) }} </span>
            </div>
            <!-- <div class="item-txt-title">{{ articleData.ShortTitle }}</div> -->
            <div v-if="articleData.ImageUrl" style="margin: 10px auto;text-align: center;">
                <img :src="articleData.ImageUrl" style="max-width:600px;max-height:480px;" />
            </div>
            <div class="item-txt-remark" v-html="articleData.Remark"></div>
        </div>
        <div class="fileDiv" v-if="articleData.Attachment">
            附件：<span class="fileSpan" @click="fileListDownload">{{ fileName }}</span>
        </div>
    </section>
    <div style="height: 80px;"></div>
    <footer class="footer">
        <Footer></Footer>
    </footer>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
.headerNav {
    margin-bottom: 50px;
}

.imgNav {
    width: 1200px;
    margin: 10px auto;
}

.section {
    width: 1200px;
    margin: 60px auto 10px auto;
    background-color: #ffffff;
    padding: 50px;

    .Title {
        text-align: center;
        padding-bottom: 15px;
        border-bottom: 1px dashed #e6e6e6;

        h1 {
            font-size: 24px;
            text-align: center;
            margin-bottom: 10px;
        }

        span {
            font-size: 14px;
            color: #333;
        }
    }
}

.footer {
    background-color: #545c64;
    color: #fff !important;
    padding: 10px 0;
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 99;
}

:deep(table) {
    border-collapse: collapse !important;
    border: 1px solid #e5e5e5 !important;
}

:deep(th),
:deep(td) {
    border: 1px solid #e5e5e5 !important;
    /* 设置单元格的边框为单线，颜色为黑色 */
    text-align: center;
    // height: 20px;
    height: 20px;
}

.fileDiv {
    text-align: center;
    font-size: 18px;

    .fileSpan {
        color: #3c9cff;
        cursor: pointer;
    }
}

:deep(tr) {

    min-height: 20px;
}
</style>
