<script setup>
import { watch, onMounted, onActivated, ref, nextTick, getCurrentInstance } from 'vue'
import {
  QuestionFilled
} from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { VueDraggableNext } from "vue-draggable-next";
import {
  NodeConfigSave, GetDataSource, NodeJsonfindByid, GetdataSourceBydicValue, GetGenerateCodeSet, PostSaveGenerateCodeSet
} from '@/api/workflow.js'
import {
  BatGetpaged
} from '@/api/user.js'
import AppWangEditor from "@/components/Editor/index.vue";//文本编辑器
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import FormPreview from "./FormPreview.vue";//预览弹窗
import FormTable from "./FormTable.vue";//表格配置
import { rulesData, rulesIntegerLimit } from "@/utils/rules.js";
import { UploadFileTypeList, controlList, popperOptions } from "@/utils/index.js";
import { componentsList, tableOption } from "./options";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
import router from '@/router'
const userStore = useUserStore()
const route = useRoute()
const props = defineProps({
  userComponents: {
    type: Array,
    default: () => {
      return [];
    },
  },
  nodeId: {
    type: String,
    default: "",
  },
});
const colWidth = ref(100)//宽度
const currentIndex = ref(-1)//当前表单项索引
const currentItem = ref({})//当前表单项
const currnetTableData = ref([])//table配置数据
const currentTableOption = ref(tableOption)//table配置columns
const currentComponents = ref([])//保存数据
const previewComponents = ref([]);//查看详情数据
const dicList = ref([])//所有数据源
const dicSelectList = ref([])//下拉框数据源
const dicTableList = ref([])////表格数据源
const tableModel = ref(false)
const previewModel = ref(false)
// 监听 colWidth 的变化
watch(colWidth, (newVal) => {
  if (currentIndex.value !== -1) {
    currentComponents.value[currentIndex.value].width = newVal;
  }
});

// 监听 userComponents 的变化
watch(
  () => props.userComponents,
  (newVal) => {
    currentComponents.value = newVal;
  },
  { immediate: true, deep: true }
);
onMounted(() => {
  if (route.query.isTagRouter) {
    currentComponents.value = [];
    NodeJsonfindByidUser()
  }
})

onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      currentComponents.value = [];
      NodeJsonfindByidUser()
    }
  })
})

// 预览
const preview = () => {
  previewModel.value = true;
  previewComponents.value = JSON.parse(JSON.stringify(currentComponents.value))
}

// 保存表单配置
const save = () => {
  // let field = currentComponents.value.find((t) => !t.field)
  // if (field) {
  //   ElMessage.error("所有元素字段(唯一字段)必填");
  //   return
  // }
  let code = currentComponents.value.find((t) => !t.code)
  if (code) {
    ElMessage.error("所有元素Code编码必填");
    return
  }
  let key = currentComponents.value.find((t) => {
    return (["radio", "cascader", "checkbox", "select", "selectList", 'OneClassId', 'TwoClassId'].includes(t.type)) && !t.key
  })
  if (key) {
    ElMessage.error("所有数据源必填");
    return
  }

  let arg = currentComponents.value.find((t) => {
    return (t.rules == "maxLength" || t.rules == "minLength" || t.rules == "decimals" || t.rules == "rangeLength") && !t.arg
  })
  if (arg) {
    ElMessage.error("请填写校验位数");
    return
  }

  // console.log("currentComponents.value", currentComponents.value);

  // 1. 创建 code 到对象的映射表
  const codeMap = currentComponents.value.reduce((map, item) => {
    map[item.code] = item;
    return map;
  }, {});

  // 2. 遍历处理父子关系
  currentComponents.value.forEach(item => {
    // console.log("item.ParentCode", item.ParentCode);
    if (item.ParentCode) {
      const parent = codeMap[item.ParentCode];
      // console.log("parent", parent);
      if (parent) {
        // 初始化父元素的 children 数组
        parent.children = parent.children || [];
        // 将当前元素添加到父元素的 children
        parent.children.push(item.code);
        parent.children = [...new Set(parent.children)]
        // 标记父元素存在子级关联
        parent.linkageParent = true;
      }
    }
    if (!item.DefaultValue) {
      item.DefaultValue = '';
    }
  });
  let data = {
    NodeId: props.nodeId,
    NodeConfig: JSON.stringify(currentComponents.value)
  }
  console.log("currentComponents.value", currentComponents.value);
  // 提交数据
  NodeConfigSaveUser(data)
}
// table表格配置
const openTableModel = (item) => {
  console.log("table表格配置item", item);
  currnetTableData.value = JSON.parse(
    JSON.stringify(currentItem.value.columns)
  );

  tableModel.value = true;
}

const removeItem = (index) => {
  currentComponents.value.splice(index, 1);
  colWidth.value = 100;
  currentIndex.value = -1;
  currentItem.value = {};
}
const clearItems = () => {
  currentComponents.value.length = 0;
  colWidth.value = 100;
  currentIndex.value = -1;
  currentItem.value = {};
}
const itemClick = (item, index) => {
  console.log("点击元素", item, index)
  currentIndex.value = index;
  currentComponents.value = currentComponents.value
  colWidth.value = currentComponents.value[currentIndex.value].width;
  currentItem.value = currentComponents.value[currentIndex.value];

  if (item.type == 'statisticstable') {
    currnetTableData.value = JSON.parse(JSON.stringify(item.columns));
  }
}
//开始拖拽事件
const onStart = (e, e1) => {
  // drag.value = true;
  console.log('开始拖拽事件', e, e1);
}
const getField = () => {
  return "field" + new Date().valueOf();
}
//左边往右边拖动时的事件
const end1 = (e, i) => {
  console.log("end1", e, i);
  if (e.from !== e.to) {
    console.log("componentsList[i]", componentsList[i]);
    let obj = JSON.parse(JSON.stringify(componentsList[i].components[e.oldIndex]));
    obj.field = getField();
    obj.width = 100;
    currentComponents.value.splice(e.newIndex, 1, obj);
    props.userComponents.splice(0);
    props.userComponents.push(...currentComponents.value);

    colWidth.value = 100;
    currentIndex.value = e.newIndex;
    currentItem.value = currentComponents.value[currentIndex.value];
    console.log("currentItem.value ", currentItem.value);
    if (currentItem.value.type == "statisticstable") {
      currentItem.value.columnsData = currentItem.value.columns
    }
    if (currentItem.value.type == "subjectnature") {
      GetdataSourceBydicValueUser('15000_2')
    }
  }

}
const onMove = (e, originalEvent) => {
  // moveId.value = e.relatedContext.element.id;
  return true;
}
//选择下拉框数据源后获取对应数据 
const selectDicChange = (key, item) => {
  // console.log(key);
  GetdataSourceBydicValueUser(key)
  // 切换数据源时清除默认值及页回显数据    
  if (["cascader", "checkbox", "selectList", 'OneClassId', 'TwoClassId'].includes(item.type)) {
    item.acquiesce = []
    item.value = []
    item.DefaultValue = []
    item.DefaultName = ''
    // console.log("item", item);
  } else {
    item.acquiesce = ''
    item.value = ''
    item.DefaultValue = ''
    item.DefaultName = ''
  }
}
//选择表格数据源后获取对应数据 
const tableDicChange = (key, item) => {

  GetdataSourceBydicValue({ code: key }).then(res => {
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      // console.log("根据数据源选择的编码获取数据", rows)
      rows.forEach(item => {
        item.isShow = true
      });
      currentItem.value.tableData = [];
      currentItem.value.columns = JSON.parse(JSON.stringify(rows));
      // console.log("currentItem.value.columns", currentItem.value.columns)
      currentItem.value.columnsData = JSON.parse(JSON.stringify(rows));

      currnetTableData.value = JSON.parse(JSON.stringify(rows));
      // currentTableOption.value = JSON.parse(JSON.stringify(rows));
      // console.log("currentItem.value.tableData", currentItem.value)
    } else {
      ElMessage.error(res.data.msg)
    }
  });

}

// 递归函数，用于根据值数组找到对应的 label 路径
const getLabelPath = (e, data) => {
  let labels = [];
  let currentOptions = data;
  for (let i = 0; i < e.length; i++) {
    const value = e[i];
    const foundOption = currentOptions.find(option => option.value === value);
    if (foundOption) {
      labels.push(foundOption.label);
      currentOptions = foundOption.children || [];
    } else {
      // 如果没有找到对应的选项，可能是数据不一致，返回空数组
      return [];
    }
  }
  return labels;
};
// 选择默认值
const DefaultValueChange = (key, item) => {
  // console.log('选择默认值', key, item)
  item.DefaultValue = key

  if (item.type == "cascader") {
    // 级联
    item.value = key
    let selectedLabel = getLabelPath(key, item.data);
    item.DefaultName = selectedLabel.join('/')
  } else if (item.type == "selectList" || item.type == "checkbox") {
    // 多选
    item.value = key
    const labels = key.map(id => String(id)).map(id => {
      const items = item.data.find(item => item.value === id);
      return items ? items.label : '';
    });
    item.DefaultName = labels.join(',');
  } else if (item.type == "select" || item.type == "radio") {
    // 单选
    item.value = key;
    item.data.filter((t) => { if (t.value == key) { item.DefaultName = t.label } })
  } else {
    item.value = key;
    item.DefaultName = key;
  }
}
// 输入默认值后反选元素默认
const changeDefaultValue = (key, item) => {
  // console.log(key)
  item.value = key;
  item.DefaultName = key;
}
//表单校验位数输入框限制：输入整数
const rulesInput = (val, name) => {
  currentItem.value[name] = rulesIntegerLimit(val);
}
// 帮助文字
const onChangeHelpRemark = (key) => {
  // console.log("帮助文字", key);
  if (currentItem.value.type == "helpremark") {
    currentItem.value.value = key;
  }
}

// 字段名称修改后，修改按钮名称
const changeButtonTitle = (key) => {
  if (currentItem.value.type == "button") {
    currentItem.value.buttonTitle = key;
  }
}
// 附件上传选择code
const fileCoDeChange = (e) => {

  let arr = fileCoDeList.value.filter(t => t.FileCategory == e)
  console.log("arr", arr)
  currentItem.value.name = arr[0].Name
  currentItem.value.FileSize = arr[0].FileSize
  currentItem.value.MaxFileNumber = arr[0].MaxFileNumber
  currentItem.value.UploadFileType = arr[0].UploadFileType.split(',')
  currentItem.value.HelpRemark = arr[0].Memo
  if (arr[0].IsFilled == 1) {
    currentItem.value.required = true
  } else {
    currentItem.value.required = false
  }
}
// 选择是否作为条件
const conditionChange = (e) => {
  console.log(e)
  // 作为条件则必填且禁用  
  if (e) {
    currentItem.value.required = true
    currentItem.value.disabled = true

  } else {
    currentItem.value.disabled = false
  }
}
// 获取下拉框数据源
const GetDataSourceUser = () => {
  GetDataSource().then(res => {
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      let dicList = rows || []
      dicSelectList.value = dicList.filter(t => t.pid != 6)
      dicTableList.value = dicList.filter(t => t.pid == 6)
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 保存节点Json数据
const NodeConfigSaveUser = (data) => {
  NodeConfigSave(data).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '保存成功')
      let tagsList = userStore.tagsList
      tagsList = tagsList.filter(t => t.path != route.path)
      userStore.setTagsList(tagsList)
      router.push({ path: "/approvalconfiguration/workflow/nodemanagement" })
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 根据Id获取节点Json数据
const NodeJsonfindByidUser = () => {
  NodeJsonfindByid({ id: props.nodeId }).then(res => {
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      // console.log("根据Id获取节点Json数据", rows)
      if (rows) {
        currentComponents.value = JSON.parse(rows);
      } else {
        currentComponents.value = []
      }
      // 获取数据源数据
      GetDataSourceUser()
      // 获取附件数据
      BatGetpagedUser()
      console.log("currentComponents.value", currentComponents.value)
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 根据数据源选择的编码获取数据
const GetdataSourceBydicValueUser = (code) => {
  GetdataSourceBydicValue({ code: code }).then(res => {
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      // console.log("根据数据源选择的编码获取数据", rows)
      currentItem.value.data = rows;
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
const fileCoDeList = ref([])
// 获取附件配置编码
const BatGetpagedUser = (filters) => {
  BatGetpaged({ pageIndex: 1, pageSize: 10000, ModuleType: 501, sortModel: [{ SortCode: "Sequence", SortType: "asc" }] }).then(res => {
    if (res.data.flag == 1) {
      console.log("备案附件列表", res.data.data)
      const { rows, total, other } = res.data.data
      fileCoDeList.value = rows || [];
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}



// 子组件：表格配置提交事件
const confirm = (data) => {
  console.log("组件提交事件", data);
  currentItem.value.columns = data
  currentItem.value.columnsData = data.filter(item => item.isShow)
  // console.log("保存表格配置", currentItem.value.columnsData);
  tableModel.value = false;
}

const inputValue = ref('');

const handleInput = (value) => {
  // 转换为小写并移除非字母数字字符
  let processed = value.toLowerCase().replace(/[^a-z0-9]/g, '');

  // 查找第一个字母的位置
  const firstLetterIndex = processed.search(/[a-z]/);

  if (firstLetterIndex === -1) {
    // 没有字母则清空
    inputValue.value = '';
    currentItem.value.code = '';
  } else {
    // 从第一个字母开始截取
    processed = processed.slice(firstLetterIndex);
    inputValue.value = processed;
    currentItem.value.code = processed;
  }
};
// 项目编号配置弹窗编辑
const dialogVisible = ref(false)
const refForm = ref()
const dialogData = ref({})
const ruleForm = {
  DateFormatValue: [
    { required: true, message: '请选择日期格式', trigger: 'change' },
  ],
  SuffixNumberType: [
    { required: true, message: '请选择自增方式', trigger: 'change' },
  ],
  NumberCount: [
    { required: true, message: '请输入末位数字位数', trigger: 'change' },
  ],
}
const dateFormatList = ref([{ value: 'yyyy', label: 'yyyy' }, { value: 'yyyyMM', label: 'yyyyMM' }, { value: 'yyyyMMdd', label: 'yyyyMMdd' }])
const suffixNumberList = ref([{ value: 5, label: '按全平台自增' }, { value: 3, label: '按单位自增' }, { value: 2, label: '按区县自增' }])
// 获取项目编号配置弹窗
const showProjectNumberConfig = (item) => {
  dialogVisible.value = true;
  GetGenerateCodeSet({ processNodeId: route.query.id, fieldCode: item.code }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      // console.log("项目编号配置弹窗", rows)
      if (rows) {
        dialogData.value = {
          ProcessNodeId: rows.Id,
          FieldCode: rows.FieldCode,
          PreType: 2,
          Name: rows.Name,
          PreSymbol: rows.PreSymbol,
          DateFormatValue: rows.DateFormatValue,
          StufixSymbol: rows.StufixSymbol,
          SuffixNumberType: rows.SuffixNumberType,
          AutoMode: rows.AutoMode,
          NumberCount: rows.NumberCount,
        }
      } else {
        nextTick(() => {
          refForm.value.resetFields()
          dialogData.value = {
            ProcessNodeId: route.query.id,
            FieldCode: item.code,
            PreType: 2,
            Name: '',
            PreSymbol: '',
            DateFormatValue: '',
            StufixSymbol: '',
            SuffixNumberType: 5,
            AutoMode: '',
            NumberCount: 4,
          }
        })
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 项目编号配置提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    PostSaveGenerateCodeSet(dialogData.value).then(res => {
      if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '保存成功')
        dialogVisible.value = false;
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })
}
const logData = (e) => {
  // 防止修改数据时交换位置的控件回到原来位置
  currentComponents.value = JSON.parse(JSON.stringify(currentComponents.value))
}
</script>
<template>
  <div class="viewContainer">
    <div class="drag-container">
      <div class="drag-left">
        <!-- <div class="left-title">组件列表</div> -->
        <div v-for="(t, i) in componentsList" :key="t.pid">
          <div class="left-title" style="padding: 10px;background-color: #f2f5fb;">{{ t.text }}</div>
          <vue-draggable-next v-model="t.components" @end="end1($event, i)" class="left-draggable-item"
            :group="{ name: 'componentsGroup', pull: 'clone', put: false }" animation="300" @start="onStart"
            :sort="false" :move="onMove">
            <transition-group>
              <div class="item" v-for="(item, index) in t.components" :key="item.id">
                <i :class="item.icon"></i> {{ item.label }}
              </div>
            </transition-group>
          </vue-draggable-next>
        </div>
        <!-- <vue-draggable-next v-model="components" @end="end1" class="left-draggable-item"
          :group="{ name: 'componentsGroup', pull: 'clone', put: false }" animation="300" @start="onStart" :sort="false"
          :move="onMove">
          <transition-group>
            <div class="item" v-for="item in components" :key="item.id">
              <i :class="item.icon"></i> {{ item.label }}
            </div>
          </transition-group>
        </vue-draggable-next> -->
      </div>
      <div class="drag-center">
        <div class="center-top">
          <el-button type="primary" size="small" plain @click="save"><i class="el-icon-check"> </i>保存</el-button>
          <el-button type="primary" size="small" plain @click="preview"><i class="el-icon-view">
            </i>预览</el-button>
          <!-- <el-button type="primary" @click="clearItems" size="small" plain><i class="el-icon-delete"> </i>清空</el-button> -->
        </div>
        <el-scrollbar style="flex: 1">
          <div class="tips" key="empty" v-if="!currentComponents.length">
            请将左边组件拖入此容器中
          </div>
          <el-form>
            <vue-draggable-next class="draggable-container" v-model="currentComponents" item-key="field" animation="300"
              @change="logData" group="componentsGroup">
              <transition-group>
                <div class="item2" :class="{ actived: index === currentIndex }" @click="itemClick(item, index)"
                  :style="{ width: item.width + '%' }" v-for="(item, index) in currentComponents" :key="index">
                  <i class="el-icon-delete" @click.stop="removeItem(index)"> </i>
                  <el-form-item :required="item.required" style="width: 100%" :label="item.name"
                    :label-width="item.type == 'line' ? '0px' : item.labelWidth ? item.labelWidth + 'px' : '200px'">
                    <template #label>
                      <el-tooltip v-if="item.isRemark" class="item" effect="dark" :content="item.HelpRemark"
                        placement="top">
                        <div>
                          <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                          </el-icon>
                        </div>
                      </el-tooltip>
                      <span> {{ item.name ? item.name + '：' : '' }} </span>
                    </template>
                    <el-col>
                      <!-- 输入框 -->
                      <el-input
                        v-if="['text', 'auditusername', 'projectname', 'projectnumber', 'projectamount', 'fundallocation', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown', 'creationnameinput', 'ProjectCode'].includes(item.type)"
                        :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name" v-model="item.value"
                        :disabled="item.readonly">
                      </el-input>
                      <!-- 描述文本 -->
                      <span v-if="item.type === 'descriptivetext' || item.type === 'projectallamount'"
                        :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#999999', 'fontSize': '14px' }">
                        {{ item.value }}</span>
                      <!-- 文本域 -->
                      <el-input v-else-if="item.type == 'textarea' || item.type == 'auditremark'" type="textarea"
                        v-model="item.value" :disabled="item.readonly"
                        :maxlength="item.maxLength ? item.maxLength : 500" show-word-limit
                        :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name">
                      </el-input>
                      <!-- 按钮 -->
                      <el-button
                        v-else-if="['button', 'projectlist', 'projectexaminelist', 'download', 'upload'].includes(item.type)"
                        type="primary" size="small">
                        {{ item.buttonTitle }}
                      </el-button>
                      <!-- 日期 -->
                      <el-date-picker v-else-if="item.type == 'date' || item.type == 'auditdate'" v-model="item.value"
                        type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="item.readonly"
                        placeholder="选择日期" :popper-options="popperOptions">
                      </el-date-picker>
                      <!-- 日期时间 -->
                      <el-date-picker v-else-if="item.type == 'datetime'" v-model="item.value" type="datetime"
                        format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :disabled="item.readonly"
                        placeholder="选择日期时间" :popper-options="popperOptions">
                      </el-date-picker>
                      <!-- 时间 -->
                      <el-time-picker v-else-if="item.type == 'time'" v-model="item.value" :disabled="item.readonly"
                        format="HH:mm:ss" value-format="HH:mm:ss" placeholder="选择时间" :popper-options="popperOptions" />
                      <!-- 单选 -->
                      <!-- , 'isnewcreation' -->
                      <el-radio-group :disabled="item.readonly"
                        v-else-if="['radio', 'auditstatuz', 'isnewcreation'].includes(item.type)">
                        <el-radio v-for="item in item.data" :key="item.value" :value="item.value">{{ item.label }}
                        </el-radio>
                      </el-radio-group>
                      <!-- 单选框 -->
                      <el-checkbox-group v-model="item.value" :disabled="item.readonly"
                        style="width: 100%; display: inline-block" v-else-if="item.type == 'checkbox'">
                        <el-checkbox v-for="item in item.data" :key="item.value" :label="item.label"
                          :value="item.value">
                        </el-checkbox>
                      </el-checkbox-group>
                      <!-- 下拉框 includes -->
                      <el-select style="width: 100%" :disabled="item.readonly" v-model="item.value"
                        v-else-if="['select', 'subjectnature', 'OneClassId', 'TwoClassId', 'creationnameselect', 'AppointAuditUserSelect'].includes(item.type)"
                        :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name">
                        <el-option v-for="item in item.data" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                      </el-select>
                      <!-- 下拉多选框 -->
                      <el-select style="width: 100%" :disabled="item.readonly" v-model="item.value" :multiple="true"
                        v-else-if="item.type == 'selectList' || item.type == 'AppointAuditUserSelectList'"
                        :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name">
                        <el-option v-for="item in item.data" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                      </el-select>
                      <!-- 级联 -->
                      <el-cascader :disabled="item.readonly" style="width: 100%" v-else-if="item.type == 'cascader'"
                        v-model="item.value" :options="item.data" :props="{ checkStrictly: true }"
                        :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name">
                      </el-cascader>
                      <!-- 开关 -->
                      <el-switch :disabled="item.readonly" v-model="item.value" style="width: 100%"
                        v-else-if="item.type == 'switch'" active-color="#13ce66" inactive-color="#0e7ef3"
                        :active-value="1" :inactive-value="0">
                      </el-switch>
                      <!-- 排序 -->
                      <el-input type="number" v-model="item.value" v-else-if="item.type == 'sort'"></el-input>
                      <!-- 分割线 -->
                      <el-divider v-else-if="item.type == 'line'" :border-style="item.isDashed"
                        :content-position="item.isCenter"
                        :style="{ '--el-border-color': item.dividerColor ? item.dividerColor : '#dedfe6' }">
                        <span :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#303133' }">
                          {{ item.dividerText }}</span>
                      </el-divider>
                      <!-- 表格 -->
                      <div v-else-if="item.type == 'statisticstable'">
                        <el-table ref="refTable" :data="item.tableData" highlight-current-row border stripe
                          header-cell-class-name="headerClassName">
                          <el-table-column v-for="column in item.columnsData" :key="column.FieldCode"
                            :prop="column.FieldCode" :label="column.FieldName" :min-width="column.Width"
                            :align="column.ContentStyle"> </el-table-column>
                        </el-table>
                      </div>
                      <!-- 编辑器 -->
                      <app-wang-editor v-else-if="item.type == 'editor'" :url="item.url" v-model="item.value"
                        :height="item.height"></app-wang-editor>
                    </el-col>
                  </el-form-item>
                </div>
              </transition-group>
            </vue-draggable-next>
          </el-form>
        </el-scrollbar>
      </div>
      <div class="drag-right">
        <div class="left-title">组件属性</div>
        <div class="attr" v-if="currentIndex != -1">
          <div class="attr-item" v-if="currentItem.type != 'descriptivetext' && currentItem.type != 'line'">
            <div class="text">字段名称</div>
            <el-input v-model="currentItem.name" placeholder="请输入字段名称" />
          </div>
          <div class="attr-item" v-if="['button', 'projectlist', 'projectexaminelist'].includes(currentItem.type)">
            <div class="text">按钮文字</div>
            <el-input v-model="currentItem.buttonTitle" @change="changeButtonTitle" />
          </div>
          <!-- <div class="attr-item">
            <div class="text"><span style="color: #F56C6C">*</span>字段(唯一字段)</div>
            <el-input v-model="currentItem.field" disabled />
          </div> -->
          <div class="attr-item" v-if="currentItem.type != 'upload'">
            <div class="text"><span style="color: #F56C6C">*</span>Code编码</div>
            <el-input v-model.trim="currentItem.code" placeholder="节点内唯一"
              :disabled="['auditstatuz', 'auditremark', 'auditusername', 'auditdate', 'projectname', 'projectamount', 'projectallamount', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown', 'subjectnature', 'OneClassId', 'TwoClassId', 'creationnameselect', 'creationnameinput', 'isnewcreation', 'ProjectCode', 'AppointAuditUserSelect', 'AppointAuditUserSelectList'].includes(currentItem.type)" />
            <span style="font-size: 12px;color: #E6A23C;">《通用组件》的‘Code编码’，请避免使用《定制组件》内所有的‘Code编码’</span>

          </div>
          <div class="attr-item" v-if="currentItem.type == 'upload'">
            <div class="text"><span style="color: #F56C6C">*</span>选择附件</div>
            <el-select v-model="currentItem.code" @change="fileCoDeChange">
              <el-option v-for="item in fileCoDeList" :key="item.FileCategory" :label="item.Name"
                :value="item.FileCategory" />
            </el-select>
          </div>
          <div class="attr-item attr2">
            <div
              v-if="['text', 'select', 'selectList', 'radio', 'checkbox', 'cascader', 'date', 'time', 'datetime', 'projectamount'].includes(currentItem.type)">
              <div class="text">是否作为条件</div>
              <el-switch v-model="currentItem.isCondition" @change="conditionChange">
              </el-switch>
            </div>
            <div
              v-if="['projectname', 'projectamount', 'text', 'textarea', 'select', 'selectList', 'radio', 'checkbox', 'cascader', 'date', 'time', 'datetime', 'upload', 'fundallocation', 'subjectnature', 'auditstatuz', 'auditremark', 'OneClassId', 'TwoClassId', 'creationnameselect', 'creationnameinput', 'isnewcreation', 'AppointAuditUserSelect', 'AppointAuditUserSelectList'].includes(currentItem.type)">
              <div class="text">是否必填</div>
              <el-switch v-model="currentItem.required" active-color="#13ce66" inactive-color="rgb(165 165 165)"
                :active-value="true" :inactive-value="false"
                :disabled="currentItem.type == 'auditstatuz' || currentItem.disabled">
              </el-switch>
            </div>
          </div>
          <div v-if="['upload'].includes(currentItem.type)">
            <div class="attr-item">
              <div class="text">文件大小限制(MB)</div>
              <el-input v-model.number="currentItem.FileSize" @input="rulesInput($event, 'FileSize')"></el-input>
            </div>
            <div class="attr-item">
              <div class="text">文件最大数量</div>
              <el-input v-model="currentItem.MaxFileNumber" @input="rulesInput($event, 'MaxFileNumber')" />
            </div>
            <div class="attr-item">
              <div class="text">文件类型</div>
              <el-select v-model="currentItem.UploadFileType" multiple>
                <el-option v-for="item in UploadFileTypeList" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </div>
          </div>
          <div class="attr-item"
            v-if="['radio', 'checkbox', 'select', 'selectList', 'cascader', 'OneClassId', 'TwoClassId'].includes(currentItem.type)">
            <div class="text"><span style="color: #F56C6C">*</span>下拉框数据源 </div>
            <el-select style="width: 100%" v-model="currentItem.key" filterable
              @change="selectDicChange($event, currentItem)" placeholder="请选择数据源字典">
              <el-option v-for="item in dicSelectList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="attr-item" v-if="currentItem.type == 'download'">
            <div class="text">模板上传</div>
            此处调用上传方法
          </div>
          <div class="attr-item" v-if="currentItem.type == 'download'">
            <div class="text">模板下载路径</div>
            <el-input v-model="currentItem.TemplateUrl" />
          </div>
          <div class="attr-item" v-if="currentItem.type == 'button'">
            <div class="text">事件名</div>
            <!-- 后面用下拉框 -->
            <el-input v-model="currentItem.click" />
          </div>
          <div class="attr-item" v-if="currentItem.type == 'textarea' || currentItem.type == 'auditremark'">
            <div class="text">最大字数</div>
            <el-input v-model="currentItem.maxLength" @input="rulesInput($event, 'maxLength')" />
          </div>
          <div class="attr-item">
            <div class="text">宽度(百分比)</div>
            <el-slider style="width: 90%" :min="10" v-model="colWidth" :step="5" show-stops>
            </el-slider>
          </div>
          <div class="attr-item" v-if="currentItem.type == 'projectnumber' || currentItem.type == 'ProjectCode'">
            <div class="text">项目编号自动生成配置</div>
            <el-button type="primary" size="small" :disabled="!currentItem.code"
              @click="showProjectNumberConfig(currentItem)"> 配置</el-button>
          </div>
          <!-- <div class="attr-item attr2"
            v-if="!['statisticstable', 'line', 'auditusername', 'auditdate', 'button', 'download', 'projectlist', 'projectexaminelist', 'descriptivetext', 'projectnumber','unitname', 'unitaddress', 'unitperiod', 'unitstreettown'].includes(currentItem.type)">
            <div>
              <div class="text">必填</div>
              <el-switch v-model="currentItem.required" active-color="#13ce66" inactive-color="rgb(165 165 165)"
                :active-value="true" :inactive-value="false"
                :disabled="currentItem.type == 'auditstatuz' || currentItem.disabled">
              </el-switch>
            </div>
            <div>
              <div class="text">只读</div>
              <el-switch v-model="currentItem.readonly" active-color="#13ce66" inactive-color="rgb(165 165 165)"
                :active-value="true" :inactive-value="false">
              </el-switch>
            </div>
          </div> -->
          <div class="attr-item" v-if="currentItem.type == 'text'">
            <div class="text">表单元素校验规则</div>
            <el-select v-model="currentItem.rules" clearable placeholder="请选择校验规则">
              <el-option v-for="item in rulesData" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <div class="attr-item"
              v-if="['maxLength', 'minLength', 'decimals', 'rangeLength', 'amount'].includes(currentItem.rules)">
              <div class="text"><span style="color: #F56C6C">*</span>校验位数</div>
              <el-input v-if="currentItem.rules != 'rangeLength'" v-model="currentItem.arg"
                @input="rulesInput($event, 'arg')"
                :placeholder="currentItem.rules == 'decimals' ? '数字，即最多几位小数' : ''"></el-input>
              <el-input v-else v-model="currentItem.arg" placeholder="位数以','分隔。如：4,10"></el-input>
            </div>
          </div>

          <div class="attr-item" v-if="currentItem.type == 'line'">
            <div class="text">文本内容</div>
            <el-input v-model="currentItem.dividerText"></el-input>
          </div>
          <div class="attr-item" v-if="currentItem.type == 'line'">
            <div class="text">分割线类型</div>
            <el-radio-group v-model="currentItem.isDashed">
              <el-radio value="solid">实线 </el-radio>
              <el-radio value="dashed">虚线 </el-radio>
            </el-radio-group>
          </div>
          <div class="attr-item" v-if="currentItem.type == 'line'">
            <div class="text">内容位置</div>
            <el-radio-group v-model="currentItem.isCenter">
              <el-radio value="left">居左 </el-radio>
              <el-radio value="center">居中 </el-radio>
              <el-radio value="right">居右 </el-radio>
            </el-radio-group>
          </div>
          <div class="attr-item attr2" v-if="currentItem.type == 'line'">
            <div>
              <div class="text">分割线颜色</div>
              <el-color-picker v-model="currentItem.dividerColor" size="default">
              </el-color-picker>
            </div>
            <div>
              <div class="text">内容颜色</div>
              <el-color-picker v-model="currentItem.dividerTextColor" size="default">
              </el-color-picker>
            </div>
          </div>
          <div class="attr-item"
            v-if="['text', 'textarea', 'radio', 'checkbox', 'select', 'selectList', 'cascader', 'date', 'datetime', 'time', 'projectname', 'descriptivetext', 'subjectnature'].includes(currentItem.type)">
            <div class="text">{{ currentItem.type == 'descriptivetext' ? '描述文字' : '默认值' }} </div>
            <el-input v-model="currentItem.DefaultValue"
              :placeholder="currentItem.type == 'descriptivetext' ? '请输入描述文字' : '请输入默认值'"
              @change="changeDefaultValue($event, currentItem)"
              v-if="['text', 'textarea', 'projectname', 'descriptivetext'].includes(currentItem.type)" />
            <el-select v-if="['radio', 'select', 'subjectnature'].includes(currentItem.type)"
              v-model="currentItem.acquiesce" @change="DefaultValueChange($event, currentItem)" placeholder="请选择默认值">
              <el-option v-for="item in currentItem.data" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <el-cascader v-if="currentItem.type == 'cascader'" v-model="currentItem.acquiesce"
              :options="currentItem.data" :props="{ checkStrictly: true }"
              @change="DefaultValueChange($event, currentItem)" placeholder="请选择默认值"></el-cascader>
            <el-select v-if="['checkbox', 'selectList'].includes(currentItem.type)" v-model="currentItem.acquiesce"
              @change="DefaultValueChange($event, currentItem)" :multiple="true" placeholder="请选择默认值">
              <el-option v-for="item in currentItem.data" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <el-date-picker v-if="currentItem.type == 'date'" v-model="currentItem.acquiesce" type="date"
              format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="DefaultValueChange($event, currentItem)"
              placeholder="选择日期">
            </el-date-picker>
            <el-date-picker v-if="currentItem.type == 'datetime'" v-model="currentItem.acquiesce"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
              @change="DefaultValueChange($event, currentItem)" type="datetime" placeholder="选择日期时间">
            </el-date-picker>
            <el-time-picker v-if="currentItem.type == 'time'" v-model="currentItem.acquiesce" format="HH:mm:ss"
              value-format="HH:mm:ss" @change="DefaultValueChange($event, currentItem)" placeholder="选择时间" />
          </div>
          <div class="attr-item attr2" v-if="currentItem.type == 'descriptivetext'">
            <div>
              <div class="text">字体颜色</div>
              <el-color-picker v-model="currentItem.dividerTextColor" size="default">
              </el-color-picker>
            </div>
          </div>
          <div class="attr-item" v-if="currentItem.type != 'line'">
            <div class="text">label宽度（字段名称宽度）</div>
            <el-input v-model="currentItem.labelWidth" @input="rulesInput($event, 'labelWidth')"></el-input>
            <span style="font-size: 12px;color: #E6A23C;">主要用于同一行多个控件情况和《描述文字》控件！(空值或0为默认200px)，如非必要，无需修改。</span>
          </div>
          <div class="attr-item"
            v-if="['text', 'textarea', 'select', 'selectList', 'projectname', 'projectamount', 'cascader'].includes(currentItem.type)">
            <div class="text">占位文本</div>
            <el-input v-model="currentItem.placeholder" placeholder="输入框或下拉框内部提示文字" />
          </div>
          <div class="attr-item"
            v-if="!['statisticstable', 'line', 'auditusername', 'auditdate', 'descriptivetext', 'projectallamount', 'projectnumber', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown', 'ProjectCode'].includes(currentItem.type)">
            <div class="text">是否需要帮助文字</div>
            <el-switch v-model="currentItem.isRemark" active-color="#13ce66" inactive-color="rgb(165 165 165)"
              :active-value="true" :inactive-value="false">
            </el-switch>
            <div v-if="currentItem.isRemark">
              <div class="text" style="margin-top: 10px;">帮助文字</div>
              <el-input v-model="currentItem.HelpRemark" />
            </div>
            <br />
            <span style="font-size: 12px;color: #E6A23C;">字段名称前
              <el-icon color="#E6A23C" class="tipIcon">
                <QuestionFilled />
              </el-icon>提示。如需在控件后方或下方提示，请使用《描述文字》控件代替。</span>
          </div>
          <div class="attr-item" v-if="['select', 'radio', 'TwoClassId'].includes(currentItem.type)">
            <div class="text">联动父级别字段Code</div>
            <el-input v-model="currentItem.ParentCode"></el-input>
          </div>
          <div class="attr-item"
            v-if="currentItem.type == 'projectamount' || (currentItem.type == 'text' && (currentItem.rules == 'integer' || currentItem.rules == 'decimals'))">
            <div class="text">是否金额控制</div>
            <el-switch v-model="currentItem.IsAmountControl" active-color="#13ce66" inactive-color="rgb(165 165 165)"
              :active-value="1" :inactive-value="2">
            </el-switch>
          </div>
          <div class="attr-item"
            v-if="['select', 'radio', 'isnewcreation', 'OneClassId', 'TwoClassId'].includes(currentItem.type) || currentItem.IsAmountControl == 1">
            <div class="text">条件控制:主控</div>
            <el-select v-model="currentItem.MasterControl">
              <el-option v-for="item in controlList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="attr-item"
            v-if="!['auditusername', 'auditdate', 'projectnumber', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown', 'isnewcreation', 'ProjectCode', 'OneClassId', 'TwoClassId'].includes(currentItem.type)">
            <div class="text">条件控制:被控</div>
            <el-select v-model="currentItem.Controlled">
              <el-option v-for="item in controlList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="attr-item">
            <div class="text">是否继承上一次数据</div>
            <el-radio-group v-model="currentItem.IsUsePreData">
              <el-radio :value="1">是</el-radio>
              <el-radio :value="2">否 </el-radio>
            </el-radio-group>
          </div>
          <div class="attr-item" v-if="currentItem.type == 'text'">
            <div class="text">是否参与计算</div>
            <el-radio-group v-model="currentItem.isCalculation">
              <el-radio :value="true">是</el-radio>
              <el-radio :value="false">否 </el-radio>
            </el-radio-group>
          </div>
          <div class="attr-item" v-if="currentItem.isCalculation">
            <div class="text">计算总值的Code编码</div>
            <el-input type="textarea" v-model="currentItem.calculatedCode" :rows="3"
              placeholder="如果有多个总值，请以','隔开。如：code1,code2,code3"></el-input>
          </div>
          <div class="attr-item" v-if="currentItem.type == 'text'">
            <div class="text">计算规则</div>
            <el-input type="textarea" v-model="currentItem.calculatedRules" :rows="3"
              placeholder="规则格式：code1,+,code2,-,code3,*,code4,+,code5,/,code6"></el-input>
          </div>
          <div v-if="currentItem.type == 'statisticstable'">
            <div class="attr-item">
              <div class="text"><span style="color: #F56C6C">*</span>表格数据源 </div>
              <el-select style="width: 100%" v-model="currentItem.key" @change="tableDicChange($event, currentItem)"
                placeholder="请选择数据源">
                <el-option v-for="item in dicTableList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div class="attr-item">
              <div class="text">数据选择类型</div>
              <el-radio-group v-model="currentItem.changeType">
                <el-radio value="radio">单选 </el-radio>
                <el-radio value="checkbox">多选 </el-radio>
              </el-radio-group>
            </div>
            <div class="attr-item" v-if="currentItem.changeType == 'radio'">
              <div class="text">勾选时是否赋值</div>
              <el-radio-group v-model="currentItem.isAssignment">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否 </el-radio>
              </el-radio-group>
            </div>
            <div class="attr-item" v-if="currentItem.type == 'statisticstable'">
              <div class="text">'剩余金额' 显示名称</div>
              <el-input v-model="currentItem.CanUseAmount"></el-input>
            </div>
            <div class="attr-item attr2">
              <el-button style="width: 100%" @click="openTableModel(currentItem)" type="primary">table配置</el-button>
            </div>
          </div>

          <div class="attr-item" v-if="currentItem.type == 'projectnumber'">
            <div class="text">是否仅在查看详情内显示</div>
            <el-switch v-model="currentItem.isDetail" active-color="#13ce66" inactive-color="rgb(165 165 165)"
              :active-value="true" :inactive-value="false">
            </el-switch>
          </div>
          <div class="attr-item"
            v-if="!['auditusername', 'auditdate', 'projectnumber', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown', 'ProjectCode', 'AppointAuditUserSelect', 'AppointAuditUserSelectList'].includes(currentItem.type)">
            <div class="text">是否仅在编辑时显示</div>
            <el-switch v-model="currentItem.isRedact" active-color="#13ce66" inactive-color="rgb(165 165 165)"
              :active-value="true" :inactive-value="false">
            </el-switch>
          </div>
        </div>
      </div>
      <!-- 详情弹窗 -->
      <app-box v-model="previewModel" :height="600" :width="1300" :lazy="true" :padding="1" :close="false"
        :footer="false" title="预览">
        <form-preview :currentComponents="previewComponents"></form-preview>
      </app-box>
      <!-- 表格操作弹窗 -->
      <form-table :tableData="currnetTableData" :columns="currentTableOption" v-model:tableModel="tableModel"
        @confirm="confirm"></form-table>
      <!-- 项目编号自动生成配置弹窗 -->
      <app-box v-model="dialogVisible" :width="680" :lazy="true" title="项目编号自动生成配置">
        <template #content>
          <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px" status-icon>
            <el-form-item label="前缀名称:">
              <el-input v-model="dialogData.Name"></el-input>
            </el-form-item>
            <el-form-item label="前缀连接符:">
              <el-input v-model="dialogData.PreSymbol"></el-input>
            </el-form-item>
            <el-form-item label="日期格式:" prop="DateFormatValue">
              <el-select v-model="dialogData.DateFormatValue">
                <el-option v-for="item in dateFormatList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="后缀连接符:">
              <el-input v-model="dialogData.StufixSymbol"></el-input>
            </el-form-item>
            <el-form-item label="自增方式:" prop="SuffixNumberType">
              <el-select v-model="dialogData.SuffixNumberType">
                <el-option v-for="item in suffixNumberList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="以日期格式中的格式编码重置:">
              <el-select v-model="dialogData.AutoMode">
                <el-option v-for="item in dateFormatList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="末位数字位数:" prop="NumberCount">
              <el-input-number v-model="dialogData.NumberCount" class="mx-4" :min="1" :precision="0"
                controls-position="right" style="width: 100%;" />
            </el-form-item>
          </el-form>
        </template>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
          </span>
        </template>
      </app-box>
    </div>
  </div>
</template>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.viewContainer {
  height: 100%;
}

.drag-container {
  display: flex;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

.drag-left {
  width: 308px;
  flex-shrink: 0;
  // display: flex;
  // justify-content: center;
  border-right: 1px solid #eee;
  flex-direction: column;
  overflow-y: auto;
}

.left-title {
  height: 42px;
  text-align: left;
  border-right: 1px solid #eee;
  padding: 10px 0 10px 11px;
  border-bottom: 1px solid #eee;
}

.drag-center {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-x: auto;
}

.left-draggable-item {
  display: inline-block;
  padding: 5px;
}

.left-draggable-item .item {
  cursor: move;
  float: left;
  width: 135px;
  text-align: center;
  border: 1px solid #eeeeee;
  padding: 2px 13px;
  text-align: left;
  line-height: 28px;
  margin: 4px;
  border-radius: 3px;
  background: #f0f9eb;
  font-size: 13px;
}

.draggable-container {
  display: inline-block;
  width: 100%;
  height: calc(100vh - 215px);
  padding: 10px 0;
}

.item2 {
  position: relative;
  cursor: move;
  padding: 10px 10px 5px 10px;
  text-align: left;
  float: left;
  margin-bottom: 10px;
}

.item2 .el-icon-delete,
.item2 .el-icon-document-copy {
  position: absolute;
  right: 10px;
  top: -5px;
  padding: 5px;
  display: none;
  color: #F56C6C;
  cursor: pointer;
}

.item2 .el-icon-document-copy {
  right: 35px;
}

.item2:hover,
.actived {
  background: #f0f9eb;
}

.item:hover {
  border: 1px dashed #787be8;
  color: #787be8;
}

.item2:hover .el-icon-delete,
.item2:hover .el-icon-document-copy {
  display: block;
  z-index: 999;
}

.drag-right {
  background: #f7fbff3d;
  width: 250px;
  flex-shrink: 0;
  border-left: 1px solid #eee;
  height: 100%;
  overflow-y: auto;
}

.drag-right::-webkit-scrollbar {
  width: 3px;
}

::-webkit-scrollbar-thumb {
  background: #f5f5f5;
  position: absolute;
  right: 2px;
  z-index: 1;
}

.drag-right:hover::-webkit-scrollbar,
.drag-right:focus::-webkit-scrollbar {
  display: block;
}

.center-top {
  height: 42px;
  line-height: 41px;
  background: #f2f5fb;
  border-bottom: 1px solid #eee;
  text-align: left;
  padding: 0 10px;
  font-size: 12px;
  color: #3391f3;
}

.center-top span {
  margin-right: 10px;
}

.attr {
  padding: 0px 15px 15px 15px;
}

.attr-item {
  text-align: left;
  margin-top: 9px;
  font-size: 14px;
}

.attr-item .text {
  padding: 0 0 4px 5px;
}

.attr2 {
  display: flex;
}

.attr2>div {
  flex: 1;
}

.tips {
  position: absolute;
  font-size: 26px;
  letter-spacing: 6px;
  left: 0px;
  right: 0px;
  top: 150px;
  width: 500px;
  margin: auto;
  color: #c5c5c5;
}

.col-line {
  line-height: 25px;
  font-weight: bold;
  border-bottom: 1px solid rgb(218 218 218);
}

.drag-container :deep(.el-col) {
  width: 100%;
}

.drag-center :deep(.el-form-item__label) {
  // line-height: 0 !important;
}

.drag-center :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

.drag-center :deep(.el-form-item) {
  margin-bottom: 10px;
}

.drag-center :deep(.el-date-editor) {
  width: 100%;
}

.drag-center :deep(.el-checkbox) {
  margin-right: 15px;
}

.drag-center :deep(.el-checkbox__label) {
  padding-left: 5px;
}

.drag-center :deep(.hello > div) {
  z-index: 500 !important;
}

.drag-center :deep(th),
.drag-center :deep(td) {
  padding: 6px 0;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left !important;
}

:deep(.el-divider__text.is-left) {
  left: 100px;
}
</style>