<script setup>
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import en from 'element-plus/dist/locale/en.mjs'
import { useSettingStore } from "@/stores";
import { computed, onMounted, nextTick } from 'vue';

import { Local } from '@/utils/storage';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '@/stores';
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const setting = useSettingStore()


// 国际化
const locale = computed(() => (setting.language === 'zh-cn' ? zhCn : en))

// 页面加载时
onMounted(() => {
  nextTick(() => {
    // 获取缓存中的布局配置
    if (Local.get('themeConfig')) {
      storesThemeConfig.setThemeConfig({ themeConfig: Local.get('themeConfig') });
      document.documentElement.style.cssText = Local.get('themeConfigStyle');
    }

  });
});


</script>

<template>
  <!-- 入口 -->
  <div style="height: 100%;">
    <el-config-provider :locale="locale">
      <router-view></router-view>
    </el-config-provider>
  </div>
</template>

<style lang="scss">
// 列表最小宽度
.viewContainer {
  min-width: 680px;
}

// form 搜索margin样式
.navFlexBox {
  margin-top: 5px !important;
  margin-bottom: 5px !important;

  .el-form-item {
    margin-top: 2px !important;
    margin-bottom: 2px !important;
  }
}

.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px !important;
    margin-right: 5px !important;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexOperation {
    margin-left: 5px;
  }

  .flexContent {
    width: 200px;
  }

  .verticalDividel {
    height: 100%;
    border-left: 1px solid #dcdfe6;
    margin: 0 8px;
  }

  .verticalIdel {
    height: 32px;
    border-left: 1px solid #dcdfe6;
    margin: 8px 5px 0 5px;
  }
}

// 超出隐藏显示省略号

.taskNameConent {
  width: 100%;
  /* 具体宽度，例如 200px 或 100% */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 弹窗标题样式
.el-dialog {
  padding: 0px !important;

  .el-dialog__header {
    background-color: #f5f7fa !important;
    margin-right: 0;
    padding: 10px 15px;
  }

  .el-dialog__body {
    padding: 15px;
  }

  .el-dialog__footer {
    padding: 15px;
  }
}

// 问号提示
.tipIcon {
  cursor: pointer;
}

// 附件样式
.fileFlex {
  display: flex;
  flex-wrap: wrap;

  div {
    width: 80px;
    margin-left: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// table表头样式
.headerClassName {
  text-align: center !important;
  background-color: #FAFAFA !important;
}

// ElMessage  换行
.el-message .el-message__content {
  white-space: pre-line !important;
  text-align: left;
}

// 表格总计行
.el-table__footer-wrapper tfoot td.el-table__cell {
  font-weight: bold;
}

// 折叠面板标题样式
.el-collapse-item__header {
  font-size: 15px !important;
  color: #0000ee !important;
}


.rowFill {
  color: #666;
  margin: 0;
}

// 暂存按钮固定定位
.vertical-btn {
  width: 30px;
  padding: 10px 6px !important;
  position: fixed;
  top: 50%;
  right: -25px;
  transform: translate(-100%, -50%);
  z-index: 99;
  text-align: center;
  background-color: #409EFF;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.8);
}

.vertical-btn:hover {
  background-color: #66b1ff
}

.vertical-text {
  /* 垂直排列，从右到左 */
  writing-mode: vertical-rl;
  /* 保持文字直立 */
  text-orientation: upright;
  /* 调整字间距 */
  letter-spacing: 2px;
}

.edit-form-item {
  .el-form-item__content {
    align-items: flex-start;
  }
}

// 无业务处理页面显示
.emptyMsg {
  text-align: center;
  color: #E6A23C;
  font-size: 28px;
  padding: 20px;
}
</style>
