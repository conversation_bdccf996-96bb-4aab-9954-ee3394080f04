<script setup>
defineOptions({
    name: 'dangerchemicalsdailygovernreportset'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Select, Search
} from '@element-plus/icons-vue'
import { DcGovernSetGet, DcGovernDateTimeSave } from '@/api/daily.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    GetGovernSetData();
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

//验证
const ruleForm = {
    BeginDay: [
        { required: true, message: '请填写开始日期', trigger: 'change' },
    ],
    EndDay: [
        { required: true, message: '请填写结束日期', trigger: 'change' },
    ]
}
const formData = ref({})
const refForm = ref()
const GetGovernSetData = () => {
    DcGovernSetGet({}).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data;
            // console.log("---", rows)
            formData.value = rows;
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (!(formData.value.BeginDay > 0 && formData.value.EndDay > 0 && formData.value.BeginDay < 29 && formData.value.EndDay < 29)) {
            ElMessage.error("请填写开始申报日和结束申报日，并且大于0小于29。");
            return;
        }
        if (formData.value.BeginDay > formData.value.EndDay) {
            ElMessage.error("开始申报日不能大于结束申报日。");
            return;
        }
        DcGovernDateTimeSave(formData.value).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '设置成功')
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}

</script>
<template>
    <div class="viewContainer">
        <el-form style="width: 80%; min-width: 600px;" :inline="true" class="mobile-box" @submit.prevent ref="refForm"
            :rules="ruleForm" :model="formData" label-width="200px" status-icon>
            <el-form-item label="学校月申报有效时间段：" prop="BeginDay">
                每月<el-input v-model="formData.BeginDay" style="width: 60px"
                    @input="integerLimitInput($event, 'BeginDay')"></el-input>日至
            </el-form-item>
            <el-form-item prop="EndDay" label-width="0px">
                <el-input v-model="formData.EndDay" style="width: 60px"
                    @input="integerLimitInput($event, 'EndDay')"></el-input>日
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">
                    (非本时间段内，学校不能进行月申报。)</span>
            </el-form-item>
            <el-form-item style="width:80%;margin-left:60px;">
                <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-right: 0px !important;
}
</style>