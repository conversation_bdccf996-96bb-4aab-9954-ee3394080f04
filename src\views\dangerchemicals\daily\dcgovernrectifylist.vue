<script setup>
defineOptions({
    name: 'dangerchemicalsdailydcgovernrectifylist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { DcGovernRectifyListFind, DcGovernRectifyRectifySave, DcGovernRectifyGetById } from '@/api/daily.js'
import { GetDictionaryCombox } from '@/api/dangerchemicals.js'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { ElMessageBox, ElMessage } from 'element-plus'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload } from "@/utils/index.js";//上传附件
import { AttachmentUpload } from '@/api/user.js'
import { useUserStore } from '@/stores';
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()

const userStore = useUserStore()
const unitType = ref(userStore.userInfo.UnitType)
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData();
    DccatalogTypeGet();

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})


//表格
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Statuz", SortType: "ASC" }, { SortCode: "RegDate", SortType: "DESC" }] })
//搜索条件
const catalogList = ref([])
const filtersChange = (row) => {
    HandleTableData()
}
const regDategeChange = (val) => {
    if (!val) filters.value.RegDateBgn = undefined
    HandleTableData()
}
const EffectiveDatele = ref("");
const regDateleChange = (val) => {
    if (val) {
        filters.value.RegDateEnd = val + " 23:59:59"
    } else {
        filters.value.RegDateEnd = undefined
    }
    HandleTableData()
}

const gradeOptions = ref([{ Id: 1, Name: '一般', }, { Id: 2, Name: '较大', }, { Id: 3, Name: '重大', }])
const natureOptions = ref([{ Id: 1, Name: '问题', }, { Id: 2, Name: '隐患', }])
const uitIdTypeOptions = ref([{ Id: 1, Name: '市级' }, { Id: 2, Name: '区级' }, { Id: 3, Name: '校级' }])
const rectifyStatuzOptions = ref([{ Id: 0, Name: '待整改' }, { Id: 1, Name: '已整改' }])

// 危化品类别
const DccatalogTypeGet = () => {
    GetDictionaryCombox({ TypeCode: "10000" }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            catalogList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 列表
const HandleTableData = () => {
    DcGovernRectifyListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.RegDateBgn = undefined;
    filters.value.RegDateEnd = undefined;
    EffectiveDatele.value = '';

    filters.value.CategoryId = undefined;
    filters.value.Nature = undefined;
    filters.value.Grade = undefined;
    filters.value.UnitIdType = undefined;
    filters.value.Statuz = undefined;
    filters.value.Name = undefined;

    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

//弹窗 
const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)
const isLook = ref(true)
const isRectifyLook = ref(true);//是否是整改查看 是：整改查看  否：上报查看
//验证
const ruleForm = {
    Measures: [
        { required: true, message: '请填写整改措施。', trigger: 'change' },
    ]
}

// 修改
const HandleRectify = (row) => {
    isLook.value = false;
    dialogVisible.value = true
    dialogData.value = JSON.parse(JSON.stringify(row));

    //处理附件。
    uploadFileData.value[0].categoryList = [];//先置空
    uploadFileData.value[0].fileLChildList = [];//先置空
    loadGovernRectifyGetByIdData(row.Id, 0);
}
//整改查看信息
const HandleLook = (row) => {
    dialogData.value = JSON.parse(JSON.stringify(row));
    isLook.value = true;
    isRectifyLook.value = true;
    dialogVisible.value = true;
    loadGovernRectifyGetByIdData(row.Id, 2);
}

//上报查看信息
const HandleReportDetail = (row) => {
    dialogData.value = JSON.parse(JSON.stringify(row));
    isLook.value = true;
    isRectifyLook.value = false;
    dialogVisible.value = true;
    loadGovernRectifyGetByIdData(row.Id, 1);
}

const loadGovernRectifyGetByIdData = (id, opttype) => {
    //上报信息：opttype = 1 ，查看整改信息 opttype：2 整改：opttype = 0
    DcGovernRectifyGetById({ id: id, opttype: opttype }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data;
            if (opttype == 1) {
                dialogData.value.Memo = rows.Memo;
                dialogData.value.Suggest = rows.Suggest;
            } else if (opttype == 2) {
                dialogData.value.Measures = rows.Measures;
            } else if (opttype == 0) {
                nextTick(() => {
                    refForm.value.resetFields()
                    dialogData.value.Measures = rows.Measures;
                })
            }
            let attachmentList = other || []
            if (attachmentList.length > 0) {
                if (opttype == 1 || opttype == 2) {
                    dialogData.value.AttachmentList = attachmentList;
                } else if (opttype == 0) {
                    //加载到附件上。
                    uploadFileData.value[0].categoryList = attachmentList;
                }
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交
const HandleSubmit = (statuz) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (dialogData.value.Measures && dialogData.value.Measures.length > 300) {
            ElMessage.error("填写的整改措施字数不能超过300。");
        }
        const attIdArr = [];
        uploadFileData.value.map(function (item, index) {
            if (item.categoryList.length > 0) {
                item.categoryList.map(function (olditem, oldindex) {
                    attIdArr.push(olditem.Id);
                });
            }
            if (item.fileLChildList.length > 0) {
                item.fileLChildList.map(function (newitem, newindex) {
                    attIdArr.push(newitem.Id);
                });
            }
        });
        const paramModel = { Id: dialogData.value.Id, Statuz: statuz, Measures: dialogData.value.Measures, AttachmentIdList: attIdArr };
        DcGovernRectifyRectifySave(paramModel).then(res => {
            if (res.data.flag == 1) {
                dialogVisible.value = false
                ElMessage.success(res.data.msg || '保存成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
//是否超期
const getIsOverdue = (row) => {
    //已修改直接返回
    if (row.Statuz == 1) return false;
    // 1. 空值检查
    if (!row?.RectifyLimit) return false;
    try {
        // 2. 统一日期处理
        const rectifyDate = new Date(row.RectifyLimit);
        const today = new Date();
        // 3. 日期有效性验证
        if (isNaN(rectifyDate.getTime())) return false;
        // 4. 清除时间部分 (仅比较日期)
        rectifyDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);
        // 5. 直接返回比较结果
        return rectifyDate < today;
    } catch {
        // 6. 异常处理
        return false;
    }
};

//附件上传

const uploadFileData = ref([{ FileCategory: 2973, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 10, Memo: "文件小于10M，支持pdf和图片、文档文件", Name: "附件：", UploadFileType: ".pdf.jpg.jpeg.png.doc.docx.xls.xlsx", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx" }])
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[index].fileLChildList.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }

}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EffectiveDatele.value) return false;
    return time >= new Date(EffectiveDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDateBgn) return false;
    return time < new Date(filters.value.RegDateBgn + ' 00:00:00');
};
</script>

<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    问题隐患清单 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>填写问题或隐患整改情况，请点击【操作】栏中的“整改”按钮。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDateBgn" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申报日期"
                            @change="regDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EffectiveDatele" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledEndDate" clearable placeholder="申报日期"
                            @change="regDateleChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.CategoryId" clearable placeholder="类别" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in catalogList" :key="item.DicValue" :label="item.DicName"
                                :value="item.DicValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Nature" clearable placeholder="性质" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in natureOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Grade" clearable placeholder="危险等级" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in gradeOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.UnitIdType" clearable placeholder="发起人" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in uitIdTypeOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>

                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="整改状态" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in rectifyStatuzOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="问题隐患清单" style="width: 180px"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column v-if="unitType == 1" prop="AreaName" label="区县名称" width="140"
                show-overflow-tooltip></el-table-column>
            <el-table-column v-if="unitType !== 3" prop="UnitName" label="学校名称" width="160"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="CategoryName" label="类别" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Name" label="问题隐患清单" min-width="300"></el-table-column>
            <el-table-column prop="Nature" label="性质" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.Nature == 1 ? '问题' : row.Nature == 2 ? '隐患' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Grade" label="危险等级" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Grade == 1 ? '一般' : row.Grade == 2 ? '较大' : row.Grade == 3 ? '重大' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="IsRectify" label="发起人" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.UnitIdType != unitType ? unitType == 1 ? '市级' : unitType == 2 ? '区级' : '校级' : '' }}
                    {{ row.UnitIdType == 1 ? '市级' : row.UnitIdType == 2 ? '区级' : '校级' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="申报时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RectifyLimit" label="整改期限" min-width="140" align="center">
                <template #default="{ row }">
                    <span v-if="getIsOverdue(row)" style="color:red;">
                        {{ row.RectifyLimit.substring(0, 10) }}
                    </span>
                    <span v-else>{{ row.RectifyLimit ? row.RectifyLimit.substring(0, 10) : '--' }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="RectifyDate" label="整改时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="整改状态" width="90" align="center">
                <template #default="{ row }">
                    {{ row.Statuz == 1 ? '已整改' : '待整改' }}
                </template>
            </el-table-column>
            <el-table-column prop="opt" fixed="right" label="操作" width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 0 && unitType == 3" type="primary" link
                        @click="HandleRectify(row)">整改</el-button>
                    <el-button v-else-if="row.Statuz == 1" type="primary" link @click="HandleLook(row)">查看</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="opt1" fixed="right" label="排查说明" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleReportDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="isLook ? '查看' : '整改'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px"
                    class="mobile-box" status-icon>
                    <el-form-item label="类别：">
                        <span>{{ dialogData.CategoryName }}</span>
                    </el-form-item>
                    <el-form-item label="问题隐患清单：">
                        <span>{{ dialogData.Name }}</span>
                    </el-form-item>
                    <el-form-item label="性质：">
                        {{ dialogData.Nature == 1 ? '问题' : dialogData.Nature == 2 ? '隐患' : '--' }}
                    </el-form-item>
                    <el-form-item label="危险等级：">
                        {{ dialogData.Grade == 1 ? '一般' : dialogData.Grade == 2 ? '较大' : dialogData.Grade == 3 ? '重大' :
                            '--' }}
                    </el-form-item>
                    <el-form-item label="整改措施：" prop="Measures" v-if="!isLook" class="boxItem">
                        <el-input type="textarea" v-model="dialogData.Measures" style="width: 80%"></el-input>
                    </el-form-item>
                    <div v-if="!isLook">
                        <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                            <template #label>
                                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                                <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                    <div>
                                        <el-icon color="#E6A23C" class="tipIcon">
                                            <QuestionFilled />
                                        </el-icon>
                                    </div>
                                </el-tooltip>
                                <span> {{ item.Name }}： </span>
                            </template>
                            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                                :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                                :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                                <el-button type="success" size="small" :icon="UploadFilled"
                                    @click="MaxFileNumberClick(item)">上传</el-button>
                            </el-upload>
                            <div class="fileFlex">
                                <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                                    <el-icon color="#F56C6C" style="cursor: pointer;"
                                        @click="delCateFile(itemCate, index)">
                                        <Delete />
                                    </el-icon>
                                    <span style="cursor: pointer;"
                                        @click="fileListDownload(itemCate, item.categoryList)">
                                        {{ itemCate.Title }}
                                    </span>
                                </div>
                                <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id" style="">
                                    <el-icon color="#F56C6C" style="cursor: pointer;"
                                        @click="delFile(itemChild, index)">
                                        <Delete />
                                    </el-icon>
                                    {{ itemChild.Title }}
                                </div>
                            </div>
                        </el-form-item>
                    </div>
                    <el-form-item label="整改措施：" v-if="isLook && isRectifyLook">
                        {{ dialogData.Measures || "--" }}
                    </el-form-item>
                    <el-form-item label="备注：" v-if="isLook && !isRectifyLook">
                        {{ dialogData.Memo || "--" }}
                    </el-form-item>
                    <el-form-item label="附件：" v-if="isLook">
                        <div v-if="dialogData.AttachmentList && dialogData.AttachmentList.length > 0">
                            <div class="fileFlex">
                                <div v-for="(itemChild) in dialogData.AttachmentList" :key="itemChild.Id"
                                    style="color:#409EFF ;">
                                    <span style="cursor: pointer;"
                                        @click="fileListDownload(itemChild, dialogData.AttachmentList)">
                                        {{ itemChild.Title }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div v-else>--</div>
                    </el-form-item>
                    <el-form-item label="整改建议：" v-if="isLook && !isRectifyLook">
                        {{ dialogData.Suggest ? dialogData.Suggest : "--" }}
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <span v-if="!isLook">
                        <el-button @click="dialogVisible = false">取消</el-button>
                        <el-button type="primary" @click="HandleSubmit(0)"> 保存 </el-button>
                        <el-button type="primary" @click="HandleSubmit(1)"> 整改完成 </el-button>
                    </span>
                    <span v-else>
                        <el-button @click="dialogVisible = false">关闭</el-button>
                    </span>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
.mobile-box {
    :deep(.el-form-item) {
        margin-bottom: 5px;
    }

    .boxItem.el-form-item {
        margin-bottom: 18px;
    }
}
</style>