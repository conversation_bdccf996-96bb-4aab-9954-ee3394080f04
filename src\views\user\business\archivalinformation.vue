<script setup>
import { onMounted, ref } from 'vue'
import {
    Getpaged, Setbyid
} from '@/api/user.js'
import { ElMessage } from 'element-plus'
// 表格初始化
const tableData = ref([])
const refTable = ref()
const headerTip = ref('')
//加载数据
onMounted(() => {
    GetpagedUser()
})
// 备案信息审核
const GetpagedUser = () => {
    Getpaged({ code: 2000 }).then((res) => {
        if (res.data.flag == 1) {
            // console.log("备案信息审核", res.data)
            const { rows } = res.data.data
            tableData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 修改状态
const HandleSwitchChange = (e) => {
    Setbyid({ id: e.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '执行成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <div class="headerPlace">
            <p> 是指学校向教育局备案的信息是否需要教育局人工审核，如选择“否”系统默认审核通过。</p>
        </div>
        <div style="width: 800px;margin: 20px;">
            <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                header-cell-class-name="headerClassName">
                <el-table-column prop="Title" label="项目名称" width="200"></el-table-column>
                <el-table-column label="是否需审核" width="120" align="center">
                    <template #default="{ row }">
                        <el-switch v-model="row.ValueNum" :active-value="1" :inactive-value="0"
                            style="--el-switch-off-color: #ff4949" inline-prompt active-text="是" inactive-text="否"
                            @change="HandleSwitchChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="Memo" label="备注" min-width="200"></el-table-column>
                <template #empty>
                    <el-empty description="没有数据"></el-empty>
                </template>
            </el-table>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.headerPlace {
    border-bottom: 1px solid #ebeef5;
    padding-left: 50px;
    font-size: 14px;
    color: #E6A23C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}

.dialog-content {
    :deep(.el-input__wrapper) {
        border: none !important;
        box-shadow: none !important;
    }

    :deep(.el-input__inner) {
        text-align: center;
    }

    .input-focused {
        border: 1px solid #409EFF;
    }
}
</style>