<script setup>
defineOptions({
    name: 'dangerchemicalsinventorylist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Upload, Edit
} from '@element-plus/icons-vue'
import {
    DcMaterialsNumAuditAdd, DccatalogGetClassTwo, DcSchoolMaterialOptFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, integerLimit, ExcelDownload } from "@/utils/index.js";
import {
    ExportDcSchoolMaterialOptFind
} from '@/api/directdata.js'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const StatuzSolicitedList = ref([])
const surplusNum = ref(1)
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1, sortModel: [{ SortCode: "ValidDate", SortType: "ASC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    ReturnNum: [
        { required: true, message: '请填写数量', trigger: 'blur' },
    ],
    BackDate: [
        { required: true, message: '请选择盘点时间', trigger: 'blur' },
    ],
    Remark: [
        { required: true, message: '请填写情况说明', trigger: 'blur' },
    ],
}

const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', },
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {

    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 弹窗
const HandleSurplus = (e) => {
    surplusNum.value = e
    dialogVisible.value = true
    formData.value = radioRows.value
    formData.value.ReturnNum = ''
    formData.value.BackDate = ''
    formData.value.Remark = ''
    nextTick(() => {
        refForm.value.resetFields()
    })

}
const radio = ref(false)//选择被绑定的Id
const radioRows = ref({}); // 选择被绑定的行
// 数据单选
const handleCurrentChange = (row) => {
    console.log("row", row)
    if (row) {
        radioRows.value = row
        radio.value = row.Id
    }
}
//  行点击事件
const HandleRadioChange = (row) => {
    radioRows.value = row
    radio.value = row.Id
}
//  列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcSchoolMaterialOptFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            radio.value = false
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.TwoCatalogId = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const HandleSubmit = () => {
    let optNum = formData.value.ReturnNum
    if (surplusNum.value == 2) {
        console.log("radioRows.value.StockNum", formData.value.StockNum + formData.value.NoGrantNum)
        if (formData.value.ReturnNum > formData.value.StockNum + formData.value.NoGrantNum) {
            ElMessage.error('填写的盘亏数不能大于库存数')
            return
        }
        optNum = 0 - formData.value.ReturnNum
    }
    let paraData = {
        OptNum: optNum,
        OptReason: formData.value.Remark,
        OptTime: formData.value.BackDate,
        OptType: 1,
        SchoolMaterialId: radio.value,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcMaterialsNumAuditAdd(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '退货成功')
                dialogVisible.value = false
                radio.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })

}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
//导出
const HandleExport = () => {
    ExportDcSchoolMaterialOptFind(filters.value).then(res => {
        ExcelDownload(res)
    });
}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}



</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    盘点危化品列表 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>第一步，导出盘点表，进行实物盘点；</li>
                    <li>第二步，进行盘盈或盘亏处理。</li>
                    <li style="color: #F56C6C;">备注：过期危化品不能采用盘亏处理，只能到【危化品报复】栏进行报废。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Upload" :disabled="tableData.length == 0"
                            @click="HandleExport">导出盘点表</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Edit" :disabled="!radio"
                            @click="HandleSurplus(1)">盘盈</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Edit" :disabled="!radio"
                            @click="HandleSurplus(2)">盘亏</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @current-change="handleCurrentChange" header-cell-class-name="headerClassName">
            <el-table-column width="55" align="center">
                <template #default="{ row }">
                    <el-radio v-model="radio" :value="row.Id" @change="HandleRadioChange(row)"></el-radio>
                </template>
            </el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="StockNum" label="存量" min-width="110" align="right">
                <template #default="{ row }">
                    {{ row.StockNum + row.NoGrantNum }}
                </template>
            </el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Price" label="单价" min-width="160" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand ? row.Brand : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="180" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="ValidDate" label="有效期至" min-width="180" align="center">
                <template #default="{ row }">
                    {{ row.ValidDate ? row.ValidDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Address" label="存放地点" min-width="120" align="center"></el-table-column>
            <el-table-column prop="InventoryAuditNum" label="盘点数量" min-width="120" align="right" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.InventoryAuditNum ? row.InventoryAuditNum : '--' }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="surplusNum == 1 ? '盘盈' : '盘亏'">
            <template #content>
                <el-form style="min-width: 320px; " class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="危化品名称：">
                        <span>{{ formData.Name }}</span>
                    </el-form-item>
                    <el-form-item label="规格属性：">
                        <span>{{ formData.Model }}</span>
                    </el-form-item>
                    <el-form-item label="品牌：">
                        <span>{{ formData.Brand }}</span>
                    </el-form-item>
                    <el-form-item label="存量：">
                        <span>{{ formData.StockNum + formData.NoGrantNum }}</span>
                    </el-form-item>
                    <el-form-item label="单位：">
                        <span>{{ formData.UnitName }}</span>
                    </el-form-item>
                    <el-form-item :label="surplusNum == 1 ? '盘盈数量 :' : '盘亏数量 :'" prop="ReturnNum" class="boxItem">
                        <el-input v-model="formData.ReturnNum" @input="limitInput($event, 'ReturnNum')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="盘点时间：" prop="BackDate" class="boxItem">
                        <el-date-picker v-model="formData.BackDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable style="width: 80%;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="情况说明：" prop="Remark" class="boxItem">
                        <el-input type="textarea" maxlength="500" v-model="formData.Remark"
                            style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 保存 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
.mobile-box {
    :deep(.el-form-item) {
        margin-bottom: 5px;
    }

    .boxItem.el-form-item {
        margin-bottom: 18px;
    }
}
</style>