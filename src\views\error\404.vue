<template>
  <div class="notFound">
    <p>你迷路啦</p>
    <router-link :to="previousPath">返回上一页</router-link>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    <router-link :to="onePagePath">返回首页</router-link>

  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores';
import { onePage } from "@/utils/index.js";
const userStore = useUserStore()
const previousPath = ref('/')
const onePagePath = ref('/')
onMounted(() => {
  if (userStore.token) {
    if (userStore.platformType == 1) {
      onePagePath.value = '/'
    } else if (userStore.platformType == 2) {
      if (userStore.userInfo.UnitType == 0 || (userStore.userInfo.UnitType > 0 && !['1200', '2200', '3200'].some(t => userStore.userInfo.RoleIds.includes(t)))) {
        onePagePath.value = '/' + userStore.onePage
      } else {
        onePagePath.value = '/approval/home/<USER>'
      }

    } else if (userStore.platformType == 3) {
      onePagePath.value = '/' + userStore.onePage
    }
    previousPath.value = userStore.previousPath || '/'
  } else {
    onePagePath.value = '/'
    previousPath.value = '/'
  }
})
</script>
<style lang="scss" scoped>
.notFound {
  text-align: center;
}

a {
  text-decoration: none;

}

a:hover {
  text-decoration: underline;

}
</style>