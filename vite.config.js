import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons' // 引入svg图标
import commonjs from 'vite-plugin-commonjs'
import path from 'path'
// 打包时间戳,防止打包部署后用户页面出现缓存问题
const timestamp = new Date().getTime()
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    commonjs(),
    // 暂时取消按需导入,因为按需导入会出现用户等待时间,不友好
    // 按需自动导入elementPlus,不需要手动import否则样式会出错
    // AutoImport({
    //   resolvers: [ElementPlusResolver()]
    // }),
    // Components({
    //   resolvers: [ElementPlusResolver()]
    // }),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
      // 指定symbolId格式
      symbolId: 'icon-[dir]-[name]'
    })
  ],
  base: '/web', //路由前缀
  resolve: {
    // @的配置
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  // 反向代理
  server: {
    open: true, // 服务启动时是否自动打开浏览器
    proxy: {
      '/api': {
        // target: 'http://xfpt.cneefix.net', //校服平台测试
        // target: 'http://sppz.cneefix.net', //审批配置平台测试
        // target: 'http://whp.cneefix.net', //危化品平台测试
        // target: 'http://ntzysxgl.jyzbhq.com/', //南通
        // target: 'http://xmgl.jyzbhq.com', //亭湖
        target: 'http://localhost:9291', //本地
        changeOrigin: true
        // 前端api路由重新,比如api要重写成api2
        // rewrite: (path) => path.replace(/^\/api/, ""),
      }
    }
  },

  build: {
    sourcemap: true, // 确保source map生成
    rollupOptions: {
      output: {
        // 入口文件名
        entryFileNames: `assets/[name].${timestamp}.js`,
        // 块文件名
        chunkFileNames: `assets/[name]-[hash].${timestamp}.js`,
        // 资源文件名 css 图片等等
        assetFileNames: `assets/[name]-[hash].${timestamp}.[ext]`
      }
    }
  }
})
