import { ElMessageBox, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
// 数量保留小数,默认两位
export function limit(val, len = 2) {
  let e =
    ('' + val) // 第一步：转成字符串
      .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
      .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(new RegExp(`^\\d*(\\.?\\d{0,${len}})`, 'g'))[0] || '' // 第五步：动态限制小数位数
  return e
}
// 正整数
export function integerLimit(val) {
  // console.log(val)
  let e =
    ('' + val)
      .replace(/[^\d]/g, '')
      .replace(/^0+(\d)/, '$1')
      .match(/^\d*/g)[0] || ''
  if (e === '') {
    return ''
  } else {
    return Number(e)
  }
}

// 获取年月日时分秒方法
export function dateDay(date) {
  let year = date.getFullYear()
  let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  let day = date.getDate()
  let hour = date.getHours()
  let minute = date.getMinutes()
  let seconds = date.getSeconds()
  let useTime =
    `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}` +
    ' ' +
    `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  // console.log("时间", useTime);
  return useTime
}
// 获取年-月-日方法  YYYY-MM-DD
export function getYearMonthDay(date) {
  // const now = new Date();
  const year = date.getFullYear()
  // getMonth() 返回的月份是从0开始的，所以需要加1
  const month = date.getMonth() + 1
  const day = date.getDate()

  // 使用 ES6 模板字符串返回格式化的日期
  return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
}

// 获取年-月-日方法  YYYY年MM月DD日
export function formatDate(date) {
  const y = date.getFullYear()
  const m = String(date.getMonth() + 1).padStart(2, '0')
  const d = String(date.getDate()).padStart(2, '0')
  return `${y}年${m}月${d}日`
}

//上年、今年、明年
export function yearDate() {
  // let date = new Date().getFullYear()
  // return [
  //   // {
  //   //   value: date + 1,
  //   //   label: date + 1
  //   // },
  //   {
  //     value: date,
  //     label: date
  //   },
  //   {
  //     value: date - 1,
  //     label: date - 1
  //   }
  // ]

  let date = new Date().getFullYear()
  let yearData = []
  for (let i = date; i >= 2019; i--) {
    yearData.push({
      value: i,
      label: i
    })
  }
  return yearData
}
//2024至今年
export function previousYearDate() {
  let date = new Date().getFullYear()
  let yearData = []
  for (let i = date; i >= 2019; i--) {
    yearData.push({
      value: i,
      label: i
    })
  }
  return yearData
}
//时间格式化（空值）
export function rectifyLimitDate(date) {
  if (!date || date == '0001-01-01 00:00:00') {
    return ''
  } else {
    return date.substring(0, 10)
  }
}
//时间格式化（‘--’）
export function disposalDate(date) {
  if (!date || date == '0001-01-01 00:00:00') {
    return '--'
  } else {
    return date.substring(0, 10)
  }
}
// 金额显示标准化
export function formatNumberWithCommas(num) {
  const parts = String(num).split('.')
  const integerPart = parts[0]
  const decimalPart = parts[1] || ''

  // 格式化整数部分（千位分隔符）
  const formattedInteger = new Intl.NumberFormat('en-US').format(integerPart)

  // 处理小数部分
  let formattedDecimal = decimalPart
  if (decimalPart.length < 2) {
    formattedDecimal = decimalPart.padEnd(2, '0') // 不足2位补0
  }

  return decimalPart ? `${formattedInteger}.${formattedDecimal}` : `${formattedInteger}.00` // 无小数时补 .00
}
// 数字显示标准化
export const formatNumber = (num) => {
  // 拆分成整数和小数部分
  const [integerPart, decimalPart] = String(num).split('.')

  // 格式化整数部分（千位分隔符）
  const formattedInteger = new Intl.NumberFormat('en-US').format(integerPart)

  // 如果有小数部分，拼接回去；否则不显示小数
  return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger
}

// 文件上传 ：导入前校验

export function unitBeforeUpload(item, file) {
  fileFile.value = file
  let name = file.name.split('.')[1]
  let arr = item.UploadFileType.split('.')
  let ary = arr.filter((t) => t != '')
  const extension = ary.includes(name)
  if (!extension) {
    ElMessage({
      message: `上传文件只能是${item.UploadFileType}格式!`,
      type: 'error'
    })
  }
  // // 校验文件大小
  let FileSize = item.FileSize
  if (item.FileSize == 0) {
    FileSize = 10
  }
  const isSize = file.size / 1024 / 1024 < FileSize
  if (!isSize) {
    ElMessage({
      message: `文件大小不能超出${FileSize}M`,
      type: 'error'
    })
  }
  return extension && isSize
}

// 文件下载
export function fileDownload(url, title) {
  // console.log("下载地址", url)
  var a = document.createElement('a')
  a.style.display = 'none'
  if (title) {
    a.download = title //下载后文件名
  }
  a.href = url
  document.body.appendChild(a)
  a.click() // 触发点击
  document.body.removeChild(a) // 然后移除
  window.URL.revokeObjectURL(a.href)
}

// 附件必传判断
export function foundReturn(Arr) {
  let found = false
  for (let i = 0; i < Arr.length; i++) {
    if (Arr[i].IsFilled == 1) {
      if (Arr[i].fileLChildList.length == 0 && Arr[i].categoryList.length == 0) {
        ElMessage.error(`请上传【${Arr[i].Name}】附件`)
        found = true // 设置标志为true
        break
      }
    }
  }
  return found
}
// tag标签添加参数
export function tagsListStore(tagsList, route) {
  // let list = userStore.tagsList
  tagsList.forEach((item) => {
    if (item.path == route.path) {
      item.query = route.query
    }
  })
  userStore.setTagsList(tagsList)
}

// 获取生成页面携带的参数
export function urlQuery(url) {
  let beginIndex = url.indexOf('@')
  let routerQuery = url.substring(beginIndex) // 参数
  return routerQuery
}
// 获取生成页面携带的参数并转成对象
export function pageQuery(url) {
  let beginIndex = url.indexOf('@')
  let routerQuery = url.substring(beginIndex + 1) // 参数
  // 字符串参数转化为对象 : 菜单自带的参数
  let routerObject = routerQuery.split('&').reduce((acc, param) => {
    const [key, value] = param.split('=')
    acc[key] = decodeURIComponent(value)
    return acc
  }, {})
  return routerObject
}

// 存在多级children，怎么回调
// 获取当前账号第一个未隐藏的菜单页面路径
export function onePage(menuData) {
  console.log('menuData', menuData)
  // 辅助函数，用于递归查找未隐藏的菜单项
  function findVisiblePath(menuItems) {
    for (let item of menuItems) {
      if (!item.IsHide) {
        if (item.children && item.children.length > 0) {
          // 如果有子菜单，递归查找
          const childPath = findVisiblePath(item.children)
          if (childPath) {
            return childPath
          }
        } else {
          // 没有子菜单，返回当前菜单项的 path
          return item.path
        }
      }
    }
    // 如果没有找到未隐藏的菜单项，返回 null
    return null
  }

  // 获取第一个可以显示的模块（一级菜单）
  const result = menuData.find((item) => !item.IsHide)
  console.log('result', result)
  // 从顶级菜单开始查找
  const path = findVisiblePath(result.children)
  return path ? path : '/' // 如果没有找到，返回 null 或根据需要返回其他默认值
}
// 用户角色数组重构
export function roleObjList(arr) {
  const result = Object.values(
    arr.reduce((acc, curr) => {
      if (!acc[curr.ModuleName]) {
        acc[curr.ModuleName] = {
          ModuleName: curr.ModuleName,
          ModuleSort: curr.ModuleSort,
          children: []
        }
      }
      acc[curr.ModuleName].children.push(curr)
      return acc
    }, {})
  ).sort((a, b) => a.ModuleSort - b.ModuleSort)
  return result
}

export function methodMul(arg1, arg2) {
  var m = 0,
    s1 = arg1.toString(),
    s2 = arg2.toString()
  try {
    m += s1.split('.')[1].length
  } catch (e) {}
  try {
    m += s2.split('.')[1].length
  } catch (e) {}
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m)
}
export function methodDiv(arg1, arg2) {
  var t1 = 0,
    t2 = 0,
    r1,
    r2
  try {
    t1 = arg1.toString().split('.')[1].length
  } catch (e) {}
  try {
    t2 = arg2.toString().split('.')[1].length
  } catch (e) {}
  r1 = Number(arg1.toString().replace('.', ''))
  r2 = Number(arg2.toString().replace('.', ''))
  return methodMul(r1 / r2, Math.pow(10, t2 - t1))
}

// excel数据流导出
export const ExcelDownload = (res) => {
  // 创建下载链接
  const blob = new Blob([res.data], {
    type:
      res.headers['content-type'] ||
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })
  const url = window.URL.createObjectURL(blob)
  const fileName = getFileNameFromHeaders(res.headers) || 'export.xlsx'
  const link = document.createElement('a')
  // console.log(url, link)
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  // 清理
  window.URL.revokeObjectURL(url)
  document.body.removeChild(link)
}

const getFileNameFromHeaders = (headers) => {
  const contentDisposition = headers['content-disposition']
  if (!contentDisposition) return null

  // 1. 首先尝试解析 filename* (RFC 5987)
  const fileNameStarMatch = contentDisposition.match(/filename\*=(?:UTF-8'')?([^;]+)/i)
  if (fileNameStarMatch && fileNameStarMatch[1]) {
    try {
      // 解码百分号编码的 UTF-8 字符串
      return decodeURIComponent(fileNameStarMatch[1].trim())
    } catch (e) {
      console.warn('Failed to decode filename*:', e)
      // 继续尝试其他方式
    }
  }

  // 2. 尝试解析普通 filename
  const fileNameMatch = contentDisposition.match(/filename="?([^;"]+)"?/i)
  if (fileNameMatch && fileNameMatch[1]) {
    // 去除可能的引号和空格
    return fileNameMatch[1].trim().replace(/^["']|["']$/g, '')
  }

  return null
}

// el-tabel表格列合并
export const ConsolidatedColumn = (data, column) => {
  const arr = []
  let pos = 0
  // 初始化
  data.forEach((item, index) => {
    if (index === 0) {
      arr.push(1)
      pos = 0
    } else {
      // 如果当前行的 column 与上一行相同
      if (item[column] === data[index - 1][column]) {
        arr[pos] += 1
        arr.push(0)
      } else {
        arr.push(1)
        pos = index
      }
    }
  })

  return arr
}
// 校服种类
export const UniformtypeList = [
  { label: '春秋装', value: '春秋装' },
  { label: '夏装', value: '夏装' },
  { label: '冬装', value: '冬装' },
  { label: '其他', value: '其他' }
]
// 校服适用性别
export const SexList = [
  { value: 1, label: '男' },
  { value: 2, label: '女' },
  { value: 3, label: '男/女' }
]
// 校服单位
export const NameList = [
  { value: '个', label: '个' },
  { value: '件', label: '件' },
  { value: '套', label: '套' },
  { value: '条', label: '条' },
  { value: '双', label: '双' },
  { value: '根', label: '根' }
]
// 校服采购有效年限
export const ValidityPeriodList = [
  { value: 1, label: '一年' },
  { value: 2, label: '两年' },
  { value: 3, label: '三年' }
]
// 附件类型
export const UploadFileTypeList = [
  { value: '.jpg.jpeg.png', label: '.jpg.jpeg.png' },
  { value: '.pdf', label: '.pdf' },
  { value: '.webp', label: '.webp' },
  { value: '.doc.docx', label: '.doc.docx' },
  { value: '.xls.xlsx', label: '.xls.xlsx' },
  { value: '.gif', label: '.gif' },
  { value: '.bmp', label: '.bmp' },
  { value: '.dwg', label: '.dwg' }
]
// 主控被控分组
export const controlList = [
  { value: 0, label: '无' },
  { value: 1, label: '组1' },
  { value: 2, label: '组2' },
  { value: 3, label: '组3' },
  { value: 4, label: '组4' },
  { value: 5, label: '组5' },
  { value: 6, label: '组6' },
  { value: 7, label: '组7' },
  { value: 8, label: '组8' },
  { value: 9, label: '组9' },
  { value: 10, label: '组10' },
  { value: 11, label: '组11' },
  { value: 12, label: '组12' },
  { value: 13, label: '组13' },
  { value: 14, label: '组14' },
  { value: 15, label: '组15' },
  { value: 16, label: '组16' },
  { value: 17, label: '组17' },
  { value: 18, label: '组18' },
  { value: 19, label: '组19' },
  { value: 20, label: '组20' },
  { value: 21, label: '组21' },
  { value: 22, label: '组22' },
  { value: 23, label: '组23' },
  { value: 24, label: '组24' },
  { value: 25, label: '组25' },
  { value: 26, label: '组26' },
  { value: 27, label: '组27' },
  { value: 28, label: '组28' },
  { value: 29, label: '组29' },
  { value: 30, label: '组30' },
  { value: 31, label: '组31' },
  { value: 32, label: '组32' },
  { value: 33, label: '组33' },
  { value: 34, label: '组34' },
  { value: 35, label: '组35' },
  { value: 36, label: '组36' },
  { value: 37, label: '组37' },
  { value: 38, label: '组38' },
  { value: 39, label: '组39' },
  { value: 40, label: '组40' },
  { value: 41, label: '组41' },
  { value: 42, label: '组42' },
  { value: 43, label: '组43' },
  { value: 44, label: '组44' },
  { value: 45, label: '组45' },
  { value: 46, label: '组46' },
  { value: 47, label: '组47' },
  { value: 48, label: '组48' },
  { value: 49, label: '组49' },
  { value: 50, label: '组50' }
]
// 日期时间控件格式
export const popperOptions = {
  placement: 'bottom-start',
  modifiers: [
    {
      name: 'flip',
      options: {
        fallbackPlacements: ['top-start', 'bottom-start'] // 设置备选位置
      }
    },
    { name: 'offset', options: { offset: [0, 4] } }
  ]
}
// 危化品单位
export const arrStatuz = [
  { id: '克', Name: '克' },
  { id: '千克', Name: '千克' },
  { id: '毫升', Name: '毫升' },
  { id: '升', Name: '升' },
  { id: '个', Name: '个' }
]
// 危化品多级平级的时候组合成树状
export function buildTree(arr) {
  const tree = []
  const map = {}
  // 先创建所有节点的映射
  arr.forEach((item) => {
    map[item.Id] = { ...item, children: [] }
  })
  // 构建树结构
  arr.forEach((item) => {
    if (item.Pid === '0') {
      tree.push(map[item.Id])
    } else {
      if (map[item.Pid]) {
        map[item.Pid].children.push(map[item.Id])
      }
    }
  })
  return tree
}

//危化品采购状态
export const purchaseStatuzArray = [
  { id: 10, Name: '等待主管审核' },
  { id: 11, Name: '主管审核退回' },
  { id: 20, Name: '等待领导审批' },
  { id: 21, Name: '领导审批退回' },
  { id: 100, Name: '审批结束' }
]

//危化品已生成计划状态
export const purchaseEndArray = [
  { id: 0, Name: '未采购' },
  { id: 1, Name: '采购中' },
  { id: 2, Name: '已采购' }
]

//获取危化品采购状态
export function getPurchaseStatuz(arr) {
  let strDesc = ''
  switch (arr) {
    case -1:
      strDesc = '已删除'
      break
    case 0:
      strDesc = '已添加'
      break
    case 10:
      strDesc = '等待主管审核'
      break
    case 11:
      strDesc = '主管审核退回'
      break
    case 20:
      strDesc = '等待领导审批'
      break
    case 21:
      strDesc = '领导审批退回'
      break
    case 100:
      strDesc = '审批结束'
      break
  }
  return strDesc
}

//获取危化品已生成计划状态
export function getPurchaseEndStatuz(arr) {
  let strDesc = ''
  switch (arr) {
    case 0:
      strDesc = '未采购'
      break
    case 1:
      strDesc = '采购中'
      break
    case 2:
      strDesc = '已采购'
      break
    default:
      strDesc = ''
      break
  }
  return strDesc
}
export function getApplyStatus(statuz) {
  let strDesc = ''
  switch (statuz) {
    case -1:
      strDesc = '已删除'
      break
    case 0:
      strDesc = '已添加'
      break
    case 10:
      strDesc = '等待部门审核'
      break
    case 11:
      strDesc = '部门审核退回'
      break
    case 20:
      strDesc = '等待主管审批'
      break
    case 21:
      strDesc = '主管审批退回'
      break
    case 30:
      strDesc = '等待库管配货'
      break
    case 31:
      strDesc = '库管配货退回'
      break
    case 40:
      strDesc = '等待领取危化品'
      break
      break
    case 100:
      strDesc = '危化品领用结束'
      break
  }
  return strDesc
}
