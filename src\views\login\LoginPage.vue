<script setup>
// 接口
import {
  user<PERSON>og<PERSON>, Get<PERSON><PERSON><PERSON>B<PERSON>, teacherGetvalidatecode, teacherUser<PERSON>ogin, LoginRefreshcaptchaimage
} from '@/api/user.js'
import {
  ArticleGetbottomcatetype, ArticleGetoperator, AnonGetconfigbymodule
} from '@/api/home.js'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { useUserStore } from '@/stores';
import router from '@/router'
import { addDynamicRoutes } from '@/router'
import md5 from 'js-md5';
import { integerLimit, onePage } from "@/utils/index.js";

const isAdmin = ref(true)
// 登录信息
const formData = ref({
  name: '',
  pass: ''
})
const userStore = useUserStore()
const imageUrl = ref('')
const imgUuid = ref('')
onMounted(() => {

  if (userStore.token) {
    if (userStore.platformType == 1) {
      router.push('/')
    } else if (userStore.platformType == 2 || userStore.platformType == 3) {
      const path = onePage(userStore.menu)
      router.push('/' + path)
    }
  }
  noticeChecked.value = userStore.noticeChecked
  // 是否记住账号密码
  if (userStore.isRemember) {
    formData.value.name = userStore.name
    formData.value.pass = atob(userStore.pass)
  }
  if (userStore.isImgCode == 5) {
    // LoginRefreshcaptchaimageUser()
    imageUrl.value = `data:image/jpg;base64,${userStore.captchaimage.Img}`;
    imgUuid.value = userStore.captchaimage.Uuid
  }
  ArticleGetoperatorUser({ code: 1010 })
  if (userStore.platformType == 2 || userStore.platformType == 3) {
    ArticleGetbottomcatetypeUser()
    AnonGetconfigbymoduleUser()
  }
  window.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress)
})
// 回车键登录
const handleKeyPress = (event) => {
  if (event.key === 'Enter') {
    if (isAdmin.value) {
      login()
    } else {
      teacherLogin
    }
  }
}
// 监听变量
watch(() => userStore.isRemember, () => {
  userStore.setName('')
  userStore.setPass('')
})
// 登录禁用
const isLogin = computed(() => {
  let isLogin = true
  if (formData.value.name && formData.value.pass) isLogin = false
  return isLogin
})
// 班主任登录禁用
const isTeacherLogin = computed(() => {
  let isLogin = true
  if (formData.value.phoneNumber && formData.value.validateCode) isLogin = false
  return isLogin
})
// 校验信息
const formRules = {
  name: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  pass: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  imgCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  phoneNumber: [{ required: true, message: '请输入手机号', trigger: 'blur' },
  {
    pattern: /^1[0-9]{10}$/,
    message: '请输入正确11位手机号码',
    trigger: 'blur'
  }],
  validateCode: [{ required: true, message: '请输入验证码', trigger: 'blur' },
  {
    pattern: /^[0-9]{4,6}$/,
    message: '请输入正确的验证码',
    trigger: 'blur'
  }],
}
//输入整数
const integerLimitInput = (val, name) => {
  formData.value[name] = integerLimit(val);
}
const refForm = ref()
// 登录
const login = () => {
  console.log('登录', noticeChecked)
  if (noticeList.value.length > 0 && !noticeChecked.value) {
    ElMessage.error('请先阅读并同意用户条款')
    return
  }
  refForm.value
    .validate()
    .then((res) => {
      // console.info('表单验证成功', res)
      const loginParams = ref({
        name: formData.value.name,
        pass: md5(formData.value.pass),
      })
      if (userStore.isImgCode == 5) {
        loginParams.value.Code = formData.value.imgCode
        loginParams.value.Uuid = imgUuid.value
      }
      userLogin(loginParams.value).then((resUser) => {
        if (resUser.data.flag == 5) {
          userStore.setImgCode(resUser.data.flag)
          // LoginRefreshcaptchaimageUser()
          imageUrl.value = `data:image/jpg;base64,${resUser.data.data.footer.Img}`;
          imgUuid.value = resUser.data.data.footer.Uuid
          userStore.setCaptchaImage(resUser.data.data.footer)
          ElMessage.error(resUser.data.msg)
        } else if (resUser.data.flag == 6) {
          // resUser.data.flag == 6  判断为特定密码时，直接进入修改密码页面
          const { rows, footer } = resUser.data.data

          userStore.setToken(rows.token)
          userStore.setTokenType(rows.token_type)
          userStore.setExpiresin(rows.expires_in * 1000)
          userStore.setUserInfo(footer)

          // 记住账号密码
          if (userStore.isRemember) {
            userStore.setName(formData.value.name)
            userStore.setPass(btoa(formData.value.pass))
          }
          userStore.setNoticeChecked(true)

          router.replace('/user/my/changepass')

          userStore.setImgCode(0)
        } else if (resUser.data.flag == 1) {
          // console.info("登录信息", resUser.data.data)
          // console.info("用户信息", resUser.data.data.footer)
          const { rows, footer } = resUser.data.data

          userStore.setToken(rows.token)
          userStore.setTokenType(rows.token_type)
          userStore.setExpiresin(rows.expires_in * 1000)
          userStore.setUserInfo(footer)

          // 记住账号密码
          if (userStore.isRemember) {
            userStore.setName(formData.value.name)
            userStore.setPass(btoa(formData.value.pass))
          }
          userStore.setNoticeChecked(true)

          if (footer.UnitStatus == 1) {
            // 已认证，调用菜单
            GetNavigationBarUser(footer.Id)
          } else if (footer.UnitStatus == 2) {
            // 需要认证：跳到新页面申请认证
            router.replace('/unitauthentic')
          }

          userStore.setImgCode(0)
        } else {
          ElMessage.error(resUser.data.msg)
        }
      })
        .catch((errUser) => {
          // 在这里处理登录失败的额外操作
          console.log("errUser", errUser)
          ElMessage.error(errUser.msg);
        })
    })
    .catch((err) => {
      ElMessage.error('请填写信息');
      console.info('表单验证失败', err)
    })
}
// 班主任登录
const teacherLogin = () => {
  if (!noticeChecked.value) {
    ElMessage.error('请先阅读并同意用户条款')
    return
  }
  // 班主任注册登录
  let paraData = {
    phoneNumber: formData.value.phoneNumber, //'手机号
    validateCode: formData.value.validateCode, //验证码
    uuid: Uuid.value, //验证码uid
    userType: 6, //账号类型：5:家长；6：班主任,
  }
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    teacherUserLogin(paraData).then(res => {
      if (res.data.flag == 1) {
        const { rows, footer } = res.data.data
        userStore.setToken(rows.token)
        userStore.setTokenType(rows.token_type)
        userStore.setExpiresin(rows.expires_in * 1000)
        userStore.setUserInfo(footer)
        userStore.setNoticeChecked(true)
        if (!footer.UnitStatus || footer.UnitStatus == 1) {
          // 已认证，调用菜单
          GetNavigationBarUser(footer.Id)
        } else if (footer.UnitStatus == 2) {
          // 需要认证：跳到新页面申请认证
          router.replace('/unitauthentic')
        }
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })
}
// 切换账号
const inputDemoAccount = (name, pass) => {
  formData.value.name = name
  formData.value.pass = pass
}
// 注册
const reg = () => {
  router.replace('/reg')
}
// 切换登录方法
const tabClick = (e) => {
  isAdmin.value = e
  // noticeChecked.value = false
}

const codeText = ref('获取验证码')
const isCode = ref(false)
const CodeLength = ref(6)
const IsExistAccount = ref(false)
const Uuid = ref()
const time = ref(Date.now() + 1000 * 60)
// 获取校验码
const getCode = () => {
  refForm.value.validateField(['phoneNumber'], valid => {
    if (!valid) {
      return
    }

    // 班主任获取验证码
    let paraData = {
      phoneNumber: formData.value.phoneNumber, //手机号
      validateType: 3, //家长、教师登录
      userType: 6, //账号类型：5:家长；6：班主任,
    }

    teacherGetvalidatecode(paraData).then(res => {
      if (res.data.flag == 1) {
        const { rows } = res.data.data
        CodeLength.value = rows.CodeLength; //验证码长度
        IsExistAccount.value = rows.IsExistAccount; //是否存在账号
        Uuid.value = rows.Uuid; //是否存在账号
        time.value = Date.now() + 1000 * rows.WaitSecond
        isCode.value = true
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  });
}
// 校验码倒计时结束
const finishChange = () => {
  // console.log('倒计时结束')
  codeText.value = '重新获取'
  isCode.value = false
}

const GetNavigationBarUser = (uid) => {
  GetNavigationBar({ uid: uid }).then((res) => {
    // console.log("菜单res", res)
    // console.log("菜单res", res.data.response.children)
    if (res.data.response.children) {
      userStore.setMenu(res.data.response.children)
      // 添加vue router路由
      addDynamicRoutes(userStore.menu)
    }
    // console.log("userStore.curPage.path",userStore.curPage.path)
    const routerPath = userStore.curPage.path
    if (routerPath && !['/login', '/reg', '/exhibition', '/information', '/articlelist', '/articledetail', '/unitauthentic', '/preview', '/'].includes(routerPath)) {
      router.replace(userStore.curPage.path)
    } else {
      let path = ''
      if (userStore.platformType == 2) {
        if (userStore.userInfo.UnitType === 0 || (userStore.userInfo.UnitType > 0 && !['1200', '2200', '3200'].some(t => userStore.userInfo.RoleIds.includes(t)))) {
          path = onePage(userStore.menu)
        } else {
          // 工作流管理平台：首页
          path = 'approval/home/<USER>'
        }
      }
      else if (userStore.platformType == 1) {
        if (![1, 2, 3].includes(userStore.userInfo.UnitType)) {
          path = onePage(userStore.menu)
        } else {
          // 校服管理平台：内部首页
          path = 'uniform/home/<USER>'
        }
      }
      else {
        path = onePage(userStore.menu)
      }
      // 获取当前账号第一个权限页面路径
      // const path = onePage(userStore.menu)
      console.log("获取当前账号第一个权限页面路径", path)
      userStore.setOnePage(path)//存储起来用于tag标签
      // 跳转路由
      // router.replace('/')
      router.replace('/' + path)
    }
  })
}

// 切换图片验证码
const imgCodeChange = () => {
  LoginRefreshcaptchaimageUser()
}
// 获取图片验证码 
const LoginRefreshcaptchaimageUser = () => {
  LoginRefreshcaptchaimage().then((res) => {
    console.log('获取验证码', res)
    const { footer } = res.data.data
    imageUrl.value = `data:image/jpg;base64,${footer.Img}`;
    imgUuid.value = footer.Uuid
  })
}
const noticeList = ref([])
const noticeChecked = ref(false)
// 获取用户条款
const ArticleGetoperatorUser = (parData) => {
  ArticleGetoperator(parData).then(res => {
    // console.log("资讯列表", res)
    if (res.data.flag == 1) {
      const { other } = res.data.data
      noticeList.value = other || []
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 查看用户须知
const detailClick = (item) => {
  const { href } = router.resolve({
    path: "/articledetail",
    query: { Id: item.Id }
  });
  window.open(href, "_blank");
}
//底部资讯分类信息
const ArticleGetbottomcatetypeUser = () => {
  ArticleGetbottomcatetype({ topCount: 3 }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      let articleFooterList = rows || [];//底部资讯分类信息
      userStore.setArticleFooter(articleFooterList)
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 获取配置信息
const AnonGetconfigbymoduleUser = () => {
  AnonGetconfigbymodule({ moduleCode: 8002 }).then(res => {
    let obj = {}
    const { rows } = res.data.data
    rows.map(item => {
      obj[item.TypeCode] = item.ConfigValue
    })
    userStore.setDefaultSet(obj)
  })
}

</script>
<template>
  <div class="login-box">
    <!-- 背景装饰元素 -->
    <div class="bg-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主登录容器 -->
    <div class="main-container">
      <!-- 左侧插图区域 -->
      <div class="illustration-section">
        <div class="illustration-content">
          <!-- 3D插图元素 -->
          <div class="illustration-3d">
            <div class="platform-base">
              <div class="platform-circle"></div>
              <div class="security-shield">
                <div class="shield-icon">🛡️</div>
              </div>
              <div class="data-screen">
                <div class="screen-content">
                  <div class="data-line"></div>
                  <div class="data-line"></div>
                  <div class="data-line"></div>
                </div>
              </div>
              <div class="floating-elements">
                <div class="element element-1">📊</div>
                <div class="element element-2">👔</div>
                <div class="element element-3">🏫</div>
              </div>
            </div>
          </div>
          <!-- 装饰点 -->
          <div class="decoration-dots">
            <div class="dot dot-1"></div>
            <div class="dot dot-2"></div>
            <div class="dot dot-3"></div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单区域 -->
      <div class="login-container">
        <!-- 系统标题和Logo -->
        <div class="system-header">
          <div class="logo-section">
            <div class="logo-icon"> <img src="@/assets/img/xfptlogo.png" alt=""></div>
            <div class="system-info">
              <h2 class="system-title">
                <span v-if="userStore.platformType == 1">{{ userStore.defaultSet['8002_DLYM_BJ'] || '中小学校服管理与备案平台'
                  }}</span>
                <span v-if="userStore.platformType == 2">{{ userStore.defaultSet['8002_DLYM_BJ'] || '审批配置平台' }}</span>
                <span v-if="userStore.platformType == 3">{{ userStore.defaultSet['8002_DLYM_BJ'] || '危化品平台' }}</span>
              </h2>
              <p class="system-subtitle">校服选用 · 校服采购 · 校服征订 · 校服评价</p>
              <!-- <p class="system-subtitle">{{ userStore.defaultSet['8002_DLYM_BJ_YCF'] || '' }}</p> -->
            </div>
          </div>
        </div>

        <!-- 登录标题区域 -->
        <div class="login-header">
          <!-- <div v-if="userStore.platformType == 1" class="login-tabs">
            <div v-if="isAdmin" class="tab-switch"> 
              <span class="switch-link" @click="tabClick(false)">班主任登录 ></span>
            </div>
            <div v-else class="tab-switch"> 
              <span class="switch-link" @click="tabClick(true)">账号登录 ></span>
            </div>
          </div> -->
          <h3 class="login-title">{{ isAdmin ? '账号登录' : '班主任登录' }}</h3>
        </div>
        <!-- 登录表单区域 -->
        <div class="form-section">
          <!-- 账号登录 -->
          <div v-if="isAdmin" class="login-form">
            <el-form @submit.prevent ref="refForm" :rules="formRules" :model="formData" label-position="left"
              label-width="80px">
              <el-form-item label="用户名" prop="name">
                <el-input v-model.trim="formData.name" :prefix-icon="User" clearable auto-complete="off"
                  placeholder="请输入用户名" class="custom-input"></el-input>
              </el-form-item>
              <el-form-item label="密码" prop="pass">
                <el-input @keyup.enter="login" v-model.trim="formData.pass" :prefix-icon="Lock" clearable
                  auto-complete="off" show-password placeholder="请输入密码" class="custom-input"></el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="imgCode" v-if="userStore.isImgCode == 5">
                <div class="captcha-container">
                  <el-input v-model="formData.imgCode" auto-complete="off" placeholder="请输入验证码"
                    class="captcha-input"></el-input>
                  <div class="captcha-image" @click="imgCodeChange">
                    <img v-if="imageUrl" :src="imageUrl" alt="验证码" />
                  </div>
                </div>
              </el-form-item>

              <el-form-item label-width="0px">
                <div class="form-options">
                  <el-checkbox class="remember-checkbox" v-model="userStore.isRemember">记住密码</el-checkbox>
                  <!-- <div @click="reg" v-if="userStore.platformType == 1" class="register-link">
                    立即注册
                  </div> -->
                </div>
              </el-form-item>

              <!-- 快速登录测试账号 -->
              <el-form-item label-width="0px">
                <div style="margin-bottom: 20px" class="count-test">
                  <el-radio-group>
                    <el-radio-button value="" label="学校"
                      @click="inputDemoAccount('yucai', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="区县"
                      @click="inputDemoAccount('浦口教育局', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="市级"
                      @click="inputDemoAccount('shiji', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="管理员"
                      @click="inputDemoAccount('超级管理员', '123456')"></el-radio-button>
                    <el-radio-button value="" label="企业"
                      @click="inputDemoAccount('junfei', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="lisi1"
                      @click="inputDemoAccount('lisi1', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="quxian1"
                      @click="inputDemoAccount('quxian1', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="quxian2"
                      @click="inputDemoAccount('quxian2', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="lyy"
                      @click="inputDemoAccount('lyy002', '123456@cneefix')"></el-radio-button>
                  </el-radio-group>
                </div>
              </el-form-item>

              <el-form-item class="login-button-item" label-width="0px">
                <el-button type="primary" class="login-button" @click="login" :disabled="isLogin">
                  <span>登录</span>
                </el-button>
              </el-form-item>

              <!-- 用户协议 -->
              <el-form-item v-if="noticeList.length > 0" class="agreement-item" label-width="0px">
                <div class="agreement-content">
                  <el-checkbox v-model="noticeChecked" class="agreement-checkbox">
                    <span class="agreement-text">阅读并接受: </span>
                  </el-checkbox>
                  <span class="agreement-links" v-for="(item, index) in noticeList" :key="item.Id">
                    <span class="agreement-link" @click="detailClick(item)">《{{ item.Title }}》</span>
                    <span v-if="index < noticeList.length - 1">、</span>
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </div>
          <!-- 班主任登录  -->
          <div v-else class="login-form">
            <div class="teacher-login-tip">未注册将自动创建并登录</div>
            <el-form @submit.prevent ref="refForm" :rules="formRules" :model="formData" label-position="left"
              label-width="80px">
              <el-form-item label="手机号码" prop="phoneNumber">
                <el-input v-model="formData.phoneNumber" clearable @input="integerLimitInput($event, 'phoneNumber')"
                  auto-complete="off" placeholder="请输入手机号码" class="custom-input"></el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="validateCode">
                <div class="code-container">
                  <el-input v-model="formData.validateCode" clearable @input="integerLimitInput($event, 'validateCode')"
                    auto-complete="off" placeholder="请输入验证码" @keyup.enter="teacherLogin" class="code-input"></el-input>
                  <el-button type="primary" plain :disabled="isCode" @click="getCode" class="code-button">
                    <span v-if="!isCode">{{ codeText }}</span>
                    <el-countdown v-else format="ss" :value="time" @finish="finishChange" suffix="秒后重新获取"
                      :value-style="{ color: '#606266', fontSize: '14px' }" />
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item class="login-button-item" label-width="0px">
                <el-button type="primary" class="login-button" @click="teacherLogin" :disabled="isTeacherLogin">
                  <span>登录</span>
                </el-button>
              </el-form-item>

              <!-- 用户协议 -->
              <el-form-item v-if="noticeList.length > 0" class="agreement-item" label-width="0px">
                <div class="agreement-content">
                  <el-checkbox v-model="noticeChecked" class="agreement-checkbox">
                    <span class="agreement-text">阅读并接受: </span>
                  </el-checkbox>
                  <span class="agreement-links" v-for="(item, index) in noticeList" :key="item.Id">
                    <span class="agreement-link" @click="detailClick(item)">《{{ item.Title }}》</span>
                    <span v-if="index < noticeList.length - 1">、</span>
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
// 主容器样式
.login-box {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

// 背景装饰元素
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 2s;
  }

  &.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }

  &.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 10%;
    right: 10%;
    animation-delay: 1s;
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 主登录容器
.main-container {
  position: relative;
  display: flex;
  width: 90%;
  max-width: 800px;
  min-height: 500px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  z-index: 2;
}

// 左侧插图区域
.illustration-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  position: relative;
  overflow: hidden;
}

.illustration-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 3D插图元素
.illustration-3d {
  position: relative;
  width: 300px;
  height: 300px;
}

.platform-base {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: rotate3d 20s linear infinite;
}

.platform-circle {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.security-shield {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px rgba(255, 154, 158, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

.shield-icon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.data-screen {
  position: absolute;
  top: 20%;
  right: 10%;
  width: 100px;
  height: 70px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: rotateY(-15deg);
}

.screen-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.data-line {
  height: 4px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 2px;

  &:nth-child(1) {
    width: 80%;
  }

  &:nth-child(2) {
    width: 60%;
  }

  &:nth-child(3) {
    width: 90%;
  }
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.element {
  position: absolute;
  font-size: 24px;
  animation: floatElement 4s ease-in-out infinite;

  &.element-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.element-2 {
    top: 30%;
    right: 20%;
    animation-delay: 1.5s;
  }

  &.element-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 3s;
  }
}

.decoration-dots {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: dotPulse 2s ease-in-out infinite;

  &.dot-2 {
    animation-delay: 0.5s;
  }

  &.dot-3 {
    animation-delay: 1s;
  }
}

@keyframes rotate3d {
  0% {
    transform: rotateY(0deg);
  }

  100% {
    transform: rotateY(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes floatElement {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes dotPulse {

  0%,
  100% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }
}

// 右侧登录容器
.login-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 40px 35px;
  background: #fff;
  position: relative;
  width: 380px !important;
}

// 系统标题区域
.system-header {
  margin-bottom: 25px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;

  // font-size: 20px;
  // box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  img {
    width: 100%;
    height: 100%;
  }
}

.system-info {
  flex: 1;
}

.system-title {
  margin: 0 0 3px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.system-subtitle {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
  line-height: 1.4;
}

// 登录标题区域
.login-header {
  margin-bottom: 20px;
}

.login-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  // text-align: center;
  font-weight: 500;
  color: #2c3e50;
}

.login-tabs {
  display: flex;
  justify-content: flex-end;
}

.tab-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.current-tab {
  color: #2c3e50;
  font-weight: 500;
}

.switch-link {
  color: #4e6ef2;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #667eea;
  }
}

// 表单区域
.form-section {
  flex: 1;
}

.login-form {
  width: 100%;
}

// 自定义输入框样式
:deep(.custom-input) {
  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e1e8ed;
    transition: all 0.3s ease;

    &:hover {
      border-color: #4e6ef2;
      box-shadow: 0 4px 12px rgba(78, 110, 242, 0.15);
    }

    &.is-focus {
      border-color: #4e6ef2;
      box-shadow: 0 4px 12px rgba(78, 110, 242, 0.2);
    }
  }

  .el-input__inner {
    height: 36px;
    font-size: 14px;
    color: #2c3e50;

    &::placeholder {
      color: #bdc3c7;
    }
  }
}

// 验证码容器
.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.captcha-input {
  width: 60%;
}

.captcha-image {
  width: 100px;
  height: 36px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #e1e8ed;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 验证码容器（班主任登录）
.code-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.code-input {
  width: 60%;
}

.code-button {
  height: 36px;
  border-radius: 8px;
  font-size: 13px;
  min-width: 110px;
}

// 表单选项
.form-options {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}

.remember-checkbox {
  :deep(.el-checkbox__label) {
    color: #7f8c8d;
    font-size: 14px;
  }
}

.register-link {
  color: #4e6ef2;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #667eea;
  }
}

// 班主任登录提示
.teacher-login-tip {
  margin-bottom: 20px;
  color: #7f8c8d;
  font-size: 14px;
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4e6ef2;
}



.demo-title {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 10px;
  text-align: center;
}



// 登录按钮
.login-button-item {
  margin: 20px 0 15px 0;
}

.login-button {
  width: 100% !important;
  height: 40px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  background: linear-gradient(135deg, #4e6ef2 0%, #667eea 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(78, 110, 242, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 110, 242, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  &.is-disabled {
    background: #bdc3c7;
    box-shadow: none;
    transform: none;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  span {
    position: relative;
    z-index: 1;
  }
}

// 用户协议
.agreement-item {
  margin-bottom: 0;
}

.agreement-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  line-height: 1.5;
}

.agreement-checkbox {
  :deep(.el-checkbox__label) {
    color: #7f8c8d;
    font-size: 13px;
    line-height: 1.5;
  }
}

.agreement-text {
  color: #7f8c8d;
}

.agreement-links {
  color: #4e6ef2;
  line-height: 1.5;
}

.agreement-link {
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #667eea;
    text-decoration: underline;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    width: 95%;
    min-width: 450px;
    min-height: auto;
  }

  .illustration-section {
    height: 200px;
    flex: none;
  }

  .illustration-3d {
    width: 200px;
    height: 200px;
  }

  .login-container {
    padding: 30px 25px;
    margin: 20px auto;
  }

  .system-title {
    font-size: 20px;
  }


}

:deep(.el-statistic__suffix) {
  font-size: 14px;
}

:deep(.el-form-item__content) {
  align-items: center;
  justify-content: space-between;

  .el-button {
    width: 120px;
  }

  img {
    height: 100%;
  }
}

// 表单项间距调整
:deep(.el-form-item) {
  margin-bottom: 15px;

  .el-form-item__label {
    color: #2c3e50;
    font-weight: 500;
    font-size: 13px;
    line-height: 36px;
  }
}

// 复选框样式优化
:deep(.el-checkbox) {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #4e6ef2;
    border-color: #4e6ef2;
  }

  .el-checkbox__inner:hover {
    border-color: #4e6ef2;
  }
}

// 倒计时样式
:deep(.el-statistic) {
  .el-statistic__content {
    font-size: 14px;
    color: #6c757d;
  }
}
</style>
