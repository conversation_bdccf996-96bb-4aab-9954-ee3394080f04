<script setup>
defineOptions({
    name: 'dangerchemicalsapplyprint'
});
import { onMounted, ref, nextTick, onActivated, watch } from 'vue'
import {
    Refresh, Back, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcApplyPrintListFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { fileDownload, tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const refTable = ref()
const filters = ref({
    pageIndex: 1,
    pageSize: 9999999,
    IsGrant: 1,
    ApplyType: 1,
    sortModel: [
        { SortCode: "ApplyUserName", SortType: "ASC" },
        { SortCode: "BatchNo", SortType: "ASC" }]
})
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }

})
const dtBegin = ref('')
const dtEnd = ref('')
const format = (date) => {
    if (date) {
        const [year, month, day] = date.split('-');
        return `${year}年${month}月${day}日`;
    } else {
        return ''
    }

}
onActivated(() => {
    dtBegin.value = format(route.query.dtBegin)
    dtEnd.value = format(route.query.dtEnd)
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })
}

// 列表
const HandleTableData = () => {
    filters.value.BeginDate = route.query.dtBegin
    filters.value.EndDate = route.query.dtEnd + " 23:59:59"
    filters.value.ApplyUserName = route.query.userName
    filters.value.Id = route.query.ids
    DcApplyPrintListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            // tableData.value = rows.data.slice(0, 20)
            tableData.value = rows.data || []
            console.log(rows.data)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 计算每个单元格应该跨越的行数
const rowspans = ref([]);
// 计算合并信息
const calculateRowspans = () => {
    const spans = [];
    let count = 1;

    for (let i = 0; i < tableData.value.length; i++) {
        if (i === tableData.value.length - 1) {
            spans.push(count);
            break;
        }
        if (tableData.value[i].ApplyUserName === tableData.value[i + 1].ApplyUserName) {
            count++;
        } else {
            spans.push(count);
            count = 1;
        }
    }
    rowspans.value = spans;
};

// 获取指定行的rowspan值
const getRowspan = (index) => {
    for (let i = 0, sum = 0; i < rowspans.value.length; i++) {
        sum += rowspans.value[i];
        if (index < sum) {
            return rowspans.value[i];
        }
    }
    return 1;
};

// 判断是否应该显示用户名单元格
const shouldShowUserNameCell = (index) => {
    if (index === 0) return true;
    return tableData.value[index].ApplyUserName !== tableData.value[index - 1].ApplyUserName;
};

// 在数据加载后计算合并信息
watch(() => tableData.value, (newVal) => {
    if (newVal && newVal.length > 0) {
        calculateRowspans();
    }
}, { immediate: true, deep: true });

const handleNativePrint = () => {
    // 获取打印区域内容
    const printContent = document.getElementById('printArea').innerHTML;
    // 创建一个隐藏的iframe
    const iframe = document.createElement('iframe');
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');
    // 添加到DOM中
    document.body.appendChild(iframe);
    // 获取iframe的document对象
    const iframeWindow = iframe.contentWindow || iframe.contentDocument;
    const iframeDoc = iframeWindow.document || iframeWindow;
    // 写入打印内容
    iframeDoc.open();
    iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>危险化学品领用台账</title>
            <style>
                @page { size: landscape; margin:10mm 5mm; }
                body { font-family: 宋体; margin: 0; padding: 0;   }
                .print-table { border-collapse: collapse; width: 100%; font-size: 14px; }
                .print-table th { border: 1px solid #ddd; padding: 5px 1px; } 
                .print-table td { border: 1px solid #ddd; padding: 6px 3px; }
                thead { display: table-header-group; }
                tr { page-break-inside: avoid; }
                .print-title { font-size: 35px; margin: 10px 0; text-align: center; }
                .print-date { font-size: 22px; text-align: right; }
                .print-signright { margin-top: 20px; margin-bottom: 10px; text-align: right; }
                .print-signcenter { border-bottom: 1px solid #000000; width: 150px; display: inline-block; margin-right: 20px; } 
            </style>
        </head>
        <body>
            ${printContent}
        </body>
        </html>
    `);
    iframeDoc.close();

    // 等待内容加载完成后打印
    iframe.onload = function () {
        setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
            // 打印完成后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 100);
        }, 200);
    };
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="handleNativePrint">打印</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="width: 1150px;" id="printArea">
            <div class="print-title">
                <span style="font-size: 35px;">
                    {{ userStore.userInfo.UnitName }}{{ route.query.printType == 1 ? '危化品出库单' : '危化品签字单' }}</span>
            </div>
            <div class="print-date">
                <span>出库时间段：{{ dtBegin }}--{{ dtEnd }}</span>
            </div>
            <table class="print-table">
                <thead>
                    <tr>
                        <th style="width: 40px;">序号</th>
                        <th style="width: 100px;">危化品名称</th>
                        <th style="width: 100px;">规格属性</th>
                        <th style="width: 80px;">品牌</th>
                        <th style="width: 50px;">数量</th>
                        <th style="width: 40px;">单位</th>
                        <th style="width: 70px;" v-if="route.query.printType == 1">单价</th>
                        <th style="width: 100px;" v-if="route.query.printType == 1">金额</th>
                        <th style="width: 85px;">申领批次</th>
                        <th style="width: 60px;">领用人</th>
                        <th style="width: 70px;" v-if="route.query.printType == 2">领用人签字</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="(row, index) in tableData" :key="index">
                        <tr>
                            <td style="text-align: center;">{{ index + 1 }}</td>
                            <td>{{ row.Name }}</td>
                            <td>{{ row.Model }}</td>
                            <td>{{ row.Brand ? row.Brand : '--' }}</td>
                            <td style="text-align: right;word-wrap:break-word;word-break: keep-all;white-space:nowrap;">
                                {{ row.Num }}</td>
                            <td style="text-align: center;word-wrap:break-word;">{{ row.UnitName }}</td>
                            <td style="word-wrap:break-word;text-align:right;word-break: keep-all;white-space:nowrap;"
                                v-if="route.query.printType == 1">{{ row.Price.toFixed(2) }}</td>
                            <td style="word-wrap:break-word;text-align:right;word-break: keep-all;white-space:nowrap;"
                                v-if="route.query.printType == 1">{{ (row.Price * row.Num).toFixed(2) }}</td>
                            <td style="text-align: center;"> {{ row.BatchNo }}</td>
                            <td style="word-wrap:break-word;text-align:center;" :rowspan="getRowspan(index)"
                                v-if="shouldShowUserNameCell(index)">{{ row.ApplyUserName }}</td>
                            <td v-if="route.query.printType == 2 && shouldShowUserNameCell(index)" id="sign"
                                :rowspan="getRowspan(index)"> </td>
                        </tr>
                    </template>
                    <!-- 添加合计行 -->
                    <tr v-if="route.query.printType == 1 && tableData.length > 0">
                        <td></td>
                        <td>总计（元）：</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td style="text-align: right;">
                            {{tableData.reduce((sum, row) => sum + row.Price * row.Num, 0).toFixed(2)}}
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
            <div class="print-signright">
                经办人： <div class="print-signcenter" style="width: 150px;"> &nbsp;</div>
            </div>
        </div>

    </div>
</template>
<style>
/* 打印样式 - 确保合并行不被分割 */
@media print {

    /* 强制表格在分页时保持行完整 */
    table.print-table {
        page-break-inside: auto;
    }

    /* 确保合并行不会被分割到不同页 */
    tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    /* 表头每页重复 */
    thead {
        display: table-header-group;
    }

    /* 合并单元格的边框处理 */
    td[rowspan] {
        border-bottom: 1px solid #000 !important;
    }

    /* 确保最后一行边框完整 */
    tr:last-child td {
        border-bottom: 1px solid #000 !important;
    }



}

.print-title {
    font-size: 35px;
    font-family: 宋体;
    margin: 10px 0;
    text-align: center;
}

.print-date {
    font-size: 22px;
    font-family: 宋体;
    text-align: right;
}

/* 屏幕显示样式（保持不变） */
.print-table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

.print-table th,
.print-table td {
    border: 1px solid #ddd;
    padding: 3px;
}
</style>
<style lang="scss" scoped>
table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

table th {
    border: 1px solid #ddd;
    padding: 10px 1px;
}

table td {
    border: 1px solid #ddd;
    padding: 6px 3px;
}

.print-signright {
    margin-top: 20px;
    font-family: 宋体;
    margin-bottom: 10px;
    text-align: right;
}

.print-signcenter {
    border-bottom: 1px solid #000000;
    display: inline-block;
    margin-right: 20px;
}
</style>