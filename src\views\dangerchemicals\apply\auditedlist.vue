<script setup>
defineOptions({
    name: 'dangerchemicalsapplyauditedlist'
});
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    DcApplyAuditedListFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { pageQuery, getApplyStatus, ConsolidatedColumn } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const routerObject = ref({})//成页面携带的参数对象  
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const EndDate = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, ProcessNumber: 20, sortModel: [{ SortCode: "BatchNo", SortType: "DESC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const getApplyStatuzArray = [
    { Id: 21, Name: '主管审批退回' },
    { Id: 30, Name: '等待库管配货' },
    { Id: 40, Name: '等待领取危化品' },
    { Id: 100, Name: '危化品领用结束' }
]
const options = ref([
    { value: 'BatchNo', label: '申领批次', },
    { value: 'UserName', label: '领用人', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)
    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.Statuz = undefined
    filters.value.BeginDate = undefined
    filters.value.EndDate = undefined
    EndDate.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.BeginDate = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.EndDate = val + " 23:59:59"
    } else {
        filters.value.EndDate = undefined
    }
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcApplyAuditedListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 计算合并信息
const spanArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'UserName')
})

// 计算合并信息
const spanBatchNoArr = computed(() => {
    return ConsolidatedColumn(tableData.value, 'BatchNo')
})
// 合并单元格方法
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    // 只对第一列（UserName）进行合并
    if (columnIndex === 0) {
        const _row = spanArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
    if (columnIndex === 1) {
        const _row = spanBatchNoArr.value[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
            rowspan: _row,
            colspan: _col
        }
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDate) return false;
    return time < new Date(filters.value.BeginDate + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="状态" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in getApplyStatuzArray" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :span-method="objectSpanMethod"
            header-cell-class-name="headerClassName">
            <el-table-column prop="UserName" label="领用人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="BatchNo" label="申领批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="申请时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.CollarRegDate ? row.CollarRegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UseTime" label="使用时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.UseTime ? row.UseTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.Statuz ? getApplyStatus(row.Statuz) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="用途" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="WithUserName" label="同领用人" min-width="110" align="center">
                <template #default="{ row }">
                    {{ row.WithUserName || '--' }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>