<script setup>
defineOptions({
  name: 'maintainenterlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search, EditPen, FolderAdd
} from '@element-plus/icons-vue'
import {
  ListGetpaged, ListDeletebyid, ListSubmit, ListSetsattuz, ListSavecopy
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { getYearMonthDay } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const selectRows = ref([])
const SchoolList = ref([])
const StatuzUseList = ref([])
const filters = ref({ pageIndex: 1, pageSize: 10 })
const dialogVisible = ref(false)
const dialogTableData = ref([])
const SchoolName = ref('')

//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})
// 列表多选
const HandleSelectChange = (selection) => {
  selectRows.value = selection
}

//新增
const HandleAdd = () => {
  router.push({ path: "./enteredit", query: { id: 0, title: "添加校服" } })
}
// 修改
const HandleEdit = (row) => {
  router.push({ path: "./enteredit", query: { id: row.Id, title: "修改校服" } })
}
// 复制
const HandleCopy = (row) => {
  ListSavecopy({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      HandleTableData()
      ElMessage.success(res.data.msg || '复制成功')
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 提交审核
const HandleAudit = () => {
  let isSchoolId = selectRows.value.find(t => t.SchoolId !== selectRows.value[0].SchoolId)
  if (isSchoolId) {
    ElMessage.error('只有同一学校才能一起提交审核')
    return
  }
  SchoolName.value = selectRows.value[0].SchoolName
  dialogVisible.value = true
  dialogTableData.value = selectRows.value
}
// 确认提交审核
const HandleSubmit = () => {
  let ids = dialogTableData.value.map(t => t.Id).join(',')
  ListSubmit({ ids: ids }).then((res) => {
    if (res.data.flag == 1) {
      HandleTableData()
      ElMessage.success(res.data.msg || '批量审核成功')
      dialogVisible.value = false
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 修改状态
const HandleSwitchChange = (e, row) => {
  ListSetsattuz({ ids: row.Id, statuz: e }).then((res) => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '状态修改成功')
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 起止日期
const HandleDateSearch = (date) => {
  if (date) {
    let dateBegin = new Date(date[0])
    let dateEnd = new Date(date[1])
    filters.value.DateBegin = getYearMonthDay(dateBegin)
    filters.value.DateEnd = getYearMonthDay(dateEnd)
  } else {
    filters.value.DateBegin = undefined
    filters.value.DateEnd = undefined
  }
  HandleTableData()
}

//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      ListDeletebyid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//列表
const HandleTableData = (isFirst) => {
  let formData = {
    pageIndex: filters.value.pageIndex,
    pageSize: filters.value.pageSize,
    UseStatuz: filters.value.UseStatuz,
    SchoolId: filters.value.SchoolId,
    DateBegin: filters.value.DateBegin,
    DateEnd: filters.value.DateEnd,
    Name: filters.value.Name,
  }
  if (isFirst) {
    formData.isFirst = true
  } else {
    formData.isFirst = false
  }

  ListGetpaged(formData).then(res => {
    if (res.data.flag == 1) {
      // console.log("采购管理列表", res.data.data)
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        StatuzUseList.value = other.StatuzUseList || [];//使用状态
        SchoolList.value = other.SchoolList || []//学校名称
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.SchoolId = undefined
  filters.value.UseStatuz = undefined
  filters.value.date = undefined
  filters.value.DateBegin = undefined
  filters.value.DateEnd = undefined
  filters.value.Name = undefined
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }

</script>
<template>
  <div class="viewContainer">
    <el-collapse>
      <el-collapse-item>
        <template #title>
          操作提示 &nbsp;
          <el-icon color="#E6A23C" :size="16">
            <QuestionFilled />
          </el-icon>
        </template>
        <ol class="rowFill">
          <li> 点击【添加】和【修改】按钮，可维护校服信息；</li>
          <li> 按照采购合同清单勾选所需“品名”后，点击【提交审核】转交学校审核； </li>
          <li> 向学校提交审核后，禁止删除； </li>
          <li> 无效品名可在“使用状态”列中设置为“禁用”。 </li>
        </ol>
      </el-collapse-item>
    </el-collapse>
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">

          <el-form-item class="flexItem flexOperation">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
            <el-button type="success" :icon="EditPen" :disabled="selectRows.length == 0"
              @click="HandleAudit">提交审核</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item label="更新时间：" class="flexItem">
            <el-date-picker v-model="filters.date" type="daterange" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
              range-separator="至" start-placeholder="起始日期" end-placeholder="截止日期" style="width: 260px"
              @change="HandleDateSearch" />
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="适用学校" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.UseStatuz" clearable placeholder="使用状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzUseList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="种类/品名" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>

        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" border
      header-cell-class-name="headerClassName">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column prop="SchoolName" label="适用学校" min-width="160"></el-table-column>
      <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"></el-table-column>
      <el-table-column prop="Name" label="品名" min-width="140" align="center"></el-table-column>
      <el-table-column prop="Price" label="单价（元）" min-width="100" align="right"></el-table-column>
      <el-table-column prop="Sex" label="适合性别" min-width="100" align="center">
        <template #default="{ row }">
          {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : row.Sex == 3 ? '男/女' : '未知' }}
        </template>
      </el-table-column>
      <el-table-column prop="StandardNum" label="标配数量" min-width="100" align="center"></el-table-column>
      <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
      <el-table-column prop="LastSubmitTime" label="提交时间" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.LastSubmitTime ? row.LastSubmitTime.substring(0, 10) : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="ModifyTime" label="更新时间" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.ModifyTime ? row.ModifyTime.substring(0, 10) : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="UseStatuz" label="使用状态" min-width="100" align="center">
        <template #default="{ row }">
          <el-switch v-model="row.UseStatuz" :active-value="1" :inactive-value="2"
            style="--el-switch-off-color: #ff4949" inline-prompt active-text="启" inactive-text="禁"
            @change="HandleSwitchChange($event, row)" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="160" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
          <el-button v-if="row.LastSubmitTime" type="primary" link @click="HandleCopy(row)">复制</el-button>
          <el-button v-if="!row.LastSubmitTime" type="primary" link @click="HandleDel(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
    <el-dialog v-model="dialogVisible" title="提交审核" draggable width="900px">
      <div class="dialog-content">
        <div style="font-size: 14px;color: #E6A23C;margin-bottom: 10px;">
          提交给【{{ SchoolName }}】审核的校服明细如下，请核对是否与本次采购合同清单一致！
        </div>
        <el-table :data="dialogTableData" highlight-current-row border stripe header-cell-class-name="headerClassName">
          <el-table-column prop="Uniformtype" label="种类" min-width="120" align="center"></el-table-column>
          <el-table-column prop="Name" label="品名" min-width="120" align="center"></el-table-column>
          <el-table-column prop="Price" label="单价（元）" min-width="80" align="right"></el-table-column>
          <el-table-column prop="Sex" label="适合性别" min-width="100" align="center">
            <template #default="{ row }">
              {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : row.Sex == 3 ? '男/女' : '未知' }}
            </template>
          </el-table-column>
          <el-table-column prop="StandardNum" label="标配数量" min-width="100" align="center"></el-table-column>
          <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit">
            确认提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>
<style lang="scss" scoped>
.dialog-content {
  min-height: 200px;
  max-height: 800px;
  overflow: auto;
}
</style>