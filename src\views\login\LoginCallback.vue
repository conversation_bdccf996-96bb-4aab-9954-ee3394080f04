<script setup>
import { userManager } from '@/utils/oidc-client.js'
import { onMounted } from 'vue'
import router, { addDynamicRoutes } from "@/router";
import { getToken } from '@/api/auth.js'
import { useUserStore } from '@/stores'
import { GetNavigationBar } from '@/api/user.js'
import { onePage } from "@/utils/index.js"

const userStore = useUserStore()

onMounted(async () => {
    try {
        // 完成认证流程
        await userManager.signinRedirectCallback()
        const user = await userManager.getUser();
        const token = await getToken(user);

        const { rows, footer } = token.data.data;

        // 设置token和用户信息到store
        userStore.setToken(rows.token);
        userStore.setTokenType(rows.token_type);
        userStore.setExpiresin(rows.expires_in * 1000);
        userStore.setUserInfo(footer);

        // 根据单位状态处理跳转
        if (footer.UnitStatus === 1) {
            await handleAuthenticated(footer.Id);
        } else if (footer.UnitStatus === 2) {
            router.replace('/unitauthentic');
        } else {
            router.replace('/unitauthentic');
        } 
    } catch (error) {
        console.error('Error during login callback:', error);
        // 处理错误，例如跳转到错误页面或显示消息
    }
    })


const handleAuthenticated = async (uid) => {
  try {
    const navRes = await GetNavigationBar({ uid });
    userStore.setMenu(navRes.data.response.children);
    addDynamicRoutes(userStore.menu)
    const currentPath = userStore.curPage.path;
    console.log(currentPath)
    if (currentPath && !['/login','/oauth/login-success', '/reg', '/exhibition', '/information', '/articlelist', '/articledetail', '/unitauthentic', '/preview', '/'].includes(currentPath)) {
        router.push(currentPath);
    } else {
      const firstPage = onePage(userStore.menu);
      userStore.setOnePage(firstPage);
      router.push('/' + firstPage);
    }
  } catch (error) {
    console.error('Navigation setup failed:', error);
    router.push('/login');
  }
};

</script>

<template>
    <div class="login-callback">
        <el-card class="callback-card">
            <div class="loading-text">
                正在处理登录信息，请稍候...
            </div>
        </el-card>
    </div>
</template>

<style scoped>
.login-callback {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
}

.callback-card {
    width: 400px;
    text-align: center;
}

.loading-text {
    font-size: 16px;
    color: #606266;
    padding: 20px;
}
</style>