<script setup>
defineOptions({
    name: 'dangerchemicalsapplycarlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Back, Search, Refresh, FolderAdd
} from '@element-plus/icons-vue'
import {
    BconfigSetgetPunit, DcapplyCarListFind, DcapplyFindByid, DcapplyInsertUpdate, DcapplyBatchApply, DcapplyDelete, DcapplyGetStocknum,
    DcapplyGrantUserComboGet, DcschoolCatalogBrandGetbyModelidv2, DcschoolCatalogComboboxGet, DcschoolCatalogCommonuseFind, DcschoolCatalogModelGetv2,
    DcschoolCatalogFindCommonuseAll
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { integerLimit, buildTree } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const schoolCatalogList = ref([])
const dangerchemicalsList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 50, Statuz: 0, IsEmptyBatchNo: true, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const whpDialogVisible = ref(false)
const isEdit = ref(false)
const formData = ref({})
const refForm = ref()
const bogetList = ref([])
const memberUserList = ref([])
const modelList = ref([])
const brandList = ref([])
const bconfigSet = ref(0)
const ruleForm = {
    UseTime: [
        { required: true, message: '请选择使用时间', trigger: 'change' },
    ],
    WithUserId: [
        { required: true, message: '请选择同领用人', trigger: 'change' },
    ],
    SchoolMaterialModelId: [
        { required: true, message: '请选择规格属性', trigger: 'change' },
    ],
    SchoolMaterialBrandId: [
        { required: true, message: '请选择品牌', trigger: 'change' },
    ]
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        DcschoolCatalogFindCommonuseAllUser()
        DcschoolCatalogComboboxGetUser()
        BconfigSetgetPunitUser()
    }

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            DcschoolCatalogFindCommonuseAllUser()
            DcschoolCatalogComboboxGetUser()
            BconfigSetgetPunitUser()
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 添加危化品
const HandleAdd = (row) => {
    router.push({ path: './fill' })
}
// 领用申请
const HandleApply = (row) => {
    isEdit.value = false
    dialogVisible.value = true
}
// 修改
const HandleEdit = (row) => {
    isEdit.value = true
    activeName.value = 'content'
    firstName.value = ''
    dangerchemicalsList.value = []
    DcapplyFindByidUser(row.Id)
    DcschoolCatalogModelGetv2User(row.SchoolCatalogId)
    DcschoolCatalogBrandGetbyModelidv2User(row.SchoolCatalogId, row.SchoolMaterialModelId)
    DcapplyGetStocknumUser(row.SchoolCatalogId, row.SchoolMaterialModelId, row.schoolMaterialBrandId)
    dialogVisible.value = true
}

// 修改提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (isEdit.value) {
            let data = {
                Id: formData.value.Id,
                SchoolCatalogId: formData.value.SchoolCatalogId,
                SchoolMaterialModelId: formData.value.SchoolMaterialModelId,
                SchoolMaterialBrandId: formData.value.SchoolMaterialBrandId,
                UnitName: formData.value.UnitName,
                Remark: formData.value.Remark,
                Num: formData.value.Num,
                Name: formData.value.Name,
            }
            DcapplyInsertUpdate(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '修改成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        } else {
            let data = {
                ids: selectRows.value.map(item => item.Id).join(','),
                withUserId: formData.value.WithUserId,
                useTime: formData.value.UseTime,
            }
            DcapplyBatchApply(data).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '申领成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        }

    })
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    DcapplyCarListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            tableData.value.forEach(item => {
                item.ApplyNum = ''
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const activeName = ref('content')
const firstName = ref('')
const isSearch = ref(false)
const HandleSearch1 = () => {
    isSearch.value = true
    if (!firstName.value) return
    let paraData = {
        Name: firstName.value,
        Statuz: 1,
        pageIndex: 0,
        pageSize: 100000,
        sortModel: [{ SortCode: "Code", SortType: "asc" }]
    }
    DcschoolCatalogCommonuseFind(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerchemicalsList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm('您确认要删除该数据吗？')
        .then(() => {
            DcapplyDelete({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success(res.data.msg || '删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: './fill' })

}
// 获取学校物品类别选择
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let arr = rows.data || []
            bogetList.value = buildTree(arr)
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}

// 根据TypeCode获取上级单位配置信息
const BconfigSetgetPunitUser = () => {
    BconfigSetgetPunit({ moduleCode: 9, typeCode: 'WHPLYMS' }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bconfigSet.value = rows
            if (rows == 2 || rows == 3) {
                DcapplyGrantUserComboGetUser()
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取同领用人、发放人
const DcapplyGrantUserComboGetUser = () => {
    DcapplyGrantUserComboGet({ type: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            memberUserList.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}

// 选择危化品
const dangerchemicalsClick = (item) => {
    console.log("item", item)
    formData.value.Id = item.Id
    formData.value.Name = item.Name
    formData.value.UnitName = item.UnitsMeasurement
    formData.value.SchoolMaterialModelId = undefined
    formData.value.SchoolMaterialBrandId = undefined
    whpDialogVisible.value = false
    DcschoolCatalogModelGetv2User(item.Id)
    DcschoolCatalogBrandGetbyModelidv2User(item.Id, 0)
}
// 选择危化品
const schoolCatalogClick = (e) => {
    isSearch.value = false
    let arr = schoolCatalogList.value.filter(item => item.Id == e)
    let name = arr[0].Name
    let index = name.lastIndexOf('> ');
    formData.value.Id = e
    formData.value.UnitName = arr[0].UnitName
    formData.value.Name = name.substring(index + 1)
    formData.value.SchoolMaterialModelId = undefined
    formData.value.SchoolMaterialBrandId = undefined
    console.log("arr", arr)
    console.log("formData.value", formData.value)
    DcschoolCatalogModelGetv2User(e)
    DcschoolCatalogBrandGetbyModelidv2User(e, 0)
}
// 获取规格属性
const DcschoolCatalogModelGetv2User = (id) => {
    DcschoolCatalogModelGetv2({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            modelList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取品牌
const DcschoolCatalogBrandGetbyModelidv2User = (id, modelId) => {
    DcschoolCatalogBrandGetbyModelidv2({ schoolCatalogId: id, modelId: modelId }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            brandList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const stockNum = ref(0)
// 获取剩余库存数量
const DcapplyGetStocknumUser = (id, modelId, brandId) => {
    DcapplyGetStocknum({ schoolCatalogId: id, schoolMaterialModelId: modelId, schoolMaterialBrandId: brandId }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            stockNum.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取危化品下拉框数据
const DcschoolCatalogComboboxGetUser = (id, modelId, brandId) => {
    DcschoolCatalogComboboxGet().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            schoolCatalogList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取详情
const DcapplyFindByidUser = (id) => {
    DcapplyFindByid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // brandList.value = rows.data || []
            formData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    领用车 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 选中需申请的危化品后，点击【领用申请】 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                        <div class="verticalDividel"></div>
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加危化品</el-button>
                        <div class="verticalDividel"></div>
                        <el-button type="primary" :icon="Search" :disabled="selectRows.length == 0"
                            @click="HandleApply">领用申请</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip>
                <template #default="{ row }">
                    <el-tooltip class="item" effect="dark" :content="row.Remark ? '用途：' + row.Remark : '无'"
                        placement="top">
                        {{ row.Name }}
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="StockNum" label="库存" min-width="120" align="right"></el-table-column>
            <el-table-column label="操作" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="isEdit ? '修改危化品信息' : '危化品领用'">
            <template #content>
                <el-form style="min-width: 100px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="120px" status-icon>
                    <div v-if="isEdit">
                        <el-form-item label="危化品名称：">
                            <el-select v-model="formData.Name" filterable @change="schoolCatalogClick"
                                style="width: 400px;">
                                <el-option v-for="item in schoolCatalogList" :key="item.Id" :label="item.Name"
                                    :value="item.Id" />
                            </el-select>
                            <el-button type="success" :icon="Search" @click="whpDialogVisible = true"
                                style="margin-left: 10px;">选择</el-button>
                        </el-form-item>
                        <el-form-item label="领用数量：">
                            <el-input-number v-model="formData.Num" :min="1" />
                            <span style="color: #F56C6C;padding: 0 10px;">({{ formData.UnitName }})</span>
                            <span style="color: #999999;">剩余库存： ({{ stockNum }})</span>
                        </el-form-item>
                        <el-form-item label="规格属性：" prop="SchoolMaterialModelId">
                            <el-select v-model="formData.SchoolMaterialModelId" style="width: 400px;">
                                <el-option v-for="item in modelList" :key="item.SchoolMaterialModelId"
                                    :label="item.Model" :value="item.SchoolMaterialModelId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="品牌：" prop="SchoolMaterialBrandId">
                            <el-select v-model="formData.SchoolMaterialBrandId" style="width: 400px">
                                <el-option v-for="item in brandList" :key="item.SchoolMaterialBrandId"
                                    :label="item.Brand" :value="item.SchoolMaterialBrandId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="用途：">
                            <el-input v-model="formData.Remark" type="textarea" style="width: 400px"></el-input>
                        </el-form-item>
                    </div>
                    <div v-else>
                        <el-form-item label="使用时间：" prop="UseTime">
                            <el-date-picker v-model="formData.UseTime" type="date" format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD" clearable style="width: 240px;">
                            </el-date-picker>
                            <span style="color: #999;padding-left: 5px;"> 是指做实验的时间</span>
                        </el-form-item>
                        <el-form-item label="同领用人：" v-if="bconfigSet == 2 || bconfigSet == 3" prop="WithUserId">
                            <el-select v-model="formData.WithUserId" filterable style="width: 240px">
                                <el-option v-for="item in memberUserList" :key="item.MemberUserId"
                                    :label="item.MemberUserName" :value="item.MemberUserId" />
                            </el-select>
                            <span style="color: #999;padding-left: 5px;"> 是指同去仓库领用危化品的人</span>
                        </el-form-item>
                    </div>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="danger" @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 保存 </el-button>
                </span>
            </template>
        </app-box>
        <app-box v-model="whpDialogVisible" :width="960" :lazy="true" title="请选择危化品">
            <template #content>
                <el-tabs tab-position="left" v-model="activeName" style="height: 480px" class="demo-tabs">
                    <el-tab-pane label="搜索" name="search">
                        <div>
                            <el-input v-model.trim="firstName" placeholder="危化品名称" style="width: 280px"> </el-input>
                            <el-button type="primary" :icon="Search" @click="HandleSearch1">搜索</el-button>
                        </div>
                        <div>
                            <div v-if="isSearch && firstName && dangerchemicalsList.length == 0"
                                style="padding: 10px;color: #E6A23C;">危化品不存在，请重新搜索！</div>
                            <ul class="SearchUi" v-else>
                                <li v-for="item in dangerchemicalsList" :key="item.Id"
                                    @click="dangerchemicalsClick(item)">
                                    {{ item.FirstName }} > {{ item.SecondName }} > {{ item.Name }}</li>
                            </ul>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="危化品" name="content">
                        <ul class="whpUi">
                            <li v-for="item in bogetList[0].children" :key="item.Id">
                                <div class="content_left">
                                    {{ item.Name }}
                                </div>
                                <div class="content_right">
                                    <div v-for="item1 in item.children" :key="item1.Id" style="padding: 2px 0;">
                                        <el-divider direction="vertical" />
                                        <span class="text" style="padding: 2px;" @click="dangerchemicalsClick(item1)">
                                            {{ item1.Name }}</span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                </el-tabs>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.whpUi {
    font-size: 12px;
    background-color: #f9f9f9;
    padding: 5px;
    padding-inline-start: 0px;
    margin: 0;

    li {
        list-style-type: none;
        font-size: 12px;
        display: flex;
        padding: 10px 5px;
        border: 1px solid #f9f9f9;
        border-bottom: 1px dotted #d1cfd0;

        .content_left {
            width: 140px;
            flex-shrink: 0;
            color: #723535;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .content_right {
            display: flex;
            flex-wrap: wrap;
            color: #3c3c3c;

            .text:hover {
                cursor: pointer;
                color: #ff9933 !important;
            }
        }
    }

    li:hover {
        background: #fff;
        border: 1px solid #f93 !important;
    }
}

.SearchUi {
    font-size: 14px;
    // background-color: #f9f9f9;
    padding: 5px;
    height: 500px;
    margin: 0;
    overflow-y: auto;

    li {
        list-style-type: none;
        padding: 5px;
        border-bottom: 1px dotted #d1cfd0;
    }

    li:hover {
        background: #fff;
        color: #ff9933 !important;
        border: 1px solid #f93 !important;
        cursor: pointer;
    }
}
</style>