<template>
    <el-dialog v-model="dialogVisible" title="重置密钥" width="500px" ::close-on-click-modal="false"
        :close-on-press-escape="false">
        <template v-if="!newSecret">
            <el-alert type="warning" :closable="false" show-icon>
                <p>注意：重置密钥后，之前的密钥将立即失效。</p>
                <p>确认获取新密钥后请及时保存！</p>
            </el-alert>
            <div style="margin-top: 20px; text-align: right;">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleConfirm" :loading="loading">
                    确认重置
                </el-button>
            </div>
        </template>

        <template v-else>
            <div class="secret-container">
                <p class="secret-title">新的密钥已生成：</p>
                <div class="secret-content">
                    <el-input v-model="newSecret" readonly type="text" size="large">
                        <template #append>
                            <el-button @click="handleCopy">
                                <el-icon>
                                    <Document />
                                </el-icon>
                                复制
                            </el-button>
                        </template>
                    </el-input>
                </div>
                <p class="secret-warning">请立即保存此密钥，关闭弹窗后将无法再次查看！</p>
            </div>
            <div style="margin-top: 20px; text-align: right;">
                <el-button type="primary" @click="handleClose">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { resetClientSecret } from '@/api/application'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    clientId: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const loading = ref(false)
const newSecret = ref('')

// 监听外部传入的显示状态
watch(() => props.modelValue, (val) => {
    dialogVisible.value = val
})

// 监听内部对话框状态变化
watch(() => dialogVisible.value, (val) => {
    if (!val) {
        emit('update:modelValue', false)
        // 重置状态
        newSecret.value = ''
    }
})

// 确认重置
const handleConfirm = async () => {
    loading.value = true
    try {
        const res = await resetClientSecret(props.clientId)
        newSecret.value = res.data.data.rows

    } catch (error) {
        ElMessage.error('重置密钥失败')
    } finally {
        loading.value = false
    }
}

// 复制密钥
const handleCopy = async () => {
    try {
        await navigator.clipboard.writeText(newSecret.value)
        ElMessage.success('复制成功')
    } catch (error) {
        ElMessage.error('复制失败')
    }
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
}
</script>

<style scoped>
.secret-container {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.secret-title {
    margin: 0 0 15px 0;
    font-weight: bold;
}

.secret-content {
    margin-bottom: 15px;
}

.secret-warning {
    margin: 15px 0 0 0;
    color: #e6a23c;
    font-size: 14px;
}
</style>