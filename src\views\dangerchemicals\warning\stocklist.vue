<script setup>
defineOptions({
    name: 'dangerchemicalswarningstocklist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy
} from '@element-plus/icons-vue'
import {
    DccatalogGetClassTwo, DcScrapListFind, DcWarningStockListFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const BackDatele = ref()
const filters = ref({
    pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Sort", SortType: "asc" }, { SortCode: "SchoolId", SortType: "asc" }, { SortCode: "Code", SortType: "ASC" }]
})
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', }
])
const tableEditDialogVisible = ref(false)
const tableEditDialogData = ref([])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcWarningStockListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.TwoCatalogId = undefined
    filters.value.BackDatege = undefined
    filters.value.BackDatele = undefined
    BackDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 查看
const HandleDetail = (row) => {
    let data = {
        SchoolCatalogId: row.SchoolCatalogId,
        SchoolId: row.SchoolId,
        SchoolMaterialModelId: row.SchoolMaterialModelId,
        pageIndex: 1,
        pageSize: 1000000,
        sortModel: [{ SortCode: "Id", SortType: "ASC" }]
    }
    DcScrapListFind(data).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableEditDialogData.value = rows.data
            tableEditDialogVisible.value = true
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="SchoolName" label="学校名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="StockNum" label="存量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Limited" label="预警值" min-width="110" align="right"></el-table-column>
            <el-table-column fixed="right" label="危化品明细" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="tableEditDialogVisible" :width="980" :lazy="true" title="危化品明细">
            <template #content>
                <el-table :data="tableEditDialogData" border stripe max-height="360px"
                    header-cell-class-name=" headerClassName">
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                        <template #default="{ row }">
                            {{ row.Brand || '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="StockNum" label="存量" min-width="110" align="right">
                        <template #default="{ row }">
                            <span :style="{ fontWeight: row.Id ? 'normal' : 'bold' }">
                                {{ row.StockNum }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
                    <el-table-column prop="RegDate" label="入库时间" min-width="180" align="center">
                        <template #default="{ row }">
                            {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="CompanyName" label="供应商" min-width="160"
                        show-overflow-tooltip></el-table-column>

                </el-table>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>