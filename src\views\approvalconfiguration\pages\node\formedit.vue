<script setup>
defineOptions({
  name: 'nodeedit'
});
import { onMounted, onActivated, nextTick, ref, getCurrentInstance } from 'vue'
import {
  QuestionFilled, Select, Right, UploadFilled, Delete, FolderChecked
} from '@element-plus/icons-vue'
import {
  GetFillingInfo, SaveFillingInfo, SubmitFillingInfo, GetLinkAgeDataSource, GetControlDetailDataSource, GetProjectDeclarationCodeName, GetProjectCode
} from '@/api/workflow.js'
import {
  AttachmentUpload
} from '@/api/user.js'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import AppWangEditor from '@/components/Editor/index.vue';
import {
  pageQuery, urlQuery, tagsListStore, fileDownload, popperOptions, formatNumberWithCommas, formatNumber
} from "@/utils/index.js";
import { rules, rulesLimit, rulesIntegerLimit, rulesAmountLimit } from "@/utils/rules.js";
const { proxy } = getCurrentInstance()
import router from '@/router'
// 初始化
const userStore = useUserStore()
const route = useRoute()
const size = ref('default')//表单元素尺寸
const routerObject = ref({})//成页面携带的参数对象 
const routerUrl = ref('')//生成页面携带的参数
const refForm = ref()
const formFields = ref({})//表单数据
const projectDeclarationId = ref()//填报Id
const totalAmount = ref()//项目清单总金额
const submitButtonName = ref('转交下一步')//提交按钮名称
const stagingButton = ref('暂存')//暂存按钮名称
const nextTipMsg = ref('')//下一步提示信息
const projectCodeValue = ref('')//南通平台定制组件:事项编号
const OneClassIdValue = ref(0)//南通平台定制组件:一级分类
const twoClassIdValue = ref(0)//南通平台定制组件:二级分类
const hideFormDataList = ref([])
//加载数据
onMounted(() => {
  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象 
  projectDeclarationId.value = route.query.projectDeclarationId
  if (route.query.isTagRouter) {
    GetFillingInfoUser()
  }
})
onActivated(() => {

  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
  projectCodeValue.value = ''
  // console.log('routerObject', routerObject.value)
  // console.log('route.query', route.query)// 获取路由参数
  routerUrl.value = urlQuery(route.path); // 获取生成页面携带的参数
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  projectDeclarationId.value = route.query.projectDeclarationId
  nextTick(() => {
    if (!route.query.isTagRouter) {
      // projectDeclarationId.value = undefined
      GetFillingInfoUser()
    }
  })
})
const radio = ref()
const multipleTable = ref(null)
// 获取填报页面信息
const GetFillingInfoUser = () => {
  // nextTick(() => {
  formFields.value = {}
  refForm.value.resetFields()
  // })
  let formparams = {
    ModuleId: routerObject.value.moduleId,
    ProcessId: routerObject.value.processId,
    ProcessNodeId: routerObject.value.processNode,
    ProjectDeclarationId: projectDeclarationId.value,
  }
  GetFillingInfo(formparams).then((res) => {
    if (res.data.flag == 1) {
      const { rows, headers, other, other1, other2, other3, footer } = res.data.data
      // console.log('获取填报页面信息res.data.data', res.data.data) 
      let data = JSON.parse(rows).filter(item => !item.isDetail)
      console.log("data", data)
      // 填报不显示的字段#############
      let dataList = JSON.parse(rows).filter(item => item.isDetail)
      dataList.forEach(item => {
        hideFormDataList.value.push({
          FieldId: item.field,
          FieldCode: item.code,
          InfoId: item.code + '_@#$',
          InfoText: item.code + '_@#$'
        })
      })
      // 将other内的数据源替换到data中
      let otherSelect = other?.listSelect.filter(t => !t.desp) || []
      let otherProject = other?.listSelect.filter(t => t.desp == 'projectnumber') || []
      // let other3Project = other3 || []
      console.log("otherProject", otherProject)
      data.forEach((aItem, index) => {
        // 找到 other中 label 与 data中 field 匹配的项
        const bItem = otherSelect.find(b => b.label === aItem.field);
        if (bItem) {
          // 替换 data中的 data 为other中的 value
          aItem.data = JSON.parse(bItem.value);
          aItem.IsLinkAge = bItem.IsLinkAge;
          aItem.FieldCode = bItem.FieldCode;
        }
        if (aItem.type == "statisticstable") {
          // 资金来源数据
          aItem.tableData = other1 || []
        }
        const cItem = otherProject.find(c => c.label === aItem.code);
        if (cItem) {
          // 替换 data中的 data 为other中的 value
          aItem.DefaultValue = cItem.value;
          aItem.DefaultName = cItem.text;
        }
        // const dItem = other3Project.find(d => d.label === aItem.field);
        // console.log('dItem', dItem)
        // if (dItem) {
        //   // 替换 data中的 data 为other中的 value
        //   aItem.DefaultValue = dItem.value;
        //   aItem.DefaultName = dItem.value;
        // }
      });
      let headerData = []
      // 表单填报的数据
      if (headers) {
        headerData = JSON.parse(headers)
        console.log('headerData', headerData)
        // 创建一个映射表，用于快速查找headerData中的InfoId
        const infoIdMap = headerData.reduce((map, item) => {
          map[item.FieldId] = item.InfoId;
          return map;
        }, {});
        // 创建一个映射表，用于快速查找headerData中的InfoText
        const infoTextMap = headerData.reduce((map, item) => {
          map[item.FieldId] = item.InfoText;
          return map;
        }, {});
        // 创建一个映射表，用于快速查找headerData中的isControlShow
        const isControlShowMap = headerData.reduce((map, item) => {
          map[item.FieldId] = item.isControlShow;
          return map;
        }, {});
        // 遍历data，并替换field对应的InfoId、InfoText、isControlShow到DefaultValue、DefaultName、isControlShow
        data.forEach(item => {
          item.DefaultValue = infoIdMap[item.field];
          item.DefaultName = infoTextMap[item.field];
          item.isControlShow = isControlShowMap[item.field];
        });
      }
      data.forEach(item => {
        if (item.UploadFileType && item.UploadFileType.length > 0) {
          item.UploadFileTypeAccept = item.UploadFileType.join('').split('.').join(',.')
        }
        if (item.code == 'ProjectAllAmount') {
          item.DefaultValue = footer
          item.DefaultName = footer
        }
      });
      console.log('获取填报页面信息data', data)
      formFields.value.formItems = data
      const item3 = formFields.value.formItems.find(item => item.type == "TwoClassId");
      if (item3) item3.data = [];
      totalAmount.value = footer
      // 资金来源数据默认选中
      if (other1?.length > 0) {
        selectRows.value = other1.filter(item => item.CurrentAmount > 0)
        nextTick(() => {
          // 多选框
          selectRows.value.forEach(row => {
            multipleTable.value[0].toggleRowSelection(row, true)
          })
          // 单选框
          if (selectRows.value.length > 0) {
            radio.value = selectRows.value[0].Id
          }
        })
        // console.log("selectRows.value", selectRows.value)
      }
      submitButtonName.value = other2.SubmitButtonName || '转交下一步'
      stagingButton.value = other2.StagingButton || '暂存'
      nextTipMsg.value = other2.NextTipMsg
      console.log("formFields.value.formItems", formFields.value.formItems)
      // 南通平台定制开发，修改数据及数据源 #########################
      const item2 = formFields.value.formItems.find(t => t.type == "creationnameselect");
      const item4 = formFields.value.formItems.find(t => t.type == "ProjectCode");
      const item5 = formFields.value.formItems.find(t => t.type == "OneClassId");
      const item6 = formFields.value.formItems.find(t => t.type == "TwoClassId");
      const item7 = formFields.value.formItems.find(t => t.type == "isnewcreation");
      if (item5 && item5.DefaultValue) {
        GetLinkAgeDataSource({ code: item5.FieldCode + item5.DefaultValue }).then(res => {
          if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (rows && rows.length > 0) {
              item6.data = JSON.parse(rows[0].value)
            }
          } else {
            ElMessage.error(res.data.msg)
          }
        })
      }
      if (item5 && item5.DefaultValue && item6 && item6.DefaultValue) {
        GetProjectDeclarationCodeName({ oneClassId: item5.DefaultValue, twoClassId: item6.DefaultValue }).then(res => {
          if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (item2) {
              item2.data = rows || [];
            }
          } else {
            ElMessage.error(res.data.msg)
          }
        })
      }
      if (item7 && item4 && item4.DefaultValue) {
        projectCodeValue.value = item4.DefaultValue
      }
      // #############################################################
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

// 表单校验
const ruleForm = (item) => {
  if (['text', 'projectname', 'projectamount', 'fundallocation'].includes(item.type)) {
    if (!item.rules) return rules['required'](item.name)
    // 当是小数类型时，小数位数为0则为整数
    if (item.rules == 'decimals' && (!item.arg || item.arg == '0')) {
      item.rules = 'integer'
    }
    // 当长度为区间范围时
    if (item.rules == 'rangeLength') {
      let arg = [1, 100]
      if (item.arg.indexOf(',')) {
        arg = item.arg.split(',')
      } else if (item.arg.indexOf('，')) {
        arg = item.arg.split('，')
      }
      return rules[item.rules](item.name, arg[0], arg[1], item.required)
    }
    // 根据校验方法名和参数调用校验方法
    return rules[item.rules](item.name, item.arg, item.required)
  }
  if (item.type == 'textarea') {
    return [{ required: true, message: '请输入' + item.name, trigger: 'blur' }]
  } else {
    return [{ required: true, message: '请选择' + item.name, trigger: 'change' }]
  }
}
//输入框限制：输入小数/整数
const rulesInput = (val, item, name) => {
  // console.log('输入框限制：输入小数/整数', val, item, name)
  if (item.rules == 'integer') {
    item[name] = rulesIntegerLimit(val);
  } else if (item.rules == 'decimals') {
    if (!item.arg || item.arg == '0') {
      item[name] = rulesIntegerLimit(val);
    } else {
      item[name] = rulesLimit(val, item.arg);
    }
  } else if (item.rules == 'amount') {
    item[name] = rulesAmountLimit(val, item.arg);
  } else { }

  if (item.type === 'fundallocation') {
    // 资金分配求和
    if (item.code.indexOf('FundAllocation_') == 0) {
      if (item.code.startsWith('FundAllocation_0_')) {
        let allocation = formFields.value.formItems.filter(t => t.code.startsWith('FundAllocation_0_'))
        // console.log('项目金额allocation', allocation)
        let numbers = allocation.map(t => Number(t.DefaultValue || 0));
        let sum = numbers.reduce((a, b) => a + b, 0);
        // console.log(sum);
        formFields.value.formItems.forEach(t => {
          if (t.code == 'ProjectAmount') {
            t.DefaultValue = sum
            t.DefaultName = sum
          }
        });
      } else if (!item.code.endsWith('_Total')) {
        let codePrefix = 'FundAllocation_' + item.code.split('_')[1] + '_';
        let allocation = formFields.value.formItems.filter(t => t.code.startsWith(codePrefix) && !t.code.endsWith('_Total'));
        let sum = allocation.reduce((total, current) => total + Number(current.DefaultValue || 0), 0);
        formFields.value.formItems.forEach(t => {
          if (t.code == 'FundAllocation_' + item.code.split('_')[1] + '_Total') {
            t.DefaultValue = sum
            t.DefaultName = sum
          }
        });
      }
    }
  }


  // ####################################################################
  // 数据计算:判断是否需要数据计算
  if (item.isCalculation && item.calculatedCode) {
    let calculatedCodeList = item.calculatedCode.split(',')
    for (let j = 0; j < calculatedCodeList.length; j++) {
      // console.log('数据计算:判断是否需要数据计算', val)
      // 获取计算总值项
      let find1 = formFields.value.formItems.find(t => t.code == calculatedCodeList[j])
      // console.log('find1', find1)
      // 处理将输入框的值替换到计算规则对应位置
      let rulesList = find1.calculatedRules.split(',')
      console.log('rulesList', rulesList)
      let itemMap = formFields.value.formItems.reduce((map, obj) => {
        map[obj.code] = obj.DefaultValue;
        return map;
      }, {});
      let replacedData = rulesList.map(t =>
        itemMap[t] !== undefined ? itemMap[t] : t
      );
      console.log('replacedData', replacedData);
      let find2 = replacedData.filter(r => { return r === '' })
      // 不存在空值时触发计算
      if (find2.length == 0) {
        // 检查是否有除数为零的情况  '/'后的值不可为0 
        const isZero1 = replacedData.some(
          (el, i, arr) => el === '/' && (arr[i + 1] === 0 || arr[i + 1] === '0')
        );
        let isZero2 = false
        // 增强的除零检查（支持括号）
        if (hasDivisionByZero(replacedData)) {
          isZero2 = true
        }
        // console.log('isZero', isZero1,isZero2)
        if (isZero1 || isZero2) {
          find1.DefaultValue = ''
          find1.DefaultName = ''
        } else {
          // console.log('calculateExpression(replacedData)', calculateExpression(replacedData))
          find1.DefaultValue = calculateExpression(replacedData)
          find1.DefaultName = calculateExpression(replacedData)
        }
      } else {
        find1.DefaultValue = ''
        find1.DefaultName = ''
      }
    }
  }
  // ####################################################################
}
// #####################################################################
//  数据计算:方法 
const infixToPostfix = (infixArray) => {
  const output = [];
  const operatorStack = [];
  const precedence = { '*': 3, '/': 3, '+': 2, '-': 2, '(': 1 };

  for (const token of infixArray) {
    if (!isNaN(token)) {
      // 如果是数字，直接加入输出
      output.push(parseFloat(token));
    } else if (token === '(') {
      // 左括号入栈
      operatorStack.push(token);
    } else if (token === ')') {
      // 右括号：弹出直到遇到左括号
      let topToken = operatorStack.pop();
      while (topToken !== '(') {
        output.push(topToken);
        topToken = operatorStack.pop();
      }
    } else if (token in precedence) {
      // 如果是运算符
      while (
        operatorStack.length > 0 &&
        precedence[operatorStack[operatorStack.length - 1]] >= precedence[token]
      ) {
        output.push(operatorStack.pop());
      }
      operatorStack.push(token);
    }
  }

  // 将剩余运算符弹出
  while (operatorStack.length > 0) {
    output.push(operatorStack.pop());
  }

  return output;
};
const evaluatePostfix = (postfixArray) => {
  const stack = [];

  for (const token of postfixArray) {
    if (typeof token === 'number') {
      stack.push(token);
    } else {
      const b = stack.pop();
      const a = stack.pop();
      switch (token) {
        case '+': stack.push(a + b); break;
        case '-': stack.push(a - b); break;
        case '*': stack.push(a * b); break;
        case '/': stack.push(a / b); break;
      }
    }
  }

  return stack[0];
};

const calculateExpression = (arr) => {
  const postfixArray = infixToPostfix(arr);
  return evaluatePostfix(postfixArray);
};




// 增强的除零检查（支持括号表达式）
const hasDivisionByZero = (infixArray) => {
  for (let i = 0; i < infixArray.length; i++) {
    const token = infixArray[i];

    if (token === '/' && i + 1 < infixArray.length) {
      const nextToken = infixArray[i + 1];

      if (nextToken === '(') {
        // 处理 /( 的情况：计算括号内的表达式并检查是否为0
        const subExpression = extractSubExpression(infixArray, i + 1);
        const subResult = evaluateSubExpression(subExpression);

        if (subResult === 0) {
          return true;
        }

        // 跳过已处理的括号表达式
        i += subExpression.length;
      } else if (parseFloat(nextToken) === 0) {
        // 处理 /0 的情况
        return true;
      }
    }
  }
  return false;
};

// 提取括号内的子表达式
const extractSubExpression = (infixArray, startIndex) => {
  const subExpression = [];
  let parenthesesCount = 0;

  for (let i = startIndex; i < infixArray.length; i++) {
    const token = infixArray[i];

    if (token === '(') {
      parenthesesCount++;
    } else if (token === ')') {
      parenthesesCount--;
    }

    subExpression.push(token);

    if (parenthesesCount === 0) {
      break;
    }
  }

  return subExpression;
};

// 计算子表达式（用于除零检查）
const evaluateSubExpression = (subExpression) => {
  try {
    // 去掉最外层的括号
    const innerExpression = subExpression.slice(1, -1);
    const postfix = infixToPostfix(innerExpression);
    return evaluatePostfix(postfix);
  } catch (error) {
    // 如果子表达式计算失败，保守起见认为可能除零
    return 0;
  }
};
// #####################################################################
//金额输入限制：最多4位小数
const amountInput = (val, row, name) => {
  row[name] = rulesLimit(val, 4);
}
// 模板下载事件
const downloadClick = (item) => { }
// 按钮事件
const buttonClick = (item) => {
  // 通过 item.click 绑定事件名称进行处理
  // refForm.value.resetFields()
}
// 输入框/文本域：DefaultName赋值
const textChange = (e, item, index) => {
  // console.log('textChange', e, item, index)
  item.DefaultName = e

  // 条件控制为主控时
  if (item.MasterControl > 0) {
    // 同组的主控元素
    let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === item.MasterControl)
    console.log('同组的主控元素', masterControlList)
    GetControlDetailDataSourceUser(item.MasterControl, masterControlList)
  }
  if (item.type === 'fundallocation') {
    // 资金分配求和影响主控事件
    if (item.code.startsWith('FundAllocation_')) {
      if (item.code.startsWith('FundAllocation_0_')) {
        let isAmount = formFields.value.formItems.filter(t => t.code == 'ProjectAmount')
        console.log('项目金额isAmount', isAmount)
        if (isAmount.length > 0 && isAmount[0].IsAmountControl > 0) {
          let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === isAmount[0].MasterControl)
          nextTick(() => {
            GetControlDetailDataSourceUser(isAmount[0].MasterControl, masterControlList)
          })
        }
      } else if (!item.code.endsWith('_Total')) {
        let isAmount = formFields.value.formItems.filter(t => t.code == 'FundAllocation_' + item.code.split('_')[1] + '_Total')
        console.log('项目金额isAmount', isAmount)
        if (isAmount.length > 0 && isAmount[0].IsAmountControl > 0) {
          let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === isAmount[0].MasterControl)
          nextTick(() => {
            GetControlDetailDataSourceUser(isAmount[0].MasterControl, masterControlList)
          })
        }
      }
    }
  }
}
// 下拉单选：DefaultName赋值
const selectChange = (e, item, index) => {
  // console.log('selectChange', e, item, index)
  item.data.filter((t) => { if (t.value == e) { item.DefaultName = t.label } })
  // 当为父级下拉框时，执行的操作
  if (item.IsLinkAge === 1) {
    GetLinkAgeDataSource({ code: item.FieldCode + e }).then(res => {
      if (res.data.flag == 1) {
        const { rows } = res.data.data
        formFields.value.formItems.forEach((aItem, index) => {
          // 找到 other中 label 与 data中 field 匹配的项
          const bItem = rows.find(bItem => bItem.label === aItem.field);
          if (bItem) {
            // 替换 data中的 data 为other中的 value
            aItem.data = JSON.parse(bItem.value);
            if (!aItem.data.find(t => t.value === aItem.DefaultValue)) {
              aItem.DefaultValue = '';
              aItem.DefaultName = '';
            }
            // console.log('aItem', aItem)
            //  // 子元素为主控时
            if (aItem.MasterControl > 0) {
              // 同组的主控元素
              let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === aItem.MasterControl)
              console.log('子元素同组的主控元素', aItem.MasterControl, masterControlList)
              GetControlDetailDataSourceUser(aItem.MasterControl, masterControlList)
            }
            removeChildrenValue(item.code, aItem)
          }
        });
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  }
  // 条件控制为主控时
  if (item.MasterControl > 0) {
    // 判断是否为南通平台定制的'是否新建事项',处理被控显隐
    if (item.type == "isnewcreation") {
      console.log('是否新建事项', item)
      const item1 = formFields.value.formItems.find(t => t.type == "creationnameinput");
      const item2 = formFields.value.formItems.find(t => t.type == "creationnameselect");

      if (item.DefaultValue == '1') {
        console.log('formFields.value.formItems', formFields.value.formItems)
        if (item1) item1.isControlShow = true;
        if (item2) item2.isControlShow = false;
      } else if (item.DefaultValue == '2') {
        if (item1) item1.isControlShow = false;
        if (item2) item2.isControlShow = true;
      }
    } else {
      // 同组的主控元素
      let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === item.MasterControl)
      console.log('同组的主控元素', masterControlList)
      GetControlDetailDataSourceUser(item.MasterControl, masterControlList)
    }

  }
  // 判断是否为南通平台定制的'一级分类'，处理选择事件
  if (item.type == "OneClassId") {
    //  清除二级分类数据、事项名称数据源、事项名称数据
    OneClassIdValue.value = e
    const item2 = formFields.value.formItems.find(t => t.type == "creationnameselect");
    const item4 = formFields.value.formItems.find(t => t.type == "ProjectCode");
    if (item2) {
      item2.data = [];
      item2.DefaultValue = '';
      item2.DefaultName = '';
    }
    if (item4) {
      item4.DefaultValue = '';
      item4.DefaultName = '';
    }
  }
  // 判断是否为南通平台定制的'一级分类'，处理选择事件
  if (item.type == "TwoClassId") {
    console.log('二级分类', e, item)
    //  重新获取事项名称数据源、事项名称数据
    twoClassIdValue.value = e

    const item2 = formFields.value.formItems.find(t => t.type == "creationnameselect");
    const item4 = formFields.value.formItems.find(t => t.type == "ProjectCode");
    GetProjectDeclarationCodeName({ oneClassId: OneClassIdValue.value, twoClassId: e }).then(res => {
      if (res.data.flag == 1) {
        const { rows } = res.data.data
        if (item2) {
          item2.data = rows || [];
          item2.DefaultValue = '';
          item2.DefaultName = '';
        }
        if (item4) {
          item4.DefaultValue = '';
          item4.DefaultName = '';
        }
      } else {
        ElMessage.error(res.data.msg)
      }
    })

  }
  // 判断是否为南通平台定制的'是否新建事项'，处理选择  是、否 的事件
  if (item.type == "isnewcreation") {
    console.log('是否新建事项', item)
    const item4 = formFields.value.formItems.find(t => t.type == "ProjectCode");
    if (item.DefaultValue == '1') {
      if (projectCodeValue.value) {
        item4.DefaultValue = projectCodeValue.value;
        item4.DefaultName = projectCodeValue.value;
      } else {
        GetProjectCode({ projectNodeId: routerObject.value.processNode }).then(res => {
          if (res.data.flag == 1) {
            const { rows } = res.data.data
            projectCodeValue.value = rows[0].value
            if (item4) {
              item4.DefaultValue = projectCodeValue.value;
              item4.DefaultName = projectCodeValue.value;
            }
          } else {
            ElMessage.error(res.data.msg)
          }
        })
      }

      // console.log('formFields.value.formItems', formFields.value.formItems)
    } else if (item.DefaultValue == '2') {
      if (item4) {
        item4.DefaultValue = '';
        item4.DefaultName = '';
      }

      const item2 = formFields.value.formItems.find(t => t.type == "creationnameselect");
      if (OneClassIdValue.value && twoClassIdValue.value && item2 && item2.data.length == 0) {
        GetProjectDeclarationCodeName({ oneClassId: OneClassIdValue.value, twoClassId: twoClassIdValue.value }).then(res => {
          if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (item2) {
              item2.data = rows || [];
            }
          } else {
            ElMessage.error(res.data.msg)
          }
        })
      }
      if (item2) {
        item2.DefaultValue = '';
        item2.DefaultName = '';
      }
    }
  }
  // 判断是否为南通平台定制的'事项名称（下拉）'，处理选择事件
  if (item.type == "creationnameselect") {
    console.log('事项名称', e, item)
    const item4 = formFields.value.formItems.find(t => t.type == "ProjectCode");
    let pname = item.data.filter(t => t.value == e)[0].pname
    if (item4) {
      item4.DefaultValue = pname;
      item4.DefaultName = pname;
    }
  }
}
// 清除子级及其子级的下拉框数据
const removeChildrenValue = (code, aItem) => {
  if (code === aItem.ParentCode && aItem.children) {
    formFields.value.formItems.forEach((d) => {
      const childrenItem = aItem.children.some(t => t == d.code)
      if (childrenItem) {
        d.DefaultValue = '';
        d.DefaultName = '';
        // console.log('d', d)
        //  // 子元素为主控时
        if (d.MasterControl > 0) {
          // 同组的主控元素
          let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === d.MasterControl)
          console.log('同组的主控元素', masterControlList)
          GetControlDetailDataSourceUser(d.MasterControl, masterControlList)
        }
      }
      if (aItem.code === d.ParentCode && d.children) {
        removeChildrenValue(aItem.code, d)
      }
    })
  }
}
// 根据主控字段编码获取被控制字段显示信息
const GetControlDetailDataSourceUser = (groupName, masterControlList) => {
  let listCodeValue = masterControlList.map(({ code, DefaultValue }) => ({
    FieldCode: code,
    FieldValue: DefaultValue
  }))
  let formparams = {
    GroupName: groupName,//组名
    ProcessNodeId: routerObject.value.processNode,//节点id
    ListCodeValue: listCodeValue.filter(t => t.FieldValue),//主控字段集合
  }
  console.log('formparams', formparams)
  GetControlDetailDataSource(formparams).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      console.log('formFields.value.formItems', formFields.value.formItems)
      formFields.value.formItems.forEach(item => {
        console.log('item.Controlled', item.Controlled, 'groupName', groupName)
        // 与主控同组的所有被控元素
        if (item.Controlled == groupName && !rows.includes(item.code)) {

          item.isControlShow = undefined;//隐藏被控元素
          console.log('隐藏被控元素', item)
          // 当被控组件为其它组的主控时，且主控被控值不同时，继续执行事件
          if (item.MasterControl > 0 && item.MasterControl != groupName) {
            // 清除隐藏元素值，为了再次回调接口时去掉入参
            item.DefaultValue = '';
            item.DefaultName = '';
            // 同组的主控元素
            let controlledMasterControlList = formFields.value.formItems.filter(t => t.MasterControl === item.MasterControl)
            // console.log('同组的主控元素', controlledMasterControlList)
            GetControlDetailDataSourceUser(item.MasterControl, controlledMasterControlList)
          }
        }
        // 显示对应的被控元素
        if (rows.includes(item.code)) {
          console.log('显示对应的被控元素item', item)
          item.isControlShow = true;
        }
      });
    }
  })
  console.log('显隐更新formFields.value.formItems', formFields.value.formItems)
}

// 多选下拉/复选框：DefaultName赋值
const multipleSelectChange = (e, item, index) => {
  // console.log('multipleSelectChange', e, item, index)
  const labels = e.map(id => String(id)).map(id => {
    const items = item.data.find(item => item.value === id);
    return items ? items.label : '';
  });
  item.DefaultName = labels.join(',');
}
// 级联：DefaultName赋值
const cascaderChange = (e, item, index) => {
  // console.log('cascaderChange', e, item, index)
  let selectedLabel = getLabelPath(e, item.data);
  item.DefaultName = selectedLabel.join('/')
}

//级联使用： 递归函数，用于根据值数组找到对应的 label 路径
const getLabelPath = (e, data) => {
  let labels = [];
  let currentOptions = data;
  for (let i = 0; i < e.length; i++) {
    const value = e[i];
    const foundOption = currentOptions.find(option => option.value === value);
    if (foundOption) {
      labels.push(foundOption.label);
      currentOptions = foundOption.children || [];
    } else {
      // 如果没有找到对应的选项，可能是数据不一致，返回空数组
      return [];
    }
  }
  return labels;
};

// 项目清单添加/修改
const projectEdit = (item) => {
  // 判断是否已存在填报Id
  // if (projectDeclarationId.value) {
  //   router.push({
  //     path: "./projectlist@moduleId=" + routerObject.value.moduleId, query: {
  //       ProcessId: routerObject.value.processId,
  //       ProjectDeclarationId: projectDeclarationId.value,
  //       FieldCode: item.code,
  //       routerUrl: routerUrl.value,
  //       page: 'edit',//前往项目清单的页面
  //       isEdit: true
  //     }
  //   })
  // } else {
  save(1, true, item.code)
  // }
}
// 保存/转交下一步
const HandleSubmit = (e) => {
  if (e == 1) {
    save(e)
  } else {
    refForm.value.validate((valid, fields) => {
      // console.log('Object.keys(formRef.value.fields)', Object.keys(fields)[0], 'fields', fields)
      if (!valid && fields) {
        const firstErrorProp = Object.keys(fields)[0];
        if (firstErrorProp) {
          refForm.value.scrollToField(firstErrorProp, {
            behavior: 'smooth',
            block: 'center'
          });
        }
        return;
      }
      save(e)
    })
  }

}

// 提交数据
const save = (e, isPath, fieldCode) => {
  // 资金分配校验
  let projectAmount = formFields.value.formItems.filter(t => t.code == 'ProjectAmount')[0]
  let amountation = formFields.value.formItems.find(t => t.code.startsWith('FundAllocation_0_'))
  if (amountation && projectAmount) {
    let allocation = formFields.value.formItems.filter(t => t.code.startsWith('FundAllocation_0_'))
    let allocationName = allocation.map(t => t.name).join('、')
    let allocationTotalName = projectAmount.name
    let sum = allocation.reduce((total, current) => total + Number(current.DefaultValue || 0), 0);
    if (projectAmount.DefaultValue != sum) {
      ElMessage.error(`[${allocationName}]总计与${allocationTotalName}不一致`)
      return
    }
  }
  let allocationTotal = formFields.value.formItems.filter(t => t.code.endsWith('_Total'));
  for (let item of allocationTotal) {
    let codePrefix = 'FundAllocation_' + item.code.split('_')[1] + '_';
    let allocation = formFields.value.formItems.filter(t => t.code.startsWith(codePrefix) && !t.code.endsWith('_Total'));
    let allocationAllTotal = formFields.value.formItems.filter(t => t.code.startsWith(codePrefix) && t.code.endsWith('_Total'));
    let allocationName = allocation.map(t => t.name).join('、')
    let allocationTotalName = allocationAllTotal[0].name
    let sum = allocation.reduce((total, current) => total + Number(current.DefaultValue || 0), 0);
    if (item.DefaultValue != sum) {
      ElMessage.error(`[${allocationName}]总计与${allocationTotalName}不一致`)
      return;
    }
  }

  let projectAllAmount = formFields.value.formItems.filter(t => t.code == 'ProjectAllAmount')[0]
  if (selectRows.value.length > 0) {
    // projectAmount  项目金额判断   
    // 单选时 判断三种金额是否存在  项目金额 > 项目清单金额 > 可使用金额 赋值到CurrentAmount
    // 多选时校验 资金来源金额总和=项目金额
    //  // 资金来源提交数据(单选)
    let statisticsRadioTableData = []
    if (projectAmount) {
      // 项目金额
      statisticsRadioTableData = selectRows.value.map(item => ({
        SourceFundId: item.Id,
        CurrentAmount: projectAmount.DefaultValue,
        ProjectName: item.ProjectName
      }))
    } else if (projectAllAmount) {
      // 项目清单金额
      statisticsRadioTableData = selectRows.value.map(item => ({
        SourceFundId: item.Id,
        CurrentAmount: projectAllAmount.DefaultValue,
        ProjectName: item.ProjectName
      }))
    } else {
      // 资金来源可使用金额
      statisticsRadioTableData = selectRows.value.map(item => ({
        SourceFundId: item.Id,
        CurrentAmount: item.CanUseAmount,
        ProjectName: item.ProjectName
      }))
    }
    // 资金来源提交数据(多选)
    let statisticsTableData = selectRows.value.map(item => ({
      SourceFundId: item.Id,
      CurrentAmount: item.CurrentAmount,
      ProjectName: item.ProjectName
    }))
    // console.log('formFields.value.formItems', formFields.value.formItems)
    formFields.value.formItems.forEach((item) => {

      if (item.type == 'statisticstable') {
        console.log('item', item)
        if (item.changeType == "radio") {
          item.DefaultValue = statisticsRadioTableData
          item.DefaultName = statisticsRadioTableData
        } else {
          item.DefaultValue = statisticsTableData
          item.DefaultName = statisticsTableData
        }
      }
    })
  }
  // 去除被控且未显示的数据
  let formFieldsData = formFields.value.formItems.filter(t => !(t.Controlled > 0 && !t.isControlShow))
  //根据数据集合提取需要的属性  
  let FieldConfig = formFieldsData.map(({ field, code, DefaultValue, DefaultName, isControlShow }) => ({
    FieldId: field,
    FieldCode: code,
    InfoId: DefaultValue,
    InfoText: DefaultName,
    isControlShow: isControlShow
  }));
  FieldConfig = [...FieldConfig, ...hideFormDataList.value]
  let formData = {
    ProcessId: routerObject.value.processId,
    ProcessNodeId: routerObject.value.processNode,
    ProjectDeclarationId: projectDeclarationId.value,
    FieldConfig: JSON.stringify(FieldConfig)
  };
  console.log('FieldConfig', FieldConfig)
  if (e == 1) {
    SaveFillingInfo(formData).then((res) => {
      if (res.data.flag == 1) {
        const { footer } = res.data.data
        projectDeclarationId.value = footer
        // 判断是否是点击预算清单新增/修改，是则跳转至预算清单列表
        if (isPath) {
          router.push({
            path: "./projectlist@moduleId=" + routerObject.value.moduleId, query: {
              ProcessId: routerObject.value.processId,
              ProjectDeclarationId: projectDeclarationId.value,
              routerUrl: routerUrl.value,
              FieldCode: fieldCode,
              page: 'edit',//前往项目清单的页面
              isEdit: true
            }
          })
          projectCodeValue.value = ''
        } else {
          ElMessage.success(res.data.msg || '暂存成功')
        }
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  } else {
    SubmitFillingInfo(formData).then((res) => {
      if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '提交成功')
        let tagsList = userStore.tagsList
        tagsList = tagsList.filter(t => t.path != route.path)
        userStore.setTagsList(tagsList)
        router.push({ path: "./pendinglist" + routerUrl.value })
        projectCodeValue.value = ''
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  }
}

const selectRows = ref([])
// 表格多选
const HandleSelectChange = (selection) => {
  selectRows.value = selection
}
// 表格单选
const HandleRadioChange = (row, item) => {
  console.log('HandleRadioChange', row, item, item.columns)
  // 资金来源为单选并且配置可赋值的情况
  if (item.changeType == "radio" && item.isAssignment) {
    formFields.value.formItems.forEach((aItem, index) => {
      // 找到 other中 label 与 data中 field 匹配的项
      const bItem = item.columns.find(b => b.FieldCode === aItem.code);
      if (bItem && !aItem.DefaultValue) {
        aItem.DefaultValue = row[bItem.FieldCode];
        aItem.DefaultName = row[bItem.FieldCode];
        refForm.value.validateField(`formItems.${index}.DefaultValue`)// 手动触发校验
      }
    });
  }
  selectRows.value = [row]
}
// ##########################################
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
  console.log('达到最大数量后限制上传', item)
  let length = item.DefaultValue?.length || 0
  console.log("length", length)
  if (item.MaxFileNumber && length >= item.MaxFileNumber) {
    numberDisabled.value = true
    ElMessage.error("上传数量已达到上限,请先删除后再上传")
    return
  } else {
    numberDisabled.value = false
  }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
  // console.log("导入前校验item", item)
  // console.log("导入前校验file", file)
  fileFile.value = file
  let str = file.name.split('.')[1]
  let name = str.toLowerCase()
  let arr = item.UploadFileType.join('').split('.')
  // console.log("arr", arr)
  let ary = arr.filter(t => t != '')
  const extension = ary.includes(name)
  if (!extension) {
    ElMessage({
      message: `上传文件只能是${item.UploadFileType}格式!`,
      type: 'error'
    })
  }
  // // 校验文件大小
  let FileSize = item.FileSize
  if (item.FileSize == 0) {
    FileSize = 10
  }
  const isSize = file.size / 1024 / 1024 < FileSize;
  if (!isSize) {
    ElMessage({
      message: `文件大小不能超出${FileSize}M`,
      type: 'error'
    })
  }
  return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
  // console.log("item", item.code, "index", index)
  AttachmentUpload({ file: fileFile.value, filecategory: Number(item.code) }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '上传成功')
      const { rows } = res.data.data
      // console.log("附件上传rows", rows[0])
      item.fileName = rows[0].Title
      // 添加重置，防止item.DefaultValue值为空的时候报错
      if (!item.DefaultValue) item.DefaultValue = []
      if (!item.DefaultName) item.DefaultName = []

      item.DefaultValue.push({
        Id: rows[0].Id,
        Title: rows[0].Title,
        Path: rows[0].Path,
        Ext: rows[0].Ext,
      })
      item.DefaultName.push({
        Id: rows[0].Id,
        Title: rows[0].Title,
        Path: rows[0].Path,
        Ext: rows[0].Ext,
      })
      // console.log("附件上传DefaultValue", item.DefaultValue)
      refForm.value.validateField(`formItems.${index}.DefaultValue`)// 手动触发校验
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// ##########################################
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
  let path = e.Path;
  viewPhotoList.value = imgList.map(t => t.Path)
  if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
    showViewer.value = true;
    viewPhotoList.value.forEach((item, index) => {
      if (path == item) {
        imgSrcIndex.value = index;
      }
    });
    // //大图预览从点击的那张开始
    let tempImgList = [...viewPhotoList.value];
    let temp = [];
    for (let i = 0; i < imgSrcIndex.value; i++) {
      temp.push(tempImgList.shift());
    }
    viewPhotoList.value = tempImgList.concat(temp);

  } else {
    let title = e.Title + e.Ext
    fileDownload(e.Path, title)
  }
}
// 删除附件
const delFile = (item1, item) => {
  // console.log("删除附件item", item)
  // console.log("删除附件item1", item1)
  item.DefaultValue = item.DefaultValue.filter(t => t.Id != item1.Id)
  item.DefaultName = item.DefaultValue.filter(t => t.Id != item1.Id)
}


</script>
<template>
  <div style="position: relative;width: 90%;">
    <!-- 暂存按钮粘性定位 -->
    <div class="vertical-btn" @click="HandleSubmit(1)">
      <span class="vertical-text">
        <el-icon color="#fff" size="20">
          <FolderChecked />
        </el-icon>
        <span style="padding-top: 10px;font-size: 20px;"> {{ stagingButton }}</span></span>
    </div>
    <el-form style="width: 100%;min-width: 560px;" :inline="true" ref="refForm" @submit.prevent :model="formFields">
      <div class="edit-form-item">
        <template v-for="(item, index) in formFields.formItems" :key="item.code">
          <el-form-item :label-width="item.type == 'line' ? '0px' : item.labelWidth ? item.labelWidth + 'px' : '200px'"
            :prop="'formItems.' + index + '.DefaultValue'" :rules="item.rules || item.required ? ruleForm(item) : []"
            :style="{ width: item.width + '%' }" v-if="!(item.Controlled > 0 && !item.isControlShow)">
            <!-- label -->
            <template #label>
              <el-tooltip v-if="item.isRemark" class="item" effect="dark" :content="item.HelpRemark" placement="top">
                <div>
                  <el-icon color="#E6A23C" class="tipIcon">
                    <QuestionFilled />
                  </el-icon>
                </div>
              </el-tooltip>
              <span> {{ item.name ? item.name + '：' : '' }} </span>
            </template>
            <!-- 输入框 -->
            <el-input
              v-if="['text', 'projectname', 'projectamount', 'fundallocation', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown', 'creationnameinput'].includes(item.type)"
              v-model="item.DefaultValue" :size="size" clearable
              :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name" :disabled="item.readonly"
              @input="rulesInput($event, item, 'DefaultValue')" @change="textChange($event, item, index)"
              style="width: 100%;" />
            <el-input v-else-if="item.type == 'projectnumber' && !item.isDetail" v-model="item.DefaultValue"
              :size="size" disabled />
            <!-- 南通平台定制组件###################### -->
            <!-- 事项编号 -->
            <el-input v-else-if="item.type == 'ProjectCode'" v-model="item.DefaultValue" :size="size" disabled />
            <!-- 描述文本 -->
            <span v-else-if="item.type === 'descriptivetext'"
              :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#999999', 'fontSize': '14px' }">
              {{ item.DefaultValue }}</span>
            <span v-else-if="item.type === 'projectallamount'">
              {{ formatNumberWithCommas(item.DefaultValue) }} </span>
            <!-- 文本域 -->
            <el-input v-else-if="item.type == 'textarea' || item.type == 'auditremark'" v-model="item.DefaultValue"
              type="textarea" :maxlength="item.maxLength ? item.maxLength : 500" show-word-limit :size="size" clearable
              :disabled="item.readonly" :autosize="{ minRows: item.minRows || 2, maxRows: item.maxRows || 10 }"
              :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name"
              @change="textChange($event, item, index)" style="width: 100%;" />
            <!-- 下拉框 -->
            <el-select
              v-else-if="['select', 'subjectnature', 'OneClassId', 'TwoClassId', 'AppointAuditUserSelect'].includes(item.type)"
              v-model="item.DefaultValue" :disabled="item.readonly" :size="size" filterable
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name" clearable
              @change="selectChange($event, item, index)" style="width: 100%;">
              <el-option v-for="item1 in item.data" :key="item1.value" :label="item1.label" :value="item1.value">
              </el-option>
            </el-select>
            <!-- 南通平台定制组件###################### -->
            <!-- 事项名称 -->
            <el-select v-else-if="['creationnameselect'].includes(item.type)" v-model="item.DefaultValue"
              :disabled="item.readonly" :size="size" filterable
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name" clearable
              @change="selectChange($event, item, index)" style="width: 100%;">
              <el-option v-for="item1 in item.data" :key="item1.value" :label="item1.label + '(' + item1.value + ')'"
                :value="item1.value">
              </el-option>
            </el-select>
            <!-- 多选下拉框 -->
            <el-select v-else-if="item.type == 'selectList' || item.type == 'AppointAuditUserSelectList'"
              v-model="item.DefaultValue" :disabled="item.readonly" :size="size" filterable multiple
              :placeholder="item.placeholder ? '请选择' + item.placeholder : item.name" clearable
              @change="multipleSelectChange($event, item, index)" style="width: 100%;">
              <el-option v-for="item1 in item.data" :key="item1.value" :label="item1.label" :value="item1.value">
              </el-option>
            </el-select>
            <!-- 单选框 -->
            <el-radio-group v-else-if="['radio', 'auditstatuz', 'isnewcreation'].includes(item.type)"
              v-model="item.DefaultValue" :disabled="item.readonly" @change="selectChange($event, item, index)"
              style="width: 100%;">
              <el-radio v-for="kv in item.data" :disabled="item.readonly" :key="kv.value" :value="kv.value">
                {{ kv.label }}
              </el-radio>
            </el-radio-group>
            <!-- 复选框 -->
            <el-checkbox-group v-else-if="item.type == 'checkbox'" v-model="item.DefaultValue" :disabled="item.readonly"
              @change="multipleSelectChange($event, item, index)" style="width: 100%;">
              <el-checkbox v-for="kv in item.data" :key="kv.value" :label="kv.label" :value="kv.value"> </el-checkbox>
            </el-checkbox-group>
            <!-- 日期 -->
            <el-date-picker v-else-if="item.type == 'date'" v-model="item.DefaultValue" type="date" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" :size="size" clearable :disabled="item.readonly"
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="textChange($event, item, index)" :popper-options="popperOptions" style="width: 100%;">
            </el-date-picker>
            <!-- 日期时间 -->
            <el-date-picker v-else-if="item.type == 'datetime'" v-model="item.DefaultValue" type="datetime"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :size="size" clearable
              :disabled="item.readonly" :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="textChange($event, item, index)" :popper-options="popperOptions" style="width: 100%;">
            </el-date-picker>
            <!-- 时间 -->
            <el-time-picker v-else-if="item.type == 'time'" v-model="item.DefaultValue" format="HH:mm:ss"
              value-format="HH:mm:ss" :size="size" :disabled="item.readonly"
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="textChange($event, item, index)" :popper-options="popperOptions" style="width: 100%;">
            </el-time-picker>
            <!-- 级联 -->
            <el-cascader v-else-if="item.type == 'cascader'" v-model="item.DefaultValue" :options="item.data"
              :props="{ checkStrictly: true }" :size="size" clearable :disabled="item.readonly"
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="cascaderChange($event, item, index)" style="width: 100%;">
            </el-cascader>
            <!-- 排序 -->
            <el-input v-else-if="item.type == 'sort'" type="number" v-model="item.DefaultValue"
              @change="textChange($event, item, index)"></el-input>
            <!-- 模板下载按钮 -->
            <el-button v-else-if="item.type == 'download'" type="primary" size="small" @click="downloadClick(item)">
              {{ item.buttonTitle }}</el-button>
            <!-- 按钮 -->
            <el-button v-else-if="item.type == 'button'" type="primary" size="small" @click="buttonClick(item)">
              {{ item.buttonTitle }}</el-button>
            <!-- 上传 -->
            <div v-else-if="item.type == 'upload'" style="display: flex;">
              <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" :accept="item.UploadFileTypeAccept"
                :before-upload="beforeAvatarUpload.bind(null, item)" :http-request="httpRequest.bind(null, item, index)"
                :disabled="numberDisabled">
                <el-button type="success" size="small" :icon="UploadFilled"
                  @click="MaxFileNumberClick(item)">上传</el-button>
              </el-upload>
              <div class="fileFlex">
                <div v-for="(item1) in item.DefaultValue" :key="item1.Id" style="cursor: pointer;">
                  <el-icon color="#F56C6C" @click="delFile(item1, item)">
                    <Delete />
                  </el-icon>
                  <span @click="fileListDownload(item1, item.DefaultValue)"> {{ item1.Title }}</span>
                </div>
              </div>
            </div>
            <!-- 预算清单 -->
            <div v-else-if="item.type == 'projectlist'">
              <el-button type="primary" size="small" @click="projectEdit(item)">
                {{ totalAmount > 0 ? '修改' : item.buttonTitle }}</el-button>
              <!-- <span v-if="item.code == 'ProjectList'" style="padding-left: 10px;">总金额： <span style="font-size: 16px;">
                {{ formatNumberWithCommas(totalAmount) }}</span> </span> -->
            </div>
            <!-- 开关 -->
            <el-switch v-else-if="item.type == 'switch'" v-model="item.DefaultValue" :disabled="item.readonly"
              active-color="#13ce66" inactive-color="#0e7ef3"
              :active-value="typeof item.DefaultValue == 'boolean' ? true : typeof item.DefaultValue == 'string' ? '1' : 1"
              :inactive-value="typeof item.DefaultValue == 'boolean' ? false : typeof item.DefaultValue == 'string' ? '0' : 0">
            </el-switch>
            <!-- 分割线 -->
            <el-divider v-else-if="item.type == 'line'" :border-style="item.isDashed" :content-position="item.isCenter"
              :style="{ 'width': '100%', '--el-border-color': item.dividerColor ? item.dividerColor : '#dedfe6' }">
              <span :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#303133' }">
                {{ item.dividerText }}</span>
            </el-divider>
            <!-- 表格 -->
            <el-table v-else-if="item.type == 'statisticstable'" ref="multipleTable" :data="item.tableData"
              highlight-current-row @selection-change="HandleSelectChange" border stripe row-key="Id"
              header-cell-class-name="headerClassName">
              <el-table-column v-if="item.changeType == 'radio'" label="" width="50" align="center">
                <template #default="{ row }">
                  <el-radio v-model="radio" :value="row.Id" @change="HandleRadioChange(row, item)"></el-radio>
                </template>
              </el-table-column>
              <el-table-column v-if="item.changeType == 'checkbox'" type="selection" width="50"></el-table-column>
              <el-table-column v-for="column in item.columnsData" :key="column.FieldCode" :prop="column.FieldCode"
                :label="column.FieldName" :min-width="column.Width" :align="column.ContentStyle">
                <template #default="{ row }">
                  <span v-if="column.ColumnFieldType == 4">
                    {{ row[column.FieldCode] ? row[column.FieldCode].substring(0, column.DateDisplay || 10) : '' }}
                  </span>
                  <span v-else-if="column.ColumnFieldType == 1">
                    {{ row[column.FieldCode] ? formatNumber(row[column.FieldCode]) : '' }}
                  </span>
                  <span v-else-if="column.ColumnFieldType == 2">
                    {{ row[column.FieldCode] ? formatNumberWithCommas(row[column.FieldCode]) : '' }}
                  </span>
                  <span v-else> {{ row[column.FieldCode] }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="CanUseAmount" :label="item.CanUseAmount || '可用金额'" min-width="120" align="right">
              </el-table-column>
              <el-table-column v-if="item.changeType == 'checkbox'" prop="CurrentAmount" label="使用金额" min-width="120"
                align="center">
                <template #default="{ row }">
                  <el-input v-model="row.CurrentAmount" @input="amountInput($event, row, 'CurrentAmount')"> </el-input>
                </template>
              </el-table-column>
            </el-table>

            <!-- 文本编辑器 -->
            <app-wang-editor ref="editor" v-else-if="item.type == 'editor'" v-model="item.DefaultValue"
              :height="item.height || 350">
            </app-wang-editor>
            <!-- 评分 -->
            <!-- <el-rate :disabled="(item.readonly || item.disabled) " v-else-if="item.type == 'rate'"
              @change="(val) => { item.onChange && item.onChange(val); }" :max="item.max"
              v-model="item.DefaultValue" />
            <div style="display: flex" v-else-if="(item.type == 'range' || item.range) ">
              <el-input :size="size" :disabled="item.readonly || item.disabled" style="flex: 1"
                v-model="item.DefaultValue[0]" clearable />
              <span style="margin: 0 5px">-</span>
              <el-input :size="size" :disabled="(item.readonly || item.disabled) " style="flex: 1"
                v-model="item.DefaultValue[1]" clearable />
            </div> -->
            <!-- 数字输入框 -->
            <!-- <el-input-number v-else-if="item.type == 'number' " :size="size" style="width: 100%" :ref="item.field"
              v-model="item.DefaultValue" :min="item.min" :disabled="item.readonly || item.disabled"
              :max="item.max" controls-position="right" @change="item.onKeyPress" /> -->
            <!-- 密码框 -->
            <!-- <el-input :size="size" clearable v-else-if="item.type == 'password' " type="password"
              v-model="item.DefaultValue" :disabled="item.readonly || item.disabled"
              :placeholder="item.placeholder ? item.placeholder : item.name" /> -->
            <!-- <el-input :size="size" clearable :ref="item.field" v-else-if="item.onKeyPress"
              :placeholder="item.placeholder ? item.placeholder : item.name" :disabled="item.readonly || item.disabled"
              v-model="item.DefaultValue" @keypress="($event) => { onKeyPress($event, item); }"
              @change="item.onKeyPress" @keyup.enter="item.onKeyPress" @blur="item.blur" @focus="item.focus"></el-input> -->
            <!-- <el-input :size="size" clearable v-else :ref="item.field"
              :placeholder="item.placeholder ? item.placeholder : item.name" :disabled="item.readonly || item.disabled"
              v-model="item.DefaultValue" @blur="item.blur" @focus="item.focus"></el-input> -->
            <!-- </div> -->
          </el-form-item>
        </template>
        <el-form-item label-width="200px" label=" ">
          <el-button type="primary" :icon="Select" @click="HandleSubmit(1)">{{ stagingButton }}</el-button>
          <el-button type="primary" :icon="Right" @click="HandleSubmit(2)">{{ submitButtonName }}</el-button>
          <span v-if="nextTipMsg" style="padding-left: 20px;color: #999;">{{ nextTipMsg }}</span>
        </el-form-item>
      </div>
      <div style="width: 100%">
        <slot name="footer"></slot>
      </div>
    </el-form>
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
  </div>
</template>
<style lang="scss" scoped>
.edit-form-item {
  display: flex;
  flex-wrap: wrap;
}

:deep(.el-form-item) {
  margin-right: 0;
}

.col-line {
  line-height: 25px;
  font-weight: bold;
  border-bottom: 1px solid rgb(218 218 218);
}

:deep(.el-divider__text.is-left) {
  left: 100px;
}
</style>
