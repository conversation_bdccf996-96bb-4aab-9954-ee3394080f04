<script setup>

import { onMounted, ref, watch } from 'vue'
import {
    Search, Refresh, FolderAdd
} from '@element-plus/icons-vue'
import {
    Unitfindchildren, Unitgetbyid, Unitdelbatch,
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filtersKey = ref({ key: '', value: 'Name' })
const yearDateList = ref([])
//加载数据
onMounted(() => {
    HandleTableData()
})

const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '请选择年度', trigger: 'change' },
    ],
    Code: [
        { required: true, message: '请输入应采购校服学校总数', trigger: 'change' },
    ],
}

// 添加账户
const HandleAdd = () => {
    dialogVisible.value = true
    formData.value = {}
    nextTick(() => {
        refForm.value.resetFields()
    })
}
//修改
const HandleEdit = (row) => {
    formData.value.Id = row.Id
    Unitgetbyid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value.Id = rows.Id
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)

    })
    dialogVisible.value = true
}
//删除
const HandleDel = (row) => {
    ElMessageBox.confirm(`确认删除用户[${row.Name}]吗?`)
        .then(() => {
            Unitdelbatch({ ids: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success('删除成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
}

//获取列表 与 搜索
const HandleTableData = (page) => {
    Unitfindchildren(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { other, rows, total } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total);
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = (page) => {
    filters.value.pageIndex = 1
    filtersKey.value.key = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
} 
</script>
<template>
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                <el-form-item class="flexItem">
                    <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                </el-form-item>
                <div class="verticalIdel"></div>
                <el-form-item class="flexItem">
                    <el-input v-model.trim="filters.key" placeholder="年度" style="max-width: 200px">
                    </el-input>
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                    <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                </el-form-item>
            </el-form>

        </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
        header-cell-class-name="headerClassName">
        <el-table-column prop="Code" label="年度" min-width="180" align="center"></el-table-column>
        <el-table-column prop="Name" label="应采购校服学校总数（所）" min-width="180" align="center"></el-table-column>
        <el-table-column prop="Brief" label="备注" min-width="100" align="center"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120" align="center">
            <template #default="{ row }">
                <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
            </template>
        </el-table-column>
        <template #empty>
            <el-empty description="没有数据"></el-empty>
        </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
        @handleChange="handlePage" />
    <!-- 添加修改单位用户信息 -- 弹窗 -->
    <app-box v-model="dialogVisible" :height="600" :width="600" :lazy="true"
        :title="formData.Id ? '修改应采购校服学校总数' : '添加应采购校服学校总数'">
        <template #content>
            <el-form class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
                label-width="140px" status-icon>
                <el-form-item label="年度：" prop="PurchaseYear">
                    <el-select v-model="formData.PurchaseYear" placeholder=" 年度">
                        <el-option v-for="item in yearDateList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="应采购校服学校总数（所）：" prop="ContractPersonNum">
                    <el-input v-model="formData.ContractPersonNum"
                        @input="integerLimitInput($event, 'ContractPersonNum')" class="item_content"></el-input>
                </el-form-item>
                <el-form-item label="备注：">
                    <el-input type="textarea" v-model="formData.Memo" :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
                </el-form-item>
            </el-form>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
            </span>
        </template>
    </app-box>
</template>
<style lang="scss" scoped></style>