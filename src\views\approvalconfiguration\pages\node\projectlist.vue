<script setup>
defineOptions({
  name: 'nodeprojectlist'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
  UploadPostexecl
} from '@/api/user.js'
import {
  ProjectListSearch, ProjectListSave, ProjectListByid, ProjectListDeleteByid, ProjectListDeleteByids, ProjectListImport
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { ElMessageBox, ElMessage } from 'element-plus'
import { FolderAdd, Refresh, Search, Delete, Back, Download, UploadFilled } from '@element-plus/icons-vue'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { rulesLimit } from "@/utils/rules.js";
import { fileDownload, tagsListStore, formatNumberWithCommas, formatNumber } from "@/utils/index.js";
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, key: '' })
const tableTotal = ref(0)
const tableData = ref([])// 表格数据
const columnData = ref([])//列表头数据
const selectRows = ref([])
const searchList = ref([])//查询条件：下拉、时间等
const formFields = ref({})
const dialogVisible = ref(false)
const refForm = ref()
const editId = ref()
const downLoadFile = ref('')
const execlName = ref('项目清单.xls')
const isEdit = ref(true)//是否可编辑
//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  isEdit.value = route.query.isEdit
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})
// 返回上一页
const HandleBack = () => {
  let tagsList = userStore.tagsList
  tagsList = tagsList.filter(t => t.path != route.path)
  userStore.setTagsList(tagsList)
  // FieldCode
  if (route.query.page == 'edit') {
    router.push({ path: './formedit' + route.query.routerUrl, query: { projectDeclarationId: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode } })
  } else if (route.query.page == 'detail') {
    router.push({ path: './detail' + route.query.routerUrl, query: { id: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode } })

  } else if (route.query.page == 'treasurydetail') {
    router.push({ path: '../treasury/detail' + route.query.routerUrl, query: { id: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode, ProcessId: route.query.ProcessId } })
  } else if (route.query.page == 'examine') {
    router.push({ path: './examine' + route.query.routerUrl, query: { id: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode } })
  }
}
//新增
const HandleAdd = () => {
  editId.value = undefined
  dialogVisible.value = true
  nextTick(() => {
    refForm.value.resetFields()
    formFields.value.formItems.forEach(item => {
      if (item.TypeStyle != 2) {
        item.DefaultValue = ''
      } else {
        item.DefaultValue = undefined
      }
    })
  })

}
//获取详情
const HandleEdit = (row) => {
  editId.value = row.Id
  dialogVisible.value = true
  ProjectListByid({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows, headers, other } = res.data.data
      // console.log('获取填报页面信息res.data.data', res.data.data)
      let obj = rows || {}
      nextTick(() => {
        refForm.value.resetFields()
        formFields.value.formItems.forEach(item => {
          item.DefaultValue = obj[item.FieldValue]
        })
      })

      console.log('formFields.value.formItems', formFields.value.formItems)
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      ProjectListDeleteByid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
const HandleSelectChange = (selection) => {
  selectRows.value = selection
}
//批量删除
const HandleBatchDel = () => {
  ElMessageBox.confirm(`确认要批量删除选中数据吗?`)
    .then(() => {
      let ids = selectRows.value.map(t => t.Id).join(',')
      ProjectListDeleteByids({ ids: ids }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
// 模板下载
const HandleDownload = () => {
  // let str = downLoadFile.value;
  // let lastDotIndex = str.lastIndexOf('.');
  // let result = str.substring(lastDotIndex);
  // console.log(result);
  // let title = '项目清单' + result
  fileDownload(downLoadFile.value, execlName.value);
}
const fileFile = ref()
const uploadRef = ref()
// 导入前校验
const beforeAvatarUpload = (file) => {
  console.log("导入前校验file", file)
  fileFile.value = file
  const extension = file.name.split('.')[1].toLowerCase() === 'xls'
  const extension2 = file.name.split('.')[1].toLowerCase() === 'xlsx'
  if (!extension && !extension2) {
    ElMessage({
      message: '上传模板只能是 xls、xlsx格式!',
      type: 'error'
    })
  }
  return extension || extension2
}
// 导入清单
const httpRequest = () => {
  UploadPostexecl([fileFile.value]).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      let fileData = {
        FilePath: rows,
        ProcessId: route.query.ProcessId,
        ProjectDeclarationId: route.query.ProjectDeclarationId,
        FieldCode: route.query.FieldCode,
      }
      // console.log('fileData', fileData)
      ProjectListImport(fileData).then(res1 => {
        if (res1.data.flag == 1) {
          ElMessage.success(res1.data.msg || '导入成功')
          HandleTableData(true)
        } else {
          ElMessage({
            showClose: true,
            message: res1.data.msg,
            type: 'error',
            duration: 5000
          })
        }
      })
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.ProcessId = route.query.ProcessId
  filters.value.ProjectDeclarationId = route.query.ProjectDeclarationId
  filters.value.FieldCode = route.query.FieldCode
  filters.value.isFirst = isFirst

  ProjectListSearch(filters.value).then(res => {
    console.log('res', res)
    if (res.data.flag == 1) {
      const { rows, total, other, headers } = res.data.data
      tableData.value = rows.data || [];//表格数据
      tableTotal.value = rows.dataCount || 0
      console.log('tableData.value', tableData.value)
      if (isFirst && other) {
        columnData.value = other.listColumn || [];//表头（列表字段）
        searchList.value = other.listSearch || []//查询条件
        let listAdd = other.listAdd || []//表单数据字段
        downLoadFile.value = other.downLoadFile || []//模板下载路径
        execlName.value = other.execlName || '项目清单.xls'//模板下载路径
        // 修改表单下拉框数据
        listAdd.forEach(item => {
          if (item.SourceValue) {
            item.SourceValue = item.SourceValue.split('|')
          }
          if (item.TypeStyle != 2) {
            item.DefaultValue = ''
          } else {
            item.DefaultValue = undefined
          }
        })
        formFields.value.formItems = listAdd
        console.log('formFields.value.formItems', formFields.value.formItems)
      }

    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.Code = undefined
  filters.value.key = undefined
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}

//输入框限制：输入小数/整数 
const rulesInput = (val, item, name) => {
  item[name] = rulesLimit(val, 4);
}
// 表单校验
const ruleForm = (item) => {
  console.log('表单校验', item)
  if (item.TypeStyle == 1 || item.TypeStyle == 2 || item.TypeStyle == 4) {
    return [{ required: true, message: '请输入' + item.Title, trigger: 'blur' }]
  } else {
    return [{ required: true, message: '请选择' + item.Title, trigger: 'change' }]
  }

}
// 提交
const HandleSubmit = (e) => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    let formData = {
      Id: editId.value,
      ProjectDeclarationId: route.query.ProjectDeclarationId,
      FieldCode: route.query.FieldCode,
    }
    for (let i = 0; i < formFields.value.formItems.length; i++) {
      formData[formFields.value.formItems[i].FieldValue] = formFields.value.formItems[i].DefaultValue
      // if (formFields.value.formItems[i].TypeStyle == 2) {
      //   formData[formFields.value.formItems[i].FieldValue] = Number(formFields.value.formItems[i].DefaultValue)
      // }
    }
    // console.log("formData", formData)
    ProjectListSave(formData).then((res) => {
      if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '保存成功')
        dialogVisible.value = false
        HandleTableData()
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })
}

</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button :icon="Back" @click="HandleBack">返回</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item class="flexItem flexOperation" v-if="isEdit">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
            <el-button type="primary" :icon="Download" @click="HandleDownload">模版下载</el-button>
            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" accept=".xlsx,.xls"
              style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload"
              :http-request="httpRequest">
              <el-button type="primary" :icon="UploadFilled">导入清单</el-button>
            </el-upload>
            <el-button type="danger" :icon="Delete" @click="HandleBatchDel"
              :disabled="selectRows.length == 0">批量删除</el-button>
          </el-form-item>
          <div v-if="isEdit" class="verticalIdel"></div>
          <el-form-item class="flexItem" v-if="searchList.length > 0">
            <el-input v-model.trim="filters.key" placeholder="请输入" style="width: 240px" class="input-with-select">
              <template #prepend>
                <el-select v-model="filters.Code" style="width: 120px">
                  <el-option v-for="item in searchList" :key="item.FieldValue" :label="item.Title"
                    :value="item.FieldValue" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item class="flexItem" v-if="searchList.length > 0">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>

        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName" @selection-change="HandleSelectChange" row-key="Id">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column v-for="column in columnData" :key="column.FieldValue" :prop="column.FieldValue"
        :label="column.Title" :min-width="column.Width" :align="column.ContentStyle">
        <template #default="{ row }">
          <!-- {{ row[column.FieldValue] }} -->
          <span v-if="column.ColumnFieldType == 5">
            {{ row[column.FieldValue] ? row[column.FieldValue].substring(0, column.DateDisplay || 10) : '' }}
          </span>
          <span v-else-if="column.ColumnFieldType == 1">
            {{ row[column.FieldValue] ? formatNumber(row[column.FieldValue]) : '' }}
          </span>
          <span v-else-if="column.ColumnFieldType == 2">
            {{ row[column.FieldValue] ? formatNumberWithCommas(row[column.FieldValue]) : '' }}
          </span>
          <span v-else> {{ row[column.FieldValue] }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center" v-if="isEdit">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleEdit(row, item)">修改</el-button>
          <el-button type="primary" link @click="HandleDel(row, item)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />

    <!-- 创建修改弹窗 -->
    <app-box v-model="dialogVisible" :width="680" :lazy="true" title="组织管理">
      <template #content>
        <el-form @submit.prevent ref="refForm" :model="formFields" label-width="140px" status-icon>
          <el-form-item v-for="(item, index) in formFields.formItems" :key="item.Id" :label="item.Title"
            :prop="'formItems.' + index + '.DefaultValue'" :rules="item.IsRequired == 1 ? ruleForm(item) : []"
            :style="{ width: item.width + '%' }">
            <!-- 输入框 -->
            <el-input v-if="item.TypeStyle == 1" v-model="item.DefaultValue" clearable :placeholder="'请输入' + item.Title"
              style="width: 100%;"></el-input>
            <!-- 数字 -->
            <el-input v-else-if="item.TypeStyle == 2" v-model="item.DefaultValue" clearable
              :placeholder="'请输入' + item.Title" @input="rulesInput($event, item, 'DefaultValue')"
              style="width: 100%;"></el-input>
            <!-- 下拉框 -->
            <el-select v-else-if="item.TypeStyle == 3" v-model="item.DefaultValue" filterable
              :placeholder="'请选择' + item.Title" clearable style="width: 100%;">
              <el-option v-for="(item1, index1) in item.SourceValue" :key="index1" :label="item1" :value="item1">
              </el-option>
            </el-select>
            <!-- 文本域 -->
            <el-input v-else-if="item.TypeStyle == 4" v-model="item.DefaultValue" type="textarea" clearable
              :autosize="{ minRows: 2, maxRows: 10 }" :placeholder="'请输入' + item.Title" style="width: 100%;" />
            <!-- 日期 -->
            <el-date-picker v-else-if="item.TypeStyle == 5" v-model="item.DefaultValue" type="date" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" clearable :placeholder="'请选择' + item.Title" style="width: 100%;">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
        </span>
      </template>
    </app-box>

  </div>
</template>
<style lang="scss" scoped></style>
