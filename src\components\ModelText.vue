<template>
    <el-dialog :model-value="modelVisible" title="关于征订校服的征求家长意见书" width="760px" :before-close="beforeClose">
        <!-- detailList -->
        <div class="content" id="myElement">
            <p><br></p>
            <p style="text-indent: 32pt; text-align: center;"><span
                    style="color: rgb(0, 0, 0); font-size: 16px; font-family: 黑体;">关于征订校服的征求家长意见书</span></p>
            <p style="text-align: justify;"><span
                    style="color: rgb(0, 0, 0); font-size: 14px; font-family: 宋体;">尊敬的各位家长：</span></p>
            <p style="text-indent: 28pt; text-align: justify;"><span
                    style="color: rgb(0, 0, 0); font-size: 14px; font-family: 宋体;">校服是校园文化的标志，是学生团体精神风貌的一种体现。您的孩子现正处于学习文化，掌握各种技能的大好时光，学生统一着装不仅能够提升我校师生的精神面貌，展现我校师生的整体风采，而且能消除学生间相互攀比的不良习气，培养学生的团队精神，增强集体荣誉感，杜绝学生穿奇装异服，形成正确的审美观；也有利于学校的教育管理及学生的安全管理，在开展校外活动，如参观、游行、校外实践活动时，便于教师的统一组织与管理，也可以提醒司机注意学生的安全。</span>
            </p>
            <p style="text-indent: 28pt; text-align: justify;"><span
                    style="color: rgb(0, 0, 0); font-size: 14px; font-family: 宋体;">经学校研究决定，拟为学生统一校服，家长可自愿选择是否订购。 </span>
            </p>
            <p style="text-indent: 24pt; text-align: justify;"><span
                    style="color: rgb(0, 0, 0); font-size: 16px; font-family: 微软雅黑;">校服明细：</span></p>
            <table style="width: 100%;">
                <tbody>
                    <tr>
                        <th width="99" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">种类</span></th>
                        <th width="158"><span style="font-size: 14px; font-family: 宋体;">品名</span></th>
                        <th width="56"><span style="font-size: 14px; font-family: 宋体;">单位</span>
                        </th>
                        <th width="144"><span style="font-size: 14px; font-family: 宋体;">预估单价（元）</span></th>
                        <th width="147"><span style="font-size: 14px; font-family: 宋体;">备注</span></th>
                    </tr>
                    <tr>
                        <td width="99" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">春秋装</span></td>
                        <td width="158" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">运动服</span></td>
                        <td width="56" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">套</span></td>
                        <td width="144"></td>
                        <td width="147"></td>
                    </tr>
                    <tr>
                        <td width="99" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">夏装</span></td>
                        <td width="158" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">T恤</span></td>
                        <td width="56" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">件</span></td>
                        <td width="144"></td>
                        <td width="147"></td>
                    </tr>
                    <tr>
                        <td width="99" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">冬装</span></td>
                        <td width="158" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">冲锋衣</span></td>
                        <td width="56" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">件</span></td>
                        <td width="144"></td>
                        <td width="147"></td>
                    </tr>
                    <tr>
                        <td width="99" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">其他</span></td>
                        <td width="158" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">西服上衣</span></td>
                        <td width="56" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">件</span></td>
                        <td width="144"></td>
                        <td width="147" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">礼服</span></td>
                    </tr>
                    <tr>
                        <td width="99" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">其他</span></td>
                        <td width="158" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">衬衫</span></td>
                        <td width="56" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">件</span></td>
                        <td width="144"></td>
                        <td width="147" style="text-align: center;"><span
                                style="font-size: 14px; font-family: 宋体;">礼服</span></td>
                    </tr>
                    <tr>
                        <td width="99" class="blank"></td>
                        <td width="158"></td>
                        <td width="56"></td>
                        <td width="144"></td>
                        <td width="147"></td>
                    </tr>
                    <tr>
                        <td width="99" class="blank"></td>
                        <td width="158"></td>
                        <td width="56"></td>
                        <td width="144"></td>
                        <td width="147"></td>
                    </tr>
                </tbody>
            </table>
            <p style="text-indent: 24pt; text-align: center;"><span style="color: rgb(0, 0, 0); font-family: 微软雅黑;">
                </span></p>
            <p style="text-indent: 262.5pt; text-align: justify;"><span style="color: rgb(0, 0, 0); font-family: 宋体;">
                    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                    &nbsp;</span><span style="color: rgb(0, 0, 0); font-size: 16px; font-family: 宋体;"> XXX学校</span></p>
            <p style="text-indent: 252pt; text-align: justify;"><span style="color: rgb(0, 0, 0); font-family: 宋体;">
                    &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
                    &nbsp;</span><span style="color: rgb(0, 0, 0); font-size: 16px; font-family: 宋体;">20XX-XX-XX</span>
            </p>
            <p><br></p>

        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="Copy">复制</el-button>
                <el-button @click="beforeClose">关闭</el-button>
            </span>
        </template>
    </el-dialog>

</template>

<script setup name="wngEditor">
import '@wangeditor/editor/dist/css/style.css';
// 定义父组件传过来的值
const props = defineProps({
    // 是否禁用
    modelVisible: {
        type: Boolean,
        default: () => false,
    },
});
// 定义子组件向父组件传值/事件
const emit = defineEmits(['update:modelVisible', 'getCopy']);

const Copy = () => {
    let htmlElement = document.getElementById('myElement');  // 获取需要转换的HTML元素
    let htmlString = htmlElement.innerHTML;  // 将HTML元素转换为字符串
    emit('update:modelVisible', false)
    emit('getCopy', htmlString);
}
// 关闭弹窗
const beforeClose = () => {
    emit('update:modelVisible', false)
}




</script>
<style lang="scss" scoped>
.content {
    margin: 5px 10px;
    -webkit-box-shadow: 0 0 5px #dcdfe6;
    box-shadow: 0 0 5px #dcdfe6;
    padding: 5px 10px;

    table {
        border-collapse: collapse !important;
        border: 1px solid #e5e5e5 !important;
        /* 设置表格的边框为单线，颜色为黑色 */
    }

    th,
    td {
        border: 1px solid #e5e5e5 !important;
        /* 设置单元格的边框为单线，颜色为黑色 */
        text-align: center;
    }

    .blank {
        height: 22px;
    }
}
</style>