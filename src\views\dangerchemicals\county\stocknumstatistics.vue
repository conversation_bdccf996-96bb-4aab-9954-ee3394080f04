<script setup>
defineOptions({
    name: 'dangerchemicalscountystocknumstatistics'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Position, Back
} from '@element-plus/icons-vue'
import {
    DccatalogGetClassTwo, DcCountyStockNumStatisticsFind, DcScrapListFind, Punitgetschoolbycountyid
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { ExcelDownload } from "@/utils/index.js"
import {
    DcCountyStockNumStatisticsExort
} from '@/api/directdata.js'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const unitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const tableTotal = ref(0)
const detailTableData = ref([])
const detailTableTotal = ref(0)
const refTable = ref()
const filters = ref({
    pageIndex: 1,
    pageSize: 10,
    sortModel: [
        { SortCode: "Sort", SortType: "ASC" },
        { SortCode: "SchoolId", SortType: "ASC" },
        { SortCode: "TwoCatalogId", SortType: "ASC" },
        { SortCode: "SchoolCatalogId", SortType: "ASC" }
    ],
    Name: route.query.name,
    Model: route.query.model,
    TwoCatalogId: route.query.twoCatalogId,
    CountyId: route.query.countyId
})

const tableEditDialogVisible = ref(false)
const tableEditDialogData = ref([])
const activeName = ref('collect')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        DccatalogGetClassTwoUser()
        PunitgetschoolbycountyidUser()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    filters.value.Name = route.query.name
    filters.value.Model = route.query.model
    filters.value.TwoCatalogId = route.query.twoCatalogId
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            DccatalogGetClassTwoUser()
            PunitgetschoolbycountyidUser()
        }
    })
})

const summation = ref({})
// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    console.log("columns", columns)
    columns.forEach((column, index) => {
        if (index === 0) {
            if (summation.value.SchoolName) {
                sums[index] = summation.value.SchoolName.replace(/<b>(.*?)<\/b>/, "$1");
            } else {
                sums[index] = '总计：'
            }
            return;
        }
        if (column.property == 'StockNum') {
            sums[index] = summation.value.StockNum || '--'
        }
    });
    return sums;
}
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })

}
// 按汇总查看列表
const HandleTableData = () => {
    if (activeName.value == 'collect') {
        DcCountyStockNumStatisticsFind(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, other } = res.data.data
                tableData.value = rows.data
                tableTotal.value = rows.dataCount
                summation.value = rows.Other[0] || {}
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        DcScrapListFind(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, total } = res.data.data
                detailTableData.value = rows.data
                detailTableTotal.value = rows.dataCount
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    if (unitType.value == 1 && route.query.name) {
        filters.value.Name = route.query.name
        filters.value.Model = route.query.model
        filters.value.TwoCatalogId = route.query.twoCatalogId
    } else {
        filters.value.Name = undefined;
        filters.value.Model = undefined;
        filters.value.TwoCatalogId = undefined;
    }
    filters.value.SchoolId = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const StatuzSolicitedList = ref([])
const schoolList = ref([])
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: route.query.countyId || 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 根据区县获取学校
const PunitgetschoolbycountyidUser = () => {
    Punitgetschoolbycountyid({ CountyId: route.query.countyId || 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            schoolList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//导出   
const HandleExport = () => {
    filters.value.pageIndex = undefined;
    filters.value.pageSize = undefined;
    DcCountyStockNumStatisticsExort(filters.value).then(res => {
        ExcelDownload(res)
    });
}
// 切换tab
const handleClick = (tab) => {
    console.log(tab.props.name, tab.index)
    activeName.value = tab.props.name
    filters.value.pageIndex = 1
    HandleTableData()

}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const HandleDetail = (row) => {
    let data = {
        CountyId: route.query.countyId,
        SchoolCatalogId: row.SchoolCatalogId,
        SchoolId: row.SchoolId,
        SchoolMaterialModelId: row.SchoolMaterialModelId,
        pageIndex: 1,
        pageSize: 1000000,
        Statuz: 1,
        StockNumgt: 0,
        sortModel: [{ SortCode: "ValidDate", SortType: "ASC" }, { SortCode: "RegDate", SortType: "DESC" }]
    }
    DcScrapListFind(data).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableEditDialogData.value = rows.data
            tableEditDialogVisible.value = true
        } else {
            ElMessage.error(res.data.msg)
        }
    })
} 
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    库存数量统计 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 入库方式：“采购”是指从供应商采购后直接入库，“退回”是指实验使用后剩余退回入库；</li>
                    <li> 是否可用：“不可用”是指危化品待报废处置。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="unitType == 1 && route.query.path">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="unitType == 1 && route.query.path"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in schoolList" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Model" clearable placeholder="规格属性"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="按汇总查看" name="collect">
                <div v-if="unitType == 1 && route.query.countyName"
                    style="font-size: 14px;color: #606266;margin-bottom: 10px;">
                    【{{ route.query.countyName }}】：库存数量统计
                </div>
                <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                    :summary-method="getSummaries" show-summary header-cell-class-name="headerClassName">
                    <el-table-column prop="SchoolName" label="学校名称" min-width="200"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="TwoCatalogName" label="危化品分类" min-width="140"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="UnitsMeasurement" label="单位" min-width="90"></el-table-column>
                    <el-table-column prop="StockNum" label="库存数量" min-width="120" align="right"> </el-table-column>
                    <el-table-column prop="Limited" label="超量预警值" min-width="120" align="right">
                        <template #default="{ row }">
                            {{ row.Id ? row.Limited ? row.Limited : '--' : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="明细" min-width="100" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
            <el-tab-pane label="按明细查看" name="detail">
                <div v-if="unitType == 1 && route.query.countyName"
                    style="font-size: 14px;color: #606266;margin-bottom: 10px;">
                    【{{ route.query.countyName }}】：库存数量统计
                </div>
                <el-table ref="refTable" :data="detailTableData" highlight-current-row border stripe
                    header-cell-class-name="headerClassName">
                    <el-table-column prop="SchoolName" label="学校名称" min-width="140"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="TwoCatalogName" label="危化品分类" min-width="140"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="90"></el-table-column>
                    <el-table-column prop="StockNum" label="库存数量" min-width="120" align="right"> </el-table-column>
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center"> </el-table-column>
                    <el-table-column prop="RegDate" label="入库时间" min-width="180" align="center">
                        <template #default="{ row }">
                            {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="ValidDate" label="有效期至" min-width="180" align="center">
                        <template #default="{ row }">
                            {{ row.ValidDate ? row.ValidDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="Address" label="存放地点" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="InputType" label="入库方式" min-width="110" align="center">
                        <template #default="{ row }">
                            {{ row.InputType == 1 ? '采购' : row.InputType == 1 ? '退回' : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="IsMayUse" label="是否可用" min-width="110" align="center">
                        <template #default="{ row }">
                            <span :style="{ color: row.IsMayUse == 0 ? 'red' : '' }">
                                {{ row.IsMayUse == 1 ? '可用' : row.IsMayUse == 0 ? '不可用' : '' }}
                            </span>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="detailTableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
        </el-tabs>
        <app-box v-model="tableEditDialogVisible" :width="980" :lazy="true" title="危化品库存明细">
            <template #content>
                <el-table ref="refTableData" :data="tableEditDialogData" border stripe max-height="360px"
                    header-cell-class-name=" headerClassName">
                    <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                        <template #default="{ row }">
                            {{ row.Brand ? row.Brand : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="StockNum" label="存量" min-width="110" align="right"> </el-table-column>
                    <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
                    <el-table-column prop="Price" label="单价" min-width="160" align="right">
                        <template #default="{ row }">
                            <span v-if="row.Id">
                                {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                            </span>
                            <span v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="Amount" label="金额" min-width="160" align="right">
                        <template #default="{ row }">
                            <span v-if="row.Id">
                                {{ row.Amount ? '￥' + Number(row.Amount).toFixed(2) : '--' }}
                            </span>
                            <span v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="RegDate" label="入库时间" min-width="180" align="center">
                        <template #default="{ row }">
                            {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="ValidDate" label="有效期至" min-width="180" align="center">
                        <template #default="{ row }">
                            {{ row.ValidDate ? row.ValidDate.substring(0, 10) : '--' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="Address" label="存放地点" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="InputType" label="入库方式" min-width="110" align="center">
                        <template #default="{ row }">
                            {{ row.InputType == 1 ? '采购' : row.InputType == 1 ? '退回' : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="IsMayUse" label="是否可用" min-width="110" align="center">
                        <template #default="{ row }">
                            <span :style="{ color: row.IsMayUse == 0 ? 'red' : '' }">
                                {{ row.IsMayUse == 1 ? '可用' : row.IsMayUse == 0 ? '不可用' : '' }}
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>