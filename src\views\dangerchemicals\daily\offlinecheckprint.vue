<script setup>
defineOptions({
    name: 'dangerchemicalsdailyofflinecheckprint'
});
import { onMounted, ref, nextTick, onActivated, watch } from 'vue'
import {
    Refresh, Back, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcGovernItemInfo
} from '@/api/daily.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { fileDownload, tagsListStore } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
        }
    })
})
// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })
}

// 列表
const HandleTableData = () => {

    DcGovernItemInfo().then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            // tableData.value = rows.data.slice(0, 20)
            tableData.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}


// 计算每个单元格应该跨越的行数
const rowspans = ref([]);

// 计算合并信息
const calculateRowspans = () => {
    const spans = [];
    let count = 1;

    for (let i = 0; i < tableData.value.length; i++) {
        if (i === tableData.value.length - 1) {
            spans.push(count);
            break;
        }

        if (tableData.value[i].DicName === tableData.value[i + 1].DicName) {
            count++;
        } else {
            spans.push(count);
            count = 1;
        }
    }

    rowspans.value = spans;
};

// 获取指定行的rowspan值
const getRowspan = (index) => {
    for (let i = 0, sum = 0; i < rowspans.value.length; i++) {
        sum += rowspans.value[i];
        if (index < sum) {
            return rowspans.value[i];
        }
    }
    return 1;
};

// 判断是否应该显示用户名单元格
const shouldShowUserNameCell = (index) => {
    if (index === 0) return true;
    return tableData.value[index].DicName !== tableData.value[index - 1].DicName;
};

// 在数据加载后计算合并信息
watch(() => tableData.value, (newVal) => {
    if (newVal && newVal.length > 0) {
        calculateRowspans();
    }
}, { immediate: true, deep: true });




const handleNativePrint = () => {
    // 获取打印区域内容
    const printContent = document.getElementById('printArea').innerHTML;

    // 创建一个隐藏的iframe
    const iframe = document.createElement('iframe');
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');

    // 添加到DOM中
    document.body.appendChild(iframe);

    // 获取iframe的document对象
    const iframeWindow = iframe.contentWindow || iframe.contentDocument;
    const iframeDoc = iframeWindow.document || iframeWindow;

    // 写入打印内容
    iframeDoc.open();
    iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>中小学危险化学品安全综合治理检查表</title>
            <style>
                @page { size: auto; margin:10mm 5mm; }
                body { font-family: 宋体; margin: 0; padding: 0;   }
                .print-table { border-collapse: collapse; width: 100%; font-size: 14px; }
                .print-table th { border: 1px solid #000; padding: 5px 1px; } 
                .print-table td { border: 1px solid #000; padding: 6px 3px; }
                thead { display: table-header-group; }
                tr { page-break-inside: avoid; }
                .print-title { font-size: 35px; margin: 10px 0; text-align: center; }  
                .print-date { font-size: 20px;font-family: 宋体;  margin: 30px 0 20px 0;   }  
                .print-signcenter { border-bottom: 1px solid #000000; width: 150px; display: inline-block; margin-right: 20px; }  
                .tdCenter { text-align: center; }
            </style>
        </head>
        <body>
            ${printContent}
        </body>
        </html>
    `);
    iframeDoc.close();

    // 等待内容加载完成后打印
    iframe.onload = function () {
        setTimeout(() => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();

            // 打印完成后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 100);
        }, 200);
    };
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="handleNativePrint">打印</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="width: 880px;" id="printArea">
            <div class="print-title">
                <span style="font-size: 35px;">
                    中小学危险化学品安全综合治理检查表
                </span>
            </div>
            <div class="print-date">
                学校名称（盖章）:<div class="print-signcenter" style="width: 150px;"> &nbsp;</div>
            </div>

            <!-- <div style='width:880px;height:300px;margin-top:30px;'> -->
            <table style="height:300px;border-bottom: none;" class="print-table">
                <tr>
                    <td width='150px;' height='30' class='tdCenter'><b>检查任务名称</b></td>
                    <td width='290px;' height='30'></td>
                    <td width='150px;' height='30' class='tdCenter'><b>检查日期</b></td>
                    <td width='290px;' height='30'></td>
                </tr>
                <tr>
                    <td class='tdCenter' height='30'><b>检查组领导人员</b></td>
                    <td colspan='3'></td>
                </tr>
                <tr>
                    <td class='tdCenter' height='30'><b>检查组专家人员</b></td>
                    <td colspan='3'></td>
                </tr>
                <tr>
                    <td class='tdCenter' height='30'><b>检查组工作人员</b></td>
                    <td colspan='3'></td>
                </tr>
                <tr>
                    <td height='180' class='tdCenter' style="border-bottom: none !important"><b>检查组意见</b></td>
                    <td colspan='3' style="border-bottom: none !important">
                        <div style='margin-top:-5px;'><b>反馈意见：</b></div><br /><br /><br /><br /><br /><br />
                        <b>检查组签字：</b>
                    </td>
                </tr>
            </table>
            <!-- </div> -->
            <table class="print-table">
                <tr>
                    <td colspan='7' height='44' class='tdCenter'><b>问题与隐患清单</b></td>
                </tr>
                <thead>
                    <tr>
                        <th width='60' height='44' class='tdCenter'>类别</th>
                        <th width='320' height='44' class='tdCenter'>项目</th>
                        <th width='60' height='44' class='tdCenter'>性质</th>
                        <th width='90' height='44' class='tdCenter'>危险等级</th>
                        <th width='100' height='44' class='tdCenter'>存在问题隐患</th>
                        <th width='90' height='44' class='tdCenter'>整改期限</th>
                        <th width='160' height='44' class='tdCenter'>整改建议</th>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="(row, index) in tableData" :key="index">
                        <tr>
                            <td class='tdCenter' :rowspan="getRowspan(index)" v-if="shouldShowUserNameCell(index)">{{
                                row.DicName }}</td>
                            <td>{{ row.Name }}</td>
                            <td class='tdCenter'>{{ row.Nature == 2 ? '隐患' : '问题' }}</td>
                            <td class='tdCenter'>{{ row.Nature == 2 ? '较大' : row.Nature == 2 ? '重大' : '一般' }}</td>
                            <td class='tdCenter'>口是 口否</td>
                            <td class='tdCenter'> </td>
                            <td class='tdCenter'> </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

    </div>
</template>
<style>
/* 打印样式 - 确保合并行不被分割 */
@media print {

    /* 强制表格在分页时保持行完整 */
    table.print-table {
        page-break-inside: auto;
    }

    /* 确保合并行不会被分割到不同页 */
    tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    /* 表头每页重复 */
    thead {
        display: table-header-group;
    }

    /* 合并单元格的边框处理 */
    td[rowspan] {
        border-bottom: 1px solid #000 !important;
    }

    /* 确保最后一行边框完整 */
    tr:last-child td {
        border-bottom: 1px solid #000 !important;
    }


}

.print-title {
    font-size: 35px;
    font-family: 宋体;
    margin: 10px 0;
    text-align: center;
}

.print-date {
    font-size: 22px;
    font-family: 宋体;
    margin: 30px 0 20px 0;
}

/* 屏幕显示样式（保持不变） */
.print-table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

.print-table th,
.print-table td {
    border: 1px solid #000;
    padding: 3px;
}
</style>
<style lang="scss" scoped>
table {
    border-collapse: collapse;
    width: 100%;
    font-family: 宋体;
    font-size: 15px;
}

table th {
    border: 1px solid #000;
    padding: 10px 1px;
}

table td {
    border: 1px solid #000;
    padding: 6px 3px;
}


.print-signcenter {
    border-bottom: 1px solid #000000;
    display: inline-block;
    margin-right: 20px;
}


.tdCenter {
    text-align: center;
}
</style>