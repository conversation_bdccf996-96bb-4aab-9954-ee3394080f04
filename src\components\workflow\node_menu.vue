<script setup>
import { onMounted, ref } from 'vue'
import { VueDraggableNext } from "vue-draggable-next";

// 定义父组件传过来的值
const props = defineProps({
    processNodeList: {
        typeof: Array,
        default: []
    }
});
const nodeList = ref(props.processNodeList)
const mousePosition = ref({
    left: -1,
    top: -1
})

const draggableOptions = ref({
    preventOnFilter: false,
    sort: false,
    disabled: false,
    ghostClass: 'tt',
    // 不使用H5原生的配置
    forceFallback: true,
    // 拖拽的时候样式
    // fallbackClass: 'flow-node-draggable'
})
// const menuList = ref([
//     {
//         id: '1',
//         type: 'group',
//         name: '开始节点',
//         ico: 'el-icon-video-play',
//         open: true,
//         children: [
//             {
//                 id: '0',
//                 type: 'start',
//                 name: '流程开始',
//                 ico: 'el-icon-time',
//                 // 自定义覆盖样式
//                 style: {}
//             },
//             {
//                 id: '1',
//                 type: 'end',
//                 name: '流程结束',
//                 ico: 'el-icon-switch-button',
//                 // 自定义覆盖样式
//                 style: {}
//             }, {
//                 id: '2',
//                 type: 'node',
//                 name: '流程节点',
//                 ico: 'el-icon-news',
//                 // 自定义覆盖样式
//                 style: {}
//             }
//         ]
//     }])
const nodeMenu = ref({})

onMounted(() => {
    // 以下是为了解决在火狐浏览器上推拽时弹出tab页到搜索问题
    if (isFirefox()) {
        document.body.ondrop = function (event) {
            // 解决火狐浏览器无法获取鼠标拖拽结束的坐标问题
            mousePosition.value.left = event.layerX
            mousePosition.value.top = event.clientY - 50
            event.preventDefault();
            event.stopPropagation();
        }
    }
})

// // 根据类型获取左侧菜单对象
// const getMenuByType = (type) => {
//     for (let i = 0; i < menuList.value.length; i++) {
//         let children = menuList.value[i].children;
//         for (let j = 0; j < children.length; j++) {
//             if (children[j].type === type) {
//                 return children[j]
//             }
//         }
//     }
// }

// 根据类型获取左侧菜单对象
const getMenuByType = (id) => {
    for (let i = 0; i < nodeList.value.length; i++) {
        if (nodeList.value[i].Id === id) {
            return nodeList.value[i]
        }
    }
}
const emit = defineEmits(['addNode']);
// 拖拽开始时触发
const move = (e) => {
    console.log("拖拽开始时触发", e);
    const draggedItemId = e.oldIndex !== undefined ? nodeList.value[e.oldIndex].Id : null;
    console.log('Dragged item ID:', draggedItemId);
    nodeMenu.value = getMenuByType(draggedItemId)
    // var type = e.item.attributes.type.nodeValue
    // nodeMenu.value = getMenuByType(type)
}
// 拖拽结束时触发
const end = (e) => {
    console.log("拖拽结束时触发", e);
    emit('addNode', e, nodeMenu.value, mousePosition.value)
}
// 是否是火狐浏览器
const isFirefox = () => {
    var userAgent = navigator.userAgent
    if (userAgent.indexOf("Firefox") > -1) {
        return true
    }
    return false
}

</script>
<template>
    <div class="flow-menu" ref="tool">
        <div>
            <div class="ef-node-pmenu-item"><i class="el-icon-notebook-2"></i>节点配置</div>
            <ul class="ef-node-menu-ul">
                <vue-draggable-next v-model="nodeList" @end="end" @start="move" :options="draggableOptions">
                    <li v-for="item in nodeList" class="ef-node-menu-li" :key="item.Id">
                        <!-- <i :class="item.ico"></i> -->
                        <!-- <i class="el-icon-news"></i>  -->
                        {{ item.NodeName }}
                    </li>
                </vue-draggable-next>
            </ul>
        </div>
    </div>
</template>
<style lang="scss" scoped>
@import "./index.css";
</style>
