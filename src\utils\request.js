import axios from 'axios'
import { ElLoading, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
import router from '../router'
const baseURL = ''
//数据请求用
const instance = axios.create({
  baseURL,
  timeout: 600000
})
// 刷新token专用
const serverRefreshToken = axios.create({
  baseURL,
  timeout: 30000
})
//获取新token的方法
async function getNewToken() {
  const res = await serverRefreshToken.request({
    url: '/api/hyun/login/refreshToken?token=' + userStore.token,
    method: 'get'
  })
  loadingInstance.close() //关闭loading
  console.log('重新获取token', res)
  if (res.data.flag == 1) {
    userStore.setToken(res.data.data.rows.token)
    userStore.setExpiresin(res.data.data.rows.expires_in * 1000)
    return true
  } else {
    ElMessage.error(res.data.message || '登录失效，请重新登录')
    userStore.logout()
    router.push('/login')
    return false
  }
}
var loadingCount = 0
var loadingInstance = null
var baseURLPath = ''
//这里是正常获取数据用的请求拦截器，主要作用是给所有请求的请求头里加上token
instance.interceptors.request.use(
  (config) => {
    if (
      config.ext === undefined ||
      config.ext.loading === undefined ||
      config.ext.loading === true
    ) {
      loadingCount++
      loadingInstance = ElLoading.service({
        lock: true,
        text: '系统正在加载数据中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }
    if (userStore.token) {
      config.headers.Authorization = userStore.token_type + ' ' + userStore.token
    }
    baseURLPath = config.url

    return config
  },
  (error) => {
    Promise.reject(error)
  }
)
// 加上防止token过期后正好短时间内多个请求重复刷新token，刷新token成功再请求
let isRefreshing = false
let refreshFnArr = []
instance.interceptors.response.use(
  async (res) => {
    // console.log("res", res)
    if (loadingCount > 0) {
      loadingCount--
      if (loadingCount <= 0) {
        loadingInstance.close()
      }
    }
    // console.log("res.data", res.data)
    if (res.data.flag >= 0 || res.data.success) {
      // console.log("res.data", res.data)
      return res
    } else {
      ElMessage.error(res.data.msg || '请求出错')
      return Promise.reject(res.data)
    }
  },
  async (err) => {
    // console.log("请求失败err", err)
    if (loadingCount > 0) {
      loadingCount--
      if (loadingCount <= 0) {
        loadingInstance.close()
      }
    }
    // token失效后获取新token 添加请求头并重新调用
    if (err.response?.status === 401 || err.response?.status === 403) {
      // router.push('/login')
      if (!isRefreshing) {
        // 如果正好段时间内触发了多个请求
        isRefreshing = true
        let bl = await getNewToken()
        if (bl) {
          refreshFnArr.forEach((fn) => {
            fn()
          })
          refreshFnArr = []
          // console.log("err.config", err.config)
          err = await instance.request(err.config)
          isRefreshing = false
          return err
        }
      } else {
        return new Promise((resolve) => {
          refreshFnArr.push(() => {
            resolve(err.config)
          })
        })
      }
    } else {
      // 错误默认情况
      if (err.response && err.response.data) {
        ElMessage.error(err.response.data.msg)
      } else {
        ElMessage.error('服务异常')
      }
      // router.push('/login')
      return Promise.reject(err)
    }
  }
)
//这里是刷新token专用的axios对象，他的作用是给请求加上刷新token专用的refreshToken
serverRefreshToken.interceptors.request.use(
  (config) => {
    console.log('serverRefreshToken', config)
    loadingInstance = ElLoading.service({
      lock: true,
      text: '正在重新获取用户信息中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    if (userStore.token) {
      config.headers.Authorization = 'Bearer ' + userStore.token
    }
    baseURLPath = config.url
    return config
  },
  (error) => {
    console.log('接口错误', error)
    router.push('/login')
    Promise.reject(error)
  }
)
export default instance
export { baseURL }
