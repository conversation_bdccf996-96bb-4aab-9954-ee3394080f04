<script setup>
import { onMounted, ref, watch, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
  getPermissionTable, removePermission, editPermission, addPermission, getPermissionTree, getPermissionGetModule, postPermissionPostApi, delPermissionDeleteApi
} from '@/api/assign.js'
import {
  getModuleListPage
} from '@/api/module.js'
import { svgIconsAllList } from "@/utils/icons.js";
// import { useUserStore } from '@/stores';
// const userStore = useUserStore()
// const { proxy } = getCurrentInstance()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
// const selectRows = ref([])
// const currentRow = ref()
const filters = ref({ page: 1 })
const modules = ref([])
const menuTrees = ref([])
// const HandleSelectChange = (selection) => {
//   selectRows.value = selection
// }
// const dialogCheck = (selection, row) => {
//   currentRow.value = null;
//   proxy.$refs.refTable.clearSelection();
//   if (selection.length === 0) {
//     return;
//   }
//   if (row) {
//     selectCurrentRow(row);
//   }
// }
// const selectCurrentRow = (val) => {
//   if (val) {
//     currentRow.value = val;
//     proxy.$refs.refTable.clearSelection();
//     proxy.$refs.refTable.toggleRowSelection(val, true);
//   }
// }

const load = (tree, treeNode, resolve) => {
  let para = {
    page: filters.value.page,
    f: tree.Id,
    key: filters.value.key,
  };

  getPermissionTable(para).then((res) => {
    // resolve(res.data.response);
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      resolve(rows.data);
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 翻页
watch(() => filters.value.page, () => {
  HandleSearch()
})
watch(() => filters.value.size, () => {
  filters.value.page = 1
  HandleSearch()
})

//加载数据
onMounted(() => {
  HandleSearch()
  // GetModuleListPage()
  GetPermissionTree()
})

// 获取接口列表
const GetModuleListPage = () => {
  getModuleListPage({ page: -1 }).then((res) => {
    modules.value = res.data.response.data;
  });
}
// 获取菜单列表(不含接口按钮)
const GetPermissionTree = () => {
  let para = { needbtn: false }
  getPermissionTree(para).then((res) => {
    // menuTrees.value = [res.data.response];
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      menuTrees.value = [rows];
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
  Name: [
    { required: true, message: '菜单名称不能为空', trigger: 'change' },
  ],

  Pid: [
    { required: true, message: '父级菜单不能为空', trigger: 'change' },
  ]
}
const addCodeDisabled = ref(false)
const clkTypeEdit = () => {
  formData.value.IsButton = false
  addCodeDisabled.value = false;

  if (formData.value.MenuType == "页面") {
    formData.value.Code = "";
  } else if (formData.value.MenuType == "目录") {
    formData.value.Code = "@url";
    addCodeDisabled.value = true;
  }


}
//新增
const HandleAdd = () => {
  formData.value = { Enabled: true, Pid: "0" }
  // formData.value.CreateBy=userStore.userInfo.AcctName;
  // formData.value.CreateId=userStore.userInfo.Id;
  formData.value.IsDeleted = false;

  dialogVisible.value = true
}
//编辑
const HandleEdit = (row) => {
  formData.value = JSON.parse(JSON.stringify(row))
  if (formData.value.Code.toLowerCase() == '@url') {
    formData.value.MenuType = "目录"
    addCodeDisabled.value = true;
  } else {
    formData.value.MenuType = "页面"
    addCodeDisabled.value = false;
  }

  dialogVisible.value = true
}

//删除
const HandleDel = (row) => {

  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      if (!row.IsButton) {
        removePermission({ id: row.Id }).then((res) => {
          HandleSearch()
          ElMessage.success('删除成功')
        })
      } else {
        // 删除API 
        delPermissionDeleteApi({ permissionid: row.Pid, moduleid: row.Id }).then(res => {
          if (res.data.flag == 1) {
            HandleSearch()
            ElMessage.success('删除成功')
          } else {
            ElMessage.error(res.data.msg)
          }
        });
      }


    })
    .catch((err) => {
      console.info(err)
    })

}

//提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;

    if (formData.value.Id) {
      //编辑
      editPermission(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '添加成功')
        GetPermissionTree()
      })
    } else {
      //新增
      addPermission(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '编辑成功')
        GetPermissionTree()
      })
    }

  })

}
//搜索
const HandleSearch = (page) => {

  if (page) filters.value.page = page

  getPermissionTable(filters.value).then(res => {
    // tableData.value = res.data.response;
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      tableData.value = rows.data || [];
      tableTotal.value = rows.dataCount;
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 重置
const HandleReset = () => {
  filters.value.key = undefined
  HandleSearch(1)
}

const pidName = ref('')
const permissionid = ref(0)
const apiData = ref([])
const apiVisible = ref(false)
const apiFormData = ref({})
const apiRefForm = ref()
const apiRuleForm = {
  Name: [
    { required: true, message: '菜单名称不能为空', trigger: 'change' },
  ],

  Pid: [
    { required: true, message: '父级菜单不能为空', trigger: 'change' },
  ]
}
// 获取页面对应API接口
const HandleApiAdd = (row) => {
  pidName.value = row.Name
  permissionid.value = row.Id
  apiVisible.value = true
  getPermissionGetModule({ pageSize: 100000, permisionid: row.Id }).then(res => {
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      apiData.value = rows.data || [];
      // tableTotal.value = rows.dataCount;
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 添加Api
const HandleApiSubmit = () => {

  apiRefForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    postPermissionPostApi({ permissionid: permissionid.value, moduleid: apiFormData.value.apiid }).then(res => {
      if (res.data.flag == 1) {
        HandleSearch()
        apiVisible.value = false
        ElMessage.success('添加成功')
        GetPermissionTree()
      } else {
        ElMessage.error(res.data.msg)
      }
    });
  })
}





</script>
<template>
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
        <el-form-item class="flexItem">
          <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
        </el-form-item>
        <div class="verticalIdel"></div>
        <el-form-item class="flexItem">
          <el-input class="flexContent" v-model.trim="filters.key" placeholder="请输入菜单/API" clearable />
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" :icon="Search" @click="HandleSearch(1)">搜索</el-button>
          <el-button :icon="Refresh" @click="HandleReset(1)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
  <el-table ref="refTable" :data="tableData" highlight-current-row border stripe lazy :load="load" row-key="Id"
    class="custom-tbl" header-cell-class-name="headerClassName">
    <el-table-column type="selection" width="50"></el-table-column>
    <el-table-column prop="Name" label="菜单/API" min-width="220" show-overflow-tooltip>
      <template #default="{ row }">
        <!-- <i class="fa" :class="row.Icon"></i> -->
        <SvgIcon :name="row.Icon" class="svg-icon" color="#606266" v-if="row.Icon" style="vertical-align: middle;">
        </SvgIcon>
        {{ row.Name }}
      </template>
    </el-table-column>
    <el-table-column prop="Code" label="路由地址" min-width="180"></el-table-column>
    <el-table-column prop="IsButton" label="是否API" width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="!row.IsButton ? 'success' : 'danger'" disable-transitions>
          {{ !row.IsButton ? "否" : "是" }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="MName" label="API接口" min-width="220"></el-table-column>
    <!-- <el-table-column prop="Func" label="API" min-width="140"></el-table-column> -->
    <el-table-column prop="OrderSort" label="序号" width="120" align="center"></el-table-column>
    <el-table-column prop="IsHide" label="是否隐藏" width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="!row.IsHide ? 'success' : 'danger'" disable-transitions>{{ !row.IsHide ? "否" : "是"
        }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="180" align="center">
      <template #default="{ row }">
        <el-button v-if="!row.IsButton && row.Code.toLowerCase() != '@url'" type="primary" link
          @click="HandleApiAdd(row)">添加API</el-button>
        <el-button v-if="!row.IsButton" type="primary" link @click="HandleEdit(row)">修改</el-button>
        <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
      </template>
    </el-table-column>
    <!-- <el-table-column prop="IskeepAlive" label="keepAlive" width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="!row.IskeepAlive ? 'success' : 'danger'" disable-transitions>
          {{ !row.IskeepAlive ? "否" : "是" }}</el-tag>
      </template>
    </el-table-column> -->
    <!-- <el-table-column prop="CreateTime" label="创建时间" min-width="140" align="center">
      <template #default="{ row }">
        {{ row.CreateTime ? row.CreateTime.substring(0, 10) : '--' }}
      </template>
    </el-table-column> -->
    <!-- <el-table-column prop="ModifyTime" label="更新时间" min-width="140" align="center">
      <template #default="{ row }">
        {{ row.ModifyTime ? row.ModifyTime.substring(0, 10) : '--' }}
      </template>
    </el-table-column> -->
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>
  <el-row>
    <el-col class="flexBox">
      <el-pagination class="flexItem" size="small" background layout="prev, pager, next" :total="tableTotal"
        v-model:current-page="filters.page" :page-size="50" />
    </el-col>
  </el-row>

  <el-dialog v-model="dialogVisible" :title="formData.Id ? '编辑' : '添加'" width="680px">
    <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="80px" status-icon>
      <el-form-item label="菜单名称" prop="Name">
        <el-input v-model="formData.Name" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="菜单类型">
        <el-radio-group @change="clkTypeEdit" v-model="formData.MenuType">
          <el-radio value="目录">目录</el-radio>
          <el-radio value="页面">页面</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="路由地址" prop="Code">
        <el-input v-model="formData.Code" :disabled="addCodeDisabled" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="图标" prop="Icon">
        <!-- <el-input v-model="formData.Icon" auto-complete="off"></el-input> -->
        <el-select v-model="formData.Icon" placeholder="请选择图标">
          <template #label="{ label, value }">
            <SvgIcon :name="value" class="svg-icon"> </SvgIcon>
          </template>
          <el-option v-for="t in svgIconsAllList" :key="t.icon" :label="t.name" :value="t.icon">
            <template #default>
              <div style="display: flex; align-items: center;">
                <SvgIcon :name="t.icon" class="svg-icon"> </SvgIcon>
                <span style="padding-left: 5px;">{{ t.name }}</span>
              </div>
            </template>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="Enabled">
        <el-select v-model="formData.Enabled" placeholder="请选择状态">
          <el-option label="激活" :value="true"></el-option>
          <el-option label="禁用" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="OrderSort">
        <el-input v-model.number="formData.OrderSort" type="number" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item prop="IsHide" label="隐藏菜单">
        <el-switch v-model="formData.IsHide"></el-switch>
      </el-form-item>

      <el-form-item prop="Pid" label="父级菜单">
        <el-tree-select v-model="formData.Pid" :data="menuTrees" filterable clearable :check-strictly="true" />
      </el-form-item>
      <!-- <el-form-item prop="IskeepAlive" label="keepAlive" width sortable>
        <el-switch v-model="formData.IskeepAlive"></el-switch>
      </el-form-item> -->
      <el-form-item label="描述" prop="Description">
        <el-input v-model="formData.Description" auto-complete="off"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="apiVisible" title="添加api" width="680px">
    <el-form @submit.prevent ref="apiRefForm" :model="apiFormData" :rules="apiRuleForm" label-width="80px" status-icon>
      <el-form-item label="父级页面">
        <span>{{ pidName }}</span>
      </el-form-item>
      <el-form-item prop="apiid" label="API接口">
        <el-select style="width: 100%" v-model="apiFormData.apiid" placeholder="请选择API" filterable>
          <el-option v-for="item in apiData" :key="item.Id" :value="item.Id" :label="item.LinkUrl">
            <span style="float: left">{{ item.LinkUrl }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ item.Name }}</span>
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="apiVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleApiSubmit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}

.custom-tbl :deep(th .el-checkbox) {
  display: none;
}
</style>