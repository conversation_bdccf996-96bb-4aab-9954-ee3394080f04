<script setup>
defineOptions({
    name: 'dangerchemicalsemergencyplanlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { EmergencyPlanFind, EmergencyPlanInsertUpdate, EmergencyPlanEdit, EmergencyPlanDelete } from '@/api/dangerchemicals.js'
import { AttachmentUpload } from '@/api/user.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { ElMessageBox, ElMessage } from 'element-plus';
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const SchoolId = ref(0)
//表格参数
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })

//培训对象
const traineesObjList = ref([
    { Id: '学生', Name: '学生', },
    { Id: '老师', Name: '老师', }
])
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const regDategeChange = (val) => {
    if (!val) filters.value.EffectiveDatege = undefined
    HandleTableData()
}
const EndDate = ref("");
const regDateleChange = (val) => {
    if (val) {
        filters.value.EffectiveDatele = val + " 23:59:59"
    } else {
        filters.value.EffectiveDatele = undefined
    }
    HandleTableData()
}

//加载数据
onMounted(() => {
    SchoolId.value = route.query.SchoolId || 0
    if (route.query.isTagRouter) {
        HandleTableData();
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    SchoolId.value = route.query.SchoolId || 0
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData();
        }
    })
})

const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)
const isAdd = ref(true)

//验证
const ruleForm = {
    Name: [
        { required: true, message: '事故演练项目名称', trigger: 'change' },
    ],
    PersonCharge: [
        { required: true, message: '请填写实施负责人', trigger: 'change' },
    ],
    Address: [
        { required: true, message: '请填写实施地点', trigger: 'change' },
    ]
}

// 多选下拉/复选框：DefaultName赋值
const multipleSelectChange = (e, item, index) => {
    // console.log('selectChange', e, item, index)
    const labels = e.map(id => String(id)).map(id => {
        const items = item.data.find(item => item.value === id);
        return items ? items.label : '';
    });
    item.DefaultName = labels.join(',');
}

// 添加
const HandleAdd = (row, e) => {
    num.value = e
    dialogVisible.value = true;
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = { TraineesArr: [] };
    })
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    });
}
const num = ref(0)
// 修改弹出窗体
const HandleEdit = (row, e) => {
    num.value = e
    isAdd.value = false
    dialogVisible.value = true
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    });
    EmergencyPlanEdit({ Id: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows
            if (rows.Trainees) {
                dialogData.value.TraineesArr = rows.Trainees.split(',');
            } else {
                dialogData.value.TraineesArr = [];
            }
            console.log(other);
            let categoryList = other || []
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要删除该数据信息吗?')
        .then(() => {
            EmergencyPlanDelete({ Id: row.Id }).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 提交
const HandleSubmit = () => {

    const attIdArr = [];
    uploadFileData.value.map(function (item, index) {
        if (item.categoryList.length > 0) {
            item.categoryList.map(function (olditem, oldindex) {
                attIdArr.push(olditem.Id);
            });
        }
        if (item.fileLChildList.length > 0) {
            item.fileLChildList.map(function (newitem, newindex) {
                attIdArr.push(newitem.Id);
            });
        }
    });
    dialogData.value.AttachmentList = attIdArr;
    dialogData.value.Trainees = dialogData.value.TraineesArr.join(',');
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        EmergencyPlanInsertUpdate(dialogData.value).then(res => {
            if (res.data.flag == 1) {
                dialogVisible.value = false
                dialogVisible.value = false
                ElMessage.success(res.data.msg || '保存成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })

}

// 列表
const HandleTableData = () => {
    EmergencyPlanFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.EffectiveDatege = undefined;
    filters.value.EffectiveDatele = undefined;
    filters.value.Name = undefined;
    filters.value.Trainees = undefined;
    EndDate.value = '';
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

//附件上传 
const uploadFileData = ref([
    { FileCategory: 2962, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, Memo: "文件小于5M，支持pdf和图片文件", Name: "事故应急预案内容：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" },
    { FileCategory: 2963, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, Memo: "文件小于5M，支持pdf和图片文件", Name: "演练现场照片：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" }
])
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.EffectiveDatege) return false;
    return time < new Date(filters.value.EffectiveDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item v-if="SchoolId == 0" class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.EffectiveDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="实施时间"
                            @change="regDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="实施时间" @change="regDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.Trainees" clearable placeholder="演练对象" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in traineesObjList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="事故演练项目名称" style="width: 180px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="事故演练项目名称" min-width="180"></el-table-column>
            <el-table-column prop="PersonCharge" label="实施负责人" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Address" label="实施地点" min-width="140"></el-table-column>
            <el-table-column prop="EffectiveDate" label="实施时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.EffectiveDate ? row.EffectiveDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Trainees" label="演练对象" min-width="120" align="center"></el-table-column>
            <el-table-column prop="RegTime" label="填报时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegTime ? row.RegTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button v-if="SchoolId == 0" type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button v-if="SchoolId == 0" type="primary" link @click="HandleDel(row)">删除</el-button>
                    <el-button v-else type="primary" link @click="HandleEdit(row, 3)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="信息维护">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px"
                    status-icon>
                    <el-form-item label="事故演练项目名称：" prop="Name">
                        <el-input :disabled="SchoolId > 0" v-model="dialogData.Name" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="实施负责人：" prop="PersonCharge">
                        <el-input :disabled="SchoolId > 0" v-model="dialogData.PersonCharge"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="实施地点：" prop="Address">
                        <el-input :disabled="SchoolId > 0" v-model="dialogData.Address" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="实施时间：" prop="EffectiveDate">
                        <el-date-picker :disabled="SchoolId > 0" type="date" placeholder="选择日期"
                            v-model="dialogData.EffectiveDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            style="width: 80%;"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="演练对象：" prop="Trainees">
                        <el-select :disabled="SchoolId > 0" v-model="dialogData.TraineesArr"
                            @change="multipleSelectChange($event, item, index)" filterable multiple clearable
                            style="width: 80%;">
                            <el-option v-for="item1 in traineesObjList" :key="item1.Id" :label="item1.Name"
                                :value="item1.Id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <el-upload v-if="!SchoolId" ref="uploadRef" class="upload-demo" :show-file-list="false"
                            :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                            :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                            <el-button type="success" size="small" :icon="UploadFilled"
                                @click="MaxFileNumberClick(item)">上传</el-button>
                        </el-upload>
                        <div class="fileFlex">
                            <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                                <el-icon v-if="!SchoolId" color="#F56C6C" style="cursor: pointer;"
                                    @click="delCateFile(itemCate, index)">
                                    <Delete />
                                </el-icon>
                                <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                                    {{ itemCate.Title }}{{ itemCate.Ext }}
                                </span>
                            </div>
                            <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                                <el-icon v-if="!SchoolId" color="#F56C6C" style="cursor: pointer;"
                                    @click="delFile(itemChild, index)">
                                    <Delete />
                                </el-icon>
                                <span style="cursor: pointer;"
                                    @click="fileListDownload(itemChild, item.fileLChildList)">
                                    {{ itemChild.Title }}{{ itemChild.Ext }}
                                </span>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button v-if="SchoolId == 0" type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>