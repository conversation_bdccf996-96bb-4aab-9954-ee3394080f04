<template>
    <div class="avatar-container" @click="editImage()">
        <el-dialog :title="title" :model-value="dialogVisibleCorpper" width="800px" append-to-body @opened="openDialog"
            :before-close="beforeClose">
            <el-row>

                <!-- :autoCropWidth="options.autoCropWidth" :autoCropHeight="options.autoCropHeight" -->
                <el-col :span="12" style="height: 300px">
                    <vue-cropper style="width: 100%; height: 100%;" ref="cropper" :img="options.img"
                        :outputSize="options.outputSize" :outputType="options.outputType" :info="options.info"
                        :canScale="options.canScale" :autoCrop="options.autoCrop" :autoCropWidth="options.autoCropWidth"
                        :autoCropHeight="options.autoCropHeight" :fixedBox="options.fixedBox" :fixed="options.fixed"
                        :fixedNumber="options.fixedNumber" :canMove="options.canMove" :canMoveBox="options.canMoveBox"
                        :original="options.original" :centerBox="options.centerBox" :infoTrue="options.infoTrue"
                        :full="options.full" :enlarge="options.enlarge" :mode="options.mode" @realTime="realTime"
                        v-if="showCropper">
                    </vue-cropper>



                </el-col>
                <el-col :span="12" style="height: 300px">
                    <!-- <div class="preview-box">
                        <img v-if="previews.url" :src="previews.url" :style="previews.img" />
                        <span v-else></span>
                    </div> -->
                    <!-- <div class="preview-boxRadius">
                        <img v-if="previews.url" :src="previews.url" :style="previews.img" />
                        <span v-else></span>
                    </div> -->

                    <div style="width: 100%;height: 300px; display: flex;">
                        <div :style="previewFileStyle6">
                            <img :style="previews.img" :src="previews.url" alt="">
                        </div>
                        <div :style="previewFileStyle3">
                            <img :style="previews.img" :src="previews.url" alt="">
                        </div>
                    </div>
                    <div style="width: 300px; position: absolute;right: 50px;bottom: 0;">

                        <!-- <el-button-group>
                            <el-button v-for="(t, i) in btnList" :key="i" @click="btnCrop(t.value)">{{ t.label
                                }}</el-button>
                        </el-button-group> -->
                    </div>
                </el-col>

            </el-row>

            <el-row style="margin-top: 12px">
                <el-col :span="12">
                    <el-row>
                        <el-col :span="8">
                            <el-upload action="#" :http-request="() => { }" accept=".jpg,.jpeg,.png"
                                :before-upload="beforeUpload" :show-file-list="false">
                                <el-button>选择</el-button>
                            </el-upload>
                        </el-col>
                        <el-col :span="4">
                            <el-button :icon="Plus" @click="changeScale(1)"></el-button>
                        </el-col>
                        <el-col :span="4">
                            <el-button :icon="Minus" @click="changeScale(-1)"></el-button>
                        </el-col>
                        <el-col :span="4">
                            <el-button :icon="RefreshLeft" @click="rotateLeft()"></el-button>
                        </el-col>
                        <el-col :span="4">
                            <el-button :icon="RefreshRight" @click="rotateRight()"></el-button>
                        </el-col>
                    </el-row>
                </el-col>
                <el-col :span="12" style="height: 40px">

                </el-col>

            </el-row>
            <el-row>
                <el-col :span="12">

                </el-col>
                <el-col :span="4" :offset="8">
                    <el-button type="primary" @click="determine()">提 交</el-button>
                </el-col>

            </el-row>
        </el-dialog>
    </div>
</template>

<script setup>
import { Plus, Minus, RefreshLeft, RefreshRight } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import { getCurrentInstance, ref, nextTick, watch, onMounted } from 'vue'
import {
    AttachmentUpload,
} from '@/api/user.js'
const { proxy } = getCurrentInstance()
const props = defineProps({
    dialogVisibleCorpper: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '上传图片'
    }
})
const btnList = ref([
    { label: '16:9', value: [16, 9] },
    { label: '4:3', value: [4, 3] },
    { label: '1:1', value: [1, 1] },
    { label: '2:3', value: [2, 3] },
    { label: '自由', value: 0 }
])

const filename = ref('图片')//图片名称
const showCropper = ref(false)
const previewFileStyle6 = ref({})
const previewFileStyle3 = ref({})
const inputWidth = ref(300)
const inputHeight = ref(300)
// cropper配置  更多配置可参考 https://www.npmjs.com/package/vue-cropper
const options = ref({
    img: '', // 裁剪图片的地址 url 地址, base64, blob
    outputSize: 1, // 裁剪生成图片的质量
    outputType: 'jpeg', // 裁剪生成图片的格式 jpeg, png, webp
    info: true, // 裁剪框的大小信息
    canScale: true, // 图片是否允许滚轮缩放
    autoCrop: true, // 是否默认生成截图框
    autoCropWidth: 150, // 默认生成截图框宽度
    autoCropHeight: 150, // 默认生成截图框高度
    fixedBox: false, // 固定截图框大小 不允许改变
    fixed: true, // 是否开启截图框宽高固定比例，这个如果设置为true，截图框会是固定比例缩放的，如果设置为false，则截图框的狂宽高比例就不固定了
    fixedNumber: [1, 1], // 截图框的宽高比例 [ 宽度 , 高度 ]
    canMove: true, // 上传图片是否可以移动
    canMoveBox: true, // 截图框能否拖动
    original: false, // 上传图片按照原始比例渲染
    centerBox: true, // 截图框是否被限制在图片里面
    infoTrue: false, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
    full: true, // 是否输出原图比例的截图
    enlarge: '1', // 图片根据截图框输出比例倍数
    mode: 'contain' // 图片默认渲染方式 contain , cover, 100px, 100% auto
})
const previews = ref({
    url: ''
})
watch(() => props.dialogVisibleCorpper, () => {
    if (!props.dialogVisibleCorpper) {
        options.value.img = ''
        previews.value.url = ''
    }
})

const btnCrop = (value) => {
    if (value != 0) {
        options.value.fixed = true
        options.value.fixedBox = false
        options.value.fixedNumber = value
    } else {
        options.value.fixed = false
        options.value.fixedBox = false
    }

    nextTick(() => {
        proxy.$refs.cropper.goAutoCrop()
    });
}
// 打开裁剪弹窗
const openDialog = () => {
    showCropper.value = true
}
// 修改图片大小 正数为变大 负数变小
const changeScale = (num) => {
    num = num || 1
    proxy.$refs.cropper.changeScale(num)
}
// 向左边旋转90度
const rotateLeft = () => {
    proxy.$refs.cropper.rotateLeft()
}
// 向右边旋转90度
const rotateRight = () => {
    proxy.$refs.cropper.rotateRight()
}
// 上传图片处理
const beforeUpload = (rawFile) => {
    // console.log("rawFile", rawFile)
    if (rawFile.type.indexOf('image/') == -1) {
        ElMessage.error('请上传图片类型文件!')
        return false
    }
    if (rawFile.size / 1024 / 1024 > 10) {
        ElMessage.error('文件大小不能超过10MB!')
        return false
    }
    filename.value = rawFile.name

    const reader = new FileReader()
    reader.readAsDataURL(rawFile)
    reader.onload = () => {
        // 图片在这里
        options.value.img = reader.result
    }
}
// 实时预览事件
const realTime = (data) => {
    previews.value = data

    inputWidth.value = data.w
    inputHeight.value = data.h
    // tempScale.value = props.previewWidth / data.w
    previewFileStyle6.value = {
        width: data.w + 'px',
        height: data.h + 'px',
        margin: 0,
        overflow: 'hidden',
        // zoom: tempScale.value,
        zoom: 0.6,
        position: 'relative',
        border: '1px solid #e8e8e8',
        'border-radius': '2px'
    }
    previewFileStyle3.value = {
        width: data.w + 'px',
        height: data.h + 'px',
        margin: 0,
        overflow: 'hidden',
        // zoom: tempScale.value,
        zoom: 0.3,
        position: 'relative',
        border: '1px solid #e8e8e8',
        'border-radius': '2px'
    }
    // previews.value.img.width='200px'
    // previews.value.img.height='150px'
}
const emit = defineEmits(['update:dialogVisibleCorpper', 'confirm'])

// 关闭弹窗
const beforeClose = () => {
    console.log('关闭')
    options.value.img = null
    previews.value.url = ''
    emit('update:dialogVisibleCorpper', false)
}

// 提交图片
const determine = () => {
    // 获取截图的 blob 数据
    proxy.$refs.cropper.getCropBlob(blob => {
        // blob 数据转换为 file
        let file = new File([blob], filename.value, { type: blob.type, lastModified: new Date().getTime() });
        Object.defineProperty(file, 'uid', { value: Math.random().toString(36).substring(2), writable: false });
        emit('confirm', file)
    })

    // options.value.img = ''
    // previews.value.url = ''

}
</script>

<style lang='scss' scoped>
.avatar-container {
    .img-box {
        border-radius: 50%;
        border: 1px solid #ccc;
        width: 10vw;
        height: 10vw;
    }
}

.preview-boxRadius {
    position: absolute;
    top: 20px;
    left: 600px;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 1px solid #ccc;
    overflow: hidden;
}

.preview-box {
    position: absolute;
    top: 20px;
    left: 400px;
    width: 150px;
    height: 150px;
    // border-radius: 50%;
    border: 1px solid #ccc;
    overflow: hidden;
}

// .preview-box {
//     position: absolute;
//     top: 20px;
//     left: 400px;
//     width: 300px;
//     height: 300px;
//     // border-radius: 50%;
//     border: 1px solid #ccc;
//     overflow: hidden;
// }</style>