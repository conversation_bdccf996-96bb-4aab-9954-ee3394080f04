<script setup>
defineOptions({
    name: 'dangerchemicalswordguidedist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { DcWorkGuideFind } from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { fileDownload } from "@/utils/index.js";//上传附件
import { AttachmentUpload, AttachmentSave, AttachmentDelete } from '@/api/user.js'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
const route = useRoute()
const userStore = useUserStore()
const unitType = ref(userStore.userInfo.UnitType)
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    //加载表格数据
    HandleTableData();

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

//表格参数
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })

const HandleTableData = () => {
    DcWorkGuideFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.Title = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

//验证
const ruleForm = {
    Title: [
        { required: true, message: '文件标题', trigger: 'change' },
    ]
}

const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)

// 修改弹出窗体
const HandleEdit = (row, e) => {
    dialogVisible.value = true
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    });
}

// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
    })
    const attobj = ref({});
    uploadFileData.value.map(function (item, index) {
        if (item.categoryList.length > 0) {
            item.categoryList.map(function (olditem, oldindex) {
                attobj.value = olditem;
            });
        }
        if (item.fileLChildList.length > 0) {
            item.fileLChildList.map(function (newitem, newindex) {
                attobj.value = newitem;
            });
        }
    });
    const param = {
        Id: attobj.value.Id,
        FileCategory: uploadFileData.value[0].FileCategory,
        ModuleType: uploadFileData.value[0].ModuleType,
        Title: dialogData.value.Title
    };
    console.log(param);
    AttachmentSave(param).then(res => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '保存成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 删除
const HandleDelete = (row, e) => {
    const param = {
        Id: row.Id,
        FileCategory: uploadFileData.value[0].FileCategory,
        ModuleType: uploadFileData.value[0].ModuleType
    };
    console.log(param);
    AttachmentDelete(param).then(res => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '删除成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//附件上传
const uploadFileData = ref([{ ModuleType: 9, FileCategory: 2976, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 1, Memo: "文件小于5M，支持pdf和图片文件", Name: "msds：", UploadFileType: ".pdf", UploadFileTypeAccept: ".pdf" }])
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[0].fileLChildList.push(rows[0])
            //uploadFileData.value[index].fileLChildList.push({Id:rows[0].Id,Path:rows[0].Path,Ext:rows[0].Ext,Title:rows[0].Title})
            // console.log("uploadFileData.value", uploadFileData.value)
            if (!(dialogData.value.Title && dialogData.value.Title.length > 0)) {
                dialogData.value.Title = rows[0].Title;
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }

}

// 附件图片预览与文件下载
const fileOpenDownload = (filePath, title, opttype) => {
    if (opttype == 1) {
        fileDownload(filePath, title)
    } else {
        fileDownload(filePath, title)
    }
}
</script>

<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">

                    <el-form-item class="flexItem" v-if="unitType == 2">
                        <el-button type="primary" :icon="Setting" @click="HandleEdit">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="unitType == 2"></div>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Title" placeholder="文件标题" style="width: 200px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Title" label="文件标题" min-width="140" max-width="260"></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="left">
                <template #default="{ row }">
                    <el-button v-if="false" type="primary" link
                        @click="fileOpenDownload(row.Path, row.Title, 1)">预览</el-button>
                    <el-button type="primary" link @click="fileOpenDownload(row.Path, row.Title, 2)">下载</el-button>
                    <el-button v-if="unitType == 2" type="primary" link @click="HandleDelete(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="信息维护">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" label-width="200px" :rules="ruleForm"
                    status-icon>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                            :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                            :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                            <el-button type="success" size="small" :icon="UploadFilled"
                                @click="MaxFileNumberClick(item)">上传</el-button>
                        </el-upload>
                        <div class="fileFlex">
                            <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id"
                                style="color:#409EFF ;width:200px">
                                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                                    <Delete />
                                </el-icon>
                                <span style="cursor: pointer;"
                                    @click="lookFileListDownload(itemCate.Path, itemCate.Ext, itemCate.Title)">
                                    {{ dialogData.Name }}(MSDS)
                                </span>
                            </div>
                            <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id" style="width:200px">
                                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                                    <Delete />
                                </el-icon>
                                {{ dialogData.Name }}(MSDS)
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="文件标题：" prop="Title">
                        <el-input v-model="dialogData.Title" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>