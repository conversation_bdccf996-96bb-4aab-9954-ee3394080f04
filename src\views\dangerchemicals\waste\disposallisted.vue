<script setup>
defineOptions({
    name: 'dangerchemicalswastedisposallisted'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcWasteDisposalDetailFind, DcWasteClassGet
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const DisposalDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 3, sortModel: [{ SortCode: "Id", SortType: "asc" }] })
const summation = ref({})
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcWasteClassGetUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 1) {
            sums[index] = '总计';
            return;
        }
        if (column.property == 'Num') {
            sums[index] = summation.value.Num;
            return;
        }
    });
    console.log("sums", sums)
    return sums;
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.OneClassId = undefined
    filters.value.BaseWasteId = undefined
    filters.value.CompanyName = undefined
    filters.value.DisposalDatege = undefined
    filters.value.DisposalDatele = undefined
    DisposalDatele.value = undefined
    levelTwoList.value = []
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const disposalDategeChange = (val) => {
    if (!val) filters.value.DisposalDatege = undefined
    HandleTableData()
}
const disposalDateleChange = (val) => {
    if (val) {
        filters.value.DisposalDatele = val + " 23:59:59"
    } else {
        filters.value.DisposalDatele = undefined
    }
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    DcWasteDisposalDetailFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            summation.value = other[0]
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const levelOneList = ref([])
const levelTwoList = ref([])
// 获取危化品一级分类|| 二级分类
const DcWasteClassGetUser = (depth = 0, pid = 0) => {
    DcWasteClassGet({ id: 0, depth: depth, pid: pid }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (pid == 0) {
                levelOneList.value = rows.data || []
            } else {
                levelTwoList.value = rows.data || []
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersOneChange = (e) => {
    filters.value.pageIndex = 1
    filters.value.BaseWasteId = undefined
    DcWasteClassGetUser(1, e)
    HandleTableData()
}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!DisposalDatele.value) return false;
    return time >= new Date(DisposalDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.DisposalDatege) return false;
    return time < new Date(filters.value.DisposalDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.DisposalDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="处置时间"
                            @change="disposalDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="DisposalDatele" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledEndDate" clearable placeholder="处置时间"
                            @change="disposalDateleChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.OneClassId" clearable placeholder="一级分类" style="width: 180px"
                            @change="filtersOneChange">
                            <el-option v-for="item in levelOneList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.BaseWasteId" clearable placeholder="二级分类" style="width: 180px"
                            @change="filtersChange">
                            <el-option v-for="item in levelTwoList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.CompanyName" placeholder="处置企业" style="width: 180px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="OneClassName" label="一级分类" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="二级分类" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"> </el-table-column>
            <el-table-column prop="DisposalDate" label="处置时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.DisposalDate ? row.DisposalDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CompanyName" label="处置企业" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="DeclareUserName" label="申请人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="AuditUserName" label="审核人" min-width="120" align="center"></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>