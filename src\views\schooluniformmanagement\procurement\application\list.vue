<script setup>
defineOptions({
  name: 'applicationlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
  Punitgetschoolbycountyid
} from '@/api/user.js'
import {
  GetUniformBuyList, UniformBuySubmit, UniformBuyDeleteById, UniformBuyAudit, UniformBuyRevoke
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate, formatNumberWithCommas } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const userStore = useUserStore()
const route = useRoute()
const hUnitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const yearDateList = ref([])
const CountyList = ref([])
const SchoolList = ref([])
const StatuzFilingList = ref([])
const OrganizationalList = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
onMounted(() => {
  yearDateList.value = previousYearDate()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

//新增
const HandleAdd = () => {
  router.push({ path: "./edit", query: { id: 0, title: "创建采购申请" } })
}
// 修改
const HandleEdit = (row) => {
  console.log(row.Id)
  router.push({ path: "./edit", query: { id: row.Id, title: "修改采购申请" } })
}
// 审核||退回： 
const HandleRecord = (row, e) => {
  isRecord.value = e
  recordId.value = row.Id
  dialogVisible.value = true
  nextTick(() => {
    dialogData.value = {}
    refForm.value.resetFields()
  })
}
// 撤销
const HandleRevoke = (row) => {
  ElMessageBox.confirm('确定撤销审核结果吗?')
    .then(() => {
      UniformBuyRevoke({ id: row.Id, OptType: 1 }).then(res => {
        if (res.data.flag == 1) {
          ElMessage.success(res.data.msg || '撤销成功')
          HandleTableData()
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//查看
const HandleDetail = (row) => {
  router.push({ path: "./detail", query: { id: row.Id } })
}
// 选择区县
const CountyChange = (e) => {
  if (!e) {
    filters.value.SchoolId = undefined
  }
  HandleTableData()
  PunitgetschoolbycountyidUser(e)
}
//新增&编辑操作
const dialogVisible = ref(false)
const isRecord = ref(true)
const recordId = ref(0)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
  statuz: [
    { required: true, message: '请选择是否通过审核', trigger: 'change' },
  ],
  FilingExplanation: [
    { required: true, message: '请输入原因', trigger: 'change' },
  ],
}

// 提交
const HandleSubmit = (row) => {
  if (hUnitType.value == 3) {
    ElMessageBox.confirm('确定提交备案吗?')
      .then(() => {
        UniformBuySubmit({ id: row.Id }).then((res) => {
          if (res.data.flag == 1) {
            HandleTableData()
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '提交成功')
          } else {
            ElMessage.error(res.data.msg)
          }
        })
      })
      .catch((err) => {
        console.info(err)
      })
  } else if (hUnitType.value == 2) {
    refForm.value.validate((valid, fields) => {
      if (!valid && fields) return;
      if (isRecord.value == 1) {
        PurchaseConfirmfilingUser()

      } else if (isRecord.value == 2) {
        PurchaseFilingrevokedUser()
      }
    })
      .catch((err) => {
        console.info(err)
      })
  }
}

//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      UniformBuyDeleteById({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  GetUniformBuyList(filters.value).then(res => {
    if (res.data.flag == 1) {
      // console.log("采购管理列表", res.data.data)
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        StatuzFilingList.value = other.listStatuz;//备案状态
        OrganizationalList.value = other.listOrganizational;//组织形式
        if (hUnitType.value == 1) {
          CountyList.value = other.listArea || []//区县名称
        }
        if (hUnitType.value == 2) {
          SchoolList.value = other.listSchool || []//学校名称
        }
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.PlanYear = undefined
  filters.value.CountyId = undefined
  filters.value.SchoolId = undefined
  filters.value.IsNeedBidding = undefined
  filters.value.Organizational = undefined
  filters.value.PurchaseStatuz = undefined
  filters.value.PurchaseNo = undefined
  if (hUnitType.value == 1) {
    SchoolList.value = []
  }
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }
// 选择区县获取学校
const PunitgetschoolbycountyidUser = (id) => {
  Punitgetschoolbycountyid({ CountyId: id }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      filters.value.SchoolId = undefined
      SchoolList.value = rows || []//学校名称
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 审核
const PurchaseConfirmfilingUser = (id) => {
  UniformBuyAudit({ id: recordId.value, statuz: dialogData.value.statuz, FilingExplanation: dialogData.value.FilingExplanation }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '提交审核成功')
      dialogVisible.value = false
      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 退回
const PurchaseFilingrevokedUser = (e) => {
  UniformBuyRevoke({ id: recordId.value, FilingExplanation: dialogData.value.FilingExplanation, OptType: 2 }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '操作成功')
      dialogVisible.value = false
      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
</script>
<template>
  <div class="viewContainer">
    <el-collapse>
      <el-collapse-item>
        <template #title>
          操作提示 &nbsp;
          <el-icon color="#E6A23C" :size="16">
            <QuestionFilled />
          </el-icon>
        </template>
        <ol class="rowFill">
          <li> 点击【创建】和【修改】按钮，可编辑校服采购需求；</li>
          <li> 在完成全部信息填报后，点击【提交】向教育局备案，提交后禁止修改数据。 </li>
        </ol>
      </el-collapse-item>
    </el-collapse>
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd" v-if="hUnitType == 3">创建</el-button>
          </el-form-item>
          <div class="verticalIdel" v-if="hUnitType == 3"></div>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.PlanYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称" style="width: 160px"
              @change="CountyChange">
              <el-option v-for="item in CountyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.UnitId" :label="item.UnitName" :value="item.UnitId" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 2">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.IsNeedBidding" clearable placeholder="是否需要招标" @change="filtersChange"
              style="width: 160px">
              <el-option :value="1" label="是" />
              <el-option :value="2" label="否" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.Organizational" clearable placeholder="组织形式" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in OrganizationalList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.PurchaseStatuz" clearable placeholder="备案状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzFilingList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.PurchaseNo" clearable placeholder="采购批次" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>

      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="PlanYear" label="年度" min-width="80" align="center"></el-table-column>
      <el-table-column prop="AreaName" label="区县名称" min-width="140" v-if="hUnitType == 1"></el-table-column>
      <el-table-column prop="SchoolName" label="学校名称" min-width="160"
        v-if="hUnitType == 1 || hUnitType == 2"></el-table-column>
      <el-table-column prop="PurchaseNo" label="采购批次" min-width="140" align="center"></el-table-column>
      <el-table-column prop="Num" label="校服数量（套）" min-width="120" align="center"></el-table-column>
      <el-table-column prop="BudgetAmount" label="预算金额（元）" min-width="140" align="right">
        <template #default="{ row }">
          {{ formatNumberWithCommas(row.BudgetAmount) }}</template>
      </el-table-column>
      <el-table-column prop="IsNeedBidding" label="是否需要招标" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.IsNeedBidding == 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="OrganizationalName" label="组织形式" min-width="140" align="center"></el-table-column>
      <el-table-column prop="PurchaseStatuzName" label="备案状态" min-width="120" align="center"></el-table-column>
      <el-table-column fixed="right" label="采购需求管理" min-width="120" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleEdit(row)"
            v-if="hUnitType == 3 && [0, 11, 21].includes(row.PurchaseStatuz)">修改</el-button>
          <el-button type="primary" link @click="HandleDel(row)"
            v-if="hUnitType == 3 && row.PurchaseStatuz == 0">删除</el-button>
          <el-button type="primary" link @click="HandleDetail(row)"
            v-if="hUnitType == 1 || hUnitType == 2 || row.PurchaseStatuz == 10 || row.PurchaseStatuz == 100">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="采购需求备案" min-width="120" align="center" v-if="hUnitType == 3">
        <template #default="{ row }">
          <el-button v-if="[0, 11, 21].includes(row.PurchaseStatuz)" type="primary" link
            @click="HandleSubmit(row)">提交</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column v-if="hUnitType == 2" fixed="right" label="操作" min-width="100" align="center">
        <template #default="{ row }">
          <el-button v-if="row.PurchaseStatuz == 10" type="primary" link @click="HandleRecord(row, 1)">审核</el-button>
          <el-button v-else-if="row.PurchaseStatuz == 100 && row.IsFiling == 0" type="primary" link
            @click="HandleRecord(row, 2)">退回</el-button>
          <el-button v-else-if="row.PurchaseStatuz == 100 && row.IsFiling == 1" type="primary" link
            @click="HandleRevoke(row)">撤销</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
    <el-dialog v-model="dialogVisible" draggable :title="isRecord == 1 ? '审核' : '退回'" width="560px"
      :close-on-click-modal="false">
      <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" status-icon>
        <el-form-item label="审核结果：" prop="statuz" label-width="120px" v-if="isRecord == 1">
          <el-radio-group v-model="dialogData.statuz">
            <el-radio value="1">通过</el-radio>
            <el-radio value="2">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="isRecord == 1 ? '不通过原因：' : ''" :label-width="isRecord == 1 ? '120px' : '0px'"
          prop="FilingExplanation" v-if="(isRecord == 1 && dialogData.statuz == 2) || isRecord == 2">
          <el-input type="textarea" v-model="dialogData.FilingExplanation" :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入原因"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped></style>