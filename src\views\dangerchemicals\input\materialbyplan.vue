<script setup>
defineOptions({
    name: 'dangerchemicalsinputmaterialbyplan'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    FolderAdd, UploadFilled, QuestionFilled, Position, Select, EditPen
} from '@element-plus/icons-vue'
import {
    DcCabinetAddressGet, DcCompanyComboxFind, DcDepositAddressFind, DcInputMaterialByPlanFind,
    DcMaterialByPlanInput, DcMaterialByPlanSave, DcSchoolMaterialTempDelete, DcSchoolModelBrandGetBrand
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { limit, integerLimit, fileDownload, tagsListStore } from "@/utils/index.js";
import { AttachmentUpload } from '@/api/user.js'
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { useUserStore } from '@/stores';
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const companyList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const dialogVisible = ref(false)
const filters = ref({ pageIndex: 1, Statuz: 100, pageSize: "100000", sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const companyId = ref()
const companyName = ref('')
const dangerChemicalsLevel = ref('')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        AttributeUser()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            AttributeUser()
        }
    })
})

// 列表
const HandleTableData = () => {
    filters.value.PurchaseOrderId = route.query.id;
    DcInputMaterialByPlanFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, footer } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            tableData.value.forEach((item, index) => {
                if (item.TempDepositAddressId = '0') item.TempDepositAddressId = ''
                if (item.TempSchoolMaterialBrandId = '0') item.TempSchoolMaterialBrandId = ''
                if (item.MsdsFile && item.MsdsFile.includes("|")) {
                    //解析出扩展名和名称，路径
                    const fileStrings = item.MsdsFile.split("|");
                    item.MsdsFilePath = fileStrings[0];
                    item.MsdsFileTitle = fileStrings[1];
                    item.Ext = "." + fileStrings[1].split(".").pop();
                }
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存||审核
const HandleSubmit = (e) => {
    listCodeValueFun()
    let find = tableData.value.find(t => !t.TempNum || !t.TempBrand || !t.TempPrice || !t.TempSum || !t.TempDepositAddressId || t.TempDepositAddressId == '0' || !t.MsdsFile)
    if (find) {
        ElMessage.error('请完善数据:请完整填写所有标红的字段，其中单价、数量必须大于0')
        return
    }
    let formData = {
        o: listCodeValueFun(),
        purchaseOrderId: route.query.id,
        companyId: companyId.value,
        companyName: companyName.value
    }
    if (e == 1) {
        DcMaterialByPlanSave(formData).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '保存成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        DcMaterialByPlanInput(formData).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '审核成功')
                let tagsList = userStore.tagsList
                tagsList = tagsList.filter(t => t.path != route.path)
                userStore.setTagsList(tagsList)
                router.push({ path: "./byplanlist" })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}

const listCodeValueFun = () => {
    // console.log("tableData.value", tableData.value)
    let listCodeValue = tableData.value.map(({
        Id,
        SchoolCatalogId,
        SchoolMaterialModelId,
        TempSchoolMaterialBrandId,
        TempNum,
        TempPrice,
        TempValidDate,
        TempWarrantyMonth,
        TempRemark,
        TempDepositAddressId,
        MsdsFile,
        TempBrand,
        TempCabinetAddress
    }) => ({
        PurchaseListId: Id,
        SchoolCatalogId: SchoolCatalogId,
        ModelId: SchoolMaterialModelId,
        BrandId: TempSchoolMaterialBrandId || '0',
        Num: TempNum,
        Price: TempPrice,
        ValidDate: TempValidDate,
        WarrantyMonth: TempWarrantyMonth,
        Remark: TempRemark,
        DepositAddressId: TempDepositAddressId || '0',
        MsdsFile: MsdsFile,
        Brand: TempBrand,
        CabinetAddress: TempCabinetAddress,
    }))
    console.log("listCodeValue", listCodeValue)

    return listCodeValue
}


// 复制
const HandleCopy = (row, index) => {
    tableData.value[index].TempSchoolMaterialBrandId = row.SchoolMaterialBrandId
    if (row.SchoolMaterialBrandId == '0') tableData.value[index].TempSchoolMaterialBrandId = ''
    tableData.value[index].TempBrand = row.TempBrand
    tableData.value[index].TempNum = row.Num
    tableData.value[index].TempPrice = row.Price
    tableData.value[index].TempSum = row.Sum
    tableData.value[index].TempSchoolMaterialModelId = row.SchoolMaterialModelId
}
// 清除
const HandleClear = (row, index) => {
    DcSchoolMaterialTempDelete({ id: row.Id }).then(res => {
        // if (res.data.flag == 1) {
        //     ElMessage.success(res.data.msg || '清除成功')
        HandleTableData()
        // } else {
        //     ElMessage.error(res.data.msg)
        // }
    })
}
const oldId = ref('')
const focusedIndex = ref(-1); // 用来存储当前聚焦的输入框的行索引   
// 点击当前行
const selectCurrentRow = (row, column, e) => {
    if (row.Id === oldId.value) return
    oldId.value = row.Id
    const rowIndex = tableData.value.findIndex(item => item.Id === row.Id);
    console.log(row, rowIndex)
    DcSchoolModelBrandGetBrandUser(row, rowIndex)
}
const handleSelectClick = (row, index, str) => {
    console.log('handleSelectClick', row, index)
    if (row.Id === oldId.value) return
    oldId.value = row.Id
    console.log('handleSelectClick', row, index)
    DcSchoolModelBrandGetBrandUser(row, index)
}
const brandChange = (e, row, index) => {
    // if (row.Id === oldId.value) return
    // oldId.value = row.Id
    console.log('brandChange', e, row, index)
    tableData.value[index].TempBrand = brandList.value.filter(t => t.SchoolMaterialBrandId == e)[0].Brand
    // console.log('brandChange', tableData.value[index].TempBrand)
}
// 供应商选择
const companyChange = (e) => {
    console.log(e)
    companyId.value = e
    companyName.value = companyList.value.filter(t => t.Id == e)[0].Name
}

const brandList = ref([])
const addressList = ref([])
const cabinetAddressList = ref([])
const DcSchoolModelBrandGetBrandUser = (row, index) => {
    // 获取品牌
    DcSchoolModelBrandGetBrand({ schoolCatalodId: row.SchoolCatalogId, modelId: row.SchoolMaterialModelId }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            brandList.value = rows.data || []
            if (brandList.value.length > 0) {
                tableData.value[index].isBrandList = true
            } else {
                tableData.value[index].isBrandList = undefined
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const AttributeUser = () => {

    // 获取学校供应商信息-查询
    DcCompanyComboxFind({ Statuz: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            companyList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
    // 获取存放地点
    DcDepositAddressFind({ Statuz: 1, pageIndex: 0, pageSize: ********* }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            addressList.value = rows.data || []

        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
    // 获取储存柜层次
    DcCabinetAddressGet().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            cabinetAddressList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })

}
//输入框限制：输入整数
const integerLimitInput = (val, row, index, name) => {
    tableData.value[index][name] = integerLimit(val);
}
//输入保留两位小数
const limitInput = (val, row, index, name) => {
    tableData.value[index][name] = limit(val);
}
//数量
const tempNumChange = (val, row, index) => {
    if (val) {
        tableData.value[index].TempSum = val * tableData.value[index].TempPrice
        if (!tableData.value[index].TempPrice) tableData.value[index].TempSum = ''
    } else {
        tableData.value[index].TempSum = ''
    }
}
//单价
const tempPriceChange = (val, row, index) => {
    if (val) {
        tableData.value[index].TempSum = val * tableData.value[index].TempNum
        if (!tableData.value[index].TempNum) tableData.value[index].TempSum = ''
    } else {
        tableData.value[index].TempSum = ''
    }
}

//附件上传
const uploadFileData = ref([{ FileCategory: 2964, fileLChildList: [], categoryList: [], IsFilled: 1, FileSize: 10, MaxFileNumber: 1, Memo: "文件小于5M，支持pdf和图片文件", Name: "MSDS：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" }])
const fileFile = ref()
const uploadRef = ref()

// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log("----2025-07-16 14:03:46--item--:", item);
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data

            tableData.value[index].MsdsFilePath = rows[0].Path;
            tableData.value[index].MsdsFileTitle = rows[0].Title;
            tableData.value[index].Ext = rows[0].Ext;
            tableData.value[index].MsdsFile = rows[0].Path + "|" + rows[0].Title + rows[0].Ext;
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)

// 附件图片预览与文件下载
const lookFileListDownload = (filePath, ext, title) => {
    viewPhotoList.value = [filePath];
    ext = (typeof ext === "string") ? ext.toLowerCase() : "";
    if (ext == ".png" || ext == ".jpg" || ext == ".jpng") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (filePath == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        fileDownload(filePath, title)
    }
}

</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    【 申请批次：{{ route.query.BatchNo }} 】 危化品入库 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 第一步，选择“供应商”； </li>
                    <li> 第二步，填写“采购入库信息”，详见《入库操作说明》； </li>
                    <li> 第三步，点击【提交审核】；提交主管部门审核通过后才能入库。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="dangerChemicalsLevel == 3">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加供应商</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="dangerChemicalsLevel == 3"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="companyName" filterable allow-create default-first-option
                            placeholder="请选择供应商" @change="companyChange" style="width: 200px">
                            <el-option v-for="item in companyList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <div class="verticalDividel"></div>
                        <el-button type="primary" :icon="Select" @click="HandleSubmit(1)">保存</el-button>
                        <div class="verticalDividel"></div>
                        <el-button type="primary" :icon="Position" @click="HandleSubmit(2)">提交审核</el-button>
                        <div class="verticalDividel"></div>
                        <el-button :icon="QuestionFilled" @click="dialogVisible = true">入库操作说明</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName" @row-click="selectCurrentRow">
            <el-table-column label="采购计划信息">
                <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip>
                    <template #default="{ row }">
                        <el-tooltip class="item" effect="dark" :content="row.remark ? row.remark : '无'" placement="top">
                            {{ row.Name }}
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
                <el-table-column prop="UnitName" label="单位" min-width="80" align="center">
                    <template #default="{ row }">
                        <span style="color: #F56C6C;"> {{ row.UnitName }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="Brand" label="品牌" min-width="80" align="center">
                    <template #default="{ row }">
                        {{ row.Brand || '--' }}
                    </template>
                </el-table-column>
                <el-table-column prop="Num" label="数量" min-width="120" align="right"> </el-table-column>
                <el-table-column prop="Price" label="单价" min-width="80" align="center">
                    <template #default="{ row }">
                        {{ row.Price || '--' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="110" align="center">
                    <template #default="{ row, $index }">
                        <el-button type="primary" link @click="HandleCopy(row, $index)">复制</el-button>
                        <el-button type="primary" link @click="HandleClear(row, $index)">清除</el-button>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="采购入库信息（标红的字段为必填项）">
                <el-table-column prop="TempBrand" label="品牌" min-width="120" align="center">
                    <template #header>
                        <span style="color: #F56C6C;">*品牌</span>
                    </template>
                    <template #default="{ row, $index }">
                        <el-select v-if="row.isBrandList" v-model="row.TempSchoolMaterialBrandId" filterable
                            allow-create default-first-option @change="brandChange($event, row, $index)"
                            @click.native.stop="handleSelectClick(row, $index, 'TempBrand')">
                            <el-option v-for="item in brandList" :key="item.SchoolMaterialBrandId" :label="item.Brand"
                                :value="item.SchoolMaterialBrandId" />
                        </el-select>
                        <el-input v-else v-model="row.TempBrand"
                            :class="{ 'input-focused': focusedIndex === $index }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="TempNum" label="数量" min-width="120" align="right">
                    <template #header>
                        <span style="color: #F56C6C;">*数量</span>
                    </template>
                    <template #default="{ row, $index }">
                        <el-input v-model="row.TempNum" @input="limitInput($event, row, $index, 'TempNum')"
                            @change="tempNumChange($event, row, $index)"
                            :class="{ 'input-focused': focusedIndex === $index }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="TempPrice" label="单价" min-width="120" align="center">
                    <template #header>
                        <span style="color: #F56C6C;">*单价</span>
                    </template>
                    <template #default="{ row, $index }">
                        <el-input v-model="row.TempPrice" @input="limitInput($event, row, $index, 'TempPrice')"
                            @change="tempPriceChange($event, row, $index)"
                            :class="{ 'input-focused': focusedIndex === $index }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="TempSum" label="金额" min-width="120" align="right">
                    <template #default="{ row, $index }">
                        <div class="sortDiv">
                            {{ row.TempSum }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="TempSum" label="有效期截止日" min-width="180" align="center">
                    <template #header>
                        <span style="color: #F56C6C;">*有效期截止日</span>
                    </template>
                    <template #default="{ row, $index }">
                        <el-date-picker v-model="row.TempValidDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable @focus="handleSelectClick(row, $index)"
                            style="width: 140px;">
                        </el-date-picker>
                    </template>
                </el-table-column>
                <el-table-column prop="TempDepositAddress" label="存放地点" min-width="160" align="center">
                    <template #header>
                        <span style="color: #F56C6C;">*存放地点</span>
                    </template>
                    <template #default="{ row, $index }">
                        <el-select v-model="row.TempDepositAddressId" filterable
                            @click.native.stop="handleSelectClick(row, $index)">
                            <el-option v-for="item in addressList" :key="item.Id" :label="item.Address"
                                :value="item.Id" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="TempCabinetAddress" label="储存柜层次" min-width="160" align="center">
                    <template #default="{ row, $index }">
                        <el-select v-model="row.TempCabinetAddress" filterable allow-create default-first-option
                            @click.native.stop="handleSelectClick(row, $index)">
                            <el-option v-for="(item, idx) in cabinetAddressList" :key="idx" :label="item.CabinetAddress"
                                :value="item.CabinetAddress" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column prop="MsdsFile" label="MSDS" min-width="180" align="center">
                    <template #header>
                        <span style="color: #F56C6C;">*MSDS</span>
                    </template>
                    <template #default="{ row, $index }">
                        <div class="sortDiv" style="text-align:left;">
                            <!-- <el-button type="primary" link @click="HandleEdit(row)">上传</el-button> -->
                            <el-upload ref="uploadRef" class="upload-demo" style="display:inline-block;"
                                :show-file-list="false" :accept="uploadFileData[0].UploadFileTypeAccept"
                                :before-upload="beforeAvatarUpload.bind(null, uploadFileData[0])"
                                :http-request="httpRequest.bind(null, uploadFileData[0], $index)">
                                <el-button type="success" size="small" :icon="UploadFilled">上传</el-button>
                            </el-upload>
                            <span v-if="row.MsdsFile && row.MsdsFile.length > 0"
                                style="cursor: pointer;color:#409eff;padding:2px 10px;"
                                @click="lookFileListDownload(row.MsdsFile, row.Ext, row.Name)">{{ row.MsdsFileTitle
                                }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="TempWarrantyMonth" label="质保期(月)" min-width="120" align="center">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.TempWarrantyMonth"
                            @input="integerLimitInput($event, row, $index, 'TempWarrantyMonth')"
                            :class="{ 'input-focused': focusedIndex === $index }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column prop="TempRemark" label="备注" min-width="180" align="center">
                    <template #default="{ row, $index }">
                        <el-input v-model="row.TempRemark"
                            :class="{ 'input-focused': focusedIndex === $index }"></el-input>
                    </template>
                </el-table-column>
            </el-table-column>

            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <app-box v-model="dialogVisible" :width="600" :lazy="true" title="入库操作说明">
            <template #content>
                <ul>
                    <li>1、“采购计划信息”供你入库参考，你需填写“采购入库信息”；</li>
                    <li>2、如入库信息与计划信息一致，请点击【复制】；</li>
                    <li>3、如入库信息与计划信息不一致，需在“备注”栏中填写原因；</li>
                    <li>4、如计划中未采购的危化品，请在“采购入库信息”中不要填写任何数据；</li>
                    <li>5、如填写错误请点击【清除】；</li>
                    <li style="color: #F56C6C;">6、”品牌”下拉框中如没有，请手填；</li>
                    <li style="color: #F56C6C;">7、”存放地点”下拉如没有，请到【危化品库管理】\存放地点设置栏中添加；</li>
                    <li style="color: #F56C6C;">8、入库数量不能大于“采购计划”中的数量；</li>
                    <li style="color: #F56C6C;">9、你所需的供应商下拉菜单中如没有，请自行添加，名称须与发票中一致。</li>
                </ul>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">关闭</el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style scoped lang="scss">
ul {
    padding-inline-start: 0px;
    margin: 0;

    li {
        width: 500px;
        padding: 5px;
        margin-left: 30px;
        list-style-type: none;
        font-size: 14px;
        color: #000000;

    }
}
</style>