<script setup>
defineOptions({
    name: 'dangerchemicalsbasecataloglist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    FolderAdd
} from '@element-plus/icons-vue'
import {
    DcbaseCatalogCmboget, DcbaseCatalogDelete, DcbaseCatalogFind, DcbaseCataloginsertUpdate, DcbaseCatalogSetStatuz
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import AppBox from "@/components/Approve/AppBox.vue";
// import {  } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const bogetList = ref([])
const tableData = ref([])
const refTable = ref()
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '请输入危化品名称', trigger: 'change' },
    ],
    UnitsMeasurement: [
        { required: true, message: '请输入危化品单位', trigger: 'change' },
    ],
}
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcbaseCatalogCmbogetUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const isAdd = ref(true)
// 添加
const HandleAdd = (row) => {
    console.log(row)
    isAdd.value = true
    dialogData.value = {}
    if (row) {
        dialogData.value.Pid = row.Id
    }
    dialogVisible.value = true
}
// 修改
const HandleEdit = (row) => {
    console.log(row)
    isAdd.value = false
    dialogData.value = row
    dialogVisible.value = true
}
// 启用/禁用
const HandleEnable = (row) => { }
// 删除
const HandleDel = (row) => {
    DcbaseCatalogDelete({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcbaseCataloginsertUpdateUser()
    })
}
// 列表
const HandleTableData = () => {
    DcbaseCatalogFind().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取危化品分类一二级
const DcbaseCatalogCmbogetUser = () => {
    DcbaseCatalogCmboget({ depth: 0, pid: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bogetList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 修改状态
const HandleSwitchChange = (e, row) => {
    // console.log(e, row)
    DcbaseCatalogSetStatuz({ id: row.Id, statuz: e }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
// 添加修改危化品分类
const DcbaseCataloginsertUpdateUser = () => {

    let paraData = dialogData.value
    paraData.Pid = dialogData.value.Pid[dialogData.value.Pid.length - 1]
    DcbaseCataloginsertUpdate(paraData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="Id" default-expand-all
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="名称" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Sort" label="排序" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Code" label="编码" min-width="100" align="center"></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="计量单位" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.Pid == '0'"> 启用 </span>
                    <el-switch v-else v-model="row.Statuz" :active-value="1" :inactive-value="2"
                        style="--el-switch-off-color: #ff4949" inline-prompt active-text="启" inactive-text="禁"
                        @change="HandleSwitchChange($event, row)" />
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleAdd(row)"
                        v-if="row.Pid == '0' || row.Pid == '1'">添加</el-button>
                    <el-button type="primary" link @click="HandleEdit(row)" v-if="row.Pid != '0'">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)" v-if="row.Pid != '0'">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="isAdd ? '添加危化品分类' : '修改分类'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="140px"
                    status-icon>
                    <el-form-item label="上级分类：" prop="Pid">
                        <el-cascader style="width: 80%" v-model="dialogData.Pid" :options="bogetList"
                            :props="{ value: 'id', label: 'text', checkStrictly: true }">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="名称：" prop="Name">
                        <el-input v-model="dialogData.Name" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="编码：">
                        <el-input v-model="dialogData.Code" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="单位：" prop="UnitsMeasurement"
                        v-if="dialogData.Pid != '0' && dialogData.Pid != '1'">
                        <el-input v-model="dialogData.UnitsMeasurement" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="排序：">
                        <el-input type="number" v-model="dialogData.Sort" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>