<script setup>
defineOptions({
  name: 'nodedetail'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
  FillingInfoDetail
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { ElMessage } from 'element-plus'
import DetailsPage from '@/views/approvalconfiguration/pages/publicpage/DetailsPage.vue' //详情页面
import { pageQuery, urlQuery, tagsListStore, fileDownload } from "@/utils/index.js";
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const routerObject = ref({})
const routerUrl = ref('')
const detailData = ref([])
const activeNames = ref([])
onMounted(() => {
  routerObject.value = pageQuery(route.path)
  if (route.query.isTagRouter) {
    FillingInfoDetailUser(route.query.id)
  }
})
onActivated(() => {
  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
  routerUrl.value = urlQuery(route.path); // 获取生成页面携带的参数
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  nextTick(() => {
    if (!route.query.isTagRouter) {
      FillingInfoDetailUser(route.query.id)
    }
  })
})

// 获取详情信息
const FillingInfoDetailUser = (id) => {
  FillingInfoDetail({ id: id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      rows.forEach((item, index) => {
        let FormContent = JSON.parse(item.FormContent)//表单元素
        let DataContent = JSON.parse(item.DataContent)//数据
        console.log("DataContent", DataContent)
        // 创建一个映射表，用于快速查找DataContent中的 InfoText
        const infotextMap = DataContent.reduce((map, item) => {
          map[item.FieldId] = item.InfoText;
          return map;
        }, {});
        // 创建一个映射表，用于快速查找DataContent中的 isControlShow
        const infotextControl = DataContent.reduce((map, item) => {
          map[item.FieldId] = item.isControlShow;
          return map;
        }, {});
        // 遍历表单元素FormContent，并替换field对应的InfoText到DefaultName
        FormContent.forEach(item => {
          if (infotextMap[item.field]) {
            item.DefaultName = infotextMap[item.field];
            item.isControlShow = infotextControl[item.field];
          }
        });
        item.data = FormContent.filter(t => !t.isRedact)
      })
      detailData.value = rows//表单数据
      activeNames.value = rows.map((item, index) => item.TabId + index)//默认展开所有 
      console.log(" detailData.value", detailData.value)
    } else {
      ElMessage.error(res.data.msg)
    }
  })
} 
</script>
<template>
  <details-page :detailData="detailData" v-model:activeNames="activeNames" :page="'detail'"></details-page>
</template>
<style lang="scss" scoped></style>
