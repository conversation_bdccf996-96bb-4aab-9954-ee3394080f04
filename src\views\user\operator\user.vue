<script setup>
import {
  Search, Refresh, FolderAdd, Delete, UploadFilled, Download
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick } from 'vue'
import { useUserStore } from '@/stores';
import {
  Usergetbyid, Usersave, userupdateuserstatuz, Rolefind, Useraccountunlock,
  Userfind, Unitfindidname, Userdelbatch, Usersetuservalidate, UploadPostexecl, UploadUserFile,Useraccountreset
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit, disposalDate, fileDownload, roleObjList } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import md5 from 'js-md5';
// 表格初始化
const userStore = useUserStore()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const strRoleIdsList = ref([])//当前账号的角色集合
const rolefindList = ref([])//角色列表
const roleTableData = ref([])
const unitList = ref([])//单位列表 
const selectRows = ref([])
const excelUrl = ref('')
const filtersKey = ref({ key: '', value: 'UnitName' })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const options = ref([
  { value: 'UnitName', label: '单位', },
  { value: 'Name', label: '姓名', },
  { value: 'Mobile', label: '手机', },
  { value: 'AcctName', label: '账号', },
  { value: 'RoleName', label: '角色', }
])
const dateFormData = ref({
  UserValidate: '',
  AccountId: ''
})

const HandleSelectChange = (selection) => {
  selectRows.value = selection
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}


//加载数据
onMounted(() => {
  HandleTableData()
  UnitfindidnameUser()
})
//输入整数
const integerLimitInput = (val, name) => {
  formData.value[name] = integerLimit(val);
}


//新增&编辑操作
const dialogVisible = ref(false)
const dialogDateVisible = ref(false)
const formData = ref({})
const isAdd = ref(true)
const refForm = ref()
const dateForm = ref()
const roleUnitType = ref()
const ruleForm = {
  Name: [
    { required: true, message: '姓名不能为空', trigger: 'blur' },
  ],
  AcctName: [
    { required: true, message: '账号不能为空', trigger: 'blur' },
  ],
  UnitId: [
    { required: true, message: '请选择单位', trigger: 'change' },
  ],
  Mobile: [
    { required: true, message: '手机号码不能为空', trigger: 'blur' },
    {
      pattern: /^1[0-9]{10}$/,
      message: '请输入11位手机号码',
      trigger: 'blur'
    }
  ],
  initPwd: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    {
      pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
      message: '密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种',
      trigger: 'blur'
    }
  ]
}
const dateFormRule = {
  UserValidate: [
    { type: 'date', required: true, message: '请选择有效期', trigger: 'change' },
  ],
}

// 添加账户
const HandleAdd = () => {
  isAdd.value = true
  dialogVisible.value = true
  nextTick(() => {
    refForm.value.resetFields()
    formData.value = { Id: '0' }
  })
  strRoleIdsList.value = []
  roleTableData.value = []
}
//修改获取详情
const HandleEdit = (row) => {
  isAdd.value = false
  formData.value.Id = row.Id
  // 获取拥有角色信息，实现反选
  Usergetbyid({ id: row.Id }).then((res) => {
    const { rows } = res.data.data
    formData.value.Name = rows.Name;//姓名
    formData.value.AcctName = rows.AcctName;//账号
    formData.value.Mobile = rows.Mobile;//手机号码
    formData.value.initPwd = rows.initPwd;//密码
    formData.value.UnitId = rows.UnitId;//单位
    formData.value.UserType = rows.UserType;//类型
    formData.value.Sex = Number(rows.Sex);//性别
    formData.value.Qq = rows.Qq;//QQ
    formData.value.Email = rows.Email;//电邮
    formData.value.Tel = rows.Tel;//座机
    formData.value.ZipCode = rows.ZipCode;//邮编
    formData.value.Address = rows.Address;//地址
    formData.value.Memo = rows.Memo;//备注

    roleUnitType.value = rows.UnitType
    RolefindUser(roleUnitType.value)
    strRoleIdsList.value = rows.StrRoleIds.split(',')
    strRoleIdsList.value = [...new Set(strRoleIdsList.value)]
    strRoleIdsList.value = strRoleIdsList.value.map(t => Number(t))
  }).catch((err) => {
    console.info(err)

  })
  dialogVisible.value = true
}

// 修改单位获取角色权限
const HandleUnitChange = (e) => {
  console.log("e", e)

  let arr = unitList.value.find(t => t.Id == e)
  if (arr) roleUnitType.value = arr.UnitType
  RolefindUser(roleUnitType.value)
  console.log("arr", arr)

}
// 根据角色类型加载角色信息
const RolefindUser = (roleType) => {
  Rolefind({ pageIndex: 1, pageSize: 100, RoleType: roleType }).then((res) => {
    const { rows } = res.data.data
    rolefindList.value = rows
    roleTableData.value = roleObjList(rolefindList.value)
  }).catch((err) => {
    console.info(err)
  })
}
// 启用
const HandleEnable = (row) => {
  ElMessageBox.confirm(`确定要启用该账户吗?`)
    .then(() => {
      userupdateuserstatuz({ userId: row.Id }).then((res) => {
        HandleTableData()
        ElMessage.success('启用成功')
      }).catch((err) => {
        console.info(err)
      })
    })
}
// 禁用
const HandleDisbale = (row) => {
  ElMessageBox.confirm(`确定要禁用该账户吗?`)
    .then(() => {
      userupdateuserstatuz({ userId: row.Id }).then((res) => {
        HandleTableData()
        ElMessage.success('禁用成功')
      }).catch((err) => {
        console.info(err)
      })
    })
}
// 解冻
const HandleUnLock = (row) => {
  ElMessageBox.confirm(`确定要解冻该账户吗?`)
    .then(() => {
      Useraccountunlock({ id: row.Id }).then((res) => {
        HandleTableData()
        ElMessage.success('解冻成功')
      }).catch((err) => {
        console.info(err)
      })
    })
}
//批量删除
const HandleDelAll = (row) => {
  ElMessageBox.confirm(`确认删除用户[${row.map(t => t.Name).join(',')}]吗?`)
    .then(() => {
      Userdelbatch(row.map(t => t.Id)).then((res) => {
        HandleTableData()
        ElMessage.success('删除成功')
      }).catch((err) => {
        console.info(err)
      })
    })

}
//删除
const HandleDel = (row) => {

  ElMessageBox.confirm(`确认删除用户[${row.Name}]吗?`)
    .then(() => {
      Userdelbatch([row.Id]).then((res) => {
        HandleTableData()
        ElMessage.success('删除成功')
      }).catch((err) => {
        console.info(err)
      })
    })
}

// 重置密码
const HandleCancel = (row) => {
  ElMessageBox.confirm(`确定要重置密码吗?`)
    .then(() => {
      Useraccountreset({ id: row.Id }).then((res) => {
        HandleTableData()
        ElMessage.success('密码已重置为：Xxpt@01688')
      }).catch((err) => {
        console.info(err)
      })
    })
}

// 设置有效期
const HandleValidateSet = (row) => {
  dialogDateVisible.value = true
  dateFormData.value.AccountId = row.AcctId
  if (!row.UserValidate || row.UserValidate == '0001-01-01 00:00:00') {
    dateFormData.value.UserValidate = ''
  } else {
    dateFormData.value.UserValidate = row.UserValidate.substring(0, 10)
  }
}
const SubmitDate = () => {
  dateForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    Usersetuservalidate(dateFormData.value).then((res) => {
      HandleTableData()
      ElMessage.success('设置有效期成功')
      dialogDateVisible.value = false

    }).catch((err) => {
      console.info(err)
    })
  })
}

// 获取单位
const UnitfindidnameUser = () => {
  Unitfindidname({ statuzgt: 0 }).then(res => {
    const { rows } = res.data.data
    unitList.value = rows
  }).catch((err) => {
    console.info(err)
  });
}
//获取列表 与 搜索
const HandleTableData = () => {
  filters.value.Name = undefined;
  filters.value.Mobile = undefined;
  filters.value.AcctName = undefined;
  filters.value.UnitName = undefined;
  filters.value.RoleName = undefined;

  // 按类型搜索
  if (filtersKey.value.key) {
    filters.value[filtersKey.value.value] = filtersKey.value.key;
  } else {
    filters.value[filtersKey.value.value] = undefined;
  }
  selectRows.value = []
  Userfind(filters.value).then(res => {
    const { other, rows, total } = res.data.data
    tableData.value = rows;
    excelUrl.value = other;
    tableTotal.value = Number(total);

  }).catch((err) => {
    console.info(err)
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filtersKey.value.key = ''
  HandleTableData()
}
// 新增或修改提交
const HandleSubmit = () => {
  if (!strRoleIdsList.value.length) {
    ElMessage.warning('请至少配置一个角色')
    return
  }
  if (formData.value.initPwd) {
    formData.value.Pwd = md5(formData.value.initPwd)
  } else {
    formData.value.Pwd = undefined
  }
  //   formData.value.initPwd = undefined
  formData.value.StrRoleIds = strRoleIdsList.value.join(',')
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    // formData.value.Address = undefined
    // formData.value.Email = undefined
    // formData.value.Memo = undefined
    // formData.value.Qq = undefined
    // formData.value.Tel = undefined
    // formData.value.ZipCode = undefined
    let data = JSON.parse(JSON.stringify(formData.value))
    data.initPwd = undefined
    Usersave(data).then(res => {
      if (res.data.flag == 1) {
        HandleTableData()

        ElMessage.success('保存成功')
        dialogVisible.value = false
      } else {
        ElMessage.error(res.data.msg)
      }
    }).catch((err) => {
      console.info(err)
    })
  })

}

const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
//  下属单位用户导入
const importExecl = () => {
  uploadVisible.value = true
}
// 模板下载
const HandleDownload = () => {
  fileDownload(excelUrl.value);

}
const fileFile = ref()
const uploadRef = ref()

// 导入前校验
const beforeAvatarUpload = (file) => {
  fileFile.value = file
  const extension = file.name.split('.')[1] === 'xls'
  const extension2 = file.name.split('.')[1] === 'xlsx'
  if (!extension && !extension2) {
    ElMessage({
      message: '上传模板只能是 xls、xlsx格式!',
      type: 'error'
    })
  }
  return extension || extension2
}
const httpRequest = () => {
  UploadPostexecl([fileFile.value]).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data

      UploadUserFile({ FilePath: rows }).then(res1 => {
        if (res1.data.flag == 1) {
          ElMessage.success(res1.data.msg || '导入成功')
          setTimeout(function () {
            HandleTableData()
            uploadVisible.value = false
          }, 1000);
        } else {
          ElMessage({
            showClose: true,
            message: res1.data.msg,
            type: 'error',
            duration: 5000
          })
        }
      })
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
</script>
<template>
  <!-- 搜索 -->
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filtersKey" class="flexBox">
        <el-form-item class="flexItem">
          <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
          <el-button type="danger" :icon="Delete" :disabled="selectRows.length == 0"
            @click="HandleDelAll">批量删除</el-button>
          <el-button type="primary" :icon="UploadFilled" @click="importExecl">单位用户导入</el-button>
        </el-form-item>
        <div class="verticalIdel"></div>
        <el-form-item class="flexItem">
          <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="width: 240px" class="input-with-select">
            <template #prepend>
              <el-select v-model="filtersKey.value" style="width: 80px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
        </el-form-item>
      </el-form>

    </el-col>
  </el-row>
  <!-- 内容 -->
  <el-table ref="refTable" :data="tableData" highlight-current-row header-cell-class-name="headerClassName"
    @selection-change="HandleSelectChange" border stripe>
    <el-table-column type="selection" width="50"></el-table-column>
    <el-table-column prop="Code" label="单位代码" min-width="150" align="center"></el-table-column>
    <el-table-column prop="UnitName" label="单位名称" min-width="160"></el-table-column>
    <el-table-column prop="Name" label="姓名" min-width="140" align="center"></el-table-column>
    <el-table-column prop="Mobile" label="手机号码" min-width="120" align="center"></el-table-column>
    <el-table-column prop="AcctName" label="登录账号" min-width="180" align="center"></el-table-column>
    <el-table-column prop="RoleNames" label="角色" min-width="160" show-overflow-tooltip align="center">
    </el-table-column>
    <el-table-column prop="ContiLoginCount" label="连续登录天数" min-width="120" align="center"></el-table-column>
    <el-table-column prop="TotalLoginCount" label="总计登录天数" min-width="120" align="center"></el-table-column>
    <el-table-column prop="LoginCount" label="总计登录次数" min-width="120" align="center"></el-table-column>
    <el-table-column prop="LastLogin" label="最近登录" min-width="120" align="center"></el-table-column>

    <el-table-column prop="Statuz" label="状态" min-width="80" align="center">
      <template #default="{ row }">
        <el-tag :type="row.Statuz != 0 ? 'success' : 'danger'" disable-transitions>
          {{ row.Statuz != 0 ? "正常" : "禁用" }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="UserType" label="用户类型" min-width="100" align="center">
      <template #default="{ row }">
        {{ row.UserType == 2 ? "内置用户" : "普通用户" }}
      </template>
    </el-table-column>
    <el-table-column prop="UserValidate" label="有效期" min-width="180" align="center">
      <template #default="{ row }">
        {{ disposalDate(row.UserValidate) }}
      </template>

    </el-table-column>
    <!-- <el-table-column label="权限" min-width="100" align="center" fixed="right">
      <template #default="{ row }">
        <el-button type="primary" link @click="HandleSet(row)">设置</el-button>
      </template>
    </el-table-column> -->
    <el-table-column prop="opt" label="操作" width="320" align="center" fixed="right">
      <template #default="{ row }">

        <el-button type="primary" link v-if="row.UserType != 2 && row.Statuz == 0"
          @click="HandleEnable(row)">启用</el-button>
        <el-button type="primary" link v-if="row.UserType != 2 && row.Statuz != 0"
          @click="HandleDisbale(row)">禁用</el-button>
        <el-button type="primary" link v-if="row.UserType != 2" @click="HandleEdit(row)">修改</el-button>
        <el-button type="primary" link v-if="row.UserType != 2" @click="HandleUnLock(row)">解冻</el-button>
        <el-button type="primary" link v-if="row.UserType != 2" @click="HandleDel(row)">删除</el-button>
        <el-button type="primary" link v-if="row.UserType != 2" @click="HandleCancel(row)">重置密码</el-button>
        <el-button type="primary" link v-if="row.UserType == 2" @click="HandleUnLock(row)">解冻</el-button>
        <el-button type="primary" link @click="HandleValidateSet(row)">设置有效期</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>
  <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
    @handleChange="handlePage" />

  <!-- 添加修改单位用户信息 -- 弹窗 -->
  <app-box v-model="dialogVisible" :height="600" :width="960" :lazy="true" :title="isAdd ? '添加用户及账号信息' : '修改用户及账号信息'">
    <template #content>
      <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="80px" status-icon
        class="addCustom">
        <fieldset>
          <legend>基本信息</legend>
          <div class="dialogFlexBox">
            <el-form-item label="姓名" prop="Name">
              <el-input v-model="formData.Name" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="Mobile">
              <el-input v-model="formData.Mobile" auto-complete="off"
                @input="integerLimitInput($event, 'Tel')"></el-input>
            </el-form-item>
            <el-form-item label="登录账号" prop="AcctName">
              <el-input v-model="formData.AcctName" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="密码" :prop="isAdd ? 'initPwd' : ''">
              <el-input v-model="formData.initPwd" show-password auto-complete="off" clearable
                :placeholder="isAdd ? '' : '如不修改密码，请留空'"></el-input>
            </el-form-item>
          </div>

          <el-form-item label="单位" prop="UnitId" style="margin-bottom: 18px !important;">
            <el-select v-model="formData.UnitId" filterable @change="HandleUnitChange">
              <el-option class="flexItem" v-for="item in unitList" :key="item.Id" :label="item.Name" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
        </fieldset>
        <fieldset>
          <legend>权限设置</legend>
          <el-form-item label="" label-width="0">
            <!-- <el-checkbox-group v-model="strRoleIdsList" style="width: 100%;">
              <el-checkbox v-for="item in rolefindList" :key="item.RoleId" :label="item.Name" :value="item.RoleId" />
            </el-checkbox-group> -->
            <el-table :data="roleTableData" border header-cell-class-name="headerClassName">
              <el-table-column prop="ModuleName" label="角色分类" min-width="160" align="center"></el-table-column>
              <el-table-column label="角色名称" width="640">
                <template #default="{ row }">
                  <el-checkbox-group v-model="strRoleIdsList">
                    <el-checkbox v-for="item in row.children" :key="item.RoleId" :label="item.Name" :value="item.RoleId"
                      style="width: 20%;">
                    </el-checkbox>
                  </el-checkbox-group>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </fieldset>
        <fieldset>
          <legend>更多信息</legend>
          <div class="dialogFlexBox2">
            <el-form-item label="类型">
              <el-radio-group v-model="formData.UserType">
                <el-radio class="radio" :value="1">普通用户</el-radio>
                <el-radio class="radio" :value="2">内置用户</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="性别">
              <el-radio-group v-model="formData.Sex">
                <el-radio class="radio" :value="1">男</el-radio>
                <el-radio class="radio" :value="0">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="QQ">
              <el-input v-model="formData.Qq" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="电邮">
              <el-input v-model="formData.Email" auto-complete="off"></el-input>
            </el-form-item>

            <el-form-item label="座机">
              <el-input v-model="formData.Tel" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="邮编">
              <el-input v-model="formData.ZipCode" auto-complete="off"></el-input>
            </el-form-item>
          </div>
          <!-- <el-form-item label="地址">
            <el-input type="textarea" v-model="formData.Address"></el-input>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="formData.Memo"></el-input>
          </el-form-item> -->
        </fieldset>
      </el-form>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit(refForm)"> 提交 </el-button>
      </span>
    </template>
  </app-box>
  <!-- 设置有效期 -->
  <el-dialog v-model="dialogDateVisible" title="设置有效期" draggable width="500px">
    <el-form @submit.prevent ref="dateForm" :model="dateFormData" :rules="dateFormRule" label-width="80px" status-icon>
      <el-form-item label="有效期" class="flexItem" prop="UserValidate">
        <el-date-picker v-model="dateFormData.UserValidate" format="YYYY/MM/DD" value-format="YYYY-MM-DD" type="date"
          placeholder="请选择有效期" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogDateVisible = false">取消</el-button>
        <el-button type="primary" @click="SubmitDate()">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 导入用户 -->
  <el-dialog v-model="uploadVisible" title="下属单位导入" width="480px">
    <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" label-width="80px" status-icon>
      <el-form-item>
        <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" accept=".xlsx,.xls"
          style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload" :http-request="httpRequest">
          <el-button type="primary" :icon="UploadFilled">单位用户导入 </el-button>
        </el-upload>
        <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}

// .addCustom {

.addCustom.el-form {
  width: 90%;
  margin: 0 auto;

  .el-form-item {
    margin-bottom: 10px;
  }

  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;

    label {
      width: 20%;
    }
  }

  .el-checkbox {
    height: var(--el-checkbox-height, 22px);
  }

}

.taskNameConent {
  width: 100%;
  /* 具体宽度，例如 200px 或 100% */
  ;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialogFlexBox,
.dialogFlexBox2 {
  display: flex;
  flex-wrap: wrap;

  .el-form-item {
    width: 50%;
  }
}

.dialogFlexBox {
  .el-form-item {
    margin-bottom: 18px !important;

    &:nth-child(3) {
      margin-bottom: 30px !important;
    }

    &:nth-child(4) {
      margin-bottom: 30px !important;
    }

  }
}

.dialogFlexBox2 {
  .el-form-item {
    margin-bottom: 5px !important;
  }
}

.el-row .el-form-item {
  margin-top: 0px;
  margin-bottom: 10px;
}

fieldset {
  // color: #333;
  border: #ccc dashed 1px;
  padding: 10px;
  margin: 10px 0;

  legend {
    font-size: 16px;
  }
}
</style>