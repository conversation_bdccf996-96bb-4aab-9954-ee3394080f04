<script setup>
defineOptions({
    name: 'dangerchemicalsinventoryrecordlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Position, Edit
} from '@element-plus/icons-vue'
import {
    DcInventoryRecordFind, DccatalogGetClassTwo
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const StatuzSolicitedList = ref([])
const InventoryDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "InventoryDate", SortType: "DESC" }] })

const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', },
])

const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

const inventoryDategeChange = (val) => {
    if (!val) filters.value.InventoryDatege = undefined
    HandleTableData()
}
const inventoryDateleChange = (val) => {
    if (val) {
        filters.value.InventoryDatele = val + " 23:59:59"
    } else {
        filters.value.InventoryDatele = undefined
    }
    HandleTableData()
}

//  列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcInventoryRecordFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.InventoryDatege = undefined
    filters.value.InventoryDatele = undefined
    filters.value.TwoCatalogId = undefined
    InventoryDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!InventoryDatele.value) return false;
    return time >= new Date(InventoryDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.InventoryDatege) return false;
    return time < new Date(filters.value.InventoryDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.InventoryDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="盘点时间"
                            @change="inventoryDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="InventoryDatele" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledEndDate" clearable placeholder="盘点时间"
                            @change="inventoryDateleChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="InventoryNum" label="盈亏数量" min-width="110" align="right">
                <template #default="{ row }">
                    <span :style="{ color: row.InventoryNum < 0 ? 'red' : '#606266' }">
                        {{ row.StockNum }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Price" label="单价" min-width="160" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="InventoryDate" label="盘点时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.InventoryDate ? row.InventoryDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="情况说明" min-width="160" show-overflow-tooltip></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>