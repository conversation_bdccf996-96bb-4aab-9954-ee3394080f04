<script setup>
defineOptions({
    name: 'biddingdetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    Getpagedbytype,
} from '@/api/user.js'
import {
    UniformBiddingGetById
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import { fileDownload, tagsListStore, ValidityPeriodList } from "@/utils/index.js";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const formData = ref({})
const refForm = ref()
const uploadFileData = ref([])
const BidPublicList = ref([])
const MethodList = ref([])
onMounted(() => {
    GetpagedbytypeUser()
    nextTick(() => {
        if (route.query.isTagRouter) {
            UniformBiddingGetByIdUser()
        }
    })
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            UniformBiddingGetByIdUser()
        }
    })
})
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    let paraData = {}
    paraData = { moduleType: 702 }
    Getpagedbytype(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.categoryList = []
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const UniformBiddingGetByIdUser = () => {
    UniformBiddingGetById({ id: route.query.id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other, footer } = res.data.data
            uploadFileData.value.forEach(item => {
                item.categoryList = []
            })
            formData.value = rows
            if (rows.ValidityPeriod === 0) formData.value.ValidityPeriod = undefined
            if (rows.BidResultPublic === 0) {
                formData.value.BidResultPublic = undefined
            } else {
                formData.value.BidResultPublic = String(rows.BidResultPublic)
            }
            if (rows.Method === 0) {
                formData.value.Method = undefined
            } else {
                formData.value.Method = String(rows.Method)
            }

            let categoryList = footer || [];//附件集合
            if (categoryList.length > 0) {
                // 遍历数组 categoryList 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 uploadFileData中具有相同 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
            BidPublicList.value = other.listBidPublic || [];//开标结果公开
            MethodList.value = other.listMethod;//采购方式
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}

</script>
<template>
    <el-form style="width: 100%;min-width: 560px;" :inline="true" class="mobile-box" @submit.prevent ref="refForm"
        :model="formData" label-width="180px" status-icon>
        <el-form-item label="年度：" class="formItem">
            <el-input v-model="formData.PlanYear" disabled></el-input>
        </el-form-item>
        <el-form-item label="采购批次：" class="formItem">
            <el-input v-model="formData.PurchaseNo" disabled></el-input>
        </el-form-item>
        <el-form-item label="采购有效年限：" class="formItem">
            <el-select v-model="formData.PurchaseYear" disabled class="item_content">
                <el-option v-for="item in ValidityPeriodList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="采购方式：" class="formItem">
            <el-select v-model="formData.Method" disabled class="item_content">
                <el-option v-for="item in MethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="开标日期：" class="formItem">
            <el-date-picker type="date" v-model="formData.BidDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="开标结果公开：" class="formItem">
            <el-select v-model="formData.BidResultPublic" disabled class="item_content">
                <el-option v-for="item in BidPublicList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="公开日期：" class="formItem" v-if="formData.BidResultPublic == 1">
            <el-date-picker type="date" v-model="formData.PublicDate" disabled format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="公开媒体名称：" class="formItem" v-if="formData.BidResultPublic == 1">
            <el-input v-model="formData.PublicMediaName" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="委托代理机构：" class="formItem">
            <el-input v-model="formData.EntrustingAgency" disabled placeholder="机构全称" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="代理机构联系人：" class="formItem">
            <el-input v-model="formData.Contact" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="代理机构联系电话：" class="formItem">
            <el-input v-model="formData.Mobile" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%;">
            <el-input type="textarea" v-model="formData.Remark" disabled></el-input>
        </el-form-item>
        <el-form-item v-for="(item, index) in uploadFileData" :key="index" :label="item.Name + '：'"
            style="width: 100%;">
            <div class="fileFlex">
                <div v-for="(itemCate, indexCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                    <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                        {{ itemCate.Title }}{{ itemCate.Ext }}
                    </span>
                </div>
            </div>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-right: 0;
}

.item_content {
    // width: 60% !important;
}

:deep(.el-date-editor.el-input) {
    width: 100% !important;
}

.formItem {
    width: 50%;
}

@media (max-width: 768px) {
    .formItem {
        width: 100%;
    }
}
</style>