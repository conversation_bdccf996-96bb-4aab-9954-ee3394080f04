<script setup>
defineOptions({
    name: 'dangerchemicalsdailygoverndetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import { DcGovernDeclareDetailFind } from '@/api/daily.js'
import { GetDictionaryCombox } from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore, fileDownload } from "@/utils/index.js";
import { useUserStore } from '@/stores';
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const t = ref(0);
const SummaryId = ref(0);
const SchoolId = ref(0);
//加载数据
onMounted(() => {
    SummaryId.value = route.query.Id || 0;
    SchoolId.value = route.query.SchoolId || 0;
    t.value = route.query.t || 0;
    if (route.query.isTagRouter) {
        HandleTableData();
        DccatalogTypeGet();
    }


})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    SummaryId.value = route.query.Id || 0;
    SchoolId.value = route.query.SchoolId || 0;
    t.value = route.query.t || 0;
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData();
            DccatalogTypeGet();
        }
    })
})

//表格
const titleData = ref([])

const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({})

//搜索条件
const catalogList = ref([])
const filtersChange = (row) => {
    HandleTableData()
}

const gradeOptions = ref([{ Id: 1, Name: '一般', }, { Id: 2, Name: '较大', }, { Id: 3, Name: '重大', }])
const natureOptions = ref([{ Id: 1, Name: '问题', }, { Id: 2, Name: '隐患', }])
const isTallyClaimOptions = ref([{ Id: 1, Name: '是', }, { Id: 0, Name: '否', }])
// 危化品类别
const DccatalogTypeGet = () => {
    GetDictionaryCombox({ TypeCode: "10000" }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            catalogList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 列表
const HandleTableData = () => {
    filters.value.id = SummaryId.value;
    DcGovernDeclareDetailFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            titleData.value = rows.Other || {};
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.categoryid = undefined;
    filters.value.istallyclaim = undefined;
    filters.value.nature = undefined;
    filters.value.grade = undefined;
    filters.value.name = undefined;

    HandleTableData()
}

const getMonthName = (month) => {
    var html = "";
    switch (month) {
        case 1:
            html = "一";
            break;
        case 2:
            html = "二";
            break;
        case 3:
            html = "三";
            break;
        case 4:
            html = "四";
            break;
        case 5:
            html = "五";
            break;
        case 6:
            html = "六";
            break;
        case 7:
            html = "七";
            break;
        case 8:
            html = "八";
            break;
        case 9:
            html = "九";
            break;
        case 10:
            html = "十";
            break;
        case 11:
            html = "十一";
            break;
        case 12:
            html = "十二";
            break;
    }
    html = (html + '月份');
    return html
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }

}
</script>
<template>
    <div class="viewContainer">

        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" style="width: 95%">
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.categoryid" clearable placeholder="类别" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in catalogList" :key="item.DicValue" :label="item.DicName"
                                :value="item.DicValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.nature" clearable placeholder="性质" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in natureOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.grade" clearable placeholder="危险等级" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in gradeOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.istallyclaim" clearable placeholder="是否存在问题隐患" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in isTallyClaimOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.name" placeholder="问题隐患清单" style="width: 180px"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div v-if="Object.keys(titleData).length > 0" style="font-size: 14px;color: #606266;margin-bottom: 10px;">
            <span v-if="titleData.UnitName">填报单位：{{ titleData.UnitName }}</span>
            <span v-if="t == 1" style="margin-left:50px;">
                填报周次：第{{ titleData.NumberCyclez }}周({{ titleData.DatePeriod }})
            </span>
            <span v-else-if="t == 2" style="margin-left:50px;">
                填报月份：{{ getMonthName(titleData.NumberCyclez) }}
            </span>
            <span style="margin-left:50px;" v-if="titleData.UserName">填报人：{{ titleData.UserName }}</span>
            <span style="margin-left:50px;" v-if="titleData.RegDate">填报时间：{{ titleData.RegDate.substring(0, 10)
            }}</span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="id" default-expand-all
            header-cell-class-name="headerClassName">
            <el-table-column prop="CategoryName" label="类别" min-width="60" align="center"></el-table-column>
            <el-table-column prop="Name" label="问题隐患清单" min-width="300"></el-table-column>
            <el-table-column prop="Nature" label="性质" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.Nature == 1 ? '问题' : row.Nature == 2 ? '隐患' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Grade" label="危险等级" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Grade == 1 ? '一般' : row.Grade == 2 ? '较大' : row.Grade == 3 ? '重大' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="IsRectify" label="存在问题隐患" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsTallyClaim != 0" style="color:red;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="RectifyLimit" label="整改期限" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.RectifyLimit ? row.RectifyLimit.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column label="附件" min-width="200" align="center">
                <template #default="{ row }">
                    <div v-if="row.AttachmentList && row.AttachmentList.length > 0">
                        <div class="fileFlex">
                            <div v-for="(itemCate) in row.AttachmentList" :key="itemCate.Id" style="color:#409EFF ;">
                                <span style="cursor: pointer;" @click="fileListDownload(itemCate, row.AttachmentList)">
                                    {{ itemCate.Title }}{{ itemCate.Ext }}
                                </span>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="Memo" label="备注" min-width="200"></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>