<script setup>
defineOptions({
    name: 'dangerchemicalsapplybacklisteasy'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcApplyFind, DcApplyEasyBack
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const GrantEnd = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, ApplyType: 1, IsGrant: 1, sortModel: [{ SortCode: "BatchNo", SortType: "DESC" }, { SortCode: "RegDate", SortType: "DESC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    backNum: [
        { required: true, message: '请填写退回数量', trigger: 'change' },
    ],
    isMayUse: [
        { required: true, message: '请选择是否二次可使用', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'BatchNo', label: '申领批次', },
    { value: 'UserName', label: '领用人', },
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const applyConfirmDetailId = ref('')
// 发放
const HandleEdit = (row) => {
    dialogVisible.value = true
    applyConfirmDetailId.value = row.Id
    formData.value.UnitName = row.UnitName
    nextTick(() => {
        refForm.value.resetFields()
    })
}
//发放提交
const HandleSubmit = () => {
    let paraData = {
        applyConfirmDetailId: applyConfirmDetailId.value,
        backNum: formData.value.backNum,
        isMayUse: formData.value.isMayUse,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcApplyEasyBack(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '退回成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.GrantBegin = undefined
    filters.value.GrantEnd = undefined
    GrantEnd.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const grantBeginChange = (val) => {
    if (!val) filters.value.GrantBegin = undefined
    HandleTableData()
}
const grantEndChange = (val) => {
    if (val) {
        filters.value.GrantEnd = val + " 23:59:59"
    } else {
        filters.value.GrantEnd = undefined
    }
    HandleTableData()
}
//输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}

// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcApplyFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!GrantEnd.value) return false;
    return time >= new Date(GrantEnd.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.GrantBegin) return false;
    return time < new Date(filters.value.GrantBegin + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    领用退回 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 退回危化品：是指领用人领多了，需退回的危化品； </li>
                    <li> 请核实已收到与退回数量一致的危化品后，再点击【退回】。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.GrantBegin" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="领用时间"
                            @change="grantBeginChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="GrantEnd" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="领用时间" @change="grantEndChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="UserName" label="领用人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="BatchNo" label="申领批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="领用数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="BackNum" label="退回数量" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row.BackNum ? row.BackNum : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="GrantDate" label="领用时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.GrantDate ? row.GrantDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UseTime" label="使用时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.UseTime ? row.UseTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="用途" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="WithUserName" label="同领用人" min-width="110" align="center">
                <template #default="{ row }">
                    {{ row.WithUserName || '--' }}
                </template>
            </el-table-column>
            <el-table-column label="标识码" min-width="120" align="center" show-overflow-tooltip>
                <template #default="{ row }">
                    {{ row.ThirdMaterialId || '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id" type="primary" link @click="HandleEdit(row)">退回</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="退回危化品">
            <template #content>
                <el-form style="min-width: 400px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="退回数量：" prop="backNum">
                        <el-input v-model="formData.backNum" @input="limitInput($event, 'backNum')"
                            style="width: 240px"></el-input>
                        <span style="color: #999;margin-left: 20px;"> 单位： (<span>{{ formData.UnitName }}</span>) </span>
                    </el-form-item>
                    <el-form-item label="是否二次可使用：" prop="isMayUse">
                        <el-radio-group v-model="formData.isMayUse">
                            <el-radio value="1">是</el-radio>
                            <el-radio value="0">否</el-radio>
                        </el-radio-group>
                        <span style="color: #999;margin-left: 20px;"> （是指退回后，可再使用的危化品） </span>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
:deep(.el-statistic__suffix) {
    font-size: 14px;
}

.marginLeft {
    margin-left: 10px;
}
</style>