<script setup>
defineOptions({
    name: 'dangerchemicalsguaranteeclaimlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import { DcGuaranteeClaimListFind } from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'

import TablePage from '@/components/TablePagination/index.vue' //分页

import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()

const unitType = ref(userStore.userInfo.UnitType)
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {


    }
    //加载表格数据
    HandleTableData(true);

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            //加载表格数据
            HandleTableData(true);
        }
    })
})


//表格参数
const tableData = ref([])
const summation = ref({})
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const SchoolList = ref([])

//搜索条件
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 列表
const HandleTableData = (isFirst) => {
    let formData = {
        pageIndex: filters.value.pageIndex,
        pageSize: filters.value.pageSize,
        Yearz: filters.value.Yearz,
        Id: filters.value.Id,
    }
    if (isFirst) {
        formData.isFirst = true
    } else {
        formData.isFirst = undefined
    }
    DcGuaranteeClaimListFind(formData).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other, headers } = res.data.data;
            tableData.value = rows;
            summation.value = headers[0];
            tableTotal.value = Number(total);
            if (isFirst) {
                SchoolList.value = other.SchoolList || []//学校名称
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    })


}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;

    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计';
            return;
        }
        if (column.property == 'EmergencyNum') {
            if (summation.value.EmergencyNum) {
                sums[index] = Number(summation.value.EmergencyNum).toFixed(2).replace(/\.?0+$/, "");
            } else {
                sums[index] = '0'
            }

        } else if (column.property == 'TrainSafeNum') {
            if (summation.value.TrainSafeNum) {
                sums[index] = Number(summation.value.TrainSafeNum).toFixed(2).replace(/\.?0+$/, "");
            } else {
                sums[index] = '0'
            }
        }
    });
    console.log("sums", sums)
    return sums;
}

// 制度与队伍建设
const HandleTeamBuild = (row, e) => {
    router.push({ path: "../system/teambuild", query: { id: row.Id } })
}
//培训与安全教育
const HandleTrainSafe = (row, e) => {
    //src\views\dangerchemicals\train\safeeducationlist.vue
    router.push({ path: "../train/safeeducationlist", query: { SchoolId: row.Id } })
}
//应急预案与演练
const HandleEmergency = (row, e) => {
    //src\views\dangerchemicals\emergency\planlist.vue
    router.push({ path: "../emergency/planlist", query: { SchoolId: row.Id } })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="适用学校"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="id"
            :summary-method="getSummaries" show-summary default-expand-all header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="学校名称" min-width="180"></el-table-column>
            <el-table-column prop="opt1" label="制度与队伍建设" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id > 0 && row.ConfigId > 0" type="primary" link
                        @click="HandleTeamBuild(row)">查看</el-button>
                    <span v-else-if="row.Id > 0">--</span>
                </template>
            </el-table-column>
            <el-table-column prop="TrainSafeNum" label="培训与安全教育数量（次）" min-width="140" align="right"></el-table-column>
            <el-table-column prop="TrainSafeNumopt" label="培训与安全教育数量明细" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id > 0 && row.TrainSafeNum > 0" type="primary" link
                        @click="HandleTrainSafe(row)">查看</el-button>
                    <span v-else-if="row.Id > 0">--</span>
                </template>
            </el-table-column>
            <el-table-column prop="EmergencyNum" label="应急预案与演练数量（次）" min-width="120" align="right"></el-table-column>
            <el-table-column prop="EmergencyNumopt" fixed="right" label="应急预案与演练数量明细" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id > 0 && row.EmergencyNum > 0" type="primary" link
                        @click="HandleEmergency(row)">查看</el-button>
                    <span v-else-if="row.Id > 0">--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>