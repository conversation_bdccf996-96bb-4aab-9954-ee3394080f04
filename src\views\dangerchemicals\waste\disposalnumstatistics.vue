<script setup>
defineOptions({
    name: 'dangerchemicalswastedisposalnumstatistics'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Position, Back
} from '@element-plus/icons-vue'
import {
    DcApplyNumStatisticsFind, GetDcWasteDisposalStatisticsYear, Punitgetschoolbycountyid, DcWasteClassGet
} from '@/api/dangerchemicals.js'
import {
    DcWasteDisposalNumStatisticsExport
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import { ExcelDownload } from "@/utils/index.js"

import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const unitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({
    sortModel: [{ SortCode: "Id", SortType: "DESC" }],
    OneClassId: route.query.classOneId,
    TwoClassId: route.query.classTwoId,
    CountyId: route.query.countyId
})

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData()
        PunitgetschoolbycountyidUser()
        GetDcWasteDisposalStatisticsYearUser()
        DcWasteClassGetUser()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    filters.value.OneClassId = route.query.classOneId
    filters.value.TwoClassId = route.query.classTwoId
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData()
            PunitgetschoolbycountyidUser()
            GetDcWasteDisposalStatisticsYearUser()
            DcWasteClassGetUser()
            if (route.query.classOneId) DcWasteClassGetUser(1, route.query.classOneId)
        }
    })
})

// 返回上一页
const HandleBack = () => {
    let tagsList = userStore.tagsList
    tagsList = tagsList.filter(t => t.path != route.path)
    userStore.setTagsList(tagsList)
    router.push({ path: route.query.path })
}
//  列表
const HandleTableData = () => {
    if (yearz.value?.length > 0) {
        filters.value.years = yearz.value.join(',')
    } else {
        filters.value.years = undefined
    }
    DcApplyNumStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    HandleTableData()
}
// 重置
const HandleReset = () => {
    if (unitType.value == 1 && route.query.classOneId) {
        filters.value.OneClassId = route.query.classOneId
        filters.value.TwoClassId = route.query.classTwoId
        DcWasteClassGetUser(1, route.query.classOneId)
    } else {
        filters.value.OneClassId = undefined;
        filters.value.TwoClassId = undefined;
    }
    filters.value.SchoolId = undefined
    yearz.value = []
    HandleTableData()
}

const schoolList = ref([])
const yearzList = ref([])
const yearz = ref([])
const levelOneList = ref([])
const levelTwoList = ref([])
// 获取危化品一级分类|| 二级分类
const DcWasteClassGetUser = (depth = 0, pid = 0) => {
    DcWasteClassGet({ id: 0, depth: depth, pid: pid }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (pid == 0) {
                levelOneList.value = rows.data || []
            } else {
                levelTwoList.value = rows.data || []
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersOneChange = (e) => {
    filters.value.TwoClassId = undefined
    DcWasteClassGetUser(1, e)
    HandleTableData()
}
// 根据区县获取学校
const PunitgetschoolbycountyidUser = () => {
    Punitgetschoolbycountyid({ CountyId: route.query.countyId || 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            schoolList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取年份
const GetDcWasteDisposalStatisticsYearUser = () => {
    GetDcWasteDisposalStatisticsYear().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            yearzList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = () => {
    HandleTableData()
}
//导出   
const HandleExport = () => {
    DcWasteDisposalNumStatisticsExport(filters.value).then(res => {
        ExcelDownload(res)
    });
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="unitType == 1 && route.query.path">
                        <el-button :icon="Back" @click="HandleBack">返回</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="unitType == 1 && route.query.path"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="yearz" multiple placeholder="年度" @change="filtersChange"
                            style="min-width: 160px">
                            <el-option v-for="item in yearzList" :key="item.Yearz" :label="item.Yearz"
                                :value="item.Yearz" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem" v-if="unitType != 3">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in schoolList" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.OneClassId" clearable placeholder="一级分类" style="width: 180px"
                            @change="filtersOneChange">
                            <el-option v-for="item in levelOneList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoClassId" clearable placeholder="二级分类" style="width: 180px"
                            @change="filtersChange">
                            <el-option v-for="item in levelTwoList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div v-if="unitType == 1 && route.query.countyName" style="font-size: 14px;color: #606266;margin-bottom: 10px;">
            【{{ route.query.countyName }}】：处置数量统计
        </div>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column v-if="unitType != 3" prop="SchoolName" label="学校名称" min-width="200"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="OneClassName" label="一级分类" min-width="120" align="center"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="TwoClassName" label="二级分类" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column v-for="item in yearzList" :key="String(item.Yearz)" :prop="String(item.YearName)"
                :label="String(item.Yearz)" min-width="120" align="right"></el-table-column>
            <el-table-column prop="SumYear" label="小计" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.SumYear || '--' }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<style lang="scss" scoped></style>