import { defineStore } from 'pinia';

/**
 * 布局配置
 * 修复：https://gitee.com/lyt-top/vue-next-admin/issues/I567R1，感谢@lanbao123
 * 2020.05.28 by lyt 优化。开发时配置不生效问题
 * 修改配置时：
 * 1、需要每次都清理 `window.localStorage` 浏览器永久缓存
 * 2、或者点击布局配置最底部 `一键恢复默认` 按钮即可看到效果
 */
export const useThemeConfig = defineStore('themeConfig', {
    state: () => ({
        themeConfig: {
            isDrawer: false,// 是否开启布局配置抽屉
            // 全局主题
            primary: '#409eff', // 默认 primary 主题颜色
            isIsDark: false,// 是否开启深色模式
            // 顶栏设置
            topBar: '#ffffff',// 默认顶栏导航背景颜色
            topBarColor: '#606266',// 默认顶栏导航字体颜色
            // 菜单设置
            menuBar: '#545c64', // 默认菜单导航背景颜色
            menuBarColor: '#eaeaea',// 默认菜单导航字体颜色
            menuBarActiveColor: 'rgba(0, 0, 0, 0.2)', // 默认菜单高亮背景色
        },
    }),
    actions: {
        setThemeConfig(data) {
            this.themeConfig = data.themeConfig;
        },
    },
});
