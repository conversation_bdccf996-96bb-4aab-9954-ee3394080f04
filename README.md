# Blog.Admin.Vue3.Beta

This is a Vue3 project + elementPlus Integration solution testing.

项目运行

nodejs建议版本>= 16

安装pnpm 运行流程（如安装不成功，请安装yarn进行操作）
npm install -g pnpm

安装依赖
pnpm install

如果失败，请安装yarn
npm install -g yarn
安装依赖
yarn

启动项目

运行
pnpm dev (不成功使用下面的)
yarn dev （需先安装yarn）

温馨提示1
Windows下提示pnpm指令无法找到可以尝试添加变量
添加pnpm到环境变量的目录
D:\nodejs安装目录\nodejs\node_global

温馨提示2
如遇到无法pnpm install
可尝试删除D:\nodejs安装目录\nodejs\node_global文件夹中的pnpm.ps1文件

###########################################
切换平台:

src/stores/modules/user.js
const platformType = 1 //平台类型 1:校服平台 2:工作流管理平台（审批配置平台） 3：危化品

src/router/index.js
const platformType = 1;//平台类型 1:校服平台 2:工作流管理平台（审批配置平台） 3：危化品

vite.config.js
切换proxy代理域名

本地开发测试时,平台切换后清除浏览器Application(应用程序)本地缓存,重新登录

####################################################
