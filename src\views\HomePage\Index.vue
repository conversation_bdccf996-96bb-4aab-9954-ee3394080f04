<script setup>
import { ElMessage } from 'element-plus'
import {
    HomeGetlistnum, Getinformationbycatetype
} from '@/api/home.js'
import { ref, onMounted, onUnmounted } from 'vue';
import HomeHeader from '@/views/HomePage/HomeHeader.vue';
import Footer from '@/views/HomePage/homefooter.vue';
import router from '@/router'
const screenheight = ref('842px')
// mounted生命周期
onMounted(() => {
    screenheight.value = (document.body.clientWidth / 1920) * 842 + 'px'
    // 修改全局body高度
    document.documentElement.style.setProperty('--body-height', 'auto');
    document.documentElement.style.setProperty('--body--webkit-scrollbar', '0px');
    HomeGetlistnumUser()
    GetinformationbycatetypeUser()
})
onUnmounted(() => {
    // 移除样式
    document.documentElement.style.setProperty('--body-height', '100%');
    document.documentElement.style.setProperty('--body--webkit-scrollbar', '6px');
});

window.onresize = () => {
    //屏幕尺寸变化就重新赋值   
    return (() => {
        if (document.body.clientWidth > 1200) {
            screenheight.value = (document.body.clientWidth / 1920) * 842 + 'px'
        }
    })()
}

const imageList = ref([])
const tableData = ref([])
// 查看校服详情
const onTableItemClick = (item) => {
    const { href } = router.resolve({
        path: "/preview",
        query: { id: item.Id, requestNum: 2 }
    });
    window.open(href, "_blank");
}
// 获取校服展示数据
const HomeGetlistnumUser = () => {
    HomeGetlistnum().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取轮播图
const GetinformationbycatetypeUser = () => {
    Getinformationbycatetype({ cateType: 3 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // console.log("轮播图", rows)
            imageList.value = rows

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <header class="headerNav">
        <HomeHeader></HomeHeader>
    </header>
    <div class="imgNav">
        <div>
            <el-carousel :height="screenheight">
                <el-carousel-item v-for="item in imageList" :key="item">
                    <img :src="item.ImageUrl"></img>
                </el-carousel-item>
            </el-carousel>
        </div>
    </div>
    <section class="section">
        <div class="flex-warp">
            <div class="item" v-for="item in tableData" :key="item.Id" @click="onTableItemClick(item)">
                <div class="flex-warp-item">
                    <div class="flex-warp-item-box">
                        <div class="item-img">
                            <img :src="item.MainImagePath" />
                        </div>
                        <div class="item-txt">
                            <div class="item-txt-title">{{ item.SchoolName }}</div>
                            <div class="item-txt-msg">
                                <span class="item-txt-type"> {{ item.Uniformtype }}</span>
                                <span class="item-txt-name"> {{ item.Name }}</span>
                                <span style="display: inline-block;width: 50px;"> {{ item.SexName }}款</span>
                            </div>
                            <div class="item-txt-supplier">
                                <span>{{ item.SupplierName }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <footer class="footer">
        <Footer></Footer>
    </footer>

</template>
<style></style>
<style lang="scss" scoped>
.headerNav {
    margin-bottom: 65px;
}

.imgNav {
    width: 100%;
    min-width: 1200px;
    margin: 0 auto;

    img {
        width: 100%;
    }
}

.section {
    width: 1200px;
    margin: 20px auto;
    margin-bottom: 80px;
}

.footer {
    background-color: #545c64;
    color: #fff !important;
    padding: 10px 0;
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: -1;
}

.flex-warp {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin: 0 -5px;
    width: 100%;

    .item {
        flex: 1;
        margin: 0 5px 5px 5px;
        width: calc((100% - 50px) / 5);
        min-width: calc((100% - 50px) / 5);
        max-width: calc((100% - 50px) / 5);
    }

    .item:nth-child(5n) {
        margin-right: 0;
    }

    .flex-warp-item {
        padding: 5px;
        width: 100%;
        height: 325px;

        .flex-warp-item-box {
            border: 1px solid var(--next-border-color-light);
            width: 100%;
            height: 100%;
            border-radius: 2px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            border: 1px solid #CDD0D6;

            &:hover {
                cursor: pointer;
                border: 1px solid var(--el-color-primary);
                transition: all 0.3s ease;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);

                .item-txt-title {
                    color: var(--el-color-primary) !important;
                    transition: all 0.3s ease;
                }

                .item-img {
                    img {
                        transition: all 0.3s ease;
                        transform: translateZ(0) scale(1.05);
                    }
                }
            }

            .item-img {
                width: 100%;
                height: 215px;
                overflow: hidden;

                img {
                    transition: all 0.3s ease;
                    width: 100%;
                    height: 100%;
                }
            }

            .item-txt {
                font-size: 12px;
                color: #666;

                .item-txt-title {
                    font-size: 14px;
                    padding: 3px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .item-txt-msg {
                    padding: 3px;
                    display: flex;
                    flex-wrap: wrap;

                    span {
                        display: inline-block;
                        padding-right: 5px;
                    }

                    .item-txt-type {
                        // max-width: 30%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .item-txt-name {
                        color: #F56C6C;
                        // padding: 0 5px;
                        // max-width: 50%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }

                .item-txt-supplier {
                    padding: 3px;
                    color: #8d8d91;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

            }
        }
    }
}
</style>
