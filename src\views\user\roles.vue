<script setup>
import { onMounted, ref, watch } from 'vue'
import {
  Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
  getRoleListPage,
  removeRole,
  editRole,
  addRole,
} from '@/api/role.js'

import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
// const selectRows = ref([])
const filters = ref({ page: 1, size: 10 })

// RoleType 角色级别(-1：无级别，0：超级管理员；1市级，2区，3校，4企业)

const RoleTypeList = ref([
  { label: '超级管理员', value: 0 },
  { label: '市级', value: 1 },
  { label: '区县', value: 2 },
  { label: '学校', value: 3 },
  { label: '企业', value: 4 },
  { label: '班主任', value: 6 },
])

// 翻页
watch(() => filters.value.page, () => {
  HandleSearch()
})
watch(() => filters.value.size, () => {
  filters.value.page = 1
  HandleSearch()
})



//加载数据
onMounted(() => {
  HandleSearch()
})


//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
  RoleId: [
    { required: true, message: '请输入角色Id', trigger: 'change' },
  ],
  Name: [
    { required: true, message: '请输入角色名称', trigger: 'change' },
  ],
  RoleType: [
    { required: true, message: '请选择角色类别', trigger: 'change' },
  ],
}
//新增
const HandleAdd = () => {
  formData.value = { Enabled: true, ModuleName: '校服平台', ModuleSort: 0 }
  dialogVisible.value = true
}
//编辑
const HandleEdit = (row) => {

  formData.value = JSON.parse(JSON.stringify(row))
  dialogVisible.value = true
}
//删除
const HandleDel = (row) => {

  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      removeRole({ id: row.RoleId }).then((res) => {
        HandleSearch()
        ElMessage.success('删除成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })

}

//提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    if (formData.value.Id) {
      //编辑
      editRole(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '添加成功')
      })
    } else {
      //新增
      addRole(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '编辑成功')
      })
    }
  })
}

//搜索
const HandleSearch = (page) => {
  if (page) filters.value.page = page
  getRoleListPage(filters.value).then(res => {
    console.log("角色列表", res.data)
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      tableData.value = rows.data || [];
      tableTotal.value = rows.dataCount;


      // const { rows, total, other } = res.data.data
      // tableData.value = rows;
      // tableTotal.value = Number(total)


    } else {
      ElMessage.error(res.data.msg)
    }



    // tableData.value = res.data.response.data;
    // tableTotal.value = res.data.response.dataCount;


  });
}
// 重置
const HandleReset = () => {
  filters.value.key = undefined
  HandleSearch(1)
}
</script>
<template>
  <!-- 搜索 -->
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
        <el-form-item class="flexItem" label-width="90">
          <el-input class="flexContent" v-model.trim="filters.key" placeholder="角色名" clearable />
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" :icon="Search" @click="HandleSearch(1)">搜索</el-button>
          <el-button :icon="Refresh" @click="HandleReset(1)">重置</el-button>
          <div class="verticalDividel"></div>
          <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
  <!-- 内容 -->
  <el-table ref="refTable" :data="tableData" highlight-current-row border header-cell-class-name="headerClassName">
    <!-- <el-table-column type="selection" width="50"></el-table-column> -->
    <el-table-column prop="Name" label="角色名" min-width="180"></el-table-column>
    <el-table-column prop="RoleId" label="角色名ID" min-width="180"></el-table-column>
    <el-table-column prop="Description" label="说明" min-width="180"></el-table-column>
    <el-table-column prop="Enabled" label="状态" width="90" align="center">
      <template #default="{ row }">
        <el-tag :type="row.Enabled ? 'success' : 'danger'" disable-transitions>
          {{ row.Enabled ? "激活" : "禁用" }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="CreateTime" label="创建时间" min-width="140" align="center">
      <template #default="{ row }">
        {{ row.CreateTime ? row.CreateTime.substring(0, 10) : '--' }}
      </template>
    </el-table-column>
    <el-table-column prop="ModifyTime" label="更新时间" min-width="140" align="center">
      <template #default="{ row }">
        {{ row.ModifyTime ? row.ModifyTime.substring(0, 10) : '--' }}
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="120" align="center">
      <template #default="{ row }">
        <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
        <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>
  <!-- 分页 -->
  <!-- <el-row>
    <el-col class="flexBox">
      <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
        :total="tableTotal" v-model:current-page="filters.page" v-model:page-size="filters.size" />
    </el-col>
  </el-row> -->


  <!-- 弹窗 -->
  <el-dialog v-model="dialogVisible" :title="formData.Id ? '编辑' : '添加'" width="560px" :close-on-click-modal="false">
    <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="80px" status-icon>

      <el-form-item label="角色Id" prop="RoleId">
        <el-input v-model="formData.RoleId" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="角色类别" prop="RoleType">
        <el-select v-model="formData.RoleType" placeholder="请选择角色类别">
          <el-option v-for="item in RoleTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="角色名称" prop="Name">
        <el-input v-model="formData.Name" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="Enabled">
        <el-select v-model="formData.Enabled" placeholder="请选择角色状态">
          <el-option label="激活" :value="true"></el-option>
          <el-option label="禁用" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序" prop="OrderSort">
        <el-input v-model="formData.OrderSort" type="number" auto-complete="off"></el-input>
      </el-form-item>

      <el-form-item label="描述" prop="Description">
        <el-input v-model="formData.Description" auto-complete="off"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}
</style>