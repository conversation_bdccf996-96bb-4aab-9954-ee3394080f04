<script setup>
defineOptions({
    name: 'dangerchemicalsschoolcataloginitlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
    AnonDangerchemicals, Punitgetschoolbycountyid, PunitGetCountybyAdmin, DcschoolCataloginitFind, DcschoolCataloginitSave, DcschoolCataloginitUpdate
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const UnitList = ref([])
const SchoolList = ref([])
const filterSchoolList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    UseUnitId: [
        { required: true, message: '请选择区县名称', trigger: 'change' },
    ],
    SchoolId: [
        { required: true, message: '请选择所属学校', trigger: 'change' },
    ]
}
const dangerChemicalsLevel = ref('')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {

    }
    AnonDangerchemicalsUser()
    PunitGetCountybyAdminUser()
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.PId = undefined
    filters.value.Id = undefined
    filters.value.Name = undefined
    HandleTableData()
}
// 添加
const HandleAdd = (row) => {
    dialogVisible.value = true
}
// 更新
const HandleEdit = (row) => {
    DcschoolCataloginitUpdate({ id: row.Id }).then(res => {
        ElMessage.success(res.data.msg || '更新成功')
        HandleTableData()
    })
}
// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        let id = ''
        if (dangerChemicalsLevel.value == 3) {
            id = dialogData.value.SchoolId
        } else {
            id = dialogData.value.UseUnitId
        }
        DcschoolCataloginitSave({ id: id }).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '提交成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 选择区县
const CountyChange = (e, num) => {
    if (dangerChemicalsLevel.value == 3) {
        PunitgetschoolbycountyidUser(e, num)
        if (num == 1) {
            filters.value.SchoolId = undefined
        } else {
            dialogData.value.SchoolId = undefined
        }
    }
    HandleTableData()
}
// 选择学校
const schoolChange = (e) => {
    filters.value.Name = SchoolList.value.filter(item => item.UnitId == e)[0].UnitName
    HandleTableData()
}

// 获取配置文件
const AnonDangerchemicalsUser = () => {
    AnonDangerchemicals().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerChemicalsLevel.value = rows.Level
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取区县集合
const PunitGetCountybyAdminUser = () => {
    PunitGetCountybyAdmin().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            UnitList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 列表
const HandleTableData = () => {
    DcschoolCataloginitFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选择区县获取学校
const PunitgetschoolbycountyidUser = (id, num) => {
    Punitgetschoolbycountyid({ CountyId: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            filters.value.SchoolId = undefined
            if (num == 1) {
                SchoolList.value = rows || []//学校名称
            } else {
                filterSchoolList.value = rows || []//学校名称
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PId" clearable placeholder="区县名称" style="width: 160px"
                            @change="CountyChange($event, 1)">
                            <el-option v-for="item in UnitList" :key="item.UnitId" :label="item.CountyName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem" v-if="dangerChemicalsLevel == 3">
                        <el-select v-model="filters.Id" clearable placeholder="学校名称" @change="schoolChange"
                            style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="AreaName" label="区县名称" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Name" v-if="dangerChemicalsLevel == 3" label="学校名称" min-width="100"
                align="center"></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">更新</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="添加单位基础库">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="140px"
                    status-icon>
                    <el-form-item label="区县名称：" prop="UseUnitId">
                        <el-select v-model="dialogData.UseUnitId" @change="CountyChange($event, 1)" style="width: 80%">
                            <el-option v-for="item in UnitList" :key="item.UnitId" :label="item.CountyName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所属学校：" prop="SchoolId" v-if="dangerChemicalsLevel == 3">
                        <el-select v-model="dialogData.SchoolId" style="width: 80%">
                            <el-option v-for="item in filterSchoolList" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped></style>