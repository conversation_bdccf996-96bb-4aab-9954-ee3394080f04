<script setup>
defineOptions({
    name: 'modulemanagement'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { Refresh, Search, FolderAdd } from '@element-plus/icons-vue'
import {
    FindmoduleList, ModuleinsertUpdate, ModulesetStatuz
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { icons, svgIconsList } from "@/utils/icons.js";
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const UnitList = ref([])
const UsageList = ref([])
const dialogVisible = ref(false)
const dialogData = ref({})
const editId = ref()
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '请输入模块名称', trigger: 'change' },
    ],
    UseUnitId: [
        { required: true, message: '请选择应用单位', trigger: 'change' },
    ],
    Usage: [
        { required: true, message: '请选择使用方式', trigger: 'change' },
    ],
    Logo: [
        { required: true, message: '请选择菜单图标', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// 创建
const HandleAdd = (row) => {
    editId.value = undefined
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value.Name = undefined;
        dialogData.value.UseUnitId = undefined;
        dialogData.value.Usage = undefined;
    })
}
// 修改
const HandleEdit = (row) => {
    editId.value = row.Id
    dialogData.value.Name = row.Name;
    dialogData.value.UseUnitId = row.StrUseUnitId;
    dialogData.value.Usage = row.StrUsage;
    dialogData.value.Logo = row.Logo;
    dialogData.value.Sort = row.Sort;
    dialogVisible.value = true
}

// 启用禁用
const HandleEnable = (row) => {
    let msg = ''
    if (row.Statuz == 2) {
        msg = `确定要启用 [${row.Name}]吗?`
    } else {
        msg = `确定要禁用 [${row.Name}]吗?`
    }

    ElMessageBox.confirm(msg)
        .then(() => {
            ModulesetStatuz({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleSearch()
                    ElMessage.success(res.data.msg || '设置成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
}

//创建/修改选用组织  提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        ModuleinsertUpdateUser()
    })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    FindmoduleList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
            if (isFirst) {
                UnitList.value = other.listUseUnitId || [];//应用单位
                UsageList.value = other.listUsage || [];//使用方式
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 添加/修改提交
const ModuleinsertUpdateUser = () => {
    let formData = {
        Id: editId.value,
        Name: dialogData.value.Name,
        UseUnitId: dialogData.value.UseUnitId,
        Usage: dialogData.value.Usage,
        Logo: dialogData.value.Logo,
        Sort: dialogData.value.Sort,
    }
    ModuleinsertUpdate(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false

            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="模块名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="模块名称" min-width="120"></el-table-column>
            <el-table-column prop="UnitName" label="应用单位" min-width="160"></el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.Statuz == 1 ? 'primary' : row.Statuz == 2 ? 'danger' : 'success'"
                        disable-transitions>
                        {{ row.Statuz == 1 ? "启用" : row.Statuz == 2 ? "禁用" : "内测" }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="Sort" label="排序" min-width="100" align="center"> </el-table-column>
            <el-table-column prop="CreateTime" label="创建时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.CreateTime.substring(0, 10) }}
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link v-if="row.Statuz == 2" @click="HandleEnable(row)">启用</el-button>
                    <el-button type="primary" link v-if="row.Statuz == 1" @click="HandleEnable(row)">禁用</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="editId ? '修改模块' : '添加模块'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                    status-icon style="padding-right: 80px;">
                    <el-form-item label="模块名称：" prop="Name">
                        <el-input v-model="dialogData.Name"></el-input>
                    </el-form-item>
                    <el-form-item label="应用单位：" prop="UseUnitId">
                        <el-select v-model="dialogData.UseUnitId">
                            <el-option v-for="item in UnitList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="使用方式：" prop="Usage">
                        <el-radio-group v-model="dialogData.Usage">
                            <el-radio v-for="item in UsageList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="菜单图标：" prop="Logo">
                        <el-select v-model="dialogData.Logo" placeholder="请选择图标">
                            <template #label="{ label, value }">
                                <SvgIcon :name="value" class="svg-icon"> </SvgIcon>
                            </template>
                            <el-option v-for="t in svgIconsList" :key="t.icon" :label="t.name" :value="t.icon">
                                <template #default>
                                    <div style="display: flex; align-items: center;">
                                        <SvgIcon :name="t.icon" class="svg-icon"> </SvgIcon>
                                        <span style="padding-left: 5px;">{{ t.name }}</span>
                                    </div>

                                </template>
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序值：" prop="Sort">
                        <el-input type="number" v-model="dialogData.Sort"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="状态：" prop="Statuz">
                    <el-radio-group v-model="formData.Statuz">
                        <el-radio v-for="item in StatuzList" :key="item.value" :value="item.value">
                            {{ item.label }}</el-radio>
                    </el-radio-group>
                </el-form-item> -->
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>