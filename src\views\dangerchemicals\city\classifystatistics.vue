<script setup>
defineOptions({
    name: 'dangerchemicalscityclassifystatistics'
});
import {
    Refresh, Search, Position
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DccatalogGetClassTwo, PUnitGetCountyByCityId, DcCityClassifyStockNumStatisticsFind
} from '@/api/dangerchemicals.js'
import { ExcelDownload } from "@/utils/index.js"
import {
    DcCityClassifyStockNumStatisticsExport
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { fileDownload } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({
    pageIndex: 1,
    pageSize: 10,
    sortModel: [
        { SortCode: "AreaId", SortType: "ASC" },
        { SortCode: "TwoCatalogId", SortType: "ASC" },
        { SortCode: "SchoolCatalogId", SortType: "ASC" }]
})
const summation = ref({})
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
    PUnitGetCountyByCityIdUser()

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];

    // 单元格赋值  
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        for (let key in summation.value) {
            if (summation.value.hasOwnProperty(key)) {
                // console.log(`键名: ${key}, 键值: ${summation.value[key]}`);
                if (column.property == key) {
                    sums[index] = summation.value[key]
                    return;
                }
            }
        }
    });
    return sums;
}

// 列表
const HandleTableData = () => {
    DcCityClassifyStockNumStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            summation.value = other[0] || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.CountyId = undefined
    filters.value.TwoBaseCatalogId = undefined
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.IsMayUse = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

const filtersChange = () => { HandleTableData() }
const StatuzSolicitedList = ref([])
const cityList = ref([])
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取下属单位集合。
const PUnitGetCountyByCityIdUser = () => {
    PUnitGetCountyByCityId({ CityId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            cityList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//导出  
const HandleExport = () => {
    DcCityClassifyStockNumStatisticsExport(filters.value).then(res => {
        ExcelDownload(res)
    });
}
// 区县名称查看
const HandleLook = (row) => {
    router.push({
        path: '/dangerchemicals/county/classifystatistics',
        query: {
            countyId: row.CountyId,
            countyName: row.AreaName,
            path: '/dangerchemicals/city/classifystatistics',
        }
    })
}
// 明细查看  
const HandleDetail = (row) => {
    router.push({
        path: '/dangerchemicals/county/classifystatistics',
        query: {
            countyId: row.CountyId,
            twoCatalogId: row.TwoCatalogId,
            model: row.Model,
            countyName: row.AreaName,
            name: row.Name,
            path: '/dangerchemicals/city/classifystatistics',
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in cityList" :key="item.UnitId" :label="item.CountyName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoBaseCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.IsMayUse" clearable placeholder="是否可用" style="width: 160px"
                            @change="filtersChange">
                            <el-option label="是" value="1" />
                            <el-option label="否" value="0" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Model" clearable placeholder="规格属性"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="AreaName" label="区县名称" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.SchoolMaterialModelId" type="primary" link @click="HandleLook(row)">
                        {{ row.AreaName }}</el-button>
                    <span v-else>{{ row.AreaName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="TwoCatalogName" label="危化品分类" min-width="120" align="center"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column label="数量">
                <el-table-column prop="StockNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="StockNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="易燃">
                <el-table-column prop="BurnNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsBurn == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="BurnNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsBurn == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="易爆">
                <el-table-column prop="BlastNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsBlast == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="BlastNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsBlast == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="易制爆">
                <el-table-column prop="DetonateNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsDetonate == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="DetonateNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsDetonate == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="有毒">
                <el-table-column prop="ToxicNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsToxic == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="ToxicNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsToxic == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="剧毒">
                <el-table-column prop="HyperToxicNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsHyperToxic == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="HyperToxicNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsHyperToxic == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="易制毒">
                <el-table-column prop="PoisonNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsPoison == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="PoisonNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsPoison == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="腐蚀">
                <el-table-column prop="CorrodeNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsCorrode == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="CorrodeNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsCorrode == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="其它">
                <el-table-column prop="OtherNum1" label="克" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsOther == 1 && row.UnitsMeasurement != '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
                <el-table-column prop="OtherNum2" label="毫升" min-width="100" align="right">
                    <template #default="{ row }">
                        {{ row.IsOther == 1 && row.UnitsMeasurement == '毫升' ? row.StockNum : '' }}
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column prop="IsMayUse" label="是否可用" min-width="110" align="center">
                <template #default="{ row }">
                    <span :style="{ color: row.IsMayUse == 0 ? 'red' : '' }">
                        {{ row.IsMayUse == 1 ? '可用' : row.IsMayUse == 0 ? '不可用' : '' }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="明细" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>