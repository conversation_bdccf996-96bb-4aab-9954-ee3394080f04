<script setup>
defineOptions({
    name: 'treasurymanagement'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { Refresh, Search } from '@element-plus/icons-vue'
import {
    FindprocessList, Processfindbyid, SourceFundInputDataSet
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10, IsSubmitStatuz: true, IsOpen: 1 })
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const projectListCodeList = ref([])
const writeSourceFundStatuzList = ref([])
const InputDataTypeList = ref([])
const statuzCheck = ref([])//选择的状态集合
const dialogVisible = ref(false)
const dialogData = ref({})
const editId = ref()
const refForm = ref()
const ruleForm = {}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})
// 修改
const HandleEdit = (row) => {
    editId.value = row.Id
    ProcessfindbyidUser(row.Id)
    dialogVisible.value = true
}


// 设置资金库
const HandleSourceOfFunds = (row) => {
    console.log(row)
    router.push({ path: "./process/sourceoffunds", query: { ModuleId: row.ModuleId, ProcessId: row.Id } })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    FindprocessList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
            if (isFirst) {
                InputDataTypeList.value = other.listInputDataType || [];//写入第三方库类型
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 详情
const ProcessfindbyidUser = (id) => {
    Processfindbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other, headers } = res.data.data
            dialogData.value = {
                ProcessId: rows.Id,
                ProcessName: rows.ProcessName,
                InputDataType: String(rows.InputDataType),
                FieldCode: rows.FieldCode,
                TypeCode: rows.TypeCode,
                WriteSourceFundStatuz: rows.WriteSourceFundStatuz,
            }
            // if (rows.WriteSourceFundStatuz) {
            //     statuzCheck.value = rows.WriteSourceFundStatuz.split(',')
            // } else {
            //     statuzCheck.value = []
            // }
            // statuzCheck.value = rows.WriteSourceFundStatuz.split(',') 
            if (other) {
                projectListCodeList.value = other.listProjectListCode || [];//项目清单Code集合
                // writeSourceFundStatuzList.value = other.listWriteSourceFundStatuz || [];//写入状态
            }
            if (headers) {

                headers.forEach((item, index) => {
                    item.value = item.ProcessNodeId + item.Statuz
                })
                writeSourceFundStatuzList.value = headers;//写入状态

                // 获取已经被选择后生成Id的
                let arr = headers.filter(item => item.Id != '0')
                statuzCheck.value = arr.map(item => item.value)
                // console.log('statuzCheck.value', statuzCheck.value)

                // console.log('writeSourceFundStatuzList.value', writeSourceFundStatuzList.value)
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//创建/修改选用组织  提交
const HandleSubmit = () => {
    console.log(statuzCheck.value)
    const result = [];
    // 遍历数组 statuzCheck.value
    statuzCheck.value.forEach(id => {
        // 在数组 writeSourceFundStatuzList.value 中查找所有 value 相同的项
        const itemsWithSameId = writeSourceFundStatuzList.value.filter(item => item.value === id);
        // 将这些项添加到结果数组中
        result.push(...itemsWithSameId);
    });
    console.log(result);
    let listSrt = result.map(item => {
        return {
            ProcessNodeId: item.ProcessNodeId,
            Statuz: item.Statuz
        }
    })

    let formatData = {
        ProcessId: dialogData.value.ProcessId,
        ProcessName: dialogData.value.ProcessName,
        InputDataType: dialogData.value.InputDataType,
        // WriteSourceFundStatuz: statuzCheck.value.join(',')
        ListSet: listSrt
    }
    if (dialogData.value.InputDataType == 2) {
        formatData.ProjectListCode = dialogData.value.ProjectListCode
    } else if (dialogData.value.InputDataType == 3) {
        formatData.FieldCode = dialogData.value.FieldCode
        formatData.TypeCode = dialogData.value.TypeCode
    }

    // dialogData.value.WriteSourceFundStatuz = statuzCheck.value.join(',')
    console.log(formatData)

    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        SourceFundInputDataSet(formatData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '修改成功')
                dialogVisible.value = false

                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="流程名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="ProcessName" label="流程名称" min-width="160"></el-table-column>
            <el-table-column prop="UnitName" label="使用单位" min-width="160"></el-table-column>
            <el-table-column prop="CreateTime" label="创建时间" min-width="160" align="center">
                <template #default="{ row }">
                    {{ row.CreateTime.substring(0, 10) }}
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="160" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleSourceOfFunds(row)">配置资金库</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <!-- 创建修改弹窗 -->
        <app-box v-model="dialogVisible" :width="780" :lazy="true" :title="editId ? '修改流程' : '添加流程'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px"
                    status-icon>
                    <el-form-item label="流程名称：" prop="ProcessName">
                        <el-input v-model="dialogData.ProcessName" disabled style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="写入第三方库类型：">
                        <el-radio-group v-model="dialogData.InputDataType">
                            <el-radio v-for="item in InputDataTypeList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="资金分配指定的字段编码：" v-if="dialogData.InputDataType == 2">
                        <el-select v-model="dialogData.ProjectListCode" style="width: 80%">
                            <el-option v-for="item in projectListCodeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="资金分配指定的字段编码：" v-if="dialogData.InputDataType == 3">
                        <el-input v-model="dialogData.FieldCode" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="资金分配指定的数据分类编码：" v-if="dialogData.InputDataType == 3">
                        <el-input v-model="dialogData.TypeCode" @input="integerLimitInput($event, 'TypeCode')"
                            style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="状态：">
                        <el-checkbox-group v-model="statuzCheck">
                            <el-checkbox v-for="item in writeSourceFundStatuzList" :key="item.value"
                                :label="item.NodeShowName + '(' + item.StatuzName + ')'" :value="item.value">
                            </el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>