<script setup>
import { onMounted, ref, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
    Syspagefind,
    Rolefind,
    Objectpermissionfind,
    Objectpermissionsave
} from '@/api/user.js'


// 表格初始化
const tableData = ref([])
const treeRef = ref()
const pageFilters = ref({ pageIndex: 1, pageSize: 10000, sortModel: [{ SortCode: "Id", SortType: "ASC" }] })
const roleFilters = ref({ pageIndex: 1, pageSize: 1000, sortModel: [{ SortCode: "Id", SortType: "ASC" }] })
const permission = ref({ pageIndex: 1, pageSize: 1000, ObjId: '0', ObjType: 2, FunType: 2, sortModel: [{ SortCode: "Id", SortType: "ASC" }] })
const permissionSave = ref({ funIds: '', funType: 2, ObjType: 2 })
const options = ref([])



//加载数据
onMounted(() => {
    SyspagefindUser()
    RolefindUser()
})

//读取权限
const HandleRead = () => {

    ObjectpermissionfindUser()
}
// 保存权限
const HandleSave = () => {
    let funIds = treeRef.value.getCheckedKeys()
    permissionSave.value.funIds = funIds.join(',')
    ObjectpermissionsaveUser()
}
// 获取所有页面
const defaultChecked = ref([])
// 选择角色
const ObjIdChange = (e) => {
    permissionSave.value.objId = e
    treeRef.value.setCheckedKeys([], false)
    ObjectpermissionfindUser()
}

const props = {
    label: 'Name',
}
// 获取所有页面
const SyspagefindUser = () => {

    Syspagefind(pageFilters.value).then(res => {
        if (res.data.flag == 1) {
            //   tableData.value = res.data.data.rows;
            const { rows } = res.data.data

            // 一级菜单
            let arr1 = rows.filter(t => !t._parentId)
            // 二三级菜单
            let arr2 = rows.filter(t => t._parentId)
            // 将三级菜单放入二级子集内
            let data1 = nestChildren(arr2)

            function mergeArrays(arr1, arr2) {
                return arr1.map(item => ({
                    ...item,
                    children: arr2.filter(child => child._parentId === item.Id).map(child => ({ ...child }))
                }));
            }
            // 将二级菜单放入一级子集内
            tableData.value = mergeArrays(arr1, data1);

            ObjectpermissionfindUser()
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 获取所有角色
const RolefindUser = () => {

    Rolefind(roleFilters.value).then(res => {
        if (res.data.flag == 1) {
            options.value = res.data.data.rows
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 获取角色权限
const ObjectpermissionfindUser = () => {

    // permission.value.ObjId = Number(filters.value.roles)
    Objectpermissionfind(permission.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // console.log("获取角色权限", rows)
            defaultChecked.value = rows.map(item => item.FunId)
            // console.log("defaultChecked.value", defaultChecked.value)

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

const nestChildren = (arr) => {
    // 副本操作以避免修改原数组
    let arrCopy = JSON.parse(JSON.stringify(arr));
    // arrCopy=arrCopy.reverse()
    // 遍历数组，为每个元素寻找子元素

    arrCopy.forEach(parent => {
        // 筛选出子元素
        const children = arrCopy.filter(child => child._parentId === parent.Id);

        // 将子元素添加到当前元素的children属性中
        parent.children = children.map(child => ({ ...child }));

        // 为了避免循环引用，从子元素列表中移除已添加的子元素
        parent.children.forEach(child => {
            const index = arrCopy.indexOf(child);
            if (index > -1) {
                arrCopy.splice(index, 1);
            }
        });
    });
    arrCopy.filter(t => !t._parentId);
    // 返回处理后的数组，此时arrCopy中仅包含顶级元素  
    return arrCopy
}
// 保存权限
const ObjectpermissionsaveUser = () => {
    Objectpermissionsave(permissionSave.value).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            ObjectpermissionfindUser()

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

const isShow = ref(false)

</script>
<template>
    <!-- 搜索 -->
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="permission" class="flexBox">
                <el-form-item label="角色：" class="flexItem" label-width="60">

                    <el-select v-model="permission.ObjId" filterable style="width: 180px" placeholder="可搜索选择"
                        @change="ObjIdChange">
                        <el-option v-for="item in options" :key="item.Id" :label="item.Name" :value="item.Id" />
                    </el-select>

                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" plain @click="HandleRead">读取权限</el-button>
                    <el-button type="primary" plain @click="HandleSave">保存权限</el-button>
                </el-form-item>
            </el-form>

        </el-col>
    </el-row>

    <el-tree ref="treeRef" :data="tableData" show-checkbox default-expand-all node-key="Id" highlight-current
        :check-strictly="true" :default-checked-keys="defaultChecked" :props="props">
        <template #default="{ node, data }">
            <el-scrollbar class="custom-tree-node-right">
                <div style="display: flex; justify-content: space-between; /* 左右分布 */">
                    <div style=" margin-right: 10px;width: 200px; ">{{ data.Name }}</div>
                    <div style=" margin-right: 10px;width: 500px;">
                        <el-tooltip class="item" effect="dark" :content="data.Memo" placement="top" :disabled="isShow">
                            <div class="taskNameConent"> {{ data.Memo }} </div>
                        </el-tooltip>
                    </div>
                </div>

            </el-scrollbar>
        </template>
    </el-tree>
</template>
<style lang="scss" scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}

.taskNameConent {
    width: 100%;
    /* 具体宽度，例如 200px 或 100% */
    ;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>