<script setup>
defineOptions({
    name: 'userschooluser'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Search, Refresh
} from '@element-plus/icons-vue'
import {
    Schoolinfouserfind,
} from '@/api/user.js'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页
const userStore = useUserStore()
const route = useRoute()
const hUnitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filtersKey = ref({ key: '', value: 'Name' })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const options = ref([
    { value: 'Name', label: '姓名', },
    { value: 'Mobile', label: '手机', },
    { value: 'AcctName', label: '账号', }
])

//加载数据 
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData();
    }
})
onActivated(() => {
    nextTick(() => {
        // tag标签添加参数
        tagsListStore(userStore.tagsList, route)
        if (!route.query.isTagRouter) {
            HandleTableData();
        }
    })
})
//列表
const HandleTableData = () => {
    filters.value.UnitId = route.query.UnitId
    // 按类型搜索
    filters.value.Name = undefined;
    filters.value.Mobile = undefined;
    filters.value.AcctName = undefined;

    // 按类型搜索
    if (filtersKey.value.key) {
        filters.value[filtersKey.value.value] = filtersKey.value.key;
    } else {
        filters.value[filtersKey.value.value] = undefined;
    }

    Schoolinfouserfind(filters.value).then(res => {
        if (res.data.flag == 1) {
            tableData.value = res.data.data.rows;
            tableTotal.value = Number(res.data.data.total);
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value.key = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
</script>
<template>
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                <el-form-item label="" class="flexItem" label-width="60">
                    <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="max-width: 300px"
                        class="input-with-select">
                        <template #prepend>
                            <el-select v-model="filtersKey.value" style="width: 100px">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                    <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
        header-cell-class-name="headerClassName">
        <el-table-column prop="Name" label="姓名" min-width="160" align="center"></el-table-column>
        <el-table-column prop="Mobile" label="手机号码" min-width="160" align="center"></el-table-column>
        <el-table-column prop="AcctName" label="登录账号" min-width="160" align="center"></el-table-column>
        <el-table-column prop="RoleNames" label="角色" min-width="160" show-overflow-tooltip align="center">
        </el-table-column>
        <el-table-column prop="LoginCount" label="登录次数" min-width="100" align="center"></el-table-column>
        <!-- <el-table-column prop="LastLogin" label="上次登录时间" min-width="180" align="center"></el-table-column> -->
        <template #empty>
            <el-empty description="没有数据"></el-empty>
        </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
        @handleChange="handlePage" />
</template>
<style lang="scss" scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}

.taskNameConent {
    width: 100%;
    /* 具体宽度，例如 200px 或 100% */
    ;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>