import { ElMessageBox, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
//验证规则
export const rules = {
  // 必填
  required(title) {
    return [
      {
        required: true,
        message: `请输入${title}`,
        trigger: 'blur'
      }
    ]
  },
  // 最大长度
  maxLength(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: new RegExp(`^.{1,${arg}}$`),
        message: `长度最多${arg}位`,
        trigger: 'blur'
      }
    ]
  },
  // 最小长度
  minLength(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: new RegExp(`^.{${arg},}$`),
        message: `长度至少${arg}位`,
        trigger: 'blur'
      }
    ]
  },
  // 长度范围
  rangeLength(title, min, max, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: new RegExp(`^.{${min},${max}}$`),
        message: `长度至少${min}位，最多${max}位`,
        trigger: 'blur'
      }
    ]
  },
  // 数字(小数)
  decimals(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: new RegExp(`^\\d+(\\.\\d{1,${arg}})?$`),
        message: `请输入数字，小数点后只能1~${arg}位`,
        trigger: 'blur'
      }
    ]
  },
  // 正整数
  integer(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^\d+$/,
        message: '请输入正整数',
        trigger: 'blur'
      }
    ]
  },
  // 金额
  amount(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: new RegExp(`^-?\\d+(\\.\\d{1,${arg}})?$`),
        message: `请输入有效金额`,
        trigger: 'blur'
      }
    ]
  },
  // 汉字
  chs(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^[\u4e00-\u9fa5]+$/,
        message: '请输入汉字',
        trigger: 'blur'
      }
    ]
  },
  // 大写英文字符/数字
  upperCase(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^[A-Z,0-9]+$/,
        message: '请输入大写英文字符/数字',
        trigger: 'blur'
      }
    ]
  },
  // 小写英文字符/数字
  lowerCase(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^[a-z,0-9]+$/,
        message: '请输入小写英文字符/数字',
        trigger: 'blur'
      }
    ]
  },
  // 邮政编码
  zip(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^[1-9]\d{5}$/,
        message: '邮政编码不存在',
        trigger: 'blur'
      }
    ]
  },
  // QQ号码
  qq(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^[1-9]\d{4,10}$/,
        message: 'QQ号码不正确',
        trigger: 'blur'
      }
    ]
  },
  // 手机号
  mobile(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^1\d{10}$/,
        message: '手机号码不正确',
        trigger: 'blur'
      }
    ]
  },
  // 电话号码
  tel(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^(\d{3,4}-)?\d{7,8}$/,
        message: '电话号码不正确',
        trigger: 'blur'
      }
    ]
  },
  // 手机号或电话号码
  phone(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^(1\d{10}|(\d{3,4}-)?\d{7,8})$/,
        message: '请输入正确的手机号或电话号码',
        trigger: 'blur'
      }
    ]
  },
  // 密码
  safepass(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern:
          /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
        message: '密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种',
        trigger: 'blur'
      }
    ]
  },
  // 邮箱
  email(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        message: '邮箱不正确',
        trigger: 'blur'
      }
    ]
  },
  // 身份证
  idCard(title, arg, idRequired) {
    return [
      {
        required: idRequired,
        message: `请输入${title}`,
        trigger: 'blur'
      },
      {
        pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\d{3}[\dXx]$/,
        message: '请输入正确的身份证号码',
        trigger: 'blur'
      }
    ]
  }
}

// 校验规则类型
export const rulesData = [
  { label: '最大长度', value: 'maxLength' },
  { label: '最小长度', value: 'minLength' },
  { label: '长度范围', value: 'rangeLength' },
  { label: '数字(小数)', value: 'decimals' },
  { label: '整数', value: 'integer' },
  { label: '金额', value: 'amount' },
  { label: '汉字', value: 'chs' },
  { label: '大写英文字符/数字', value: 'upperCase' },
  { label: '小写英文字符/数字', value: 'lowerCase' },
  { label: '手机号', value: 'mobile' },
  { label: '电话号码', value: 'tel' },
  { label: '手机号/电话号码', value: 'phone' },
  { label: '身份证', value: 'idCard' },
  { label: '邮政编码', value: 'zip' },
  { label: 'QQ号码', value: 'qq' },
  { label: '密码', value: 'safepass' },
  { label: '邮箱', value: 'email' }
]
// 小数val：值, len：小数点后几位
export function rulesLimit(val, len) {
  let e =
    ('' + val) // 第一步：转成字符串
      .replace(/[^\d^\.]+/g, '') // 第二步：把不是数字，不是小数点的过滤掉
      .replace(/^0+(\d)/, '$1') // 第三步：第一位0开头，0后面为数字，则过滤掉，取后面的数字
      .replace(/^\./, '0.') // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
      .match(new RegExp(`^\\d*(\\.?\\d{0,${len}})`, 'g'))[0] || '' // 第五步：动态限制小数位数
  return e
}

// 整数
export function rulesIntegerLimit(val) {
  // console.log(val)
  let e =
    ('' + val)
      .replace(/[^\d]/g, '')
      .replace(/^0+(\d)/, '$1')
      .match(/^\d*/g)[0] || ''
  if (e === '') {
    return ''
  } else {
    return Number(e)
  }
}
// 金额val：值, len：小数点后几位

export function rulesAmountLimit(val, len) {
  console.log(val, len)
  const strVal = String(val)

  // 动态构造限制小数位数的正则
  const decimalLimitRegex = new RegExp(`^(-?\\d*\\.?\\d{0,${len}}).*`, 'g')

  // 允许负号、数字和小数点，并处理各种情况
  const processedValue = strVal
    .replace(/[^-\d.]/g, '') // 只允许数字、负号和小数点
    .replace(/(?!^)-/g, '') // 防止在中间输入负号（只能开头）
    .replace(/^(-)|(-)/g, '$1') // 确保只有一个负号（在开头）
    .replace(/\.+/g, '.') // 只允许一个小数点
    .replace(decimalLimitRegex, '$1') // 动态限制小数点后位数
    .replace(/^\./, '0.') // 如果开头是小数点，补零
    .replace(/^(-?)0+(\d)/, '$1$2') // 去除前导零（但保留负号）

  return processedValue
}
