import { defineStore } from 'pinia'
import { ref, nextTick } from 'vue'
import router from '@/router'
// 用户信息持久化

export const useUserStore = defineStore(
  'user',
  () => {
    const token = ref('')
    const expires_in = ref(0)
    const token_type = ref('')
    const menu = ref([])
    const userInfo = ref({})
    const defaultSet = ref({})
    const articleFooter = ref([])
    const serviceData = ref({})
    const aboutData = ref({})
    const currentPage = ref('')
    const onePage = ref('')
    const platformType = 2 //平台类型 1:校服平台  2:工作流管理平台（审批配置平台） 3：危化品
    const previousPath = ref('/')
    const isImgCode = ref(0)
    const noticeChecked = ref(false)
    const captchaimage = ref({ Img: '', Uuid: '' })
    const pageTitleObj = ref({
      projectTitle: '创建/修改选用方案',
      managementTitle: '创建/修改合同履约',
      applicationTitle: '创建/修改采购申请',
      biddingTitle: '创建/修改招标结果',
      solicitsubscripTitle: '生成/修改征订单',
      evaluateTitle: '创建/修改评价',
      maintainTitle: '添加/修改校服',
      examineTitle: '项目审批',
      applyTitle: '待审核物品'
    })

    // 菜单缓存
    const firstPage = {} // 首页
    const prePage = ref({}) //上页
    const curPage = ref({}) // 当前页
    const tagsList = ref([]) // 列表

    // 设置一个激活导航
    const setOneActiveTag = (path, prePath) => {
      // console.log('setOneActiveTag', path, prePath)
      // 去除首页标签
      tagsList.value = tagsList.value.filter(
        (t) =>
          ![
            '/',
            '/login',
            '/reg',
            '/exhibition',
            '/information',
            '/articlelist',
            '/articledetail',
            '/unitauthentic',
            '/preview'
          ].includes(t.path)
      )

      //点了自己
      // if (newTag.path === curPage.value.path) return;
      let allRoutes = router.getRoutes() //获取所有路由
      let findRoute = allRoutes.find((t) => t.path === path)
      let findTag = tagsList.value.find((t) => t.path === path)
      tagsList.value.forEach((t) => {
        t.active = false
      })

      if (findRoute) {
        if (findTag) {
          findTag.title = findRoute.meta.title
        } else {
          findTag = {}
          findTag.title = findRoute.meta.title
          findTag.path = findRoute.path
          tagsList.value.push(findTag)
        }
        findTag.active = true
        curPage.value = findTag
      }
      if (prePath) {
        let findPreRoute = allRoutes.find((t) => t.path === prePath)
        if (findPreRoute) prePage.value = findPreRoute
      }
      // console.log('tag标签数组tagsList', tagsList.value)
    }

    // 删除一个导航
    const removeOneTag = (path) => {
      let path111 = tagsList.value.filter((t) => t.path === prePage.value.path)
      // console.log('tagsList.valuetagsList.valuetagsList.value', tagsList.value)
      //不允许删除只有一个标签
      if (tagsList.value.length == 1) return
      // // 只有一个标签时删除返回首页
      // if (tagsList.value.length == 1) {
      //   router.push('/')
      // };
      //删除
      let tag = tagsList.value.find((t) => t.path === path)
      if (!tag) return
      //如果是激活就跳转到上一个路由
      if (tag.active) {
        prePage.value.active = true
        if (prePage.value.path) {
          nextTick(() => {
            // 跳转到上一个路由,判断上个路由是否已被删除
            let arr = tagsList.value.filter((t) => t.path == prePage.value.path)
            let i = tagsList.value.length - 1
            // console.log('arr', arr)
            if (arr.length) {
              // 将路由参数加上，避免删除标签后参数消失，页面数据出错
              let pathObj = arr[0].query || {}
              pathObj.isTagRouter = true
              // console.log('pathObj', pathObj)
              router.push({ path: prePage.value.path, query: pathObj })
            } else {
              // 如果删除跳到最后一个标签
              let pathObj = tagsList.value[i].query || {}
              pathObj.isTagRouter = true
              router.push({ path: tagsList.value[i].path, query: pathObj })
            }

            prePage.value = {}
          })
        } else {
          // 跳转到首个路由
          router.push(onePage.value)
        }
      }

      tagsList.value.splice(
        tagsList.value.findIndex((t) => t.path === path),
        1
      )
      tagsList.value = tagsList.value.filter((t) => t.path) //去除无路径标签
    }

    // 关闭其他页面
    const closeOtherPage = () => {
      // if (firstPage.path === curPage.value.path) {
      //   firstPage.active = true
      //   tagsList.value = [firstPage]
      // } else {
      //   firstPage.active = false
      //   tagsList.value = [firstPage, curPage.value]
      //   router.push(curPage.value.path)
      // }
      // tagsList.value = tagsList.value.filter((t) => t.path) //去除无路径标签
      tagsList.value = tagsList.value.filter(
        (t) =>
          t.path == curPage.value.path ||
          t.path == '/approval/home/<USER>' ||
          t.path == '/uniform/home/<USER>'
      )
    }

    // 关闭所有页面
    const closeAllPage = () => {
      tagsList.value = [firstPage]
      tagsList.value = tagsList.value.filter((t) => t.path) //去除无路径标签
      console.log('onePage.value', onePage.value)
      router.push('/' + onePage.value) //回到第一个菜单页面
      // router.push('/')//回到首页
    }

    const timer = ref()

    // 设置token
    const setToken = (val) => {
      token.value = val
    }
    // 设置token过期时间
    const setExpiresin = (val) => {
      expires_in.value = val
    }
    // 设置token类型
    const setTokenType = (val) => {
      token_type.value = val
    }
    // 设置登录失败次数
    const setImgCode = (val) => {
      isImgCode.value = val
    }
    // 设置菜单
    const setMenu = (val) => {
      menu.value = val
    }
    // 设置菜单
    const setTagsList = (val) => {
      tagsList.value = val
    }

    // 设置用户信息
    const setUserInfo = (val) => {
      userInfo.value = val
    }
    // 设置新增/修改页面title（tag标签）
    const setPageTitleObj = (val) => {
      pageTitleObj.value = val
    }
    // 设置当前页面
    const setCurrentPage = (val) => {
      currentPage.value = val
    }
    // 设置当前页面
    const setOnePage = (val) => {
      onePage.value = val
    }
    // 设置配置信息
    const setDefaultSet = (val) => {
      defaultSet.value = val
    }
    // 设置配置信息
    const setArticleFooter = (val) => {
      articleFooter.value = val
    }
    // 设置平台客服（资讯）信息
    const setServiceData = (val) => {
      serviceData.value = val
    }
    // 设置关于我们（资讯信息
    const setAboutData = (val) => {
      aboutData.value = val
    }
    // 路由跳转的上一页路径（主要用于404页面返回）
    const setPreviousPath = (val) => {
      previousPath.value = val
    }
    // 保存用户条款
    const setNoticeChecked = (val) => {
      noticeChecked.value = val
    }
    // 保存用户条款
    const setCaptchaImage = (val) => {
      captchaimage.value = val
    }

    // 退出登录
    const logout = () => {
      token.value = ''
      token_type.value = ''
      expires_in.value = 0
      menu.value = []
      currentPage.value = ''
      onePage.value = ''
      firstPage.active = true
      tagsList.value = []
      prePage.value = {}
      curPage.value = {}
      userInfo.value = {}
      pageTitleObj.value = {}
    }

    const isRemember = ref(false)
    const name = ref('')
    const pass = ref('')

    const setName = (newName) => {
      name.value = newName
    }
    const setPass = (newPass) => {
      pass.value = newPass
    }

    return {
      token,
      expires_in,
      token_type,
      isImgCode,
      name,
      pass,
      menu,
      currentPage,
      onePage,
      isRemember,
      tagsList,
      prePage,
      curPage,
      userInfo,
      pageTitleObj,
      defaultSet,
      articleFooter,
      serviceData,
      aboutData,
      platformType,
      previousPath,
      noticeChecked,
      captchaimage,
      setDefaultSet,
      setArticleFooter,
      setServiceData,
      setAboutData,
      setUserInfo,
      setPageTitleObj,
      setName,
      setPass,
      setToken,
      setExpiresin,
      setTokenType,
      setImgCode,
      setMenu,
      logout,
      setOneActiveTag,
      removeOneTag,
      closeOtherPage,
      closeAllPage,
      setCurrentPage,
      setOnePage,
      setTagsList,
      setPreviousPath,
      setNoticeChecked,
      setCaptchaImage
    }
  },
  {
    persist: true //持久化
  }
)
