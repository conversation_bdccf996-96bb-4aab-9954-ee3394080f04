<script setup>
defineOptions({
    name: 'evaluateexaminelist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    EvaluateGetevaluatelist
} from '@/api/purchase.js'
import {
    Punitgetschoolbycountyid
} from '@/api/user.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const CountyList = ref([])
const SchoolList = ref([])
const SupplierList = ref([])
const StatuzList = ref([])

//加载数据
onMounted(() => {
    yearDateList.value = previousYearDate()
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// 查看
const HandleDetail = (row) => {
    router.push({ path: "./examinedetail", query: { id: row.Id } })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    EvaluateGetevaluatelist(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            console.log(rows, other)
            if (isFirst) {
                if (hUnitType.value == 1) {
                    CountyList.value = other.listArea || []//区县名称
                }
                if (hUnitType.value == 2) {
                    SchoolList.value = other.listSchool || []//学校名称
                }
                SupplierList.value = other.listSupplier;//供应商
                StatuzList.value = other.listStatuz;//调换状态
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.PurchaseYear = undefined
    filters.value.CompanyId = undefined
    filters.value.Statuz = undefined
    filters.value.Key = undefined
    filters.value.CountyId = undefined
    filters.value.SchoolId = undefined
    if (hUnitType.value == 1) {
        SchoolList.value = []
    }
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 选择区县
const CountyChange = (e) => {
    if (!e) {
        filters.value.SchoolId = undefined
    }
    HandleTableData()
    PunitgetschoolbycountyidUser(e)
}
// 选择区县获取学校
const PunitgetschoolbycountyidUser = (id) => {
    Punitgetschoolbycountyid({ CountyId: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            filters.value.SchoolId = undefined
            SchoolList.value = rows || []//学校名称
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yearDateList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
                        <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称"
                            style="width: 160px" @change="CountyChange">
                            <el-option v-for="item in CountyList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.UnitId" :label="item.UnitName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem" v-if="hUnitType == 2">
                        <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SchoolList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.CompanyId" clearable filterable placeholder="供应商"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SupplierList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="评价状态" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in StatuzList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="合同批次"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="PurchaseYear" label="年度" min-width="80" align="center"></el-table-column>
            <el-table-column prop="AreaName" label="区县名称" min-width="140" v-if="hUnitType == 1"></el-table-column>
            <el-table-column prop="SchoolName" label="学校名称" min-width="160"
                v-if="hUnitType == 1 || hUnitType == 2"></el-table-column>
            <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="EvaluateNum" label="参与评价人数" min-width="110" align="center"></el-table-column>
            <el-table-column prop="EvaluateScore" label="评价综合等级" min-width="110" align="center"></el-table-column>
            <el-table-column prop="EvaluateDeadline" label="评价截止时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.EvaluateDeadline ? row.EvaluateDeadline.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="SupplierName" label="供应商" min-width="200"></el-table-column>
            <el-table-column prop="EvaluateStatuz" label="评价状态" min-width="100" align="center">
                <template #default="{ row }">
                    <!-- 1：正在评价，2：评价结束 -->
                    {{ row.EvaluateStatuz == 1 ? '正在评价' : '评价结束' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="评价明细" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.EvaluateNum > 0" type="primary" link @click="HandleDetail(row)">查看</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>