<script setup>
import { onMounted, ref, watch } from 'vue'
import {
    Refresh, Search, FolderAdd, Download, Upload, Finished
} from '@element-plus/icons-vue'
import {
    PuserGetuserxfpage,
    Usermyunitupdateuserstatuz,
    PuserSetuserxfpwd,
    Usersetuservalidate
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import md5 from 'js-md5';
import { disposalDate } from "@/utils/index.js";
import router from '@/router'
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
//新增&编辑操作
const dialogIndex = ref(1)
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    Pwd: [
        { required: true, message: '新密码不能为空', trigger: 'change' },
    ],
    UserValidate: [
        { required: true, message: '请选择有效期', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    HandleTableData()
})
// 修改
const HandleEdit = (row, e) => {
    dialogIndex.value = e
    dialogData.value.AccountId = row.AcctId
    dialogData.value.AccountName = row.AcctName
    if (!row.UserValidate || row.UserValidate == '0001-01-01 00:00:00') {
        dialogData.value.UserValidate = ''
    } else {
        dialogData.value.UserValidate = row.UserValidate.substring(0, 10)
    }
    dialogVisible.value = true
}
// 修改状态
const HandleSwitchChange = (e, row) => {
    Usermyunitupdateuserstatuz({ userId: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            if (e == 1) {
                ElMessage.success('启用成功')
            } else {
                ElMessage.success('禁用成功')
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
//密码重置 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (dialogIndex.value == 1) {
            let params = {
                AccountId: dialogData.value.AccountId,
                Pwd: md5(dialogData.value.Pwd),
            }
            PuserSetuserxfpwd(params).then(res => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success('重置成功')
                    dialogVisible.value = false
                } else {
                    ElMessage.error(res.data.msg)
                }
            });
        } else {
            let params = {
                AccountId: dialogData.value.AccountId,
                UserValidate: dialogData.value.UserValidate,
            }
            Usersetuservalidate(params).then((res) => {
                HandleTableData()
                ElMessage.success('设置有效期成功')
                dialogVisible.value = false
            }).catch((err) => {
                console.info(err)
            })
        }
    })
}

//列表
const HandleTableData = () => {
    PuserGetuserxfpage(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
</script>
<template>
    <div class="viewContainer">
        <!-- 搜索 -->
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" placeholder="用户名称/手机号码/账号" style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <!-- 内容 -->
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column type="index" width="50" />
            <el-table-column prop="Name" label="用户名称" min-width="180" align="center"></el-table-column>
            <el-table-column prop="RoleNames" label="用户角色" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Mobile" label="手机号码" min-width="120" align="center"></el-table-column>
            <el-table-column prop="AcctName" label="账号" min-width="120" align="center"></el-table-column>
            <!-- <el-table-column label="账号状态" min-width="120" align="center">
                <template #default="{ row }">
                    <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="0"
                        style="--el-switch-off-color: #ff4949" @change="HandleSwitchChange($event, row)" />
                </template>
</el-table-column> -->
            <!-- <el-table-column prop="UserValidate" label="有效期" min-width="180" align="center">
                <template #default="{ row }">
                    {{ disposalDate(row.UserValidate) }}
                </template>

            </el-table-column> -->
            <!-- <el-table-column fixed="right" label="操作" min-width="180" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row, 1)">重置密码</el-button>
                    <el-button type="primary" link @click="HandleEdit(row, 2)">设置有效期</el-button>
                </template>
            </el-table-column> -->
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <!-- 分页 -->
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />

        <!-- 弹窗 -->
        <el-dialog v-model="dialogVisible" :title="dialogIndex == 1 ? '密码重置' : '有效期设置'" draggable width="480px">

            <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="100px"
                status-icon>
                <el-form-item label="账号：" v-if="dialogIndex == 1">
                    {{ dialogData.AccountName }}
                </el-form-item>
                <el-form-item label="密码：" prop="Pwd" v-if="dialogIndex == 1">
                    <el-input v-model="dialogData.Pwd"></el-input>
                </el-form-item>

                <el-form-item label="有效期：" prop="UserValidate" v-if="dialogIndex == 2">
                    <el-date-picker v-model="dialogData.UserValidate" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                        type="date" placeholder="请选择有效期" />
                </el-form-item>

            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit">
                        提交
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>

</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>