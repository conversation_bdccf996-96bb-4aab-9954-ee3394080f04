<script setup>
import { onMounted, watch, nextTick, ref } from 'vue'
import {
  QuestionFilled, Select
} from '@element-plus/icons-vue'
import AppWangEditor from '@/components/Editor/index.vue';
import { popperOptions } from "@/utils/index.js";
import { rules, rulesLimit, rulesIntegerLimit } from "@/utils/rules.js";
const size = ref('default')
const refForm = ref()
const formFields = ref({})
const props = defineProps({
  currentComponents: {
    type: Array,
    default: [],
  },
})
watch(
  () => props.currentComponents,
  (newVal, oldVal) => {
    formFields.value.formItems = props.currentComponents
    formFields.value.formItems = formFields.value.formItems.filter(item => !item.isDetail)
    nextTick(() => {
      refForm.value.resetFields()
    })
  }
);
//加载数据
onMounted(() => {
  console.log("props.currentComponents", props.currentComponents)
  formFields.value.formItems = props.currentComponents
  formFields.value.formItems = formFields.value.formItems.filter(item => !item.isDetail)
  nextTick(() => {
    refForm.value.resetFields()
  })
})
// 提交
const HandleSubmit = (e) => {
  console.log("提交", formFields.value.formItems)
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;

  })
}
// 表单校验
const ruleForm = (item) => {
  if (item.type == 'text' || item.type == 'projectname' || item.type == 'projectamount') {
    if (!item.rules) return rules['required'](item.name)
    // 当是小数类型时，小数位数wei0则为整数
    if (item.rules == 'decimals' && (!item.arg || item.arg == '0')) {
      item.rules = 'integer'
    }
    if (item.rules == 'rangeLength') {
      let arg = []
      if (item.arg.indexOf(',')) {
        arg = item.arg.split(',')
      } else if (item.arg.indexOf('，')) {
        arg = item.arg.split('，')
      }
      return rules[item.rules](item.name, arg[0], arg[1], item.required)
    }
    // 根据校验方法名和参数调用校验方法
    return rules[item.rules](item.name, item.arg, item.required)
  }
  if (item.type == 'textarea') {
    return [{ required: true, message: '请输入' + item.name, trigger: 'blur' }]
  } else {
    return [{ required: true, message: '请选择' + item.name, trigger: 'change' }]
  }

}
//输入框限制：输入小数/整数
const rulesInput = (val, item, name) => {
  if (item.rules == 'integer') {
    item[name] = rulesIntegerLimit(val);
  } else if (item.rules == 'decimals') {
    if (!item.arg || item.arg == '0') {
      item[name] = rulesIntegerLimit(val);
    } else {
      item[name] = rulesLimit(val, item.arg);
    }
  }

  if (item.type === 'fundallocation') {
    // 资金分配求和
    if (item.code.indexOf('FundAllocation_') == 0) {
      if (item.code.startsWith('FundAllocation_0_')) {
        let allocation = formFields.value.formItems.filter(t => t.code.startsWith('FundAllocation_0_'))
        // console.log('项目金额allocation', allocation)
        let numbers = allocation.map(t => Number(t.DefaultValue));
        let sum = numbers.reduce((a, b) => a + b, 0);
        // console.log(sum);
        formFields.value.formItems.forEach(t => {
          if (t.code == 'ProjectAmount') {
            t.DefaultValue = sum
            t.DefaultName = sum
          }
        });
      } else if (!item.code.endsWith('_Total')) {
        let codePrefix = 'FundAllocation_' + item.code.split('_')[1] + '_';
        let allocation = formFields.value.formItems.filter(t => t.code.startsWith(codePrefix) && !t.code.endsWith('_Total'));
        let sum = allocation.reduce((total, current) => total + Number(current.DefaultValue), 0);
        formFields.value.formItems.forEach(t => {
          if (t.code == 'FundAllocation_' + item.code.split('_')[1] + '_Total') {
            t.DefaultValue = sum
            t.DefaultName = sum
          }
        });
      }
    }
  }

}
//金额输入限制：最多4位小数
const amountInput = (val, row, name) => {
  row[name] = rulesLimit(val, 4);
}
</script>

<template>
  <div class="formBody">
    <el-form style="width: 100%" :inline="true" ref="refForm" @submit.prevent :model="formFields">
      <div class="edit-form-item">
        <el-form-item v-for="(item, index) in formFields.formItems" :key="item.code"
          :label-width="item.type == 'line' ? '0px' : item.labelWidth ? item.labelWidth + 'px' : '200px'"
          :prop="'formItems.' + index + '.DefaultValue'" :rules="item.rules || item.required ? ruleForm(item) : []"
          :style="{ width: item.width + '%' }">
          <!-- label -->
          <template #label>
            <el-tooltip v-if="item.isRemark" class="item" effect="dark" :content="item.HelpRemark" placement="top">
              <div>
                <el-icon color="#E6A23C" class="tipIcon">
                  <QuestionFilled />
                </el-icon>
              </div>
            </el-tooltip>
            <span> {{ item.name ? item.name + '：' : '' }} </span>
          </template>
          <!-- 输入框 -->
          <el-input
            v-if="['text', 'projectname', 'projectamount', 'fundallocation', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown', 'creationnameinput', 'ProjectCode'].includes(item.type)"
            v-model="item.DefaultValue" :size="size" clearable
            :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name" :disabled="item.readonly"
            @input="rulesInput($event, item, 'DefaultValue')" style="width: 100%;"></el-input>
          <el-input v-else-if="item.type == 'projectnumber' && !item.isDetail" v-model="item.DefaultValue" :size="size"
            disabled></el-input>
          <!-- 描述文本 -->
          <span v-if="item.type === 'descriptivetext'"
            :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#999999', 'fontSize': '14px' }">{{
              item.DefaultValue }}</span>
          <!-- 文本域 -->
          <el-input v-else-if="item.type == 'textarea' || item.type == 'auditremark'" v-model="item.DefaultValue"
            type="textarea" :maxlength="item.maxLength ? item.maxLength : 500" show-word-limit :size="size" clearable
            :disabled="item.readonly" :autosize="{ minRows: item.minRows || 2, maxRows: item.maxRows || 10 }"
            :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name" style="width: 100%;" />
          <!-- 下拉框 -->
          <el-select
            v-else-if="['select', 'subjectnature', 'OneClassId', 'TwoClassId', 'creationnameselect', 'AppointAuditUserSelect'].includes(item.type)"
            v-model="item.DefaultValue" :disabled="item.readonly" :size="size" filterable
            :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name" clearable style="width: 100%;">
            <el-option v-for="item1 in item.data" :key="item1.value" :label="item1.label" :value="item1.value">
            </el-option>
          </el-select>
          <!-- 多选下拉框 -->
          <el-select v-else-if="item.type == 'selectList' || item.type == 'AppointAuditUserSelectList'"
            v-model="item.DefaultValue" :disabled="item.readonly" :size="size" filterable multiple
            :placeholder="item.placeholder ? '请选择' + item.placeholder : item.name" style="width: 100%;">
            <el-option v-for="item1 in item.data" :key="item1.value" :label="item1.label" :value="item1.value">
            </el-option>
          </el-select>
          <!-- 单选框 -->
          <el-radio-group v-else-if="['radio', 'auditstatuz', 'isnewcreation'].includes(item.type)"
            v-model="item.DefaultValue" :disabled="item.readonly" style="width: 100%;">
            <el-radio v-for="kv in item.data" :disabled="item.readonly" :key="kv.value" :value="kv.value">
              {{ kv.label }}
            </el-radio>
          </el-radio-group>
          <!-- 复选框 -->
          <el-checkbox-group v-else-if="item.type == 'checkbox'" v-model="item.DefaultValue" :disabled="item.readonly"
            style="width: 100%;">
            <el-checkbox v-for="kv in item.data" :key="kv.value" :label="kv.label" :value="kv.value"> </el-checkbox>
          </el-checkbox-group>
          <!-- 日期 -->
          <el-date-picker v-else-if="item.type == 'date'" v-model="item.DefaultValue" type="date" format="YYYY-MM-DD"
            value-format="YYYY-MM-DD" :size="size" clearable :disabled="item.readonly"
            :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name" style="width: 100%;"
            :popper-options="popperOptions">
          </el-date-picker>
          <!-- 日期时间 -->
          <el-date-picker v-else-if="item.type == 'datetime'" v-model="item.DefaultValue" type="datetime"
            format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :size="size" clearable
            :disabled="item.readonly" :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
            style="width: 100%;" :popper-options="popperOptions">
          </el-date-picker>
          <!-- 时间 -->
          <el-time-picker v-else-if="item.type == 'time'" v-model="item.DefaultValue" format="HH:mm:ss"
            value-format="HH:mm:ss" :size="size" :disabled="item.readonly"
            :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name" style="width: 100%;"
            :popper-options="popperOptions">
          </el-time-picker>
          <!-- 级联 -->
          <el-cascader v-else-if="item.type == 'cascader'" v-model="item.DefaultValue" :options="item.data"
            :props="{ checkStrictly: true }" :size="size" clearable :disabled="item.readonly"
            :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name" style="width: 100%;">
          </el-cascader>
          <!-- 排序 -->
          <el-input v-else-if="item.type == 'sort'" type="number" v-model="item.DefaultValue"></el-input>
          <!-- 按钮 -->
          <el-button
            v-else-if="['button', 'download', 'projectlist', 'projectexaminelist', 'upload'].includes(item.type)"
            type="primary" size="small"> {{ item.buttonTitle }}</el-button>
          <el-switch v-else-if="item.type == 'switch'" v-model="item.DefaultValue" :disabled="item.readonly"
            active-color="#13ce66" inactive-color="#0e7ef3"
            :active-value="typeof item.DefaultValue == 'boolean' ? true : typeof item.DefaultValue == 'string' ? '1' : 1"
            :inactive-value="typeof item.DefaultValue == 'boolean' ? false : typeof item.DefaultValue == 'string' ? '0' : 0">
          </el-switch>
          <!-- 分割线 -->
          <el-divider v-else-if="item.type == 'line'" :border-style="item.isDashed" :content-position="item.isCenter"
            :style="{ 'width': '100%', '--el-border-color': item.dividerColor ? item.dividerColor : '#dedfe6' }">
            <span :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#303133' }">
              {{ item.dividerText }}</span>
          </el-divider>
          <!-- 表格 -->
          <el-table v-else-if="item.type == 'statisticstable'" ref="refTable" :data="item.tableData"
            highlight-current-row border stripe header-cell-class-name="headerClassName">
            <el-table-column v-if="item.changeType == 'radio'" label="" width="50" align="center">
              <template #default="{ row }">
                <el-radio v-model="item.radio1" :value="row.Id"></el-radio>
              </template>
            </el-table-column>
            <el-table-column v-if="item.changeType == 'checkbox'" type="selection" width="50"
              align="center"></el-table-column>
            <el-table-column v-for="column in item.columnsData" :key="column.FieldCode" :prop="column.FieldCode"
              :label="column.FieldName" :min-width="column.Width" :align="column.ContentStyle">
              <template #default="{ row }">
                <el-input v-if="column.FieldCode == 'actualAmount'" v-model="row[column.FieldCode]"
                  @input="amountInput($event, row, column.FieldCode)"> </el-input>
                <span v-else>{{ row[column.FieldCode] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 文本编辑器 -->
          <app-wang-editor ref="editor" v-else-if="item.type == 'editor'" v-model="item.DefaultValue"
            :height="item.height || 350">
          </app-wang-editor>
        </el-form-item>
        <el-form-item style="width: 100%;margin-left: 140px;">
          <el-button type="primary" :icon="Select" @click="HandleSubmit">提交</el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>
<style lang="scss" scoped>
.formBody {
  padding: 10px;
  margin: 5px;
  border: 1px solid rgb(240, 240, 240);
  background-color: rgb(255, 255, 255);
}

.edit-form-item {
  display: flex;
  flex-wrap: wrap;
}

:deep(.el-form-item) {
  margin-right: 0;
}

.col-line {
  line-height: 25px;
  font-weight: bold;
  border-bottom: 1px solid rgb(218 218 218);
}


:deep(.el-divider__text.is-left) {
  left: 100px;
}
</style>
