<script setup>
defineOptions({
    name: 'querystatisticsmanagement'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
    FindpageDefinitionList, GetDatabydicValue, PageDefinitionSave, GetPageDefinitionByid,
    DelPageDefinitionByid, PageDefinitionStatuzSet, GetFieldCodeListBymoduleId
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { integerLimit } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const moduleIdList = ref([])
const pageTypeList = ref([])
const processIdsList = ref([])
const defaultSortList = ref([])
const sortTypeList = ref([])
const sourceDataList = ref([])
const totalCounmnList = ref([])
const unittypeList = ref([])
const useTypeList = ref([])
const useUnitFieldList = ref([])
const useUnitIdList = ref([])
const useUserFieldList = ref([])
const fieldCodeList = ref([])
const dialogVisible = ref(false)
const dialogData = ref({})
const editId = ref()
const refForm = ref()
const ruleForm = {
    ModuleId: [
        { required: true, message: '请选择模块', trigger: 'change' },
    ],
    Name: [
        { required: true, message: '请输入名称', trigger: 'change' },
    ],
    ShowName: [
        { required: true, message: '请输入显示名称', trigger: 'change' },
    ],
    UseUnitId: [
        { required: true, message: '请选择使用单位', trigger: 'change' },
    ],
    Unittype: [
        { required: true, message: '请选择单位类型', trigger: 'change' },
    ],
    ProcessIds: [
        { required: true, message: '请选择对应流程', trigger: 'change' },
    ],
    SourceData: [
        { required: true, message: '请选择数据源', trigger: 'change' },
    ],
    FieldCode: [
        { required: true, message: '请选择项目清单Code', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// 创建
const HandleAdd = () => {
    editId.value = undefined
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {};
        dialogData.value.ProcessIds = []
    })
}
// 修改
const HandleEdit = (row) => {
    editId.value = row.Id
    GetPageDefinitionByidUser(row.Id)
    dialogVisible.value = true
}
// 删除
const HandleDel = (row) => {

    ElMessageBox.confirm('确定删除吗?')
        .then(() => {
            DelPageDefinitionByid({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}


// 启用禁用
const HandleEnable = (row) => {
    let msg = ''
    let operateType = 1//（1：禁用启用,2：提交）
    if (row.Statuz == 1) {
        msg = `确定要禁用 [${row.Name}]吗?`
        operateType = 1
    } else if (row.Statuz == 2) {
        msg = `确定要启用 [${row.Name}]吗?`
        operateType = 1
    } else {
        msg = `确定要提交 [${row.Name}]吗?`
        operateType = 2
    }

    ElMessageBox.confirm(msg)
        .then(() => {
            PageDefinitionStatuzSet({ id: row.Id, operateType: operateType }).then((res) => {
                if (res.data.flag == 1) {
                    HandleSearch()
                    ElMessage.success(res.data.msg || '设置成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
}

//创建/修改选用组织  提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        PageDefinitionSaveUser()
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Key = undefined
    HandleTableData()
}
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
// 列表设置
const HandleList = (row) => {
    console.log(row)
    router.push({ path: "./querystatistics/list", query: { ModuleId: row.ModuleId, PageDefinitionId: row.Id, SourceData: row.SourceData } })
}
//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    FindpageDefinitionList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
            if (isFirst) {
                moduleIdList.value = other.listModuleId || [];//模块
                pageTypeList.value = other.listPageType || [];//页面类型
                processIdsList.value = other.listProcessIds || [];//流程
                defaultSortList.value = other.listDefaultSort || [];//默认排序字段
                defaultSortList.value = [...new Map(defaultSortList.value.map(item => [item.value, item])).values()];
                sortTypeList.value = other.listSortType || [];//排序规则
                sourceDataList.value = other.listSourceData || [];//数据源
                totalCounmnList.value = other.listTotalCounmn || [];//总计显示位置
                totalCounmnList.value = [...new Map(totalCounmnList.value.map(item => [item.value, item])).values()];
                unittypeList.value = other.listUnittype || [];//适用单位
                useTypeList.value = other.listUseType || [];//适用范围
                useUnitFieldList.value = other.listUseUnitField || [];//使用单位字段名称
                useUnitIdList.value = other.listUseUnitId || [];//使用单位
                useUserFieldList.value = other.listUseUserField || [];//使用用户字段名称
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 添加/修改提交
const PageDefinitionSaveUser = () => {
    let processIds = dialogData.value.ProcessIds.join(',')
    if (dialogData.value.SourceData != 4) {
        dialogData.value.FieldCode = undefined
    }
    let formData = {
        Id: dialogData.value.Id,
        ModuleId: dialogData.value.ModuleId,//模块名称
        Name: dialogData.value.Name,//名称
        ShowName: dialogData.value.ShowName,//显示名称
        UseUnitId: dialogData.value.UseUnitId,//使用单位
        Unittype: Number(dialogData.value.Unittype),//单位类型
        SourceData: Number(dialogData.value.SourceData),//数据源
        FieldCode: dialogData.value.FieldCode,//预算清单Code
        ProcessIds: processIds,//对应流程
        PageSize: dialogData.value.PageSize,//每页显示数量
        TotalName: dialogData.value.TotalName,//总计显示名称
        TotalCounmn: dialogData.value.TotalCounmn,//总计显示位置
        UseType: Number(dialogData.value.UseType),//适用范围
        DefaultSort: dialogData.value.DefaultSort,//默认排序字段
        SortType: Number(dialogData.value.SortType),//排序规则
        UseUnitField: dialogData.value.UseUnitField,//使用单位字段名称
        UseUserField: dialogData.value.UseUserField,//使用用户字段名称
        DefaultWhere: dialogData.value.DefaultWhere,//默认查询条件

    }
    PageDefinitionSave(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false

            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const GetPageDefinitionByidUser = (id) => {
    GetPageDefinitionByid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows

            dialogData.value.Unittype = String(rows.Unittype)
            dialogData.value.UseType = String(rows.UseType)
            dialogData.value.SourceData = String(rows.SourceData)
            dialogData.value.SortType = String(rows.SortType)
            dialogData.value.ModuleId = String(rows.ModuleId)
            dialogData.value.ProcessIds = rows.ProcessIds.split(',')

            defaultSortList.value = other.listDefaultSort || [];//默认排序字段
            defaultSortList.value = [...new Map(defaultSortList.value.map(item => [item.value, item])).values()];
            totalCounmnList.value = other.listTotalCounmn || [];//总计显示位置
            totalCounmnList.value = [...new Map(totalCounmnList.value.map(item => [item.value, item])).values()];
            useUnitFieldList.value = other.listUseUnitField || [];//使用单位字段名称
            useUserFieldList.value = other.listUseUserField || [];//使用用户字段名称

            GetFieldCodeListBymoduleIdUser(dialogData.value.ModuleId)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选择模块
const getModuleId = (e) => {
    dialogData.value.FieldCode = undefined
    GetFieldCodeListBymoduleIdUser(e)
}
// 根据模块Id获取项目清单Code集合
const GetFieldCodeListBymoduleIdUser = (id) => {
    GetFieldCodeListBymoduleId({ moduleId: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            fieldCodeList.value = rows || [];
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选择数据源获取
const changeSourceData = (e) => {
    GetDatabydicValue({ sourceData: e }).then((res) => {
        if (res.data.flag == 1) {
            const { other } = res.data.data
            totalCounmnList.value = other.listTotalCounmn || [];//总计显示位置
            totalCounmnList.value = [...new Map(totalCounmnList.value.map(item => [item.value, item])).values()];
            useUnitFieldList.value = other.listUseUnitField || [];//使用单位字段名称
            useUserFieldList.value = other.listUseUserField || [];//使用用户字段名称
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="名称/显示名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>

                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="名称" min-width="140"></el-table-column>
            <el-table-column prop="ShowName" label="显示名称" min-width="140"></el-table-column>
            <el-table-column prop="StrPageType" label="页面类型" min-width="110" align="center"></el-table-column>
            <el-table-column prop="StrUnittype" label="适用单位" min-width="110" align="center"></el-table-column>
            <el-table-column prop="StrUseType" label="适用范围" min-width="160" align="center"></el-table-column>
            <el-table-column prop="PageSize" label="每页显示数量" min-width="120" align="center"></el-table-column>
            <el-table-column prop="TotalName" label="总计显示名称" min-width="120" align="center"></el-table-column>
            <el-table-column prop="DefaultSort" label="默认排序字段" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.Statuz == 1 ? 'primary' : row.Statuz == 2 ? 'danger' : 'success'"
                        disable-transitions>
                        {{ row.Statuz == 1 ? "启用" : row.Statuz == 2 ? "禁用" : "设置中" }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="230" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <!-- <el-button type="primary" link @click="HandleEdit(row)">复制</el-button> -->
                    <el-button type="primary" link @click="HandleList(row)">列表设置</el-button>
                    <el-button type="primary" link @click="HandleEnable(row)">
                        {{ row.Statuz == 1 ? "禁用" : row.Statuz == 2 ? "启用" : "提交" }}
                    </el-button>
                    <el-button type="primary" link v-if="row.Statuz == 3" @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="800" :lazy="true" :title="editId ? '修改查询统计配置' : '添加查询统计配置'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                    status-icon style="padding-right: 100px;">
                    <el-form-item label="模块名称：" prop="ModuleId">
                        <el-select v-model="dialogData.ModuleId" @change="getModuleId">
                            <el-option v-for="item in moduleIdList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="名称：" prop="Name">
                        <el-input v-model="dialogData.Name"></el-input>
                    </el-form-item>
                    <el-form-item label="显示名称：" prop="ShowName">
                        <el-input v-model="dialogData.ShowName"></el-input>
                    </el-form-item>
                    <el-form-item label="使用单位：" prop="UseUnitId">
                        <el-select v-model="dialogData.UseUnitId">
                            <el-option v-for="item in useUnitIdList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="单位类型：" prop="Unittype">
                        <el-select v-model="dialogData.Unittype">
                            <el-option v-for="item in unittypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="数据源：" prop="SourceData">
                        <el-select v-model="dialogData.SourceData">
                            <el-option v-for="item in sourceDataList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目清单Code：" prop="FieldCode" v-if="dialogData.SourceData == 4">
                        <el-select v-model="dialogData.FieldCode">
                            <el-option v-for="item in fieldCodeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="对应流程：" prop="ProcessIds">
                        <el-select v-model="dialogData.ProcessIds" multiple>
                            <el-option v-for="item in processIdsList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="每页显示数量：">
                        <el-input v-model="dialogData.PageSize"
                            @input="integerLimitInput($event, 'PageSize')"></el-input>
                    </el-form-item>
                    <el-form-item label="总计显示名称：">
                        <el-input v-model="dialogData.TotalName"></el-input>
                    </el-form-item>
                    <el-form-item label="总计显示位置：">
                        <el-select v-model="dialogData.TotalCounmn" clearable filterable allow-create
                            default-first-option>
                            <el-option v-for="item in totalCounmnList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="适用范围：" prop="UseType">
                        <el-select v-model="dialogData.UseType">
                            <el-option v-for="item in useTypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="默认排序字段：" prop="DefaultSort">
                        <el-select v-model="dialogData.DefaultSort" clearable filterable allow-create
                            default-first-option>
                            <el-option v-for="item in defaultSortList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="排序规则：">
                        <el-select v-model="dialogData.SortType" clearable>
                            <el-option v-for="item in sortTypeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="使用单位字段名称：">
                        <el-select v-model="dialogData.UseUnitField" clearable>
                            <el-option v-for="item in useUnitFieldList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="使用用户字段名称：">
                        <el-select v-model="dialogData.UseUserField" clearable>
                            <el-option v-for="item in useUserFieldList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="默认查询条件：">
                        <el-input v-model="dialogData.DefaultWhere"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>