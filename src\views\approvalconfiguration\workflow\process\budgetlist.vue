<script setup>
defineOptions({
    name: 'processbudgetlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { Select } from '@element-plus/icons-vue'
import {
    ProjectListFieldSetList, ProjectListFieldSetByid, ProjectListFieldSetSave
} from '@/api/workflow.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { integerLimit, tagsListStore } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, ConfigType: 1 })
const yesNoList = ref([{ value: '1', label: '是' }, { value: '2', label: '否' }])
const dateDisplayList = ref([
    { value: 10, label: '年-月-日' },
    { value: 4, label: '年份' },
    { value: 7, label: '年-月' },
    { value: 13, label: '年-月-日 时' },
    { value: 16, label: '年-月-日 时:分' },
    { value: 19, label: '年-月-日 时:分:秒' },
])
const useMenuTypeList = ref([
    { Value: '1', Description: '数字' },
    { Value: '2', Description: '金额' },
    { Value: '3', Description: '文本' },
    { Value: '4', Description: '日期' },
])
const titleStyleList = ref([])
const typeStyleList = ref([])
const fieldCodeList = ref([])
const dialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    Title: [
        { required: true, message: '请输入标题名称', trigger: 'change' },
    ],
    Width: [
        { required: true, message: '请输入节点名称', trigger: 'change' },
    ],
    FormWidth: [
        { required: true, message: '请输入表单元素宽度(百分比)', trigger: 'change' },
    ],
    SourceValue: [
        { required: true, message: '请输入下拉框数据源', trigger: 'change' },
    ]
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        filters.value.ProcessId = route.query.id
        HandleTableData(true)
    }
})

onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)

    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.ProcessId = route.query.id
            HandleTableData(true)
        }
    })
})
//输入整数
const integerLimitInput = (val, name) => {
    dialogData.value[name] = integerLimit(val);
}
// 修改
const HandleEdit = (row) => {
    // 获取详情
    ProjectListFieldSetByid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            // console.log("rows", rows)
            dialogData.value = rows
            dialogData.value.TypeStyle = String(dialogData.value.TypeStyle)
            dialogData.value.ColumnFieldType = String(dialogData.value.ColumnFieldType)
            dialogData.value.IsShow = String(dialogData.value.IsShow)
            dialogData.value.SearchIsShow = String(dialogData.value.SearchIsShow)
            dialogData.value.IsRequired = String(dialogData.value.IsRequired)
            dialogData.value.IsSort = String(dialogData.value.IsSort)
            // dialogData.value.SourceValue = String(dialogData.value.SourceValue)

            if (rows.ColumnFieldType == 5) {
                dialogData.value.DateDisplay = rows.DateDisplay || 10
            } else {
                dialogData.value.DateDisplay = undefined
            }

            dialogVisible.value = true
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//修改 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        ProjectListFieldSetSaveUser()
    })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    ProjectListFieldSetList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
            if (isFirst) {
                typeStyleList.value = other.listTypeStyle || [];//文本框类型
                titleStyleList.value = other.listTitleStyle || [];//对齐方式
                fieldCodeList.value = other.listFieldCode || [];//项目清单code
                if (fieldCodeList.value.length > 0) {
                    filters.value.FieldCode = fieldCodeList.value[0].value
                } else {
                    filters.value.FieldCode = undefined
                }
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 修改
const ProjectListFieldSetSaveUser = () => {
    ProjectListFieldSetSave(dialogData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const activeName = ref('biaodan')
const paneList = ref([
    { label: '表单数据', value: 1, name: 'biaodan' },
    { label: '列表数据', value: 2, name: 'liebiao' },
    { label: 'Excel数据', value: 3, name: 'excel' }
])
const handleClick = (tab) => {
    console.log(tab, tab.index, paneList.value[Number(tab.index)].value)
    // 切换后重置搜索条件
    // filters.value.ConfigType = 1 + Number(tab.index)
    filters.value.ConfigType = paneList.value[Number(tab.index)].value
    filters.value.pageIndex = 1
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox" v-if="fieldCodeList.length > 0">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="选择清单：" class="flexItem">
                        <el-select v-model="filters.FieldCode" clearable @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in fieldCodeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item class="flexItem">
                                <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                            </el-form-item> -->
                </el-form>
            </el-col>
        </el-row>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane v-for="item in paneList" :key="item.value" :label="item.label" :name="item.name">
                <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                    header-cell-class-name="headerClassName">
                    <el-table-column prop="Title" label="标题名称" min-width="140"></el-table-column>
                    <el-table-column prop="FieldValue" label="字段名称" min-width="160"></el-table-column>
                    <!-- <el-table-column prop="FieldCode" label="项目清单Code" min-width="130" align="center"></el-table-column> -->
                    <el-table-column prop="IsShow" label="是否显示" min-width="90" align="center">
                        <template #default="{ row }">
                            <span v-if="row.IsShow == 1">
                                <el-icon color="#67C23A"> <Select /> </el-icon>
                            </span>
                            <span v-else></span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="SearchIsShow" label="搜索是否显示" min-width="110" align="center"
                        v-if="filters.ConfigType == 2">
                        <template #default="{ row }">
                            {{ row.SearchIsShow == 1 ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="ContentStyle" label="内容对其方式" min-width="110" align="center"
                        v-if="filters.ConfigType == 2">
                    </el-table-column>
                    <el-table-column prop="StrTypeStyle" label="文本框类型" min-width="120" align="center"
                        v-if="filters.ConfigType == 1">
                    </el-table-column>
                    <el-table-column prop="ColumnFieldType" label="列展示类型" min-width="130" align="center"
                        v-if="filters.ConfigType == 2">
                        <template #default="{ row }">
                            {{ row.ColumnFieldType == 1 ? '数字' : row.ColumnFieldType == 2
                                ? '金额' : row.ColumnFieldType == 3 ? '文本' : row.ColumnFieldType == 4 ? '日期' : '' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="IsRequired" label="是否必填" min-width="90" align="center">
                        <template #default="{ row }">
                            {{ row.IsRequired == 1 ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="IsSort" label="是否排序" min-width="90" align="center">
                        <template #default="{ row }">
                            {{ row.IsSort == 1 ? '是' : '否' }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="Sort" label="排序值" width="80" align="center"> </el-table-column>
                    <el-table-column fixed="right" label="操作" min-width="100" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <el-empty description="没有数据"></el-empty>
                    </template>
                </el-table>
                <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex"
                    v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
            </el-tab-pane>
        </el-tabs>
        <app-box v-model="dialogVisible" :width="760" :lazy="true"
            :title="filters.ConfigType == 1 ? '修改表单字段' : filters.ConfigType == 2 ? '修改表格列字段' : '修改Excel数据字段'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                    status-icon>
                    <el-form-item label="标题名称：" prop="Title">
                        <el-input v-model="dialogData.Title"></el-input>
                    </el-form-item>
                    <el-form-item label="字段名称：" prop="FieldValue">
                        <el-input v-model="dialogData.FieldValue" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="是否显示：" prop="IsShow">
                        <el-radio-group v-model="dialogData.IsShow">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="列宽度：" prop="Width" v-if="filters.ConfigType == 2">
                        <el-input v-model="dialogData.Width" @input="integerLimitInput($event, 'Width')"></el-input>
                    </el-form-item>
                    <el-form-item label="表单元素宽度(百分比)：" prop="FormWidth" v-if="filters.ConfigType == 1">
                        <el-input v-model="dialogData.FormWidth" placeholder="请输入0-100的数字"
                            @input="integerLimitInput($event, 'FormWidth')"></el-input>
                    </el-form-item>
                    <el-form-item label="内容对齐方式：" v-if="filters.ConfigType == 2">
                        <el-select v-model="dialogData.ContentStyle">
                            <el-option v-for="item in titleStyleList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="文本框类型：" v-if="filters.ConfigType == 1">
                        <el-select v-model="dialogData.TypeStyle">
                            <el-option v-for="item in typeStyleList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="列展示数据类型：" v-if="filters.ConfigType == 2">
                        <el-select v-model="dialogData.ColumnFieldType">
                            <el-option v-for="item in useMenuTypeList" :key="item.Value" :label="item.Description"
                                :value="item.Value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="下拉框数据源：" prop="SourceValue"
                        v-if="filters.ConfigType == 1 && dialogData.TypeStyle == 3">
                        <el-input v-model="dialogData.SourceValue" placeholder="每一项以'|'分割。如：a|b|c"></el-input>
                    </el-form-item>
                    <el-form-item label="日期格式：" v-if="dialogData.ColumnFieldType == 5">
                        <el-select v-model="dialogData.DateDisplay">
                            <el-option v-for="item in dateDisplayList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否必填" prop="IsRequired">
                        <el-radio-group v-model="dialogData.IsRequired">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否排序" prop="IsSort">
                        <el-radio-group v-model="dialogData.IsSort">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="排序值：" prop="Sort">
                        <el-input type="number" v-model="dialogData.Sort"></el-input>
                    </el-form-item>
                    <el-form-item label="是否可作为搜索条件" prop="SearchIsShow" v-if="filters.ConfigType == 2">
                        <el-radio-group v-model="dialogData.SearchIsShow">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="搜索条件控件类型：" v-if="dialogData.SearchIsShow == 1">
                        <el-select v-model="dialogData.TypeStyle">
                            <el-option v-for="item in typeStyleList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="搜索排序值：" prop="SearchSort" v-if="filters.SearchIsShow == 1">
                        <el-input type="number" v-model="dialogData.SearchSort"></el-input>
                    </el-form-item>
                    <el-form-item label="提示文字：" prop="TipMsg" v-if="filters.ConfigType == 1">
                        <el-input v-model="dialogData.TipMsg"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-bottom: 10px !important;
    margin-top: 10px !important;
}
</style>