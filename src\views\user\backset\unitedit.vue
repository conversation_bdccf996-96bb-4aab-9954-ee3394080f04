<script setup>
import { onMounted, ref } from 'vue';
import {
    QuestionFilled, Select
} from '@element-plus/icons-vue'
import {
    PunitGetxfunitinfo, PunitSetxfunitinfo
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    OrganizationCode: [
        { required: true, message: '请输入组织机构代码：', trigger: 'change' },
    ],
    Name: [
        { required: true, message: '请输入单位全称', trigger: 'change' },
    ]
}
onMounted(() => {
    PunitGetxfunitinfoUser()
})

//提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        ElMessageBox.confirm('确定提交吗?')
            .then(() => {
                let paramsData = {
                    Name: formData.value.Name,
                    OrganizationCode: formData.value.OrganizationCode
                }
                PunitSetxfunitinfo(paramsData).then((res) => {
                    if (res.data.flag == 1) {
                        ElMessage.success(res.data.msg || '执行成功')
                    } else {
                        ElMessage.error(res.data.msg)
                    }
                })
            })
            .catch((err) => {
                console.info(err)
            })
    })
}
const PunitGetxfunitinfoUser = () => {
    PunitGetxfunitinfo().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows
            if (rows.OrganizationCode) {
                formData.value.OrganizationCode = rows.OrganizationCode
            } else {
                formData.value.OrganizationCode = ''
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <el-form style="width: 900px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
        label-width="240px" status-icon>
        <el-form-item label="单位全称：" prop="Name">
            <el-input v-model="formData.Name" auto-complete="off" placeholder="与单位公章一致" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="组织机构代码：" prop="OrganizationCode">
            <template #label>
                <el-tooltip class="item" effect="dark" content="这是单位唯一标识，请保证正确" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 组织机构代码： </span>
            </template>
            <el-input v-model="formData.OrganizationCode" auto-complete="off" style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item label="单位隶属：">
            <template #label>
                <el-tooltip class="item" effect="dark" content="只能由运营商修改" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 单位隶属： </span>
            </template>
            <el-input v-model="formData.ParentUnitName" disabled style="width: 50%;"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
        </el-form-item>
    </el-form>
</template>
<style lang="scss" scoped>
.attestBox {
    p {
        // font-size: 16px;
        color: #E6A23C;
    }

    span {
        // font-size: 16px;
        color: #F56C6C;
        padding-left: 50px;
    }

}
</style>