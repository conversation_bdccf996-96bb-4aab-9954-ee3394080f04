<script setup>
import { onMounted, ref, watch } from 'vue'
import {
    Select
} from '@element-plus/icons-vue'
import {
    Bconfigsetgetbyunit,
    Bconfigsetsavebyunit,
    UploadPostfile
} from '@/api/user.js'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { pageQuery } from "@/utils/index.js";
// 初始化
const userStore = useUserStore()
const route = useRoute()

const formData = ref({})
const configList = ref([])
const refForm = ref()
const routerObject = ref({})
//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象 
    BconfigsetgetbyunitUser()
})

const BconfigsetgetbyunitUser = () => {
    Bconfigsetgetbyunit({ moduleCode: routerObject.value.m, optType: routerObject.value.opt }).then(res => {
        if (res.data.flag == 1) {
            console.log("单位超管数量配置信息", res.data.data)
            const { rows } = res.data.data

            // formItems.value = rows
            // if (rows.length > 0 && rows[0].ComboValues) {
            //     configList.value = JSON.parse(rows[0].ComboValues)
            //     configList.value.forEach((item) => {
            //         item.Id = String(item.Id)
            //     })
            // }
            rows.forEach((item) => {
                if (item.ComboValues) {
                    item.ComboValues = JSON.parse(item.ComboValues)
                    item.ComboValues.forEach((item1) => {
                        item1.Id = String(item1.Id)
                    })
                }
            })
            formData.value.formItems = rows
            let obj = {}
            rows.map(item => {
                obj[item.TypeCode] = item.ConfigValue
            })
            userStore.setDefaultSet(obj)


        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const fileFile = ref()
const filleIndex = ref(0)
const httpRequestIndex = (i) => {
    filleIndex.value = i
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file

    const isLt2M = file.size / 1024 / 1024 < 10
    const isJPG =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/jpg'

    if (!isJPG) {
        ElMessage({
            message: '上传图片只能是 JPG/PNG 格式!',
            type: 'error'
        })
    }
    if (!isLt2M) {
        ElMessage({
            message: '上传图片大小不能超过 10MB!',
            type: 'error'
        })
    }
    return isLt2M && isJPG

}
const httpRequest = (i) => {
    UploadPostfile({ file: fileFile.value, roleId: 10, subdir: "User", moduleType: 7 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value.formItems[filleIndex.value].ConfigValue = rows.Path
        } else {
            ElMessage.error(res.data.msg)
        }

    })
}

const HandleSubmit = () => {
    // console.log("formItems", formItems.value)
    //根据数据集合提取需要的属性
    const newArrayOfObjects = formData.value.formItems.map(({ Id, ConfigValue }) => ({ Id, ConfigValue }));
    // 修改属性名为对应入参字段
    const paraList = newArrayOfObjects.map(({ ConfigValue, Id }) => ({
        ConfigValue,
        ConfigSetId: Id
    }));
    // console.log("paraList", paraList)
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        const formparams = {
            List: paraList,
            ModuleCode: routerObject.value.m
        }
        Bconfigsetsavebyunit(formparams).then(res => {

            if (res.data.flag == 1) {
                BconfigsetgetbyunitUser()
                ElMessage.success('保存成功')
            } else {
                ElMessage.error(res.data.msg)
            }

        }).catch((err) => {
            console.info(err)
        })
    })
}
</script>
<template>
    <el-form style=" min-width: 600px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
        label-width="180px" status-icon>

        <div v-for="(item, index) in formData.formItems" :key="item.Id" class="flexItem">
            <el-form-item :label="item.TypeName" v-if="item.ValueType == 1 || item.ValueType == 2">
                <el-input v-model="item.ConfigValue" auto-complete="off" style="width:300px;"></el-input>
                <!-- <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" :before-upload="beforeAvatarUpload"
          :http-request="httpRequest">
          <el-button type="primary" @click="httpRequestIndex(index)" style="margin: 0 5px;">上传</el-button>
        </el-upload> -->
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">
                    {{ item.Remark }}</span>
            </el-form-item>
            <el-form-item :label="item.TypeName" v-if="item.ValueType == 3">
                <el-radio-group v-model="item.ConfigValue" style="width: 300px;">
                    <el-radio class="radio" :value="'1'">是</el-radio>
                    <el-radio class="radio" :value="'2'">否</el-radio>
                </el-radio-group>
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">
                    {{ item.Remark }}</span>
            </el-form-item>
            <el-form-item :label="item.TypeName" v-if="item.ValueType == 4">
                <el-select v-model="item.ConfigValue" style="width: 300px;">
                    <el-option class="flexItem" v-for="item1 in item.ComboValues" :key="item1.Id" :label="item1.Name"
                        :value="item1.Id">
                    </el-option>
                </el-select>
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">
                    {{ item.Remark }}</span>
            </el-form-item>
            <el-form-item :label="item.TypeName" v-if="item.ValueType == 8">
                <el-input v-model="item.ConfigValue" readonly style="width: 300px;"></el-input>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                    :before-upload="beforeAvatarUpload" :http-request="httpRequest">
                    <el-button type="primary" @click="httpRequestIndex(index)" style="margin: 0 5px;">上传</el-button>
                </el-upload>
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">
                    {{ item.Remark }}</span>
            </el-form-item>
        </div>

        <el-form-item>
            <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
        </el-form-item>
    </el-form>
</template>
<style lang="scss" scoped></style>
