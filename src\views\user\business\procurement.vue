<script setup>
import { onMounted, ref } from 'vue';
import {
    Getpaged, Setbyid
} from '@/api/user.js'
import { ElMessage } from 'element-plus'
const formData = ref({})
const refForm = ref()
const headerTip = ref('')
const cities = ref([])

onMounted(() => {
    GetpagedUser()
})
// 校服采购方式
const GetpagedUser = () => {
    Getpaged({ code: 1000 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            headerTip.value = rows[0].Memo
            cities.value = rows
            let arr = rows.filter(t => t.ValueNum == 1)
            formData.value.checkedCities = arr.map(t => t.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const handleCheckedCitiesChange = (e, item) => {
    if (formData.value.checkedCities.length == 0) {
        ElMessage.error('校服采购方式至少保证有一个选中')
        formData.value.checkedCities.push(item.Id)
        return
    }
    Setbyid({ id: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '执行成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <el-form style="width: 100%;margin-top: 30px;" class="mobile-box" ref="refForm" :model="formData"
        label-width="180px" status-icon>
        <el-form-item label-width="50px" style="border-bottom: 1px dashed #E4E7ED">
            <span style="color: #E6A23C;">用于学校征求家长意见书中“采购方式”的选项；建议选择“学校集中采购”和“家长自行采购”</span>
        </el-form-item>
        <el-form-item label="校服采购方式：" prop="Name">
            <el-checkbox-group v-model="formData.checkedCities">
                <div v-for="(item, index) in cities" :key="item.Id">
                    <el-checkbox :label="item.Title" :value="item.Id" @change="handleCheckedCitiesChange($event, item)">
                    </el-checkbox>
                    <span class="place">({{ item.Explanation }})</span>
                </div>
            </el-checkbox-group>
        </el-form-item>
    </el-form>
</template>
<style lang="scss" scoped>
.headerPlace {
    border-bottom: 1px solid #ebeef5;
    padding-left: 50px;
    font-size: 14px;
    color: #999;
}

.el-checkbox {
    width: 160px;

}

.place {
    font-size: 12px;
    color: #9f9f9f;
}
</style>