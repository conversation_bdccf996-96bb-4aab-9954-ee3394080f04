<script setup>
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd, DocumentChecked
} from '@element-plus/icons-vue'
import {
    DepartmentListFind, DepartmentInsertUpdate, DepartmentDelById, DepartmentEnable, DepartmentUserListFind,
    UserInDepartBatchInsert
} from '@/api/user.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { limit, integerLimit, tagsListStore } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const tableFilters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "ASC" }] })
const tableData = ref([])
const tableTotal = ref(0)
const tableDialogData = ref([])
const tableDialogTotal = ref(0)
const refTable = ref()
const refTableData = ref()
const selectRows = ref([])//选中的行 
const departId = ref('')
const dialogVisible = ref(false)
const tableDialogVisible = ref(false)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '请填写部门名称', trigger: 'change' },
    ]
}
const options = ref([
    { value: 'Name', label: '姓名', },
    { value: 'Mobile', label: '手机号码', },
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {

    HandleTableData();
})
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 添加
const HandleAdd = (e) => {
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            Id: '0',
            IsManage: false,
            Memo: '',
            Name: '',
            moduleIds: ''
        }
    })
}
// 部门管理 修改 
const HandleEdit = (row) => {
    dialogData.value = {
        Id: row.Id,
        IsManage: false,
        Memo: row.Memo,
        Name: row.Name,
        moduleIds: ''
    }
    dialogVisible.value = true

}

//修改 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DepartmentInsertUpdate(dialogData.value).then(res => {
            if (res.data.flag == 1) {
                dialogVisible.value = false
                HandleTableData()
                ElMessage.success(res.data.msg || '操作成功')
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

// 启用禁用  
const HandleSwitchChange = (e, row) => {
    DepartmentEnable({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 删除 
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要删除此部门吗?删除之后该部门的人员自动解散!',
        '删除确认', { type: 'warning', })
        .then(() => {
            DepartmentDelById({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })

}

//列表
const HandleTableData = () => {
    DepartmentListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = undefined
    HandleTableData()

}
// 分页
const handlePage = (val) => {
    HandleTableData()
}


// 添加用户
const HandleAddPersonnel = (row) => {
    tableFilters.value.DepartmentId = row.Id
    departId.value = row.Id
    DepartmentUserListFindUser()
    tableDialogVisible.value = true
}
// 修改用户
const HandleEditPersonnel = (row) => {
    router.push({ path: './usermanager', query: { id: row.Id, Name: row.Name } })
}
//获取用户列表
const DepartmentUserListFindUser = () => {
    tableFilters.value.Name = undefined;
    tableFilters.value.Model = undefined;
    if (filtersKey.value) {
        tableFilters.value[filtersValue.value] = filtersKey.value;
    } else {
        tableFilters.value[filtersValue.value] = undefined;
    }
    DepartmentUserListFind(tableFilters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableDialogData.value = rows;
            tableDialogTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 用户列表搜索
const HandleDialogSearch = () => {
    tableFilters.value.pageIndex = 1
    DepartmentUserListFindUser()
}
// 用户列表重置
const HandleDialogReset = () => {
    tableFilters.value.pageIndex = 1
    filtersKey.value = ''
    DepartmentUserListFindUser()
}
// 用户列表分页
const handleTablePage = (val) => {
    DepartmentUserListFindUser()
}
// 用户列表提交
const HandlePersonnelSubmit = (row) => {
    UserInDepartBatchInsertUser(row.Id)
}
// 用户列表批量提交
const HandlePersonnelAllSubmit = () => {
    let ids = selectRows.value.map(item => item.Id).join(',')
    UserInDepartBatchInsertUser(ids)
}


// 用户列表选择提交
const UserInDepartBatchInsertUser = (ids) => {
    //根据数据集合提取需要的属性 
    let formData = {
        ids: ids,//用户id集合
        departId: departId.value,//部门Id
    }

    UserInDepartBatchInsert(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '选择成功')
            tableDialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>

                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="部门名称"
                            style="width: 200px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="部门名称" min-width="160"></el-table-column>
            <el-table-column prop="RegTime" label="创建时间" min-width="160" align="center">
                <template #default="{ row }">
                    {{ row.RegTime.substring(0, 10) }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="80" align="center">
                <!-- <template #default="{ row }">
                    <el-tag :type="row.Statuz == 0 ? 'danger' : row.Statuz == -1 ? 'warning' : 'primary'"
                        disable-transitions>
                        {{ row.Statuz == 0 ? "禁用" : row.Statuz == -1 ? "已删除" : "启用" }}
                    </el-tag>
                </template> -->
                <template #default="{ row }">

                    <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="0" inline-prompt active-text="启"
                        inactive-text="禁" style="--el-switch-off-color: #ff4949"
                        @change="HandleSwitchChange($event, row)" />
                </template>
            </el-table-column>
            <el-table-column prop="Memo" label="备注" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UserCount" label="人数" min-width="160" align="right"></el-table-column>

            <el-table-column label="部门管理" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <el-table-column label="部门人员管理" fixed="right" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleAddPersonnel(row)">添加</el-button>
                    <el-button type="primary" link @click="HandleEditPersonnel(row)">修改</el-button>
                </template>
            </el-table-column>

            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <!-- 选择用户 -->
        <app-box v-model="tableDialogVisible" :width="680" :lazy="true" title="添加用户">
            <template #content>
                <div style="max-height: 520px;">
                    <el-row class="navFlexBox">
                        <el-col>
                            <el-form @submit.prevent :inline="true" :model="tableFilters" class="flexBox">
                                <el-form-item class="flexItem">
                                    <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                                        class="input-with-select">
                                        <template #prepend>
                                            <el-select v-model="filtersValue" style="width: 120px">
                                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                                    :value="item.value" />
                                            </el-select>
                                        </template>
                                    </el-input>
                                </el-form-item>
                                <el-form-item class="flexItem">
                                    <el-button type="primary" :icon="Search" @click="HandleDialogSearch">搜索</el-button>
                                    <el-button :icon="Refresh" @click="HandleDialogReset">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                    <el-table ref="refTableData" :data="tableDialogData" highlight-current-row max-height="450"
                        @selection-change="HandleSelectChange" border stripe header-cell-class-name="headerClassName">
                        <el-table-column type="selection" width="50"></el-table-column>
                        <el-table-column prop="Name" label="姓名" min-width="120"></el-table-column>
                        <el-table-column prop="Sex" label="性别" min-width="80" align="center">
                            <template #default="{ row }">
                                {{ row.Sex === 0 ? '女' : '男' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="Mobile" label="手机号码" min-width="120" align="center"></el-table-column>
                        <el-table-column label="操作" width="80" align="center">
                            <template #default="{ row }">
                                <el-button type="primary" link @click="HandlePersonnelSubmit(row)">选择</el-button>
                            </template>
                        </el-table-column>
                        <template #empty>
                            <el-empty description="没有数据"></el-empty>
                        </template>
                    </el-table>
                    <table-page :total="tableDialogTotal" v-model:pageIndex="tableFilters.pageIndex"
                        v-model:pageSize="tableFilters.pageSize" @handleChange="handleTablePage" />
                </div>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="tableDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandlePersonnelAllSubmit" :disabled="selectRows.length == 0"> 提交
                    </el-button>
                </span>
            </template>
        </app-box>
        <!-- 新增、修改部门 -->
        <app-box v-model="dialogVisible" :width="560" :lazy="true" :title="dialogData.Id == '0' ? '添加部门信息' : '修改部门信息'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="100px"
                    status-icon>
                    <el-form-item label="部门名称：" prop="Name">
                        <el-input v-model="dialogData.Name"></el-input>
                    </el-form-item>
                    <el-form-item label="备注：">
                        <el-input type="textarea" v-model="dialogData.Memo"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>