<script setup>
import { onMounted, ref } from 'vue';
import {
    FolderChecked
} from '@element-plus/icons-vue'
import {
    GetSchoolUnitInfo, SaveSchoolUnitInfo
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit, limit } from "@/utils/index.js";
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const formData = ref({})
const refForm = ref()
const ruleForm = {}
const streetTownList = ref([])
onMounted(() => {
    GetSchoolUnitInfoUser()
})
// 获取单位信息
const GetSchoolUnitInfoUser = () => {
    GetSchoolUnitInfo().then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            streetTownList.value = other?.listTown || []
            formData.value = rows
            if (rows.SchoolNature == 1) {
                formData.value.SchoolNatureName = '公办校'
            } else if (rows.SchoolNature == 2) {
                formData.value.SchoolNatureName = '民办校'
            } else {
                formData.value.SchoolNatureName = '其他'
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
// 提交
const HandleSubmit = () => {
    let TownName = ''
    if (formData.value.StreetTown) {
        TownName = streetTownList.value.find(t => t.Id == formData.value.StreetTown)?.Name || ''
    }
    let paraData = {
        Code: formData.value.Code || '',
        OrganizationCode: formData.value.OrganizationCode || '',
        StreetTown: formData.value.StreetTown || '',
        TownName: TownName,
        Address: formData.value.Address || '',
        Url: formData.value.Url || '',
        Introduction: formData.value.Introduction || '',
        ClassNum: formData.value.ClassNum || 0,
        StudentNum: formData.value.StudentNum || 0,
        TeacherNum: formData.value.TeacherNum || 0,
        UnderTeacherNum: formData.value.UnderTeacherNum || 0,
        FloorArea: formData.value.FloorArea || 0,
        BuildArea: formData.value.BuildArea || 0,
    }
    SaveSchoolUnitInfo(paraData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success('保存成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <el-form style="width: 900px;margin-left: 30px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
        :rules="ruleForm" label-width="140px" status-icon>
        <fieldset>
            <legend>基础信息</legend>
            <el-form-item label="单位全称：">
                <el-input v-model="formData.Name" disabled></el-input>
            </el-form-item>
            <el-form-item label="单位代码：">
                <el-input v-model="formData.Code" style="width: 80%;"></el-input>
                <span style="padding-left: 20px;color: #999999">或单位预算代码</span>
            </el-form-item>
            <div class="dialogFlexBox">
                <el-form-item label="单位性质：">
                    <el-input v-model="formData.SchoolNatureName" disabled></el-input>
                </el-form-item>
                <el-form-item label="单位属性：">
                    <el-input v-model="formData.SchoolStageName" disabled></el-input>
                </el-form-item>
                <el-form-item label="组织机构代码：">
                    <el-input v-model="formData.OrganizationCode" auto-complete="off"></el-input>
                </el-form-item>
                <el-form-item label="所在地区：">
                    <el-input v-model="formData.ProvinceCityCountyName" disabled></el-input>
                </el-form-item>
                <el-form-item label="所属街道：">
                    <el-select v-model="formData.StreetTown">
                        <el-option v-for="item in streetTownList" :key="item.Id" :label="item.Name" :value="item.Id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="单位地址：">
                    <el-input v-model="formData.Address" auto-complete="off"></el-input>
                </el-form-item>
                <el-form-item label="单位官网：">
                    <el-input v-model="formData.Url" auto-complete="off"></el-input>
                </el-form-item>
            </div>
            <el-form-item label="单位简介：">
                <el-input type="textarea" v-model="formData.Introduction"></el-input>
            </el-form-item>
        </fieldset>
        <fieldset>
            <legend>其他信息</legend>
            <div class="dialogFlexBox">
                <el-form-item label="班级总数(班)：">
                    <el-input v-model="formData.ClassNum" auto-complete="off"
                        @input="integerLimitInput($event, 'ClassNum')"></el-input>
                </el-form-item>
                <el-form-item label="学生总数(人)：">
                    <el-input v-model="formData.StudentNum" auto-complete="off"
                        @input="integerLimitInput($event, 'StudentNum')"></el-input>
                </el-form-item>
                <el-form-item label="教职工数(人)：">
                    <el-input v-model="formData.TeacherNum" auto-complete="off"
                        @input="integerLimitInput($event, 'TeacherNum')"></el-input>
                </el-form-item>
                <el-form-item label="其中在编教师(人)：">
                    <el-input v-model="formData.UnderTeacherNum" auto-complete="off"
                        @input="integerLimitInput($event, 'UnderTeacherNum')"></el-input>
                </el-form-item>
                <el-form-item label="占地面积(㎡)：">
                    <el-input v-model="formData.FloorArea" auto-complete="off"
                        @input="limitInput($event, 'FloorArea')"></el-input>
                </el-form-item>
                <el-form-item label="建筑面积(㎡)：">
                    <el-input v-model="formData.BuildArea" auto-complete="off"
                        @input="limitInput($event, 'BuildArea')"></el-input>
                </el-form-item>
            </div>
        </fieldset>
        <el-form-item label-width="140px">
            <el-button type="primary" :icon="FolderChecked" @click="HandleSubmit">提交</el-button>
        </el-form-item>
    </el-form>
</template>
<style lang="scss" scoped>
.dialogFlexBox {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
        width: 50%;
        margin-bottom: 10px;
    }
}

fieldset {
    // color: #333;
    border: #ccc dashed 1px;
    padding: 10px;
    margin: 10px 0;

    legend {
        font-size: 16px;
    }
}
</style>