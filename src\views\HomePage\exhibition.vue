<script setup>
import { ElMessage } from 'element-plus'
import { ref, onMounted, onUnmounted } from 'vue'
import {
    HomeGetpagelist
} from '@/api/home.js'
import HomeHeader from '@/views/HomePage/HomeHeader.vue';
import TablePage from '@/components/TablePagination/index.vue' //分页
import Footer from '@/views/HomePage/homefooter.vue';
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
onMounted(() => {
    document.documentElement.style.setProperty('--body--webkit-scrollbar', '0px');
    formData.value.Name = route.query.Name
    HomeGetpagelistUser()
})
onUnmounted(() => {
    // 移除样式
    document.documentElement.style.setProperty('--body--webkit-scrollbar', '6px');
});
const tableData = ref([])
const tableTotal = ref(0)
const formData = ref({ pageIndex: 1, pageSize: 10 })
// 分页
const handlePage = () => {
    HomeGetpagelistUser()
}
// 查看校服详情
const onTableItemClick = (item) => {
    const { href } = router.resolve({
        path: "/preview",
        query: { id: item.Id, requestNum: 2 }
    });
    window.open(href, "_blank");
}
// 获取校服展示数据
const HomeGetpagelistUser = () => {
    HomeGetpagelist(formData.value).then((res) => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            // console.log("校服展示", rows)
            tableData.value = rows || []
            tableTotal.value = Number(total)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取子组件数据
const updateData = (data) => {
    // console.log("updateData", data)
    formData.value.Name = data.Name
    HomeGetpagelistUser()
}
</script>
<template>
    <header class="headerNav">
        <HomeHeader :Name="route.query.Name" @exhibitionName="updateData"></HomeHeader>
    </header>
    <section class="section">
        <div class="flex-warp">
            <div class="item" v-for="item in tableData" :key="item.Id" @click="onTableItemClick(item)">
                <div class="flex-warp-item">
                    <div class="flex-warp-item-box">
                        <div class="item-img">
                            <img :src="item.MainImagePath" />
                        </div>
                        <div class="item-txt">
                            <div class="item-txt-title">{{ item.SchoolName }}</div>
                            <div class="item-txt-msg">
                                <span class="item-txt-type"> {{ item.Uniformtype }}</span>
                                <span class="item-txt-name"> {{ item.Name }}</span>
                                <span style="display: inline-block;width: 50px;"> {{ item.SexName }}款</span>
                            </div>
                            <div class="item-txt-supplier">
                                <span>{{ item.SupplierName }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <table-page :total="tableTotal" v-model:pageIndex="formData.pageIndex" v-model:pageSize="formData.pageSize"
            @handleChange="handlePage" />
    </section>
    <footer class="footer">
        <Footer></Footer>
    </footer>

</template>
<style lang="scss" scoped>
.headerNav {
    margin-bottom: 60px;
}

.imgNav {
    width: 1200px;
    margin: 10px auto;
    // margin-top: 100px;
}

.section {
    width: 1200px;
    margin: 10px auto;
    margin-bottom: 80px;
}

.footer {
    background-color: #545c64;
    color: #fff !important;
    padding: 10px 0;
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 99;
}

.flex-warp {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin: 0 -5px;
    width: 100%;

    .item {
        flex: 1;
        margin: 0 5px 5px 5px;
        width: calc((100% - 50px) / 5);
        min-width: calc((100% - 50px) / 5);
        max-width: calc((100% - 50px) / 5);
    }

    .item:nth-child(5n) {
        margin-right: 0;
    }

    .flex-warp-item {
        padding: 5px;
        width: 100%;
        height: 325px;

        .flex-warp-item-box {
            border: 1px solid var(--next-border-color-light);
            width: 100%;
            height: 100%;
            border-radius: 2px;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            border: 1px solid #CDD0D6;

            &:hover {
                cursor: pointer;
                border: 1px solid var(--el-color-primary);
                transition: all 0.3s ease;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);

                .item-txt-title {
                    color: var(--el-color-primary) !important;
                    transition: all 0.3s ease;
                }

                .item-img {
                    img {
                        transition: all 0.3s ease;
                        transform: translateZ(0) scale(1.05);
                    }
                }
            }

            .item-img {
                width: 100%;
                height: 215px;
                overflow: hidden;

                img {
                    transition: all 0.3s ease;
                    width: 100%;
                    height: 100%;
                }
            }

            .item-txt {
                font-size: 12px;
                color: #666;

                .item-txt-title {
                    font-size: 14px;
                    padding: 3px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .item-txt-msg {
                    padding: 3px;
                    display: flex;
                    flex-wrap: wrap;

                    span {
                        display: inline-block;
                        padding-right: 5px;
                    }

                    .item-txt-type {
                        // max-width: 30%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .item-txt-name {
                        color: #F56C6C;
                        // padding: 0 5px;
                        // max-width: 50%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }


                .item-txt-supplier {
                    padding: 3px;
                    color: #8d8d91;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

            }
        }
    }
}
</style>
