<script setup>
import { onMounted, onActivated, nextTick, ref, watch } from 'vue'
import { Link } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { pageQuery, urlQuery, tagsListStore, fileDownload, formatNumberWithCommas, formatNumber } from "@/utils/index.js";
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const size = ref('default')
const routerObject = ref({})
const routerUrl = ref('')
const refForm = ref()
const activeNamesList = ref([])
const props = defineProps({
    // 是否是待处理/待审核
    detailData: {
        type: Array,
        default: []
    },
    activeNames: {
        type: Array,
        default: []
    },
    page: {
        type: String,
        default: 'detail'
    }
})
watch(() => props.activeNames, (e) => {
    activeNamesList.value = e
})
onMounted(() => {
    routerObject.value = pageQuery(route.path)
})
onActivated(() => {
    routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
    routerUrl.value = urlQuery(route.path); // 获取生成页面携带的参数
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    // console.log("props.detailData", props.detailData)
    // console.log("props.activeNamesList", activeNamesList.value)
})

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }

}
// 项目清单查看
const projectDetail = (item, processNodeId) => {
    router.push({
        path: "./projectdetail@moduleId=" + routerObject.value.moduleId, query: {
            ProcessId: routerObject.value.processId,
            ProcessNodeId: processNodeId,
            ProjectDeclarationId: route.query.id,
            FieldCode: item.code,
            routerUrl: routerUrl.value,
            page: props.page,
            type: item.type
        }
    })
}
</script>
<template>
    <div class="demo-collapse">
        <el-collapse v-model="activeNamesList">
            <el-collapse-item v-for="(item, index) in detailData" :key="item.TabId" :name="item.TabId + index">
                <template #title>
                    <div>{{ item.TabName }}</div>
                </template>
                <el-form style="width: 100%;min-width: 560px;" :inline="true" ref="refForm" @submit.prevent
                    :model="item.data">
                    <div class="edit-form-item">
                        <template v-for="(item1, index) in item.data" :key="item1.code">
                            <el-form-item v-if="!(item1.Controlled > 0 && !item1.isControlShow)"
                                :label-width="item1.type == 'line' ? '0px' : item1.labelWidth ? item1.labelWidth + 'px' : '200px'"
                                :style="{ width: item1.width + '%' }"
                                :class="item1.type == 'statisticstable' ? 'item_col-line' : ''">
                                <template #label>
                                    <span> {{ item1.name ? item1.name + '：' : '' }} </span>
                                </template>
                                <div v-if="item1.type == 'upload'">
                                    <div class="fileFlex"
                                        style="line-height: 24px;background-color: #67C23A;color: #ffffff; ">
                                        <div v-for="(item2, index2) in item1.DefaultName" :key="item2.Id"
                                            @click="fileListDownload(item2, item1.DefaultName)"
                                            style="cursor: pointer;">
                                            <!-- <el-icon style="vertical-align: middle;">
                                                <Link />
                                            </el-icon> -->
                                            {{ item2.Title }}
                                            <!-- <img :src="item2.Path" alt="" style="width: 20px;height: 20px;vertical-align: middle;"> -->
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if="item1.type == 'projectlist' || item1.type == 'projectexaminelist'">
                                    <el-button type="primary" size="small"
                                        @click="projectDetail(item1, item.ProcessNodeId)">
                                        查看</el-button>
                                </div>
                                <div v-else-if="item1.type == 'statisticstable'">
                                    <ul>
                                        <li v-for="(item2, index2) in item1.DefaultName" :key="item2.Id"
                                            class="col-line">
                                            {{ item2.ProjectName }} </li>
                                    </ul>
                                </div>
                                <el-divider v-else-if="item1.type == 'line'" :border-style="item1.isDashed"
                                    :content-position="item1.isCenter"
                                    :style="{ 'width': '100%', '--el-border-color': item1.dividerColor ? item1.dividerColor : '#dedfe6' }">
                                    <span
                                        :style="{ 'color': item1.dividerTextColor ? item1.dividerTextColor : '#303133' }">
                                        {{ item1.dividerText }}</span>
                                </el-divider>
                                <span v-else-if="item1.type == 'descriptivetext'"
                                    style="color: #999999;font-size: 14px;">
                                    {{ item1.DefaultName }}</span>
                                <span v-else-if="item1.type === 'projectallamount'">
                                    {{ formatNumberWithCommas(item1.DefaultName) }} </span>
                                <el-input v-else v-model="item1.DefaultName" :size="size" disabled></el-input>
                            </el-form-item>
                        </template>
                    </div>
                </el-form>
            </el-collapse-item>
        </el-collapse>
        <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    </div>
</template>
<style lang="scss" scoped>
.edit-form-item {
    display: flex;
    flex-wrap: wrap;
}

:deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 10px;
    // align-items: center;
}

.col-line {
    line-height: 25px;
    // font-weight: bold;
    border-bottom: 1px solid rgb(218 218 218);
}

ul {
    padding-inline-start: 0px;
    margin: 0;

    li {
        list-style-type: none;
        font-size: 12px;

        border-bottom: none !important;

    }
}

:deep(.el-divider__text.is-left) {
    left: 100px;
}

.item_col-line {
    :deep(.el-form-item__content) {
        align-items: center;
    }
}
</style>
