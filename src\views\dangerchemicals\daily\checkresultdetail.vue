<script setup>
defineOptions({
    name: 'dangerchemicalsdailycheckresultdetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, Select, UploadFilled
} from '@element-plus/icons-vue'
import {
    GovernTaskUnitGetById, GovernTaskUnitUpdate, DcGovernItemReportGetById, DcGovernItemReportSave
} from '@/api/daily.js'
import { GetDictionaryCombox } from '@/api/dangerchemicals.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { AttachmentUpload } from '@/api/user.js'
import { tagsListStore } from "@/utils/index.js";
import { ElMessageBox, ElMessage } from 'element-plus'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const taskId = ref(0)
const schoolId = ref(0)
const governTaskUnitId = ref(0)
const activeNames = ref([100, 200])
const formData = ref({})
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const ruleForm = {
    CheckingDate: [
        { required: true, message: '请选择检查时间', trigger: 'blur' },
    ],
    Title: [
        { required: true, message: '文件标题', trigger: 'change' },
    ]
}

//加载数据
onMounted(() => {
    taskId.value = route.query.taskId
    governTaskUnitId.value = route.query.governTaskUnitId
    schoolId.value = route.query.schoolId
    if (route.query.isTagRouter) {
        loadData()
        DccatalogTypeGet()
    }

})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    taskId.value = route.query.taskId
    governTaskUnitId.value = route.query.governTaskUnitId
    schoolId.value = route.query.schoolId
    nextTick(() => {
        if (!route.query.isTagRouter) {
            loadData()
            DccatalogTypeGet()
        }
    })
})

//加载数据
const loadData = () => {
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    });

    //formData.value = {}
    GovernTaskUnitGetById({ GovernTaskUnitId: governTaskUnitId.value }).then((res) => {
        if (res.data.flag == 1) {
            formData.value = res.data.data.rows
            let categoryList = res.data.data.footer || []
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
    loadTable()
}

//加载表格
const loadTable = () => {
    filters.value.Id = schoolId.value
    DcGovernItemReportGetById(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (rows.Other) {
                // name.value = rows.Other.GovernTask.Name
                taskId.value = rows.Other.GovernTask.Id
            }
            if (rows.Statistics) {
                summation.value = rows.Statistics
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}


//=========================附件上传开始=========================//


const uploadFileData = ref([
    { FileCategory: 2975, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 10, Memo: "文件小于5M，支持pdf和图片文件", Name: "现场检查表：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" },
])


const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}



const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}


const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }

}



//=========================附件上传结束=========================//

//保存
const HandleSubmit = () => {
    formData.value.GovernTaskId = route.query.taskId
    formData.value.Id = route.query.governTaskUnitId

    const attIdArr = [];
    uploadFileData.value.map(function (item, index) {
        if (item.categoryList.length > 0) {
            item.categoryList.map(function (olditem, oldindex) {
                attIdArr.push(olditem.AttachmentDataId);
            });
        }
        if (item.fileLChildList.length > 0) {
            item.fileLChildList.map(function (newitem, newindex) {
                attIdArr.push(newitem.Id);
            });
        }
    });
    //附件Id集合
    formData.value.ListFileId = attIdArr;

    GovernTaskUnitUpdate(formData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const catalogList = ref([])
const natureOptions = ref([{ Id: 1, Name: '问题', }, { Id: 2, Name: '隐患', }])
const gradeOptions = ref([{ Id: 1, Name: '一般', }, { Id: 2, Name: '较大', }, { Id: 3, Name: '重大', }])
const isTallyClaimOptions = ref([{ Id: 1, Name: '是', }, { Id: 0, Name: '否', }])

// 危化品分类
const DccatalogTypeGet = () => {
    GetDictionaryCombox({ TypeCode: "10000" }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            catalogList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = (row) => {
    loadTable()
}

// 搜索
const HandleSearch = () => {
    loadTable()
}

// 重置
const HandleReset = () => {
    filters.value.categoryid = undefined;
    filters.value.nature = undefined;
    filters.value.grade = undefined;
    filters.value.name = undefined;

    loadTable()
}

//弹窗
const dialogVisible = ref(false)
const isAdd = ref(true)
const dialogData = ref({})
//验证


const upladTableFileData = ref([{ FileCategory: 2972, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 10, Memo: "文件小于10M，支持pdf和图片、文档文件", Name: "附件：", UploadFileType: ".pdf.jpg.jpeg.png.doc.docx.xls.xlsx", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx" }])


// 修改
const HandleEdit = (row) => {
    dialogVisible.value = true
    dialogData.value = JSON.parse(JSON.stringify(row));
    //处理附件。
    upladTableFileData.value[0].categoryList = [];//先置空
    upladTableFileData.value[0].fileLChildList = [];//先置空
    if (row.AttachmentList && row.AttachmentList.length > 0) {
        upladTableFileData.value[0].categoryList = row.AttachmentList;
    }

}

// 提交
const DataSubmit = () => {
    //验证，存在问题，必须填写整改期限，备注限制。
    if (dialogData.value && dialogData.value.RectifyLimit == 1) {
        //验证整改期限
        if (!(dialogData.value.RectifyLimit && dialogData.value.RectifyLimit >= new Date().getDate())) {
            ElMessage.error("存在问题，请选择整改期限，并且整改期限要不小于当前时间。")
        }
    }

    const attIdArr = [];
    upladTableFileData.value.map(function (item, index) {
        if (item.categoryList.length > 0) {
            item.categoryList.map(function (olditem, oldindex) {
                attIdArr.push(olditem.Id);
            });
        }
        if (item.fileLChildList.length > 0) {
            item.fileLChildList.map(function (newitem, newindex) {
                attIdArr.push(newitem.Id);
            });
        }
    });

    const dataModelArr = [{ Id: dialogData.value.Id, GovernItemId: dialogData.value.GovernItemId, IsTallyClaim: dialogData.value.IsTallyClaim, RectifyLimit: dialogData.value.RectifyLimit, AttachmentIdList: attIdArr, Memo: dialogData.value.Memo, Suggest: dialogData.value.Suggest }];
    const type = ref(0);
    DcGovernItemReportSave({ schoolid: schoolId.value, taskid: taskId.value, reporttype: 3, list: dataModelArr, GovernTaskUnitId: governTaskUnitId.value }).then(res => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '设置成功')
            loadTable()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}



const fileFileTable = ref()
const uploadRefTable = ref()
const fileIdListTable = ref([])
const numberDisabledTable = ref(false)

// 达到最大数量后限制上传
const MaxFileNumberClickTable = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabledTable.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabledTable.value = false
    }
}
// 导入前校验
const beforeAvatarUploadTable = (item, file) => {
    fileFileTable.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}


const httpRequestTable = (item, index) => {
    AttachmentUpload({ file: fileFileTable.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            upladTableFileData.value[index].fileLChildList.push(rows[0])
            // 获取上传后附件Id
            fileIdListTable.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFileTable = (item, index) => {
    upladTableFileData.value[index].fileLChildList = upladTableFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdListTable.value = fileIdListTable.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFileTable = (item, index) => {
    upladTableFileData.value[index].categoryList = upladTableFileData.value[index].categoryList.filter(t => t.Id != item.Id)
    fileIdListTable.value = fileIdListTable.value.filter(t => t.Id != item.Id)
}

</script>
<template>
    <div class="demo-collapse">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="基本信息" :name="100">
                <el-form style="min-width: 100px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="180px" status-icon>

                    <el-form-item label="检查时间：" prop="CheckingDate">
                        <el-date-picker type="date" placeholder="选择日期" v-model="formData.CheckingDate"
                            format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 400px"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="现场参加检查人员：">
                    </el-form-item>
                    <el-form-item label="领导：" prop="Leaderusers">
                        <el-input v-model="formData.Leaderusers" style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item label="专家：" prop="ExpertUsers">
                        <el-input v-model="formData.ExpertUsers" style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item label="工作人员：" prop="WorkUsers">
                        <el-input v-model="formData.WorkUsers" style="width: 400px"></el-input>
                    </el-form-item>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <div class="fileFlex">
                            <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                                <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                                    {{ itemCate.Title }}{{ itemCate.Ext }}
                                </span>
                            </div>
                            <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                                <span style="cursor: pointer;"
                                    @click="fileListDownload(itemChild, item.fileLChildList)">
                                    {{ itemChild.Title }}{{ itemChild.Ext }}
                                </span>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </el-collapse-item>
            <el-collapse-item title="检查信息" :name="200">
                <div class="viewContainer">
                    <el-row class="navFlexBox">
                        <el-col>
                            <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                                <el-form-item class="flexItem">
                                    <el-select v-model="filters.categoryid" clearable placeholder="类别"
                                        style="width: 160px" @change="filtersChange">
                                        <el-option v-for="item in catalogList" :key="item.DicValue"
                                            :label="item.DicName" :value="item.DicValue" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item class="flexItem">
                                    <el-select v-model="filters.nature" clearable placeholder="性质" style="width: 160px"
                                        @change="filtersChange">
                                        <el-option v-for="item in natureOptions" :key="item.Id" :label="item.Name"
                                            :value="item.Id" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item class="flexItem">
                                    <el-select v-model="filters.grade" clearable placeholder="危险等级" style="width: 160px"
                                        @change="filtersChange">
                                        <el-option v-for="item in gradeOptions" :key="item.Id" :label="item.Name"
                                            :value="item.Id" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item class="flexItem">
                                    <el-select v-model="filters.istallyclaim" clearable placeholder="是否存在问题隐患"
                                        style="width: 160px" @change="filtersChange">
                                        <el-option v-for="item in isTallyClaimOptions" :key="item.Id" :label="item.Name"
                                            :value="item.Id" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item class="flexItem">
                                    <el-input v-model.trim="filters.name" placeholder="问题隐患清单" style="width: 180px">
                                    </el-input>
                                </el-form-item>
                                <el-form-item class="flexItem">
                                    <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                                    <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="id"
                        default-expand-all header-cell-class-name="headerClassName">
                        <el-table-column prop="CategoryName" label="类别" min-width="80" align="center"></el-table-column>
                        <el-table-column prop="Name" label="问题隐患清单" min-width="300"></el-table-column>
                        <el-table-column prop="Nature" label="性质" min-width="80" align="center">
                            <template #default="{ row }">
                                {{ row.Nature == 1 ? '问题' : row.Nature == 1 ? '隐患' : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="Grade" label="危险等级" min-width="100" align="center">
                            <template #default="{ row }">
                                {{ row.Grade == 1 ? '一般' : row.Grade == 2 ? '较大' : row.Grade == 3 ? '重大' : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="IsRectify" label="存在问题隐患" min-width="100" align="center">
                            <template #default="{ row }">
                                <span v-if="row.IsTallyClaim != 0" style="color:red;">是</span>
                                <span v-else>否</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="RectifyLimit" label="整改期限" min-width="140" align="center">
                            <template #default="{ row }">
                                {{ row.RectifyLimit ? row.RectifyLimit.substring(0, 10) : '--' }}
                            </template>
                        </el-table-column>
                        <el-table-column label="附件" min-width="200" align="center">
                            <template #default="{ row }">
                                <div v-if="row.AttachmentList && row.AttachmentList.length > 0">
                                    <div class="fileFlex">
                                        <div v-for="(itemCate) in row.AttachmentList" :key="itemCate.Id"
                                            style="color:#409EFF ;">
                                            <span style="cursor: pointer;"
                                                @click="fileListDownload(itemCate, row.AttachmentList)">
                                                {{ itemCate.Title }}{{ itemCate.Ext }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="Memo" label="备注" min-width="200"></el-table-column>
                        <el-table-column prop="Suggest" label="整改建议" min-width="200"></el-table-column>
                        <template #empty>
                            <el-empty description="没有数据"></el-empty>
                        </template>
                    </el-table>
                </div>
                <!-- 图片预览 -->
                <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
            </el-collapse-item>
        </el-collapse>
    </div>
</template>
<style lang="scss" scoped></style>