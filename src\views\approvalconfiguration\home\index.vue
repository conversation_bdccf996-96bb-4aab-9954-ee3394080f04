<script setup>
defineOptions({
    name: 'homeindex'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
    GetProcessIndexInfo
} from '@/api/workflow.js'
import {
    GetArticleList, Getinformationbycatetype
} from '@/api/home.js'
import { MoreFilled } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { ElMessage } from 'element-plus'
import { pageQuery, urlQuery, tagsListStore, fileDownload } from "@/utils/index.js";
import router from '@/router'
const route = useRoute()
const fillingList = ref([])//填报数据
const approvalList = ref([])//待审批数据 
const fillingActiveNamesList = ref([])//待审批数据折叠面板展开
const approvalActiveNamesList = ref([])//待审批数据折叠面板展开 
const articleList = ref([])//待审批数据折叠面板展开 
const msg = ref('')
const imageList = ref([])
onMounted(() => {
    document.documentElement.style.fontSize = (window.innerWidth / 1920) * 16 + 'px';
    HandleTableData()

})

//列表
const HandleTableData = () => {
    GetProcessIndexInfo().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            fillingList.value = other?.listFilling || []
            approvalList.value = other?.listApproval || []

            for (let i = 0; i < fillingList.value.length; i++) {
                // 取每一项的listProcessView前五项
                fillingList.value[i].listProcessView = fillingList.value[i].listProcessView.slice(0, 5)
            }
            for (let i = 0; i < approvalList.value.length; i++) {
                // 取每一项的listProcessView前五项
                approvalList.value[i].listProcessView = approvalList.value[i].listProcessView.slice(0, 5)
            }
            fillingActiveNamesList.value = fillingList.value.map(item => item.ProcessId) || []
            approvalActiveNamesList.value = approvalList.value.map(item => item.ProcessId) || []
            if (fillingList.value.length == 0 && approvalList.value.length == 0) {
                msg.value = '暂无需要处理业务'
            } else {
                msg.value = ''
            }
            // 判断待处理业务是否小于3，小于3则获取首页图片
            nextTick(() => {
                let length = fillingList.value.length + approvalList.value.length
                if (length < 3) {
                    GetinformationbycatetypeUser(3 - length)
                }
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    });
    GetArticleList().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            articleList.value = rows || []
            for (let i = 0; i < articleList.value.length; i++) {
                // 取每一项的ListArticle前五项
                articleList.value[i].ListArticle = articleList.value[i].ListArticle.slice(0, 5)
            }

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 获取首页图片
const GetinformationbycatetypeUser = (num) => {
    Getinformationbycatetype({ cateType: 4, topCount: num }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // console.log("轮播图", rows)
            imageList.value = rows

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 处理 ：填报
const handleEdit = (item) => {
    let routerUrl = `@moduleId=${item.ModuleId}&processId=${item.ProcessId}&processNode=${item.ProcessNodeId}`
    router.push({ path: "/approvalconfiguration/pages/node/formedit" + routerUrl, query: { projectDeclarationId: item.ProjectDeclarationId } })
}
// 处理 ：填报
const handleDetail = (item) => {
    let routerUrl = `@moduleId=${item.ModuleId}&processId=${item.ProcessId}&processNode=${item.ProcessNodeId}`
    router.push({ path: "/approvalconfiguration/pages/node/detail" + routerUrl, query: { id: item.ProjectDeclarationId } })
}
// 处理 ：审批
const handleExamine = (item) => {
    let routerUrl = `@moduleId=${item.ModuleId}&processId=${item.ProcessId}&processNode=${item.ProcessNodeId}`
    router.push({ path: "/approvalconfiguration/pages/node/examine" + routerUrl, query: { id: item.ProjectDeclarationId, Statuz: item.Statuz, title: '审批' } })
}
// 待处理列表 
const handlePendinglist = (item) => {
    let routerUrl = `@moduleId=${item.ModuleId}&processId=${item.ProcessId}&processNode=${item.ProcessNodeId}`
    router.push({ path: "/approvalconfiguration/pages/node/pendinglist" + routerUrl })
}
// 查看资讯详情
const detailClick = (item) => {
    const { href } = router.resolve({
        path: "/articledetail",
        query: { Id: item.Id }
    });
    window.open(href, "_blank");
}
// 查看资讯列表
const listClick = (item) => {
    const { href } = router.resolve({
        path: "/articlelist",
        query: { Id: item.Id, IsMany: item.IsMany }
    });
    window.open(href, "_blank");
}
</script>
<template>

    <div class="container" style="display: flex;">
        <div class="container_left">
            <el-card v-for="item in fillingList" :key="item.ProcessId" :name="item.ProcessId" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <div class="header-left">
                            <div class="process-name">{{ item.ProcessName }}</div>
                        </div>
                        <div class="header-right">
                            <span class="status-label">进行中：</span>
                            <span class="status-number">{{ item.Num }}</span>
                        </div>
                    </div>
                </template>
                <ul>
                    <li v-for="t in item.listProcessView" :key="t.ProjectDeclarationId">
                        <div class="li_title" @click="handlePendinglist(t)">
                            <span class="ProjectName">{{ t.ProjectName }}</span>
                            <span class="StatuzDesc">{{ t.StatuzDesc }}</span>
                            <span class="CreateDate">{{ t.CreateDate.substring(0, 10) }}</span>
                        </div>
                        <div class="li_btn">
                            <el-button v-if="t.OperateType == 1" type="primary" size="small"
                                @click="handleEdit(t)">处理</el-button>
                            <el-button v-if="t.OperateType == 2" type="primary" size="small"
                                @click="handleDetail(t)">查看</el-button>

                        </div>
                    </li>
                </ul>
            </el-card>
            <el-card v-for="item in approvalList" :key="item.ProcessId" :name="item.ProcessId" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <div class="header-left">
                            <div class="process-name">{{ item.ProcessName }}</div>
                        </div>
                        <div class="header-right">
                            <span class="status-label">待处理：</span>
                            <span class="status-number">{{ item.Num }}</span>
                        </div>
                    </div>
                </template>
                <ul>
                    <li v-for="t in item.listProcessView" :key="t.ProjectDeclarationId">
                        <div class="li_title" @click="handlePendinglist(t)">
                            <span class="ProjectName">{{ t.ProjectName }}</span>
                            <span class="StatuzDesc">{{ t.StatuzDesc }}</span>
                            <span class="CreateDate">{{ t.CreateDate.substring(0, 10) }}</span>
                        </div>
                        <div class="li_btn">
                            <el-button type="primary" size="small" @click="handleExamine(t)">处理</el-button>
                        </div>
                    </li>
                </ul>
            </el-card>
            <el-card v-for="item in imageList" :key="item.Id" shadow="hover">
                <img :src="item.ImageUrl" style="width: 100%;height: 100%;" />
            </el-card>
        </div>
        <div class="container_right">
            <el-card v-for="item in articleList" :key="item.Id" shadow="hover" class="card_img">
                <template #header>
                    <div class="card-header card-header-article">
                        <div class="header-left">
                            <div class="article-name">{{ item.Name }}</div>
                        </div>
                        <div class="header-right">
                            <el-icon class="more-icon" @click="listClick(item)">
                                <MoreFilled />
                            </el-icon>
                        </div>
                    </div>
                </template>
                <ul>
                    <li v-for="(t, i) in item.ListArticle" :key="t.Id">
                        <div class="li_title li_title2" @click="detailClick(t)">
                            <div class="liTitle">{{ i + 1 }}.{{ t.Title }}</div>
                            <span class="liDate"> {{ t.RegDate.substring(0, 10) }}</span>
                        </div>
                    </li>
                </ul>
            </el-card>
        </div>
    </div>
</template>
<style lang="scss" scoped>
:deep(.el-card) {
    width: 100%;
    // height: 100%;
    margin-bottom: .75rem;
    height: 15rem;
}

:deep(.el-card__header) {
    padding: 0.375rem 1rem;
}

:deep(.el-card__body) {
    // padding: .3125rem;
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 0rem;
}

:deep(.el-collapse-item__header) {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

:deep(.el-collapse-item__wrap) {
    border-bottom: none;
}

:deep(.el-collapse-item__content) {
    padding-bottom: 0rem;
}

// :deep(.el-collapse-item__arrow) {
//     margin-right: 1.25rem;
// }

:deep(.el-collapse-item__arrow.is-active) {
    padding: 0;
}

:deep(.el-button--small) {
    font-size: 0.75rem;
    padding: 0.3125rem 0.6875rem;
    --el-button-size: 1.5rem;
    height: 1.5rem;
}



.container {
    display: flex;
    // justify-content: space-between;
    // align-items: flex-start;
    // flex-wrap: wrap;
    width: 104rem;
}

.container_left {
    width: 75rem;
    flex-shrink: 0;
    margin-right: 1.25rem;
}

.container_right {
    width: 29rem;
    // width: 90%;
}

.emptyMsg {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.25rem;
    font-weight: bold;
    color: #999;
}


.home_title {
    padding: .625rem 0rem;
    background-color: #FAFAFA !important;

}

.container_left ul {
    padding-inline-start: 0rem;
    margin: 0rem;
    padding: .3125rem;

    li {
        list-style-type: none;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        padding: .375rem .625rem;
        border-bottom: .0625rem dashed #e6e6e6;
        font-size: .9375rem;


        span {
            display: inline-block;
            padding-right: 1.25rem;
        }

        .ProjectName {
            width: 15rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .StatuzDesc {
            width: 12.5rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .CreateDate {
            width: 7.5rem;
        }

        .li_title {
            display: flex;
            align-items: center;
        }

        .li_btn {
            padding-left: 1.25rem;
            width: 5rem;
            flex-shrink: 0;
        }

        .li_title:hover {
            cursor: pointer;
            color: var(--el-color-primary);
            text-decoration: underline;
        }

        .li_title2 {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .liTitle {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .liDate {
            width: 6.875rem;
            flex-shrink: 0;
        }

    }
}

/* 简化的card-header样式 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.process-name,
.article-name {
    font-size: 0.95rem;
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.status-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: normal;
}

.status-number {
    font-size: 0.875rem;
    font-weight: 600;
    color: #409eff;
}

.more-icon {
    color: #409eff;
    font-size: 1.125rem;
    cursor: pointer;
    transition: color 0.2s ease;
}

.more-icon:hover {
    color: #1d4ed8;
}

.container_right ul {
    padding-inline-start: 0rem;
    margin: 0rem;
    padding: .3125rem;
    width: 100%;

    li {
        list-style-type: none;
        padding: .375rem .625rem;
        border-bottom: .0625rem dashed #e6e6e6;
        font-size: .9375rem;

        .li_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .li_title:hover {
            cursor: pointer;
            color: var(--el-color-primary);
            text-decoration: underline;
        }


        .liTitle {
            width: 20rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .liDate {
            width: 6.25rem;
            flex-shrink: 0;
        }

    }
}

.article {
    width: 100%;
    height: 15rem;
    margin-bottom: .625rem;
}
</style>
