<script setup>
defineOptions({
  name: 'nodeexamine'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
  QuestionFilled, Select, UploadFilled, FolderChecked, Right
} from '@element-plus/icons-vue'
import {
  FillingInfoAudit, SubmitAppRoval, SaveApproval, GetLinkAgeDataSource, GetControlDetailDataSource
} from '@/api/workflow.js'
import {
  AttachmentUpload
} from '@/api/user.js'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import DetailsPage from '@/views/approvalconfiguration/pages/publicpage/DetailsPage.vue' //详情页面
import { pageQuery, urlQuery, tagsListStore, fileDownload, popperOptions } from "@/utils/index.js";
import { rules, rulesLimit, rulesIntegerLimit, rulesAmountLimit } from "@/utils/rules.js";
import router from '@/router'
// 初始化
const userStore = useUserStore()
const route = useRoute()
const size = ref('default')
const routerObject = ref({})
const routerUrl = ref('')
const refForm = ref()
const formFields = ref({})
const detailData = ref([])
const activeNames = ref([])
const hideFormDataList = ref([])
const currentTabName = ref('')//当前节点名称
const submitButtonName = ref('审批')//提交按钮名称
const stagingButton = ref('')//暂存按钮名称
//加载数据
onMounted(() => {
  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象 
  if (route.query.isTagRouter) {
    FillingInfoAuditUser()
  }
})
onActivated(() => {

  // 修改tag标签名称########################
  if (route.query.title) {
    // 修改pinia数据
    userStore.$patch(state => {
      state.pageTitleObj.examineTitle = route.query.title
    })
  }
  // pageTitleObj
  let tagsList = userStore.tagsList
  // 使用forEach遍历数组  修改tag标签名称
  tagsList.forEach(item => {
    if (item.path == route.path) {
      // 如果path匹配，则修改title  
      item.title = userStore.pageTitleObj.examineTitle;
    }
  });
  userStore.setTagsList(tagsList)
  // ########################

  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
  routerUrl.value = urlQuery(route.path); // 获取生成页面携带的参数
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  nextTick(() => {
    if (!route.query.isTagRouter) {
      FillingInfoAuditUser()
    }
  })
})
// 获取填报页面信息
const FillingInfoAuditUser = () => {
  let formparams = {
    id: route.query.id,
    ProcessNodeId: routerObject.value.processNode,
  }
  FillingInfoAudit(formparams).then((res) => {
    if (res.data.flag == 1) {
      const { rows, headers, other } = res.data.data
      // nextTick(() => {
      formFields.value = {}
      refForm.value.resetFields()
      // })

      // console.log('获取填报页面信息res.data.data', res.data.data)
      // 需要填写审批信息的部分
      let data = JSON.parse(headers.FormContent)
      // console.log("data", data)
      data = JSON.parse(headers.FormContent).filter(item => !item.isDetail)
      // 审核审批不显示的字段#############
      let dataList = JSON.parse(headers.FormContent).filter(item => item.isDetail)

      dataList.forEach(item => {
        hideFormDataList.value.push({
          FieldId: item.field,
          FieldCode: item.code,
          InfoId: item.code + '_@#$',
          InfoText: item.code + '_@#$'
        })
      })
      // console.log('hideFormDataList.value', hideFormDataList.value)
      // #################
      console.log('data', data)
      // 将other内的数据源替换到data中
      let otherSelect = other?.listSelect.filter(t => !t.desp) || []
      let otherProject = other?.listSelect.filter(t => t.desp == 'projectnumber') || []
      let otherQuery = other?.listQuery || []
      currentTabName.value = other?.currentTabName || ''
      // let other3Project = other3 || []
      console.log("otherProject", otherProject)
      data.forEach((aItem, index) => {
        // 找到 other中 label 与 data中 field 匹配的项
        const bItem = otherSelect.find(b => b.label === aItem.field);
        if (bItem) {
          // 替换 data中的 data 为other中的 value
          aItem.data = JSON.parse(bItem.value);
          aItem.IsLinkAge = bItem.IsLinkAge;
          aItem.FieldCode = bItem.FieldCode;
        }
        const cItem = otherProject.find(c => c.label === aItem.code);
        if (cItem) {
          // 替换 data中的 data 为other中的 value
          aItem.DefaultValue = cItem.value;
          aItem.DefaultName = cItem.text;
        }
        const eItem = otherQuery.find(e => e.FieldCode === aItem.code);
        if (eItem) {
          // 替换 data中的 data 为other中的 value 
          if (eItem.InfoId && eItem.InfoId != '[]') {
            if (eItem.InfoId.indexOf('[') > -1 && eItem.InfoId.indexOf(']') > -1) {
              aItem.DefaultValue = JSON.parse(eItem.InfoId);
            } else {
              aItem.DefaultValue = eItem.InfoId;
            }
            aItem.DefaultName = eItem.InfoText;
          }
        }
        console.log('aItem', aItem)
        // 找到 b 数组中 label 与 a 数组中 field 匹配的项
        const dItem = data.find(d => d.label === d.field);
        if (dItem) {
          // 替换 a 数组中的 data 为 b 数组中的 value
          aItem.data = JSON.parse(dItem.value);
        }
      });
      data.forEach(item => {
        if (item.UploadFileType && item.UploadFileType.length > 0) {
          item.UploadFileTypeAccept = item.UploadFileType.join('').split('.').join(',.')
        }
      });
      // console.log('获取填报页面信息data', data)
      // 是否存在暂存数据，如果存在则赋值给data
      if (headers.DataContent) {
        let headersDataContent = JSON.parse(headers.DataContent)//数据
        console.log('获取填报页面信息headersDataContent', headersDataContent)
        // 创建一个映射表，用于快速查找headersDataContent中的 InfoId
        const infotextMap = headersDataContent.reduce((map, item) => {
          map[item.FieldId] = item.InfoId;
          return map;
        }, {});
        // 创建一个映射表，用于快速查找headerData中的InfoText
        const infoTextMap = headersDataContent.reduce((map, item) => {
          map[item.FieldId] = item.InfoText;
          return map;
        }, {});
        // 创建一个映射表，用于快速查找headersDataContent中的 isControlShow
        const infotextControl = headersDataContent.reduce((map, item) => {
          map[item.FieldId] = item.isControlShow;
          return map;
        }, {});
        // 遍历表单元素FormContent，并替换field对应的InfoText到DefaultName  
        data.forEach(item => {
          if (infotextMap[item.field]) {
            item.DefaultValue = infotextMap[item.field];
            item.DefaultName = infoTextMap[item.field];
            item.isControlShow = infotextControl[item.field];
          }
        });
      } else {
        refForm.value.resetFields()
      }
      console.log('获取填报页面信息data', data)
      formFields.value.formItems = data
      // 已审核过只需查看的部分
      rows.forEach((item, index) => {
        let FormContent = JSON.parse(item.FormContent)//表单元素
        let DataContent = JSON.parse(item.DataContent)//数据
        // 创建一个映射表，用于快速查找DataContent中的 InfoText
        const infotextMap = DataContent.reduce((map, item) => {
          map[item.FieldId] = item.InfoText;
          return map;
        }, {});
        // 创建一个映射表，用于快速查找DataContent中的 isControlShow
        const infotextControl = DataContent.reduce((map, item) => {
          map[item.FieldId] = item.isControlShow;
          return map;
        }, {});
        // 遍历表单元素FormContent，并替换field对应的InfoText到DefaultName  
        FormContent.forEach(item => {
          if (infotextMap[item.field]) {
            item.DefaultName = infotextMap[item.field];
            item.isControlShow = infotextControl[item.field];
          }
        });
        item.data = FormContent.filter(t => !t.isRedact)
      })

      activeNames.value = rows.map((item, index) => item.TabId + index)//默认展开所有
      detailData.value = rows//表单数据(查看)
      submitButtonName.value = other.submitButtonName || '审批'
      stagingButton.value = other.stagingButton
      console.log('detailData.value', detailData.value)
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

// 表单校验
const ruleForm = (item) => {
  if (item.type == 'text') {
    if (!item.rules) return rules['required'](item.name)
    // 当是小数类型时，小数位数wei0则为整数
    if (item.rules == 'decimals' && (!item.arg || item.arg == '0')) {
      item.rules = 'integer'
    }
    if (item.rules == 'rangeLength') {
      let arg = []
      if (item.arg.indexOf(',')) {
        arg = item.arg.split(',')
      } else if (item.arg.indexOf('，')) {
        arg = item.arg.split('，')
      }
      return rules[item.rules](item.name, arg[0], arg[1], item.required)
    }
    // 根据校验方法名和参数调用校验方法
    return rules[item.rules](item.name, item.arg, item.required)
  }
  if (item.type == 'textarea') {
    return [{ required: true, message: '请输入' + item.name, trigger: 'blur' }]
  } else {
    return [{ required: true, message: '请选择' + item.name, trigger: 'change' }]
  }

}
//输入框限制：输入小数/整数
const rulesInput = (val, item, name) => {
  if (item.rules == 'integer') {
    item[name] = rulesIntegerLimit(val);
  } else if (item.rules == 'decimals') {
    if (!item.arg || item.arg == '0') {
      item[name] = rulesIntegerLimit(val);
    } else {
      item[name] = rulesLimit(val, item.arg);
    }
  } else if (item.rules == 'amount') {
    item[name] = rulesAmountLimit(val, item.arg);
  } else { }
}
// 模板下载事件
const downloadClick = (item) => { }
// 按钮事件
const buttonClick = (item) => {
  // 通过 item.click 绑定事件名称进行处理
  // refForm.value.resetFields()
}
// 输入框/文本域：DefaultName赋值
const textChange = (e, item, index) => {
  // console.log('textChange', e, item, index)
  item.DefaultName = e


  // 条件控制为主控时000000
  if (item.MasterControl > 0) {
    // 同组的主控元素
    let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === item.MasterControl)
    console.log('同组的主控元素', masterControlList)
    GetControlDetailDataSourceUser(item.MasterControl, masterControlList)
  }
  if (item.type === 'fundallocation') {
    // 资金分配求和影响主控事件
    if (item.code.startsWith('FundAllocation_')) {
      if (item.code.startsWith('FundAllocation_0_')) {
        let isAmount = formFields.value.formItems.filter(t => t.code == 'ProjectAmount')
        console.log('项目金额isAmount', isAmount)
        if (isAmount.length > 0 && isAmount[0].IsAmountControl > 0) {
          let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === isAmount[0].MasterControl)
          nextTick(() => {
            GetControlDetailDataSourceUser(isAmount[0].MasterControl, masterControlList)
          })
        }
      } else if (!item.code.endsWith('_Total')) {
        let isAmount = formFields.value.formItems.filter(t => t.code == 'FundAllocation_' + item.code.split('_')[1] + '_Total')
        console.log('项目金额isAmount', isAmount)
        if (isAmount.length > 0 && isAmount[0].IsAmountControl > 0) {
          let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === isAmount[0].MasterControl)
          nextTick(() => {
            GetControlDetailDataSourceUser(isAmount[0].MasterControl, masterControlList)
          })
        }
      }
    }
  }

}
// 下拉单选/单选框：DefaultName赋值
const selectChange = (e, item, index) => {
  // console.log('selectChange', e, item, index)
  item.data.filter((t) => { if (t.value == e) { item.DefaultName = t.label } })


  // 当为父级下拉框时，执行的操作000000
  if (item.IsLinkAge === 1) {
    GetLinkAgeDataSource({ code: item.FieldCode + e }).then(res => {
      if (res.data.flag == 1) {
        const { rows } = res.data.data
        formFields.value.formItems.forEach((aItem, index) => {
          // 找到 other中 label 与 data中 field 匹配的项
          const bItem = rows.find(bItem => bItem.label === aItem.field);
          if (bItem) {
            // 替换 data中的 data 为other中的 value
            aItem.data = JSON.parse(bItem.value);
            if (!aItem.data.find(t => t.value === aItem.DefaultValue)) {
              aItem.DefaultValue = '';
              aItem.DefaultName = '';
            }
            // console.log('aItem', aItem)
            //  // 子元素为主控时
            if (aItem.MasterControl > 0) {
              // 同组的主控元素
              let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === aItem.MasterControl)
              console.log('同组的主控元素', masterControlList)
              GetControlDetailDataSourceUser(aItem.MasterControl, masterControlList)
            }
            removeChildrenValue(item.code, aItem)
          }
        });
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  }
  // 条件控制为主控时
  if (item.MasterControl > 0) {
    // 同组的主控元素
    let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === item.MasterControl)
    console.log('同组的主控元素', masterControlList)
    GetControlDetailDataSourceUser(item.MasterControl, masterControlList)
  }


}




// 清除子级及其子级的下拉框数据000000
const removeChildrenValue = (code, aItem) => {
  if (code === aItem.ParentCode && aItem.children) {
    formFields.value.formItems.forEach((d) => {
      const childrenItem = aItem.children.some(t => t == d.code)
      if (childrenItem) {
        d.DefaultValue = '';
        d.DefaultName = '';
        // console.log('d', d)
        //  // 子元素为主控时
        if (d.MasterControl > 0) {
          // 同组的主控元素
          let masterControlList = formFields.value.formItems.filter(t => t.MasterControl === d.MasterControl)
          console.log('同组的主控元素', masterControlList)
          GetControlDetailDataSourceUser(d.MasterControl, masterControlList)
        }
      }
      if (aItem.code === d.ParentCode && d.children) {
        removeChildrenValue(aItem.code, d)
      }
    })
  }
}
// 根据主控字段编码获取被控制字段显示信息000000
const GetControlDetailDataSourceUser = (groupName, masterControlList) => {
  let listCodeValue = masterControlList.map(({ code, DefaultValue }) => ({
    FieldCode: code,
    FieldValue: DefaultValue
  }))
  let formparams = {
    GroupName: groupName,//组名
    ProcessNodeId: routerObject.value.processNode,//节点id
    ListCodeValue: listCodeValue.filter(t => t.FieldValue),//主控字段集合
  }
  GetControlDetailDataSource(formparams).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      console.log('formFields.value.formItems', formFields.value.formItems)
      formFields.value.formItems.forEach(item => {
        // 与主控同组的所有被控元素
        if (item.Controlled == groupName && !rows.includes(item.code)) {
          item.isControlShow = false;//隐藏被控元素
          // console.log('item', item)
          // 当被控组件为其它组的主控时，且主控被控值不同时，继续执行事件
          if (item.MasterControl > 0 && item.MasterControl != groupName) {
            // 清除隐藏元素值，为了再次回调接口时去掉入参
            item.DefaultValue = '';
            item.DefaultName = '';
            // 同组的主控元素
            let controlledMasterControlList = formFields.value.formItems.filter(t => t.MasterControl === item.MasterControl)
            // console.log('同组的主控元素', controlledMasterControlList)
            GetControlDetailDataSourceUser(item.MasterControl, controlledMasterControlList)
          }
        }
        // 显示对应的被控元素
        if (rows.includes(item.code)) {
          item.isControlShow = true;
        }
      });
    }
  })
}








// 多选下拉/复选框：DefaultName赋值
const multipleSelectChange = (e, item, index) => {
  // console.log('selectChange', e, item, index)
  const labels = e.map(id => String(id)).map(id => {
    const items = item.data.find(item => item.value === id);
    return items ? items.label : '';
  });
  item.DefaultName = labels.join(',');
}
// 级联：DefaultName赋值
const cascaderChange = (e, item, index) => {
  // console.log('cascaderChange', e, item, index)
  let selectedLabel = getLabelPath(e, item.data);
  item.DefaultName = selectedLabel.join('/')
}

//级联使用： 递归函数，用于根据值数组找到对应的 label 路径
const getLabelPath = (e, data) => {
  let labels = [];
  let currentOptions = data;
  for (let i = 0; i < e.length; i++) {
    const value = e[i];
    const foundOption = currentOptions.find(option => option.value === value);
    if (foundOption) {
      labels.push(foundOption.label);
      currentOptions = foundOption.children || [];
    } else {
      // 如果没有找到对应的选项，可能是数据不一致，返回空数组
      return [];
    }
  }
  return labels;
};
// 项目清单查看
const projectDetail = (item, processNodeId, path) => {
  router.push({
    path: "./projectdetail@moduleId=" + routerObject.value.moduleId, query: {
      ProcessId: routerObject.value.processId,
      ProcessNodeId: processNodeId,
      ProjectDeclarationId: route.query.id,
      FieldCode: item.code,
      routerUrl: routerUrl.value,
      page: 'examine',
      type: item.type
    }
  })
}
// 项目清单填报
const projectEdit = (item) => {
  router.push({
    path: "./projectlist@moduleId=" + routerObject.value.moduleId, query: {
      ProcessId: routerObject.value.processId,
      ProjectDeclarationId: route.query.id,
      FieldCode: item.code,
      routerUrl: routerUrl.value,
      page: 'examine',//前往项目清单的页面
      isEdit: true
    }
  })
}
// 项目清单审核
const projectExamineEdit = (item) => {
  router.push({
    path: "./projectexaminelist@moduleId=" + routerObject.value.moduleId, query: {
      ProcessId: routerObject.value.processId,
      ProcessNodeId: routerObject.value.processNode,
      ProjectDeclarationId: route.query.id,
      FieldCode: item.code,
      routerUrl: routerUrl.value,
      isEdit: true
    }
  })
}
// 提交
const HandleSubmit = (e) => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) {
      const firstErrorProp = Object.keys(fields)[0];
      if (firstErrorProp) {
        refForm.value.scrollToField(firstErrorProp, {
          behavior: 'smooth',
          block: 'center'
        });
      }
      return;
    };
    // 去除被控且未显示的数据
    let formFieldsData = formFields.value.formItems.filter(t => !(t.Controlled > 0 && !t.isControlShow))
    //根据数据集合提取需要的属性
    let FieldConfig = formFieldsData.map(({ field, code, DefaultValue, DefaultName, isControlShow }) => ({
      FieldId: field,
      FieldCode: code,
      InfoId: DefaultValue,
      InfoText: DefaultName,
      isControlShow: isControlShow
    }));
    FieldConfig = [...FieldConfig, ...hideFormDataList.value]
    // console.log('FieldConfig', FieldConfig)
    let formData = {
      ProcessId: routerObject.value.processId,
      ProcessNodeId: routerObject.value.processNode,
      ProjectDeclarationId: route.query.id,
      Statuz: route.query.Statuz,
      FieldConfig: JSON.stringify(FieldConfig)
    };

    SubmitAppRoval(formData).then((res) => {
      if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '提交成功')
        let tagsList = userStore.tagsList
        tagsList = tagsList.filter(t => t.path != route.path)
        userStore.setTagsList(tagsList)
        router.push({ path: "./pendinglist" + routerUrl.value })
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })
}
// 暂存
const HandleStaging = (e) => {

  // 去除被控且未显示的数据
  formFields.value.formItems = formFields.value.formItems.filter(t => !(t.Controlled > 0 && !t.isControlShow))
  //根据数据集合提取需要的属性
  let FieldConfig = formFields.value.formItems.map(({ field, code, DefaultValue, DefaultName, isControlShow }) => ({
    FieldId: field,
    FieldCode: code,
    InfoId: DefaultValue,
    InfoText: DefaultName,
    isControlShow: isControlShow
  }));
  FieldConfig = [...FieldConfig, ...hideFormDataList.value]
  console.log('FieldConfig', FieldConfig)
  let formData = {
    ProcessId: routerObject.value.processId,
    ProcessNodeId: routerObject.value.processNode,
    ProjectDeclarationId: route.query.id,
    Statuz: route.query.Statuz,
    FieldConfig: JSON.stringify(FieldConfig)
  };

  SaveApproval(formData).then((res) => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '暂存提交成功')
      // let tagsList = userStore.tagsList
      // tagsList = tagsList.filter(t => t.path != route.path)
      // userStore.setTagsList(tagsList)
      // router.push({ path: "./pendinglist" + routerUrl.value })
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}


// ##########################################
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
  // console.log('达到最大数量后限制上传', item)
  let length = item.DefaultValue.length
  if (item.MaxFileNumber && length >= item.MaxFileNumber) {
    numberDisabled.value = true
    ElMessage.error("上传数量已达到上限,请先删除后再上传")
    return
  } else {
    numberDisabled.value = false
  }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
  // console.log("导入前校验item", item)
  // console.log("导入前校验file", file)
  fileFile.value = file
  let str = file.name.split('.')[1]
  let name = str.toLowerCase()
  let arr = item.UploadFileType.join('').split('.')
  // console.log("arr", arr)
  let ary = arr.filter(t => t != '')
  const extension = ary.includes(name)
  if (!extension) {
    ElMessage({
      message: `上传文件只能是${item.UploadFileType}格式!`,
      type: 'error'
    })
  }
  // // 校验文件大小
  let FileSize = item.FileSize
  if (item.FileSize == 0) {
    FileSize = 10
  }
  const isSize = file.size / 1024 / 1024 < FileSize;
  if (!isSize) {
    ElMessage({
      message: `文件大小不能超出${FileSize}M`,
      type: 'error'
    })
  }
  return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
  console.log("item", item, "index", index)
  AttachmentUpload({ file: fileFile.value, filecategory: Number(item.code) }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '上传成功')
      const { rows } = res.data.data
      // console.log("附件上传rows", rows[0])
      item.fileName = rows[0].Title
      // 添加重置，防止item.DefaultValue值为空的时候报错
      if (!item.DefaultValue) item.DefaultValue = []
      if (!item.DefaultName) item.DefaultName = []
      item.DefaultValue.push({
        Id: rows[0].Id,
        Title: rows[0].Title,
        Path: rows[0].Path,
        Ext: rows[0].Ext,
      })
      item.DefaultName.push({
        Id: rows[0].Id,
        Title: rows[0].Title,
        Path: rows[0].Path,
        Ext: rows[0].Ext,
      })
      // console.log("附件上传DefaultValue", item.DefaultValue)
      refForm.value.validateField(`formItems.${index}.DefaultValue`)// 手动触发校验
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
  let path = e.Path;
  viewPhotoList.value = imgList.map(t => t.Path)
  if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
    showViewer.value = true;
    viewPhotoList.value.forEach((item, index) => {
      if (path == item) {
        imgSrcIndex.value = index;
      }
    });
    // //大图预览从点击的那张开始
    let tempImgList = [...viewPhotoList.value];
    let temp = [];
    for (let i = 0; i < imgSrcIndex.value; i++) {
      temp.push(tempImgList.shift());
    }
    viewPhotoList.value = tempImgList.concat(temp);
  } else {
    let title = e.Title + e.Ext
    fileDownload(e.Path, title)
  }
}
// 删除附件
const delFile = (item1, item) => {
  // console.log("删除附件item", item)
  // console.log("删除附件item1", item1)
  item.DefaultValue = item.DefaultValue.filter(t => t.Id != item1.Id)
  item.DefaultName = item.DefaultValue.filter(t => t.Id != item1.Id)
}
</script>
<template>
  <div style="position: relative;width: 90%;">
    <!-- 暂存按钮粘性定位 -->
    <div class="vertical-btn" v-if="stagingButton" @click="HandleStaging">
      <span class="vertical-text">
        <el-icon color="#fff" size="20">
          <FolderChecked />
        </el-icon>
        <span style="padding-top: 10px;font-size: 20px;"> {{ stagingButton }}</span></span>
    </div>
    <!-- 详情部分 -->
    <details-page :detailData="detailData" v-model:activeNames="activeNames" :page="'examine'"></details-page>
    <!-- 审核表单 -->
    <div style="color: #0000EE;padding: 15px 0;font-size: 15px;">{{ currentTabName }}</div>
    <el-form style="width: 80%;min-width: 600px;" :inline="true" ref="refForm" @submit.prevent :model="formFields">
      <div class="edit-form-item">
        <template v-for="(item, index) in formFields.formItems" :key="item.code">
          <el-form-item :label-width="item.type == 'line' ? '0px' : item.labelWidth ? item.labelWidth + 'px' : '200px'"
            :prop="'formItems.' + index + '.DefaultValue'" :rules="item.rules || item.required ? ruleForm(item) : []"
            :style="{ width: item.width + '%' }" v-if="!(item.Controlled > 0 && !item.isControlShow)">
            <!-- label -->
            <template #label>
              <el-tooltip v-if="item.isRemark" class="item" effect="dark" :content="item.HelpRemark" placement="top">
                <div>
                  <el-icon color="#E6A23C" class="tipIcon">
                    <QuestionFilled />
                  </el-icon>
                </div>
              </el-tooltip>
              <span> {{ item.name ? item.name + '：' : '' }} </span>
            </template>
            <!-- 输入框 -->
            <el-input
              v-if="['text', 'projectname', 'projectamount', 'fundallocation', 'unitname', 'unitaddress', 'unitperiod', 'unitstreettown'].includes(item.type)"
              v-model="item.DefaultValue" :size="size" clearable
              :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name" :disabled="item.readonly"
              @change="textChange($event, item, index)" @input="rulesInput($event, item, 'DefaultValue')"
              style="width: 100%;"></el-input>
            <!-- 描述文本 -->
            <span v-else-if="item.type === 'descriptivetext'"
              :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#999999', 'fontSize': '14px' }">
              {{ item.DefaultValue }}</span>
            <span v-else-if="item.type === 'projectallamount'">
              {{ item.DefaultValue }}</span>
            <!-- 文本域 -->
            <el-input v-else-if="item.type == 'textarea' || item.type == 'auditremark'" v-model="item.DefaultValue"
              type="textarea" :maxlength="item.maxLength ? item.maxLength : 500" show-word-limit :size="size" clearable
              :disabled="item.readonly" :autosize="{ minRows: item.minRows || 2, maxRows: item.maxRows || 10 }"
              :placeholder="item.placeholder ? item.placeholder : '请输入' + item.name"
              @change="textChange($event, item, index)" style="width: 100%;" />
            <!-- 下拉框 -->
            <el-select
              v-else-if="['select', 'subjectnature', 'OneClassId', 'TwoClassId', 'AppointAuditUserSelect'].includes(item.type)"
              v-model="item.DefaultValue" :disabled="item.readonly" :size="size" filterable
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name" clearable
              @change="selectChange($event, item, index)" style="width: 100%;">
              <el-option v-for="item1 in item.data" :key="item1.value" :label="item1.label" :value="item1.value">
              </el-option>
            </el-select>
            <!-- 多选下拉框 -->
            <el-select v-else-if="item.type == 'selectList' || item.type == 'AppointAuditUserSelectList'"
              v-model="item.DefaultValue" :disabled="item.readonly" :size="size" filterable multiple
              :placeholder="item.placeholder ? '请选择' + item.placeholder : item.name" clearable
              @change="multipleSelectChange($event, item, index)" style="width: 100%;">
              <el-option v-for="item1 in item.data" :key="item1.value" :label="item1.label" :value="item1.value">
              </el-option>
            </el-select>
            <!-- 单选框 -->
            <el-radio-group v-else-if="item.type == 'radio' || item.type == 'auditstatuz'" v-model="item.DefaultValue"
              :disabled="item.readonly" @change="selectChange($event, item, index)" style="width: 100%;">
              <el-radio v-for="kv in item.data" :disabled="item.readonly" :key="kv.value" :value="kv.value">
                {{ kv.label }}
              </el-radio>
            </el-radio-group>
            <!-- 复选框 -->
            <el-checkbox-group v-else-if="item.type == 'checkbox'" v-model="item.DefaultValue" :disabled="item.readonly"
              @change="multipleSelectChange($event, item, index)" style="width: 100%;">
              <el-checkbox v-for="kv in item.data" :key="kv.value" :label="kv.label" :value="kv.value"> </el-checkbox>
            </el-checkbox-group>
            <!-- 日期 -->
            <el-date-picker v-else-if="item.type == 'date'" v-model="item.DefaultValue" type="date" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" :size="size" clearable :disabled="item.readonly"
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="textChange($event, item, index)" :popper-options="popperOptions" style="width: 100%;">
            </el-date-picker>
            <!-- 日期时间 -->
            <el-date-picker v-else-if="item.type == 'datetime'" v-model="item.DefaultValue" type="datetime"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" :size="size" clearable
              :disabled="item.readonly" :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="textChange($event, item, index)" :popper-options="popperOptions" style="width: 100%;">
            </el-date-picker>
            <!-- 时间 -->
            <el-time-picker v-else-if="item.type == 'time'" v-model="item.DefaultValue" format="HH:mm:ss"
              value-format="HH:mm:ss" :size="size" :disabled="item.readonly"
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="textChange($event, item, index)" :popper-options="popperOptions" style="width: 100%;">
            </el-time-picker>
            <!-- 级联 -->
            <el-cascader v-else-if="item.type == 'cascader'" v-model="item.DefaultValue" :options="item.data"
              :props="{ checkStrictly: true }" :size="size" clearable :disabled="item.readonly"
              :placeholder="item.placeholder ? item.placeholder : '请选择' + item.name"
              @change="cascaderChange($event, item, index)" style="width: 100%;">
            </el-cascader>
            <!-- 排序 -->
            <el-input v-else-if="item.type == 'sort'" type="number" v-model="item.DefaultValue"
              @change="textChange($event, item, index)"></el-input>
            <!-- 模板下载按钮 -->
            <el-button v-else-if="item.type == 'download'" type="primary" size="small" @click="downloadClick(item)">
              {{ item.buttonTitle }}</el-button>
            <!-- 按钮 -->
            <el-button v-else-if="item.type == 'button'" type="primary" size="small" @click="buttonClick(item)">
              {{ item.buttonTitle }}</el-button>
            <!-- 上传 -->
            <div v-else-if="item.type == 'upload'" style="display: flex;">
              <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" :accept="item.UploadFileTypeAccept"
                :before-upload="beforeAvatarUpload.bind(null, item)" :http-request="httpRequest.bind(null, item, index)"
                :disabled="numberDisabled">
                <el-button type="success" size="small" :icon="UploadFilled"
                  @click="MaxFileNumberClick(item)">上传</el-button>
              </el-upload>
              <div class="fileFlex">
                <div v-for="(item1, index1) in item.DefaultValue" :key="item1.Id" style="cursor: pointer;">
                  <el-icon color="#F56C6C" @click="delFile(item1, item)">
                    <Delete />
                  </el-icon>
                  <span @click="fileListDownload(item1, item.DefaultValue)"> {{ item1.Title }}</span>
                </div>
              </div>
            </div>
            <!-- 项目清单(填报) -->
            <div v-else-if="item.type == 'projectlist'">
              <el-button type="primary" size="small" @click="projectEdit(item)"> {{ item.buttonTitle }}</el-button>
              <!-- <span style="padding-left: 10px;" v-if="item.code == 'ProjectList'">总金额： <span style="font-size: 16px;">
              {{ totalAmount }}</span> </span> -->
            </div>
            <!-- 项目清单（审核） -->
            <div v-else-if="item.type == 'projectexaminelist'">
              <el-button type="primary" size="small" @click="projectExamineEdit(item)">
                {{ item.buttonTitle }}</el-button>
            </div>
            <!-- 开关 -->
            <el-switch v-else-if="item.type == 'switch'" v-model="item.DefaultValue" :disabled="item.readonly"
              active-color="#13ce66" inactive-color="#0e7ef3"
              :active-value="typeof item.DefaultValue == 'boolean' ? true : typeof item.DefaultValue == 'string' ? '1' : 1"
              :inactive-value="typeof item.DefaultValue == 'boolean' ? false : typeof item.DefaultValue == 'string' ? '0' : 0">
            </el-switch>
            <!-- 分割线 -->
            <el-divider v-else-if="item.type == 'line'" :border-style="item.isDashed" :content-position="item.isCenter"
              :style="{ 'width': '100%', '--el-border-color': item.dividerColor ? item.dividerColor : '#dedfe6' }">
              <span :style="{ 'color': item.dividerTextColor ? item.dividerTextColor : '#303133' }">
                {{ item.dividerText }}</span>
            </el-divider>
          </el-form-item>
        </template>
        <el-form-item label-width="200px" label=" ">
          <el-button v-if="stagingButton" type="primary" :icon="Select" @click="HandleStaging">
            {{ stagingButton }}</el-button>
          <el-button type="primary" :icon="Right" @click="HandleSubmit">{{ submitButtonName }}</el-button>
        </el-form-item>

      </div>
      <div style="width: 100%">
        <slot name="footer"></slot>
      </div>
    </el-form>

    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
  </div>
</template>
<style lang="scss" scoped>
.edit-form-item {
  display: flex;
  flex-wrap: wrap;
}

:deep(.el-form-item) {
  margin-right: 0;
  // align-items: center;
}

.col-line {
  line-height: 25px;
  font-weight: bold;
  border-bottom: 1px solid rgb(218 218 218);
}

ul {
  padding-inline-start: 0px;
  margin: 0;

  li {
    list-style-type: none;
    font-size: 12px;

    border-bottom: none !important;

  }
}

:deep(.el-divider__text.is-left) {
  left: 100px;
}
</style>
