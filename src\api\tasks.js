import request from '@/utils/request.js'

//分页查询接口-查询   (FromBody)
export const TaskQzGetPaged = (params) => {
  return request.post('/api/TasksQz/Get', params)
}

//获取任务详情接口-查询   (FromBody)
export const TaskQzGetById = (params) => {
  return request.post('/api/TasksQz/getbyid', null, {
    params: params
  })
}

//任务信息-保存添加   (FromBody)
export const TaskQzAddSave = (params) => {
  return request.post('/api/TasksQz/Post', params)
}

//任务信息-保存修改   (FromBody)
export const TaskQzEditSave = (params) => {
  return request.post('/api/TasksQz/Put', params)
}

//任务信息-删除   (FromBody)
export const TaskQzDelete = (params) => {
  return request.post('/api/TasksQz/Delete', null, {
    params: params
  })
}

//分页查询任务日志接口-查询   (FromBody)
export const TasksLogGetPaged = (params) => {
  return request.post('/api/TasksQz/GetTaskLogs', params)
}


//查询平台历史记录日志分页接口-查询   (FromBody)
export const HistoryTraceGetPaged = (params) => {
  return request.post('/api/hyun/historytrace/getpaged', params)
}

//查询平台历史记录日志接口-查询   (FromBody)
export const HistoryTraceGetById = (params) => {
  return request.post('/api/hyun/historytrace/getbyid', params)
}