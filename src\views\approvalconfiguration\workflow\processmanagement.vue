<script setup>
defineOptions({
    name: 'processmanagement'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { Refresh, Search, FolderAdd, Delete } from '@element-plus/icons-vue'
import {
    FindprocessList, ProcessinsertUpdate, Processfindbyid, ProcesssetStatuz, ProcessDeletebyid, GetGroupSetList, GroupSetInsertUpdate, GetDataSource
} from '@/api/workflow.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yesNoList = ref([{ value: '1', label: '是' }, { value: '2', label: '否' }])
const isOpenList = ref([{ value: 1, label: '是' }, { value: 2, label: '否' }])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const UnitList = ref([])
const UsageList = ref([])
const ModuleList = ref([])
const InputDataTypeList = ref([])
const dialogVisible = ref(false)
const dialogData = ref({})
const editId = ref()
const selectRows = ref([])
const refForm = ref()

const ruleForm = {
    ModuleId: [
        { required: true, message: '请选择模板', trigger: 'change' },
    ],
    ProcessName: [
        { required: true, message: '请输入流程名称', trigger: 'change' },
    ],
    UseUnitId: [
        { required: true, message: '请选择使用单位', trigger: 'change' },
    ],
    FieldCode: [
        { required: true, message: '请输入资金分配指定的字段编码即字段code', trigger: 'change' },
    ],
    TypeCode: [
        { required: true, message: '请输入资金分配指定的数据分类编码', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
        GetDataSourceUser()
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
            GetDataSourceUser()
        }
    })
})
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 创建
const HandleAdd = (row) => {
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            Usage: '1',
            InputDataType: '1',
            IsOpen: 1,
            IsShowHistoryFile: '2',
            IsSignature: '2',
            IsControlAmount: '1',
            IsOpenControl: '2',
        }
    })
}
// 修改
const HandleEdit = (row) => {
    editId.value = row.Id
    ProcessfindbyidUser(row.Id)
    dialogVisible.value = true
}


// 启用禁用
const HandleEnable = (row) => {
    let msg = ''
    if (row.Statuz == 2) {
        msg = `确定要启用 [${row.ProcessName}]吗?`
    } else {
        msg = `确定要禁用 [${row.ProcessName}]吗?`
    }

    ElMessageBox.confirm(msg)
        .then(() => {
            ProcesssetStatuz({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleSearch()
                    ElMessage.success(res.data.msg || '设置成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
}

//创建/修改选用组织  提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        ProcessinsertUpdateUser()
    })
}
//删除
const HandleDel = () => {
    let row = selectRows.value[0]
    // console.log(row)

    ElMessageBox.confirm(`确定删除流程【${row.ProcessName}】吗?`)
        .then(() => {
            ProcessDeletebyid({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {

                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 设置
const HandleSetting = (row) => {
    console.log(row)
    router.push({ path: "../extension/index", query: { id: row.Id } })
}
// 状态描述
const HandleStateDescription = (row) => {
    console.log(row)
    router.push({ path: "./process/statedescription", query: { id: row.Id } })
}

// 预算清单配置
const HandleBudgetList = (row) => {
    console.log(row)
    router.push({ path: "./process/budgetlist", query: { id: row.Id } })
}
// 消息推送配置
const HandleMessage = (row) => {
    console.log(row)
    router.push({ path: "./process/messageconfig", query: { id: row.Id, ModuleName: row.ModuleName, ProcessName: row.ProcessName } })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    FindprocessList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
            if (isFirst) {
                UnitList.value = other.listUnit || [];//使用单位
                ModuleList.value = other.listModule || [];//模块
                InputDataTypeList.value = other.listInputDataType || [];//写入第三方库类型
                UsageList.value = other.listUsage || [];//使用方式
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });

}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const groupDialogVisible = ref(false)
const groupTableData = ref([]);
const dicSelectList = ref([])//下拉框数据源
const processId = ref([])
// 分组设置
const HandleGrouping = (row) => {
    processId.value = row.Id
    GetGroupSetList({ ProcessId: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            groupTableData.value = Array.from({ length: 5 }, (_, i) =>
                i < rows.data.length ? rows.data[i] : { GroupName: '', DropValue: '' }
            );
            console.log(' groupTableData.value', groupTableData.value)
            groupDialogVisible.value = true
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}

// 分组设置提交
const HandleGroupSubmit = (row) => {
    let list = groupTableData.value.filter(t => t.GroupName && t.DropValue)
    if (list.length > 1) {
        let findList = list.filter(t => t.GroupName == list[0].GroupName)
        if (findList.length > 1) {
            ElMessage.error('分组名称不能重复')
            return
        }
    }
    let foemList = list.map(t => {
        return {
            GroupName: t.GroupName,
            Value: t.DropValue,
        }
    })
    let formData = {
        ProcessId: processId.value,
        ListProcessSet: foemList
    }
    console.log(formData)
    GroupSetInsertUpdate(formData).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            groupDialogVisible.value = false
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 获取字典数据源
const GetDataSourceUser = () => {
    GetDataSource().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            let dicList = rows || []
            dicSelectList.value = dicList.filter((t) => {
                let val = t.value.split('_')[1]
                return val == '2' || val == '3'
            })
            // t.pid == 2 || t.pid == 3)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 添加/修改提交
const ProcessinsertUpdateUser = () => {
    ProcessinsertUpdate(dialogData.value).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false

            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const ProcessfindbyidUser = (id) => {
    Processfindbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, footer } = res.data.data
            dialogData.value = {
                Id: rows.Id,
                ModuleId: rows.ModuleId,
                ProcessName: rows.ProcessName,
                UseUnitId: rows.UseUnitId,
                Usage: String(rows.Usage),
                InputDataType: String(rows.InputDataType),
                IsOpen: rows.IsOpen,
                IsShowHistoryFile: String(rows.IsShowHistoryFile),
                IsSignature: String(rows.IsSignature),
                IsControlAmount: String(rows.IsControlAmount),
                IsOpenControl: String(rows.IsOpenControl || '2'),
                FieldCode: rows.FieldCode,
                TypeCode: rows.TypeCode,
                SourceFundName: rows.SourceFundName,
                PSort: rows.PSort || 0,
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
                        <el-button type="danger" :icon="Delete" @click="HandleDel"
                            :disabled="selectRows.length != 1">删除</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="流程名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="ProcessName" label="流程名称" min-width="160"></el-table-column>
            <el-table-column prop="UnitName" label="使用单位" min-width="160"></el-table-column>
            <el-table-column prop="CreateTime" label="创建时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.CreateTime.substring(0, 10) }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.Statuz == 3 ? 'primary' : row.Statuz == 1 ? 'success' : 'danger'"
                        disable-transitions>
                        {{ row.Statuz == 3 ? "设置中" : row.Statuz == 1 ? "启用" : "禁用" }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="480" align="center">
                <template #default="{ row }">

                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleSetting(row)">设置</el-button>
                    <el-button type="primary" link @click="HandleGrouping(row)">分组设置</el-button>
                    <el-button type="primary" link @click="HandleStateDescription(row)">状态描述</el-button>
                    <el-button type="primary" link @click="HandleBudgetList(row)">项目清单配置</el-button>
                    <el-button type="primary" link @click="HandleMessage(row)">消息配置</el-button>
                    <el-button type="primary" link v-if="row.Statuz == 2" @click="HandleEnable(row)">启用</el-button>
                    <el-button type="primary" link v-if="row.Statuz == 1" @click="HandleEnable(row)">禁用</el-button>
                    <!-- <el-button type="primary" link @click="HandleDel(row)">删除</el-button> -->

                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <!-- 创建修改弹窗 -->
        <app-box v-model="dialogVisible" :width="780" :lazy="true" :title="editId ? '修改流程' : '添加流程'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="240px"
                    status-icon style="padding-right: 100px;">
                    <el-form-item label="模块名称：" prop="ModuleId" style="margin-bottom: 18px;">
                        <el-select v-model="dialogData.ModuleId">
                            <el-option v-for="item in ModuleList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="流程名称：" prop="ProcessName">
                        <el-input v-model="dialogData.ProcessName"></el-input>
                    </el-form-item>
                    <el-form-item label="使用单位：" prop="UseUnitId">
                        <el-select v-model="dialogData.UseUnitId">
                            <el-option v-for="item in UnitList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="使用方式：">
                        <el-radio-group v-model="dialogData.Usage">
                            <el-radio v-for="item in UsageList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否显示历史附件：">
                        <el-radio-group v-model="dialogData.IsShowHistoryFile">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否开启资金库：">
                        <el-radio-group v-model="dialogData.IsOpen">
                            <el-radio v-for="item in isOpenList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <!-- <el-form-item label="写入第三方库类型：">
                        <el-radio-group v-model="dialogData.InputDataType">
                            <el-radio v-for="item in InputDataTypeList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="资金分配指定的字段编码：" v-if="dialogData.InputDataType == 3">
                        <el-input v-model="dialogData.FieldCode" ></el-input>
                    </el-form-item>
                    <el-form-item label="资金分配指定的数据分类编码：" v-if="dialogData.InputDataType == 3">
                        <el-input v-model="dialogData.TypeCode" ></el-input>
                    </el-form-item> -->
                    <el-form-item label="是否签名：" prop="IsSignature">
                        <el-radio-group v-model="dialogData.IsSignature">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否控制金额：">
                        <template #label>
                            <el-tooltip class="item" effect="dark" content="控制填报金额不能超过余额" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> 是否控制金额： </span>
                        </template>
                        <el-radio-group v-model="dialogData.IsControlAmount">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否开启单位权限控制：">
                        <el-radio-group v-model="dialogData.IsOpenControl">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="资金库菜单名称：">
                        <el-input v-model="dialogData.SourceFundName"></el-input>
                    </el-form-item>
                    <el-form-item label="排序值：">
                        <el-input type="number" v-model="dialogData.PSort"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
        <!-- 分组设置 -->
        <app-box v-model="groupDialogVisible" :width="780" :lazy="true" title="分组设置">
            <template #content>
                <el-table ref="refTable" :data="groupTableData" border header-cell-class-name="headerClassName">
                    <el-table-column prop="GroupName" label="分组名称" min-width="160">
                        <template #default="{ row }">
                            <el-input v-model.trim="row.GroupName" clearable placeholder="请输入"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="TypeCode" label="字典类型" min-width="160">
                        <template #default="{ row }">
                            <el-select v-model="row.DropValue" clearable placeholder="请选择">
                                <el-option v-for="item in dicSelectList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="groupDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleGroupSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>