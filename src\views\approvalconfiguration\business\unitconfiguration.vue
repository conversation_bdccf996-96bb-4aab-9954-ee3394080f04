<script setup>
defineOptions({
  name: 'businessunitconfiguration'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import { Search, Setting, Refresh } from '@element-plus/icons-vue'
import {
  GetUnitGroupSetList, GetGroupItemList, UnitGroupBatchSet, GroupItemCheck
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页  
import AppBox from "@/components/Approve/AppBox.vue";
// 初始化 
const route = useRoute()
const navTabs = ref([])
const groupTabs = ref([])
const tableData = ref([])
const refTable = ref()
const tableTotal = ref(0)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
  GroupItemId: [
    { required: true, message: '请选择字典类型', trigger: 'change' },
  ],
}
const groupItemList = ref([])
const filters = ref({ pageIndex: 1, pageSize: 10 })
const processId = ref('')//流程ID
const groupId = ref('')//分组ID
const typeBox = ref('')//分组ID
const activeName = ref('')
const activeName1 = ref('')
const groupItemName = ref('')//分组项名称
const dialogVisible = ref(false)
const msg = ref('')
//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    GetUnitGroupSetListUser(true)
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      GetUnitGroupSetListUser(true)
    }
  })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
  selectRows.value = selection
}

// 获取单位分组设置列表
const GetUnitGroupSetListUser = (isFirst, process, group) => {
  filters.value.isFirst = isFirst
  filters.value.ProcessId = process
  filters.value.GroupId = group
  GetUnitGroupSetList(filters.value).then((res) => {
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      if (isFirst) {
        navTabs.value = other.listBasicGroupSet || []
        if (navTabs.value.length > 0) {
          activeName.value = navTabs.value[0].ProcessId
          activeName1.value = navTabs.value[0].ListProcessGroup[0].GroupId
          groupTabs.value = navTabs.value[0].ListProcessGroup
          processId.value = navTabs.value[0].ProcessId
          groupId.value = navTabs.value[0].ListProcessGroup[0].GroupId
          groupItemName.value = navTabs.value[0].ListProcessGroup[0].GroupName
          typeBox.value = navTabs.value[0].ListProcessGroup[0].TypeBox
          msg.value = ''
        } else {
          msg.value = '经过系统分析， 无需您配置此功能'
        }
      }
      tableData.value = rows.data || []
      tableTotal.value = rows.dataCount
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  GetUnitGroupSetListUser(undefined, processId.value, groupId.value)
}
// 重置
const HandleReset = () => {
  filters.value.Name = undefined
  filters.value.pageIndex = 1
  GetUnitGroupSetListUser(undefined, processId.value, groupId.value)
}
// 分页
const handlePage = (val) => {
  GetUnitGroupSetListUser(undefined, processId.value, groupId.value)
}
// 检测
const HandleDetection = () => {
  GroupItemCheck().then((res) => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '已完成')
    } else if (res.data.flag == 2) {
      ElMessage({
        showClose: true,
        message: res.data.msg || '',
        type: 'warning',
        duration: 5000
      })
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 批量设置
const HandleBatchSet = () => {
  GetGroupItemListUaer()
  dialogData.value.GroupItemId = undefined
  dialogVisible.value = true
}
// 提交设置操作人
const HandleSubmit = (row) => {
  let formData = {
    ProcessId: processId.value,
    GroupId: groupId.value,
    ListSchoolId: selectRows.value.map((t) => t.SchoolId),
    GroupItemId: dialogData.value.GroupItemId,
    GroupItemName: groupItemList.value.filter((t) => t.value == dialogData.value.GroupItemId)[0]?.label,
    TypeBox: groupItemList.value.filter((t) => t.value == dialogData.value.GroupItemId)[0]?.pid
  }
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    UnitGroupBatchSet(formData).then((res) => {
      if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '设置成功')
        dialogVisible.value = false
        GetUnitGroupSetListUser(undefined, processId.value, groupId.value)
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })

}
// 获取字典类型数据
const GetGroupItemListUaer = () => {
  GetGroupItemList({ ProcessId: processId.value, groupId: groupId.value, TypeBox: typeBox.value }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      groupItemList.value = rows
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

// 切换tab
const handleClick = (tab) => {
  // console.log(tab, tab.props.name, tab.index)
  filters.value.pageIndex = 1
  groupTabs.value = navTabs.value[Number(tab.index)].ListProcessGroup;//分组
  processId.value = navTabs.value[Number(tab.index)].ProcessId
  groupId.value = navTabs.value[Number(tab.index)].ListProcessGroup[0].GroupId
  groupItemName.value = navTabs.value[Number(tab.index)].ListProcessGroup[0].GroupName
  typeBox.value = navTabs.value[Number(tab.index)].ListProcessGroup[0].TypeBox
  activeName1.value = groupId.value
  GetUnitGroupSetListUser(undefined, processId.value, groupId.value)
}
// 切换tab
const handleItemClick = (tab) => {
  // console.log(tab.props.name, tab.index)
  filters.value.pageIndex = 1
  groupId.value = groupTabs.value[Number(tab.index)].GroupId
  groupItemName.value = groupTabs.value[Number(tab.index)].GroupName
  typeBox.value = groupTabs.value[Number(tab.index)].TypeBox
  GetUnitGroupSetListUser(undefined, processId.value, groupId.value)
}

</script>
<template>
  <div v-if="navTabs.length > 0">
    <el-row>
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filtersKey" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="Setting" :disabled="selectRows.length == 0"
              @click="HandleBatchSet">批量设置</el-button>
            <el-button type="success" :icon="Search" @click="HandleDetection">检测</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item class="flexItem">
            <el-input v-model.trim="filters.Name" placeholder="单位名称" style="width: 240px"> </el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-tabs v-model="activeName" type="border-card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane v-for="item in navTabs" :key="item.ProcessId" :label="item.ProcessName" :name="item.ProcessId">
        <el-tabs v-model="activeName1" class="demo-tabs" @tab-click="handleItemClick">
          <el-tab-pane v-for="item1 in item.ListProcessGroup" :key="item1.GroupId" :label="item1.GroupName"
            :name="item1.GroupId">
            <el-table ref="refTable" :data="tableData" max-height="530" highlight-current-row border stripe
              header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
              <el-table-column type="selection" width="50"></el-table-column>
              <el-table-column prop="SchoolName" label="单位名称" width="400" show-overflow-tooltip></el-table-column>
              <el-table-column prop="GroupItemName" :label="groupItemName" width="400"
                show-overflow-tooltip></el-table-column>
              <template #empty>
                <el-empty description="没有数据"></el-empty>
              </template>
            </el-table>
            <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
              @handleChange="handlePage" />
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
    </el-tabs>
    <app-box v-model="dialogVisible" :width="560" :lazy="true" :title="'[' + groupItemName + ']' + ' 字典类型设置'">
      <template #content>
        <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="120px" status-icon>
          <el-form-item label="字典类型设置：" prop="GroupItemId">
            <el-select v-model="dialogData.GroupItemId" placeholder="请选择" style="width: 80%;">
              <el-option v-for="item in groupItemList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
        </span>
      </template>
    </app-box>
  </div>
  <div v-else class="emptyMsg">
    <span> {{ msg }} </span>
  </div>
</template>
<style lang="scss" scoped></style>
