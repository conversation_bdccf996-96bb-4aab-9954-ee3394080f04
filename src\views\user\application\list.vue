<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem flexOperation">
                        <el-button type="success" @click="handleAdd()">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.ClientId" clearable placeholder="请输入应用ID"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Enable" clearable placeholder="状态" style="width: 160px">
                            <el-option v-for="item in PublishStatusList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Display" clearable placeholder="是否显示" style="width: 160px">
                            <el-option v-for="item in DisplayStatusList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch(1)">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset(1)">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table style="width: 100%" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="IconUri" label="图标" width="100" align="center">
                <template #default="{ row }">
                    <el-avatar fit="fill" :src="row.IconUri" />
                </template>
            </el-table-column>
            <el-table-column prop="ClientId" label="应用ID" width="auto" align="center">
            </el-table-column>
            <el-table-column prop="Sort" label="排序" width="60" align="center">
            </el-table-column>
            <el-table-column prop="DisplayName" label="应用名称" width="auto" align="center"></el-table-column>
            <el-table-column prop="Enable" label="状态" width="120" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.Enable ? 'success' : 'warning'" effect="dark">
                        {{ row.Enable ? '已上线' : '维护中' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="Display" label="是否显示" width="120" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.Display ? 'success' : 'warning'" effect="dark">
                        {{ row.Display ? '显示' : '不显示' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="300">
                <template #default="scope">
                    <el-button size="small" :type="scope.row.Enable ? 'danger' : 'success'"
                        @click="handleEnableSwitch(scope.row.Id)">
                        {{ scope.row.Enable ? '下线' : '上线' }}
                    </el-button>
                    <el-button size="small" :type="scope.row.Display ? 'warning' : 'success'"
                        @click="handleDisplaySwitch(scope.row.Id)">
                        {{ scope.row.Display ? '不显示' : '显示' }}
                    </el-button>
                    <el-button size="small" type="danger" @click="handleEdit(scope.row.Id)">
                        修改
                    </el-button>
                    <el-button size="small" type="danger" @click="handleResetSecret(scope.row.Id)">
                        重置密钥
                    </el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />

        <!-- 重置密钥对话框 -->
        <reset-secret-dialog v-model="showResetDialog" :client-id="currentClientId" />
    </div>
</template>

<script setup>
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import {
    Refresh, Search, Sort
} from '@element-plus/icons-vue'
import TablePage from '@/components/TablePagination/index.vue'
import { searchApplication, enableSwitch, displaySwitch } from '@/api/application'
import ResetSecretDialog from '@/components/ResetSecretDialog/index.vue'

const router = useRouter()
const tableData = ref([])
const tableTotal = ref(0)
const filters = ref({ pageIndex: 1, pageSize: 10 })
const PublishStatusList = [{
    label: '全部状态',
    value: ''
}, {
    label: '已上线',
    value: true
}, {
    label: '维护中',
    value: false
}]
const DisplayStatusList = [{
    label: '全部',
    value: ''
}, {
    label: '显示',
    value: true
}, {
    label: '不显示',
    value: false
}]

const HandleSearch = (pageIndex) => {
    filters.value.pageIndex = pageIndex
    HandleTableData()
}

const handlePage = () => {
    HandleTableData();
}

const HandleReset = () => {
    filters.value = {
        pageIndex: 1
    }
    HandleTableData()
}

const handleAdd = () => {
    router.push({ path: '/user/application/add' })
}

const handleEnableSwitch = async (id) => {
    await enableSwitch(id)
    HandleTableData()
}

const handleDisplaySwitch = async (id) => {
    await displaySwitch(id)
    HandleTableData()
}

const HandleTableData = () => {
    searchApplication(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows || [];
            tableTotal.value = Number(total)

        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

const showResetDialog = ref(false)
const currentClientId = ref('')

// 处理重置密钥
const handleResetSecret = (id) => {
    currentClientId.value = id
    showResetDialog.value = true
}

// 跳转到编辑页面
const handleEdit = (id) => {
    router.push({
        path: '/user/application/add',
        query: { id }
    })
}

onMounted(() => {
    HandleSearch(1)
})
</script>