<script setup>
defineOptions({
    name: 'dangerchemicalsdailygovernitemreport'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { DcGovernItemReportGetById, DcGovernItemReportSave } from '@/api/daily.js'
import { GetDictionaryCombox } from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
//弹出窗体
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, pageQuery, tagsListStore } from "@/utils/index.js";//上传附件
import { AttachmentUpload } from '@/api/user.js'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const routerObject = ref({})//成页面携带的参数对象 
const SchoolId = ref(0);
//加载数据
onMounted(() => {
    SchoolId.value = route.query.SchoolId || 0
    if (route.query.isTagRouter) {
        HandleTableData();
        DccatalogTypeGet();
    }
})
onActivated(() => {
    routerObject.value = pageQuery(route.path)
    SchoolId.value = route.query.SchoolId || 0
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData();
            DccatalogTypeGet();
        }
    })
})

//表格
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({})

//搜索条件
const catalogList = ref([])
const filtersChange = (row) => {
    HandleTableData()
}

const gradeOptions = ref([{ Id: 1, Name: '一般', }, { Id: 2, Name: '较大', }, { Id: 3, Name: '重大', }])
const natureOptions = ref([{ Id: 1, Name: '问题', }, { Id: 2, Name: '隐患', }])
const isTallyClaimOptions = ref([{ Id: 1, Name: '是', }, { Id: 0, Name: '否', }])
// 危化品类别
const DccatalogTypeGet = () => {
    GetDictionaryCombox({ TypeCode: "10000" }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            catalogList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 列表
const HandleTableData = () => {
    DcGovernItemReportGetById(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = total
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.categoryid = undefined;
    filters.value.nature = undefined;
    filters.value.grade = undefined;
    filters.value.name = undefined;
    filters.value.istallyclaim = undefined;
    HandleTableData()
}

const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)

//验证
const ruleForm = {
    RectifyLimit: [
        { required: true, message: '请选择整改期限', trigger: 'change' },
    ]
}

// 修改
const HandleEdit = (row) => {
    dialogVisible.value = true
    dialogData.value = JSON.parse(JSON.stringify(row));
    //处理附件。
    uploadFileData.value[0].categoryList = [];//先置空
    uploadFileData.value[0].fileLChildList = [];//先置空
    if (row.AttachmentList && row.AttachmentList.length > 0) {
        uploadFileData.value[0].categoryList = row.AttachmentList;
    }
}
// 提交
const HandleSubmit = () => {
    if (dialogData.value.IsTallyClaim == 1) {
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
        })
    }
    //验证，存在问题，必须填写整改期限，备注限制。
    // if(dialogData.value && dialogData.value.IsTallyClaim==1){
    //     //验证整改期限

    //     if(!(dialogData.value.RectifyLimit &&  new Date(dialogData.value.RectifyLimit) >= new Date().setHours(0, 0, 0, 0))){
    //         ElMessage.error("存在问题，请选择整改期限，并且整改期限要大于等于当前日期。");
    //         return;
    //     }
    // }
    const attIdArr = [];
    uploadFileData.value.map(function (item, index) {
        if (item.categoryList.length > 0) {
            item.categoryList.map(function (olditem, oldindex) {
                attIdArr.push(olditem.Id);
            });
        }
        if (item.fileLChildList.length > 0) {
            item.fileLChildList.map(function (newitem, newindex) {
                attIdArr.push(newitem.Id);
            });
        }
    });

    const dataModelArr = [{ Id: dialogData.value.Id, GovernItemId: dialogData.value.GovernItemId, IsTallyClaim: dialogData.value.IsTallyClaim, RectifyLimit: dialogData.value.RectifyLimit, AttachmentIdList: attIdArr, Memo: dialogData.value.Memo }];
    DcGovernItemReportSave({ schoolid: 0, taskid: 0, reporttype: 0, list: dataModelArr, GovernTaskUnitId: 0 }).then(res => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '设置成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}


//申报
const HandleDeclare = () => {
    //直接申报
    const reporttype = ref(0);
    if (routerObject.value.t == 1) {
        reporttype.value = 1;
    } else if (routerObject.value.t == 2) {
        reporttype.value = 2;
    }
    DcGovernItemReportSave({ schoolid: 0, taskid: 0, reporttype: reporttype.value, GovernTaskUnitId: 0 }).then(res => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '申报成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//附件上传
const uploadFileData = ref([{ FileCategory: 2972, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 10, Memo: "文件小于10M，支持pdf和图片、文档文件", Name: "附件：", UploadFileType: ".pdf.jpg.jpeg.png.doc.docx.xls.xlsx", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx" }])
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // console.log("uploadFileData.value", uploadFileData.value)
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item v-if="SchoolId == 0 && tableTotal > 0" class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleDeclare">申报</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="SchoolId == 0"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.categoryid" clearable placeholder="类别" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in catalogList" :key="item.DicValue" :label="item.DicName"
                                :value="item.DicValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.nature" clearable placeholder="性质" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in natureOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.grade" clearable placeholder="危险等级" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in gradeOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.istallyclaim" clearable placeholder="是否存在问题隐患" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in isTallyClaimOptions" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.name" placeholder="问题隐患清单" style="width: 180px"> </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="CategoryName" label="类别" min-width="60" align="center"></el-table-column>
            <el-table-column prop="Name" label="问题隐患清单" min-width="300"></el-table-column>
            <el-table-column prop="Nature" label="性质" min-width="80" align="center">
                <template #default="{ row }">
                    {{ row.Nature == 1 ? '问题' : row.Nature == 2 ? '隐患' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Grade" label="危险等级" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Grade == 1 ? '一般' : row.Grade == 2 ? '较大' : row.Grade == 3 ? '重大' : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="IsRectify" label="存在问题隐患" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsTallyClaim != 0" style="color:red;">是</span>
                    <span v-else>否</span>
                </template>
            </el-table-column>
            <el-table-column prop="RectifyLimit" label="整改期限" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.RectifyLimit ? row.RectifyLimit.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column label="附件" min-width="200" align="center">
                <template #default="{ row }">
                    <div v-if="row.AttachmentList && row.AttachmentList.length > 0">
                        <div class="fileFlex">
                            <div v-for="(itemCate) in row.AttachmentList" :key="itemCate.Id" style="color:#409EFF ;">
                                <span style="cursor: pointer;" @click="fileListDownload(itemCate, row.AttachmentList)">
                                    {{ itemCate.Title }}{{ itemCate.Ext }}
                                </span>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="Memo" label="备注" min-width="200" show-overflow-tooltip></el-table-column>
            <el-table-column prop="opt" fixed="right" label="操作" width="100" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsRectify == 1">
                        <el-button v-if="row.IsTallyClaim == 0" type="primary" link
                            @click="HandleEdit(row)">填写</el-button>
                        <el-button v-else type="primary" link @click="HandleEdit(row, 3)">修改</el-button>
                    </span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="信息维护">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="200px"
                    status-icon>
                    <el-form-item label="类别：">
                        <!-- <el-input :disabled="SchoolId" v-model="dialogData.CategoryName" style="width: 80%"></el-input> -->
                        <span>{{ dialogData.CategoryName }}</span>
                    </el-form-item>
                    <el-form-item label="问题隐患清单：">
                        <!-- <el-input :disabled="SchoolId" v-model="dialogData.Name" style="width: 80%"></el-input> -->
                        <span>{{ dialogData.Name }}</span>
                    </el-form-item>
                    <el-form-item label="性质：">
                        <!-- <el-input :disabled="SchoolId" v-model="dialogData.Name" style="width: 80%"></el-input> -->
                        {{ dialogData.Nature == 1 ? '问题' : dialogData.Nature == 2 ? '隐患' : '--' }}
                    </el-form-item>
                    <el-form-item label="危险等级：">
                        <!-- <el-input :disabled="SchoolId" v-model="dialogData.Name" style="width: 80%"></el-input> -->
                        {{ dialogData.Grade == 1 ? '一般' : dialogData.Grade == 2 ? '较大' : dialogData.Grade == 3 ? '重大' :
                            '--' }}
                    </el-form-item>
                    <el-form-item label="是否存在危化品管理：" prop="IsTallyClaim">
                        <el-radio-group v-model="dialogData.IsTallyClaim">
                            <el-radio :value="1" label="是"> </el-radio>
                            <el-radio :value="0" label="否"> </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="整改期限：" prop="RectifyLimit" v-if="dialogData.IsTallyClaim == 1">
                        <el-date-picker type="date" placeholder="选择日期" v-model="dialogData.RectifyLimit"
                            format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 80%;"></el-date-picker>
                    </el-form-item>
                    <div v-if="dialogData.IsTallyClaim == 1">
                        <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                            <template #label>
                                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                                <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                    <div>
                                        <el-icon color="#E6A23C" class="tipIcon">
                                            <QuestionFilled />
                                        </el-icon>
                                    </div>
                                </el-tooltip>
                                <span> {{ item.Name }}： </span>
                            </template>
                            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                                :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                                :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                                <el-button type="success" size="small" :icon="UploadFilled"
                                    @click="MaxFileNumberClick(item)">上传</el-button>
                            </el-upload>
                            <div class="fileFlex">
                                <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id"
                                    style="color:#409EFF ;width:200px">
                                    <el-icon color="#F56C6C" style="cursor: pointer;"
                                        @click="delCateFile(itemCate, index)">
                                        <Delete />
                                    </el-icon>
                                    <span style="cursor: pointer;"
                                        @click="lookFileListDownload(itemCate.Path, itemCate.Ext, itemCate.Title)">
                                        {{ itemCate.Title }}
                                    </span>
                                </div>
                                <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id" style="width:200px">
                                    <el-icon color="#F56C6C" style="cursor: pointer;"
                                        @click="delFile(itemChild, index)">
                                        <Delete />
                                    </el-icon>
                                    {{ itemChild.Title }}
                                </div>
                            </div>
                        </el-form-item>
                    </div>
                    <el-form-item label="备注：" prop="Memo" v-if="dialogData.IsTallyClaim == 1">
                        <el-input type="textarea" v-model="dialogData.Memo" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>