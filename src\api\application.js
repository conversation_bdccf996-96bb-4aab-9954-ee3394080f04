import request from '@/utils/request.js'

export const searchApplication = (params) => {
  return request.post('/api/application/getpaged', params)
}

export const getApplicationById = (id) => {
  return request.get(`/api/application/${id}`)
}

export const addOrEditApplication = (params) => {
  return request.post('/api/application/addOrEdit', params)
}
export const enableSwitch = (id) => {
  return request.put(`/api/application/enable/switch/${id}`)
}

export const displaySwitch = (id) => {
  return request.put(`/api/application/display/switch/${id}`)
}

export const resetClientSecret = (clientId) => {
    return request.put(`/api/application/resetSecret/${clientId}`)
}

