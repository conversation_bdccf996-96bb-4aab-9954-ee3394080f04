<script setup>
// 登录接口
import {
  LoginRefreshcaptchaimage, LoginXfsendcode, LoginSchoolandcompanyreg, GetNavigationBar
} from '@/api/user.js'
import {
  ArticleGetoperator
} from '@/api/home.js'
import { ElMessage } from 'element-plus'
// 响应式
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
// 记住我
import { useUserStore } from '@/stores';
//路由
import router from '@/router'
import { addDynamicRoutes } from '@/router'
import md5 from 'js-md5';
import { integerLimit } from "@/utils/index.js";

const imageUrl = ref('')
const Uuid = ref('')
const UidMobile = ref('')
const UidCode = ref('')
// 登录信息
const formData = ref({
  name: '',
  pass: ''
})
const userStore = useUserStore()
// mounted生命周期
onMounted(() => {
  if (userStore.token) {
    router.push('/')
  }
  noticeChecked.value = userStore.noticeChecked
  LoginRefreshcaptchaimageUser()
  ArticleGetoperatorUser({ code: 1010 })
  window.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress)
})
// 回车键注册
const handleKeyPress = (event) => {
  if (event.key === 'Enter') {
    reg()
  }
}
//输入整数
const integerLimitInput = (val, name) => {
  formData.value[name] = integerLimit(val);
}
const validatePassSec = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请再次输入密码"));
  } else if (value !== formData.value.oldPassWord) {
    callback(new Error("两次输入密码不一致!"));
  } else {
    callback();
  }
};
// 校验信息
const formRules = {
  UnitType: [{ required: true, message: '请选择单位类型', trigger: 'blur' }],
  Mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' },
  {
    pattern: /^1[0-9]{10}$/,
    message: '请输入正确11位手机号码',
    trigger: 'blur'
  }],
  imgCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  Code: [{ required: true, message: '请输入校验码', trigger: 'blur' },
  {
    pattern: /^[0-9]{4,6}$/,
    message: '请输入正确的验证码',
    trigger: 'blur'
  }],
  oldPassWord: [{ required: true, message: '请输入密码', trigger: 'blur' },
  {
    pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
    message: '密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种',
    trigger: 'blur'
  }],
  newPassWord: [{ required: true, validator: validatePassSec, trigger: "blur" }],
  Name: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
}
const refForm = ref()
// 去登录
const login = () => {
  router.replace('/login')
}
// 切换图片验证码
const imgCodeChange = () => {
  LoginRefreshcaptchaimageUser()
}
// 获取图片验证码 
const LoginRefreshcaptchaimageUser = () => {
  LoginRefreshcaptchaimage().then((res) => {
    // console.log('获取验证码', res)
    const { footer } = res.data.data
    imageUrl.value = `data:image/jpg;base64,${footer.Img}`;
    Uuid.value = footer.Uuid
  })
}
// 注册
const reg = () => {
  if (noticeList.value.length > 0 && !noticeChecked.value) {
    ElMessage.error('请先阅读并同意用户条款')
    return
  }
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    let regParams = {
      UnitType: formData.value.UnitType, //单位类型
      Mobile: formData.value.Mobile, //手机号
      PassWord: md5(formData.value.oldPassWord), //密码
      Code: formData.value.Code, //短信验证码
      UidMobile: UidMobile.value, //手机号唯一标识
      UidCode: UidCode.value, //短信验证码唯一标识
      Name: formData.value.Name, //单位名称
    }

    LoginSchoolandcompanyreg(regParams).then((res) => {
      if (res.data.flag == 1) {
        const { rows, footer } = res.data.data
        userStore.setToken(rows.token)
        userStore.setTokenType(rows.token_type)
        userStore.setExpiresin(rows.expires_in * 1000)
        userStore.setUserInfo(footer)
        userStore.setNoticeChecked(true)
        if (footer.UnitStatus == 1) {
          // 已认证，调用菜单
          GetNavigationBarUser()
        } else if (footer.UnitStatus == 2) {
          // 需要认证：跳到新页面申请认证
          router.replace('/unitauthentic')
        }
      } else {
        ElMessage.error(res.data.msg)
      }
    })
      .catch((err) => {
        // 在这里处理登录失败的额外操作
      })
  })
}

const codeText = ref('获取校验码')
const isCode = ref(false)
const time = ref(Date.now() + 1000 * 60 * 2)
// 获取短信校验码
const getCode = () => {
  refForm.value.validateField(['imgCode', 'Mobile'], valid => {
    if (!valid) {
      return
    }
    // 获取验证码
    let paraData = {
      phoneNumber: formData.value.Mobile, //手机号
      codeNum: formData.value.imgCode, //图片验证码
      uid: Uuid.value, //图片验证码uid
    }

    LoginXfsendcode(paraData).then(res => {
      // console.log("res", res.data)
      if (res.data.flag == 1) {
        const { other } = res.data.data
        UidMobile.value = other.UidMobile; //手机号唯一标识
        UidCode.value = other.UidCode; //短信验证码唯一标识
        time.value = Date.now() + 1000 * 120
        isCode.value = true
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  });
}
// 校验码倒计时结束
const finishChange = () => {
  // console.log('倒计时结束')
  codeText.value = '重新获取'
  isCode.value = false
}
// 获取菜单
const GetNavigationBarUser = () => {
  GetNavigationBar().then((res) => {
    // console.log("菜单res", res.data.response)
    const rows = res.data.response
    userStore.setMenu(res.data.response.children)
    // console.log("userStore.menu", userStore.menu)
    // 添加vue router路由
    addDynamicRoutes(userStore.menu)
    nextTick(() => {
      router.replace('/user/backset/unit')
    });
  })
}

const noticeList = ref([])
const noticeChecked = ref(false)

// 获取用户条款
const ArticleGetoperatorUser = (parData) => {
  ArticleGetoperator(parData).then(res => {
    // console.log("资讯列表", res)
    if (res.data.flag == 1) {
      const { other } = res.data.data
      noticeList.value = other || []
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 查看用户须知
const detailClick = (item) => {
  const { href } = router.resolve({
    path: "/articledetail",
    query: { Id: item.Id }
  });
  window.open(href, "_blank");
}

</script>
<template>
  <div class="login-box">
    <!-- 背景装饰元素 -->
    <div class="bg-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主注册容器 -->
    <div class="main-container">
      <!-- 左侧插图区域 -->
      <div class="illustration-section">
        <div class="illustration-content">
          <!-- 3D插图元素 -->
          <div class="illustration-3d">
            <div class="platform-base">
              <div class="platform-circle"></div>
              <div class="security-shield">
                <div class="shield-icon">🏫</div>
              </div>
              <div class="data-screen">
                <div class="screen-content">
                  <div class="data-line"></div>
                  <div class="data-line"></div>
                  <div class="data-line"></div>
                </div>
              </div>
              <div class="floating-elements">
                <div class="element element-1">📝</div>
                <div class="element element-2">👔</div>
                <div class="element element-3">⭐</div>
              </div>
            </div>
          </div>
          <!-- 装饰点 -->
          <div class="decoration-dots">
            <div class="dot dot-1"></div>
            <div class="dot dot-2"></div>
            <div class="dot dot-3"></div>
          </div>
        </div>
      </div>

      <!-- 右侧注册表单区域 -->
      <div class="login-container">
        <!-- 系统标题和Logo -->
        <div class="system-header">
          <div class="logo-section">
            <div class="logo-icon">🏫</div>
            <div class="system-info">
              <h2 class="system-title">{{ userStore.defaultSet['8002_DLYM_BJ'] || '中小学校服管理与备案平台' }}</h2>
              <p class="system-subtitle">校服选用 · 校服采购 · 校服征订 · 校服评价</p>
              <!-- <p class="system-subtitle">{{ userStore.defaultSet['8002_DLYM_BJ_YCF'] || '' }}</p> -->
            </div>
          </div>
        </div>

        <!-- 注册标题区域 -->
        <div class="register-header">
          <h3 class="register-title">用户注册</h3>
          <div class="login-link">
            已有账号？<span class="link-text" @click="login">立即登录</span>
          </div>
        </div>
        <!-- 注册表单区域 -->
        <div class="form-section">
          <div class="register-form">
            <el-form @submit.prevent ref="refForm" :rules="formRules" :model="formData" label-position="left"
              label-width="90px">
              <!-- {UnitType:3代表学校，4代表企业，Name：单位名称，Mobile：手机号码，PassWord：密码（需要MD5加密），Code：短信验证码，
                UidMobile：手机号唯一标识,UidCode:短信验证码唯一标识}备注：其中UidMobile、UidCode调用获取短信验证码获得 -->

              <el-form-item label="单位类型" prop="UnitType" class="unit-type-item">
                <el-radio-group v-model="formData.UnitType" class="custom-radio-group">
                  <el-radio class="custom-radio" :value="3">学校</el-radio>
                  <el-radio class="custom-radio" :value="4">企业</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="手机号码" prop="Mobile">
                <el-input v-model="formData.Mobile" @input="integerLimitInput($event, 'Mobile')" auto-complete="off"
                  placeholder="请输入手机号码" class="custom-input"></el-input>
              </el-form-item>

              <el-form-item label="图片验证码" prop="imgCode">
                <div class="captcha-container">
                  <el-input v-model="formData.imgCode" auto-complete="off" placeholder="请输入验证码"
                    class="captcha-input"></el-input>
                  <div class="captcha-image" @click="imgCodeChange">
                    <img v-if="imageUrl" :src="imageUrl" alt="验证码" />
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="短信验证码" prop="Code">
                <div class="code-container">
                  <el-input v-model="formData.Code" @input="integerLimitInput($event, 'Code')" auto-complete="off"
                    placeholder="请输入短信验证码" class="code-input"></el-input>
                  <el-button type="primary" plain :disabled="isCode" @click="getCode" class="code-button">
                    <span v-if="!isCode">{{ codeText }}</span>
                    <el-countdown v-else format="ss" :value="time" @finish="finishChange" suffix="秒后重新获取"
                      :value-style="{ color: '#606266', fontSize: '14px' }" />
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="设置密码" prop="oldPassWord">
                <el-input v-model="formData.oldPassWord" auto-complete="off" show-password placeholder="请输入密码"
                  class="custom-input"></el-input>
              </el-form-item>

              <el-form-item label="确认密码" prop="newPassWord">
                <el-input v-model="formData.newPassWord" auto-complete="off" show-password placeholder="请再次输入密码"
                  class="custom-input"></el-input>
              </el-form-item>

              <el-form-item label="单位名称" prop="Name">
                <el-input v-model="formData.Name" auto-complete="off" placeholder="请输入单位名称"
                  class="custom-input"></el-input>
              </el-form-item>

              <el-form-item class="register-button-item" label-width="0px">
                <el-button type="primary" class="register-button" @click="reg">
                  <span>立即注册</span>
                </el-button>
              </el-form-item>

              <!-- 用户协议 -->
              <el-form-item v-if="noticeList.length > 0" class="agreement-item" label-width="0px">
                <div class="agreement-content">
                  <el-checkbox v-model="noticeChecked" class="agreement-checkbox">
                    <span class="agreement-text">阅读并接受: </span>
                  </el-checkbox>
                  <span class="agreement-links" v-for="(item, index) in noticeList" :key="item.Id">
                    <span class="agreement-link" @click="detailClick(item)">《{{ item.Title }}》</span>
                    <span v-if="index < noticeList.length - 1">、</span>
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
// 主容器样式
.login-box {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

// 背景装饰元素
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 2s;
  }

  &.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }

  &.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 10%;
    right: 10%;
    animation-delay: 1s;
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 主注册容器
.main-container {
  position: relative;
  display: flex;
  width: 90%;
  max-width: 900px;
  min-height: 550px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  z-index: 2;
}

// 左侧插图区域
.illustration-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  position: relative;
  overflow: hidden;
}

.illustration-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 3D插图元素
.illustration-3d {
  position: relative;
  width: 300px;
  height: 300px;
}

.platform-base {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: rotate3d 20s linear infinite;
}

.platform-circle {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.security-shield {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px rgba(255, 154, 158, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

.shield-icon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.data-screen {
  position: absolute;
  top: 20%;
  right: 10%;
  width: 100px;
  height: 70px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: rotateY(-15deg);
}

.screen-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.data-line {
  height: 4px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 2px;

  &:nth-child(1) {
    width: 80%;
  }

  &:nth-child(2) {
    width: 60%;
  }

  &:nth-child(3) {
    width: 90%;
  }
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.element {
  position: absolute;
  font-size: 24px;
  animation: floatElement 4s ease-in-out infinite;

  &.element-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.element-2 {
    top: 30%;
    right: 20%;
    animation-delay: 1.5s;
  }

  &.element-3 {
    bottom: 20%;
    left: 20%;
    animation-delay: 3s;
  }
}

.decoration-dots {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  animation: dotPulse 2s ease-in-out infinite;

  &.dot-2 {
    animation-delay: 0.5s;
  }

  &.dot-3 {
    animation-delay: 1s;
  }
}

@keyframes rotate3d {
  0% {
    transform: rotateY(0deg);
  }

  100% {
    transform: rotateY(360deg);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes floatElement {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes dotPulse {

  0%,
  100% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }
}

// 右侧注册容器
.login-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 30px 35px;
  background: #fff;
  position: relative;
  overflow-y: auto;
  width: 420px !important;
}

// 系统标题区域
.system-header {
  margin-bottom: 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.system-info {
  flex: 1;
}

.system-title {
  margin: 0 0 3px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.system-subtitle {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
  line-height: 1.4;
}

// 注册标题区域
.register-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.register-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
}

.login-link {
  font-size: 13px;
  color: #7f8c8d;
}

.link-text {
  color: #4e6ef2;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #667eea;
  }
}

// 表单区域
.form-section {
  flex: 1;
}

.register-form {
  width: 100%;
}

// 单位类型选择
.unit-type-item {
  margin-bottom: 15px;
}

.custom-radio-group {
  display: flex;
  gap: 15px;
}

:deep(.custom-radio) {
  .el-radio__input.is-checked .el-radio__inner {
    background-color: #4e6ef2;
    border-color: #4e6ef2;
  }

  .el-radio__inner:hover {
    border-color: #4e6ef2;
  }

  .el-radio__label {
    color: #2c3e50;
    font-weight: 500;
    font-size: 14px;
  }
}

// 自定义输入框样式
:deep(.custom-input) {
  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e1e8ed;
    transition: all 0.3s ease;

    &:hover {
      border-color: #4e6ef2;
      box-shadow: 0 4px 12px rgba(78, 110, 242, 0.15);
    }

    &.is-focus {
      border-color: #4e6ef2;
      box-shadow: 0 4px 12px rgba(78, 110, 242, 0.2);
    }
  }

  .el-input__inner {
    height: 36px;
    font-size: 14px;
    color: #2c3e50;

    &::placeholder {
      color: #bdc3c7;
    }
  }
}

// 验证码容器
.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.captcha-input {
  width: 60%;
}

.captcha-image {
  width: 100px;
  height: 36px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #e1e8ed;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 短信验证码容器
.code-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.code-input {
  width: 60%;
}

.code-button {
  height: 36px;
  border-radius: 8px;
  font-size: 13px;
  min-width: 120px;
}

// 注册按钮
.register-button-item {
  margin: 20px 0 15px 0;
}

.register-button {
  width: 100% !important;
  height: 40px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  background: linear-gradient(135deg, #4e6ef2 0%, #667eea 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(78, 110, 242, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 110, 242, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  span {
    position: relative;
    z-index: 1;
  }
}

// 用户协议
.agreement-item {
  margin-bottom: 0;
}

.agreement-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  line-height: 1.5;
}

.agreement-checkbox {
  :deep(.el-checkbox__label) {
    color: #7f8c8d;
    font-size: 13px;
    line-height: 1.5;
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #4e6ef2;
    border-color: #4e6ef2;
  }

  :deep(.el-checkbox__inner:hover) {
    border-color: #4e6ef2;
  }
}

.agreement-text {
  color: #7f8c8d;
}

.agreement-links {
  color: #4e6ef2;
  line-height: 1.5;
}

.agreement-link {
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #667eea;
    text-decoration: underline;
  }
}

// 表单项间距调整
:deep(.el-form-item) {
  margin-bottom: 15px;

  .el-form-item__label {
    color: #2c3e50;
    font-weight: 500;
    font-size: 13px;
    line-height: 36px;
  }
}

// 倒计时样式
:deep(.el-statistic) {
  .el-statistic__content {
    font-size: 14px;
    color: #6c757d;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    width: 95%;
    min-width: 480px;
    min-height: auto;
  }

  .illustration-section {
    height: 200px;
    flex: none;
  }

  .illustration-3d {
    width: 200px;
    height: 200px;
  }

  .login-container {
    padding: 30px 25px;
    margin: 20px auto;
  }

  .system-title {
    font-size: 18px;
  }

  .register-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .custom-radio-group {
    gap: 15px;
  }
}

:deep(.el-statistic__suffix) {
  font-size: 14px;
}

:deep(.el-form-item__content) {
  align-items: center;
  justify-content: space-between;

  .el-button {
    width: 150px;
  }

  img {
    height: 100%;
  }
}

.notice:hover {
  text-decoration: underline;
}
</style>
