{"name": "vue3-big-event-ui", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.5", "echarts": "^6.0.0", "element-china-area-data": "^6.1.0", "element-plus": "^2.10.5", "font-awesome": "^4.7.0", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "oidc-client": "^1.11.5", "pinia": "^2.1.7", "qrcode": "^1.5.4", "qrcode.vue": "^3.4.1", "qs": "^6.13.0", "slate": "^0.118.1", "vue": "^3.4.14", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.1.4", "vue-draggable-next": "^2.2.1", "vue-qr": "^4.0.9", "vue-router": "^4.2.5", "vue3-print-nb": "^0.1.4", "wangeditor": "^4.7.15"}, "devDependencies": {"@rushstack/eslint-patch": "^1.7.0", "@vitejs/plugin-vue": "^4.6.2", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "fast-glob": "^3.3.2", "pinia-plugin-persistedstate": "^3.2.1", "prettier": "^3.2.4", "sass": "^1.70.0", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.1", "vite-plugin-commonjs": "^0.10.4", "vite-plugin-svg-icons": "^2.0.1"}}