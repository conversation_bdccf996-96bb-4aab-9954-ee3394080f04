<script setup>
defineOptions({
    name: 'selectionopiniondetail'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'

import {
    SchemeGetopinionpaged
} from '@/api/selection.js'
import { ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const PurchaseMethodList = ref([])
const OpinionStatuzList = ref([])
const ClassList = ref([])
const GradeList = ref([])
const MethodRate = ref([])
const SchemeNo = ref('')
const filters = ref({ pageIndex: 1, pageSize: 10 })
//加载数据
onMounted(() => {
    // 存在参数并且由切换tag标签时刷新浏览器
    if ((route.query.isTagRouter)) {
        filters.value.UniformSchemeId = route.query.id
        HandleTableData(true);
    }
})
onActivated(() => {
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    console.log("userStore.tagsList", userStore.tagsList)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.UniformSchemeId = route.query.id
            HandleTableData(true);
        }
    })
})
//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    SchemeGetopinionpaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            SchemeNo.value = other.SchemeNo
            if (isFirst) {
                PurchaseMethodList.value = other.PurchaseMethod || [];//采购方式
                OpinionStatuzList.value = other.OpinionStatuzList || [];//选用意见
                ClassList.value = other.ClassList || [];//班级
                GradeList.value = other.GradeList || [];//年级
                MethodRate.value = other.MethodRate || [];
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = (page) => {
    filters.value.pageIndex = 1
    filters.value.GradeId = undefined
    filters.value.PurchaseMethod = undefined
    filters.value.ClassName = undefined
    filters.value.Statuz = undefined
    filters.value.Name = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="选用意见" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in OpinionStatuzList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PurchaseMethod" clearable placeholder="采购方式" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in PurchaseMethodList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.GradeId" clearable placeholder="年级" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in GradeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.ClassName" clearable placeholder="班级" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in ClassList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="学生姓名"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>

            </el-col>
        </el-row>
        <div style="font-size: 14px;color: #606266;margin-bottom: 10px;margin-top: 20px;">
            批次编号：<span style="color: #999;">{{ SchemeNo }}</span>
            <span style="padding-left: 50px;">
                <span v-for="(item, index) in MethodRate" :key="index" style="padding-left: 15px;">
                    {{ item.Method }}：(<span style="color: #999;">{{ item.Rate }}%</span>)</span>
            </span>
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <!-- <el-table-column prop="ParentName" label="家长姓名" min-width="100" align="center"></el-table-column> -->

            <el-table-column prop="GradeName" label="年级" min-width="100" align="center"></el-table-column>
            <el-table-column prop="ClassName" label="班级" min-width="100" align="center"></el-table-column>
            <el-table-column prop="StudentName" label="学生姓名" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Mobile" label="手机号码" min-width="140" align="center"></el-table-column>
            <el-table-column prop="UserName" label="选用意见" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Statuz === 1 ? '同意' : '不同意' }}
                </template>
            </el-table-column>
            <el-table-column prop="PurchaseMethodName" label="采购方式" min-width="100" align="center"></el-table-column>
            <!-- <el-table-column prop="IDCard6" label="身份证号" min-width="100" align="center">
                <template #default="{ row }">
                    *{{ row.IDCard6 }}
                </template>
            </el-table-column> -->
            <el-table-column prop="Opinion" label="意见" min-width="160" show-overflow-tooltip> </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>