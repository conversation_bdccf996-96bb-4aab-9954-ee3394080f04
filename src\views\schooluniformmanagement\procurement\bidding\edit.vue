<script setup>
defineOptions({
    name: 'biddingedit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    QuestionFilled, Select, UploadFilled, Delete
} from '@element-plus/icons-vue'
import {
    AttachmentUpload, Getpagedbytype,
} from '@/api/user.js'
import {
    UniformBiddingGetById, UniformBiddingSave, BiddingDelattachmentbyid
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { yearDate, foundReturn, fileDownload, tagsListStore, ValidityPeriodList } from "@/utils/index.js";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const formData = ref({})
const refForm = ref()
const uploadFileData = ref([])
const yearDateList = ref([])
const BidPublicList = ref([])
const MethodList = ref([])
const BiddingStatuz = ref(0)//审核不通过或退回 状态值
const FilingExplanation = ref('')//审核不通过或退回原因
const Id = ref('')
const ruleForm = {
    ValidityPeriod: [
        { required: true, message: '请选择采购有效年限', trigger: 'change' },
    ],
    Method: [
        { required: true, message: '请选择采购方式', trigger: 'change' },
    ],
    BidDate: [
        { required: true, message: '请选择开标日期', trigger: 'change' },
    ],
    BidResultPublic: [
        { required: true, message: '请选择开标结果公开', trigger: 'change' },
    ],
    PublicDate: [
        { required: true, message: '请选择公开日期', trigger: 'change' },
    ],
    PublicMediaName: [
        { required: true, message: '请输入公开媒体名称', trigger: 'change' },
    ],
    Mobile: [
        {
            pattern: /^(1\d{10}|(\d{3,4}-)?\d{7,8})$/,
            message: '请输入正确的代理机构联系电话',
            trigger: 'change'
        }
    ],
}
onMounted(() => {
    yearDateList.value = yearDate()
    GetpagedbytypeUser()
    Id.value = route.query.id
    if (route.query.isTagRouter) {
        UniformBiddingGetByIdUser()
    }
})

onActivated(() => {
    // 修改tag标签名称########################
    if (route.query.title) {
        // 修改pinia数据
        userStore.$patch(state => {
            state.pageTitleObj.biddingTitle = route.query.title
        })
    }
    let tagsList = userStore.tagsList
    // 使用forEach遍历数组  修改tag标签名称
    tagsList.forEach(item => {
        if (item.path == route.path) {
            // 如果path匹配，则修改title  
            item.title = userStore.pageTitleObj.biddingTitle;
        }
    });
    userStore.setTagsList(tagsList)
    // ########################
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    fileIdList.value = []
    Id.value = route.query.id
    nextTick(() => {
        if (!route.query.isTagRouter) {
            UniformBiddingGetByIdUser()
        }
    })
})

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 702 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const UniformBiddingGetByIdUser = () => {
    UniformBiddingGetById({ id: route.query.UniformBuyId }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other, footer } = res.data.data
            uploadFileData.value.forEach(item => {
                item.fileLChildList = []
                item.categoryList = []
            })
            fileIdList.value = []
            refForm.value.resetFields()
            if (rows) {
                formData.value = rows
                if (rows.ValidityPeriod === 0) formData.value.ValidityPeriod = undefined
                if (rows.BidResultPublic === 0) {
                    formData.value.BidResultPublic = undefined
                } else {
                    formData.value.BidResultPublic = String(rows.BidResultPublic)

                }
                if (rows.Method === 0) {
                    formData.value.Method = undefined
                } else {
                    formData.value.Method = String(rows.Method)
                }
                FilingExplanation.value = rows.FilingExplanation || ''
                BiddingStatuz.value = rows.BiddingStatuz
            } else {
                formData.value = {}
                FilingExplanation.value = ''
            }
            let categoryList = footer || [];//附件集合
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
            BidPublicList.value = other.listBidPublic || [];//开标结果公开
            MethodList.value = other.listMethod || [];//采购方式
            // nextTick(() => {
            //     refForm.value.resetFields()
            // })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//提交
const HandleSubmit = (e) => {
    if (e == 1) {
        // 提交
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            // 判断必传的附件是否已传  
            let found = foundReturn(uploadFileData.value)
            if (found) {
                return
            }
            UniformBiddingSaveUser(e)
        })
    } else {
        // 保存不校验 
        UniformBiddingSaveUser(e)
    }
}
// 采购管理添加保存
const UniformBiddingSaveUser = (ButtonType) => {
    let ValidityPeriodName = ''
    if (formData.value.ValidityPeriod) {
        ValidityPeriodName = ValidityPeriodList.find(t => t.value == formData.value.ValidityPeriod).label
    }
    let MethodName = ''
    if (formData.value.Method) {
        MethodName = MethodList.value.find(t => t.value == formData.value.Method).label
    }

    let pramsData = {
        Id: Id.value,
        UniformBuyId: route.query.UniformBuyId,
        ButtonType: ButtonType,//（0：保存，1：提交）
        ValidityPeriod: formData.value.ValidityPeriod,
        ValidityPeriodName: ValidityPeriodName,
        Method: Number(formData.value.Method) || undefined,
        MethodName: MethodName,
        BidDate: formData.value.BidDate || undefined,
        BidResultPublic: Number(formData.value.BidResultPublic) || undefined,
        PublicDate: formData.value.PublicDate || undefined,
        PublicMediaName: formData.value.PublicMediaName,
        EntrustingAgency: formData.value.EntrustingAgency,
        Contact: formData.value.Contact,
        Mobile: formData.value.Mobile,
        Remark: formData.value.Remark,
        ListAttachmentId: fileIdList.value.map(t => t.Id)
    }
    console.log('pramsData', pramsData)
    UniformBiddingSave(pramsData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '提交成功')
            Id.value = res.data.data.rows.Id
            if (ButtonType == 1) {
                let tagsList = userStore.tagsList
                tagsList = tagsList.filter(t => t.path != route.path)
                userStore.setTagsList(tagsList)
                router.push({ path: "./list" })
            } else {
                UniformBiddingGetByIdUser(Id.value)
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// /////    附件处理      /////
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.categoryList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: item.FileCategory }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    BiddingDelattachmentbyid({ id: Id.value, attid: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}

</script>


<template>
    <el-form style="width: 100%;min-width: 560px;" :inline="true" class="mobile-box" @submit.prevent ref="refForm"
        :model="formData" :rules="ruleForm" label-width="180px" status-icon>
        <el-form-item v-if="BiddingStatuz == 11 || BiddingStatuz == 21"
            :label="BiddingStatuz == 11 ? '审核不通过原因：' : '退回原因：'"
            style="width: 100%; border-bottom: 1px dashed #E4E7ED; ">
            <span style="color: #F56C6C;padding-left: 10px;">{{ FilingExplanation }}</span>
        </el-form-item>
        <el-form-item label="年度：" class="formItem">
            <el-input v-model="formData.PlanYear" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="采购批次：" class="formItem">
            <el-input v-model="formData.PurchaseNo" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="采购有效年限：" prop="ValidityPeriod" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="是指本次采购的有效年限" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 采购有效年限： </span>
            </template>
            <el-select v-model="formData.ValidityPeriod" class="item_content">
                <el-option v-for="item in ValidityPeriodList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="采购方式：" prop="Method" class="formItem">
            <el-select v-model="formData.Method" class="item_content">
                <el-option v-for="item in MethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="开标日期：" prop="BidDate" class="formItem">
            <el-date-picker type="date" v-model="formData.BidDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="开标结果公开：" prop="BidResultPublic" class="formItem">
            <el-select v-model="formData.BidResultPublic" class="item_content">
                <el-option v-for="item in BidPublicList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="公开日期：" prop="PublicDate" class="formItem" v-if="formData.BidResultPublic == 1">
            <el-date-picker type="date" v-model="formData.PublicDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="公开媒体名称：" prop="PublicMediaName" class="formItem" v-if="formData.BidResultPublic == 1">
            <el-input v-model="formData.PublicMediaName" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="委托代理机构：" class="formItem">
            <el-input v-model="formData.EntrustingAgency" placeholder="机构全称" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="代理机构联系人：" class="formItem">
            <el-input v-model="formData.Contact" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="代理机构联系电话：" prop="Mobile" class="formItem">
            <el-input v-model="formData.Mobile" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%;">
            <el-input type="textarea" v-model="formData.Remark" :maxlength="300" show-word-limit
                :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
        </el-form-item>
        <!-- 附件上传 -->
        <el-form-item v-for="(item, index) in uploadFileData" :key="index" style="width: 100%;">
            <template #label>
                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> {{ item.Name }}： </span>
            </template>
            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" :accept="item.UploadFileTypeAccept"
                :before-upload="beforeAvatarUpload.bind(null, item)" :http-request="httpRequest.bind(null, item, index)"
                :disabled="numberDisabled">
                <el-button type="success" size="small" :icon="UploadFilled"
                    @click="MaxFileNumberClick(item)">上传</el-button>
            </el-upload>
            <div class="fileFlex">
                <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                    <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                        {{ itemCate.Title }}{{ itemCate.Ext }}
                    </span>
                </div>
                <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                    <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemChild, item.fileLChildList)">
                        {{ itemChild.Title }}{{ itemChild.Ext }}
                    </span>
                </div>
            </div>
        </el-form-item>
        <el-form-item label=" ">
            <el-button type="primary" :icon="Select" @click="HandleSubmit(0)">保存</el-button>
            <el-button type="primary" :icon="Select" @click="HandleSubmit(1)">提交</el-button>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-right: 0;
}

.item_content {
    // width: 60% !important;
}

:deep(.el-date-editor.el-input) {
    width: 100% !important;
}

.formItem {
    width: 50%;
}

@media (max-width: 768px) {
    .formItem {
        width: 100%;
    }
}
</style>