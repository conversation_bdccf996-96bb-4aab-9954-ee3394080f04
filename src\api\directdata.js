import request from '@/utils/directrequest.js'

// 导出：获取文件
export const UploadDown = (params) => {
  request.get('/api/hyun/upload/down', {
    params,
    responseType: 'blob'
  })
}
// 班主任事务-学生管理-导出
export const Exportteacherstu = (params) => {
  return request.post('/api/hyun/pstudent/exportteacherstu', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 采购汇总列表-导出   (FromBody)
export const DcSchoolPurchaseSummaryListExport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolpurchasesummarylistexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 导出盘点表   (FromBody)
export const ExportDcSchoolMaterialOptFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/exportdcschoolmaterialoptfind', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 导出领用台账   (FromBody)
export const ExportDcApplyFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/exportdcapplyfind', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 导出采购台账   (FromBody)
export const ExportDcStandbookPurchaseFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/exportdcstandbookpurchasefind', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 导出存量台账   (FromBody)
export const ExportDcStandbookStockFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/exportdcstandbookstockfind', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 导出处置台账   (FromBody)
export const ExportDcStandbookWasteFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/exportdcstandbookwastefind', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 导出危化品存量库   (FromBody)
export const ExportDcScrapList = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/exportdcscraplist', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 采购清单列表-导出    (FromBody)
export const DcPurchaseListExportExcel = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaselistexportexcel', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 采购已生成计划列表-导出   (long id)
export const DcPurchaseEndExport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseendexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 采购明细-导出   (long id)
export const DcPurchaseDetailListExport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasedetaillistexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 按属性统计数量-导出   (FromBody)
export const DcClassifyStockNumStatisticsExport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcclassifystocknumstatisticsexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 库存数量统计-导出    (FromBody)
export const DcCountyStockNumStatisticsExort = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccountystocknumstatisticsexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 处置数量统计-导出   (FromBody)
export const DcWasteDisposalNumStatisticsExport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalnumstatisticsexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 市级库存数量统计-导出  (FromBody)
export const DcCityStockNumStatisticsExport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccitystocknumstatisticsexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 市级按属性统计数量-导出  (FromBody)
export const DcCityClassifyStockNumStatisticsExport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccityclassifystocknumstatisticsexport', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}

// 查询统计页面 - 导出查询统计数据
export const DownloadSearchList = (params) => {
  return request.post('/api/hyun/process/downloadsearchlist', params, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json' // 确保请求头正确
    }
  })
}