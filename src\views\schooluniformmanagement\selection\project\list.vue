<script setup>
defineOptions({
  name: 'projectlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search, FolderAdd
} from '@element-plus/icons-vue'
import {
  SchemeGetpaged, SchemeDelbyid, SchemeSavecopy
} from '@/api/selection.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
// 二维码下载
import QRCode from 'qrcode';
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const StatuzSolicitedList = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, isFirst: true, SourceType: 1 })
const yearDateList = ref([])

//加载数据
onMounted(() => {
  yearDateList.value = previousYearDate()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      // 重置列表（点菜单重置，tag标签不重置。看后期是否需要重置）
      // filters.value = { pageIndex: 1, pageSize: 10, isFirst: true, SourceType: 1 }
      HandleTableData(true);
    }
  })
})

//新增
const HandleAdd = () => {
  router.push({ path: "./edit", query: { id: 0, title: "创建选用方案" } })
}
// 修改
const HandleEdit = (row) => {
  router.push({ path: "./edit", query: { id: row.Id, title: "修改选用方案" } })
}
// 复制
const HandleCopy = (row) => {
  SchemeSavecopy({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      HandleTableData()
      ElMessage.success(res.data.msg || '复制成功')
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      SchemeDelbyid({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  SchemeGetpaged(filters.value).then(res => {
    if (res.data?.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        StatuzSolicitedList.value = other.StatuzSolicited
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()

}
// 重置
const HandleReset = () => {
  filters.value.Name = undefined
  filters.value.SchemeYear = undefined
  filters.value.SolicitedStatuz = undefined
  filters.value.pageIndex = 1
  HandleTableData()
}
// 分页
const handlePage = () => {
  HandleTableData()
}
const filtersChange = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}

// //下载二维码图片
const qrCodeDataUrl = ref(null);
const generateQRCode = async (text, size) => {
  try {
    qrCodeDataUrl.value = await QRCode.toDataURL(text, { scale: size / 100 });
  } catch (error) {
    console.error('生成二维码时出错:', error);
  }
};

const downloadQRCode = (rows) => {
  let url = window.location.protocol + '//' + window.location.host
  let path = url + "/ui/#" + "/pages/selection/newedit?id=" + rows.Id;//二维码信息  
  let title = '校服选用' + rows.SchemeNo + '.png'
  generateQRCode(path, 400).then(() => {
    // 创建一个临时的 <a> 元素来触发下载
    const a = document.createElement('a');
    a.href = qrCodeDataUrl.value;
    a.download = title;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  });
};
</script>

<template>
  <div class="viewContainer">
    <el-collapse>
      <el-collapse-item>
        <template #title>
          操作提示 &nbsp;
          <el-icon color="#E6A23C" :size="16">
            <QuestionFilled />
          </el-icon>
        </template>
        <ol class="rowFill">
          <li> 点击【创建】和【修改】按钮，可编辑校服选用方案；</li>
          <li> 点击【修改】可发布或撤销校服征求意见，也可调整“征求意见截止时间； </li>
          <li> 点击【复制】可新增一条选用方案。 </li>
        </ol>
      </el-collapse-item>
    </el-collapse>
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
          </el-form-item>
          <div class="verticalIdel"></div>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SchemeYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SolicitedStatuz" clearable placeholder="状态" style="width: 160px"
              @change="filtersChange">
              <el-option v-for="item in StatuzSolicitedList" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="选用批次" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="SchemeYear" label="年度" min-width="100" align="center"></el-table-column>
      <el-table-column prop="SchemeNo" label="选用批次" min-width="140" align="center"></el-table-column>
      <el-table-column prop="SolicitedNum" label="应征求人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="SolicitedDeadlineStr" label="征求意见截止时间" min-width="180" align="center">
        <template #default="{ row }">
          <span v-if="new Date(row.SolicitedDeadlineStr).setHours(23, 59, 59, 0) < new Date()"
            style="color: #F56C6C;">{{
              row.SolicitedDeadlineStr.substring(0, 10) }}</span>
          <span v-else>{{ row.SolicitedDeadlineStr.substring(0, 10) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="ReleaseTimeStr" label="发布时间" min-width="180" align="center"> </el-table-column>
      <el-table-column prop="SolicitedStatuzName" label="状态" min-width="120" align="center"></el-table-column>
      <el-table-column label="选用二维码" min-width="100" align="center">
        <template #default="{ row }">
          <el-button
            v-if="row.SolicitedStatuz == 1 && new Date(row.SolicitedDeadlineStr).setHours(23, 59, 59, 0) > new Date()"
            type="primary" link @click="downloadQRCode(row)">下载</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="选用方案" min-width="160" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
          <el-button v-if="row.SolicitedStatuz == 1" type="primary" link @click="HandleCopy(row)">复制</el-button>
          <el-button v-if="row.SolicitedStatuz == 0" type="primary" link @click="HandleDel(row)">删除</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>