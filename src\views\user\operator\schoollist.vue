<script setup>
import { onMounted, ref, watch } from 'vue'
import { useUserStore } from '@/stores';
import {
    Unlock, UploadFilled, Lock, Search, Refresh, DocumentCopy
} from '@element-plus/icons-vue'
import {
    SchoolInfoAdminFind, batchlockunlock, Areagetbypid, AreaFind
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
// 表格初始化
const userStore = useUserStore()
const tableData = ref([])
const tableTotal = ref(0)
const lockListData = ref({ ids: '', isLock: true })
const refTable = ref()
const selectRows = ref([])
const processVersion = userStore.defaultSet.ProcessVersion
const provincialDataList = ref([{ Name: "全部", Id: "-1" }])
const cityDataList = ref([{ Name: "全部", Id: "-1" }])
const countyDataList = ref([{ Name: "全部", Id: "-1" }])
const areaFormData = ref([])
const filters = ref({ Name: '', pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const hUnitType = ref(userStore.userInfo.UnitType)
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}

//加载数据
onMounted(() => {
    console.log("hUnitType", hUnitType.value)
    HandleTableData()
    // AreagetbypidUser()
    AreaFindUser()

})
// 获取省级 
const AreagetbypidUser = (pid) => {
    Areagetbypid({ pid: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            rows.unshift({ Name: "全部", Id: "-1" });
            provincialDataList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取区县  
const AreaFindUser = () => {
    AreaFind().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // rows.unshift({ label: "全部", value: "-1" });
            areaFormData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }

    })
}


//获取列表  
const HandleTableData = () => {
    selectRows.value = []
    SchoolInfoAdminFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            tableData.value = res.data.data.rows;
            tableTotal.value = Number(res.data.data.total);
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.Name = ''
    // filters.value.CountyAreaId = '-1'
    // filters.value.ProvinceIdAreaId = '-1'
    // filters.value.CityAreaId = '-1'
    filters.value.CountyId = undefined
    HandleTableData()

}
// 选择省级
const ProvincialChange = (id) => {
    console.log(id)
    Areagetbypid({ pid: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            rows.unshift({ Name: "全部", Id: "-1" });
            cityDataList.value = rows
            filters.value.CityAreaId = '-1'
            filters.value.CountyAreaId = '-1'
        } else {
            ElMessage.error(res.data.msg)
        }

    })
}
// 选择市级
const CityChange = (id) => {
    Areagetbypid({ pid: id }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            rows.unshift({ Name: "全部", Id: "-1" });
            countyDataList.value = rows
            filters.value.CountyAreaId = '-1'
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 用户账户
const HandleUser = (row) => {
    router.push({ path: "../school/user", query: { UnitId: row.Id } })
}
// 单位明细
const HandleDetail = (row) => {
    router.push({ path: "../school/detail", query: { Id: row.Id, Name: row.Name } })
}
// 场所信息
const HandlePlace = (row) => {
    router.push({ path: "../school/propertyinfo", query: { UnitId: row.Id } })
}

// 锁定
const HandleLock = (row) => {
    batchlockunlockUser(row.seId, true)
}
// 解锁
const HandleunLock = (row) => {
    batchlockunlockUser(row.seId, false)
}
// 批量锁定
const HandleLockAll = (row) => {
    let ids = row.map(t => t.seId).join(',')
    batchlockunlockUser(ids, true)
}
// 批量解锁
const HandleunLockAll = (row) => {
    let ids = row.map(t => t.seId).join(',')
    batchlockunlockUser(ids, false)
}
// 关联
const HandleSetLink = () => { }

// 解锁/锁定
const batchlockunlockUser = (ids, isLock) => {
    lockListData.value = {
        ids: ids,
        isLock: isLock
    }
    batchlockunlock(lockListData.value).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const handleChange = (value) => {
    console.log(value)
}
</script>
<template>
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                <!-- <el-form-item label="省" class="flexItem" label-width="50">
                    <el-select v-model="filters.ProvinceIdAreaId" style="width: 120px" @change="ProvincialChange">
                        <el-option v-for="item in provincialDataList" :key="item.Id" :label="item.Name"
                            :value="item.Id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="市" class="flexItem" label-width="30">
                    <el-select v-model="filters.CityAreaId" style="width: 120px" @change="CityChange">
                        <el-option v-for="item in cityDataList" :key="item.Id" :label="item.Name" :value="item.Id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="区" class="flexItem" label-width="30">
                    <el-select v-model="filters.CountyAreaId" style="width: 120px">
                        <el-option v-for="item in countyDataList" :key="item.Id" :label="item.Name" :value="item.Id" />
                    </el-select>
                </el-form-item> -->
                <el-form-item class="flexItem">
                    <el-cascader :options="areaFormData" v-model="filters.CountyId"
                        :props="{ emitPath: false, checkStrictly: true }" :show-all-levels="false"
                        placeholder="市级/区县" />
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-input v-model.trim="filters.Name" placeholder="单位名称" style="max-width: 300px">
                    </el-input>
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                    <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                </el-form-item>
                <!-- <div class="verticalIdel"></div>
                <el-form-item class="flexItem">
                    <el-button type="primary" :icon="Lock" :disabled="selectRows.length == 0"
                        @click="HandleLockAll(selectRows)">批量锁定</el-button>
                    <el-button type="primary" :icon="Unlock" :disabled="selectRows.length == 0"
                        @click="HandleunLockAll(selectRows)">批量解锁</el-button>
                </el-form-item> -->
            </el-form>
        </el-col>
    </el-row>
    <!-- 内容 -->
    <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" stripe
        border header-cell-class-name="headerClassName">
        <!-- <el-table-column type="selection" width="50"></el-table-column> -->
        <el-table-column prop="Name" label="单位名称" width min-width="180"></el-table-column>
        <el-table-column prop="StageName" label="单位属性" width min-width="100" align="center"></el-table-column>
        <el-table-column prop="userCount" label="注册用户数" width min-width="100" align="center">
            <template #default="{ row }">
                <span style="color: #F56C6C;">{{ row.userCount }}</span>
            </template>
        </el-table-column>
        <!-- <el-table-column prop="propertyCount" label="录入场馆数" width min-width="100" align="center">
            <template #default="{ row }">
                <span style="color: #F56C6C;">{{ row.propertyCount }}</span>
            </template>
        </el-table-column> -->
        <el-table-column prop="ClassNum" label="班级总数" width min-width="100" align="center"></el-table-column>
        <el-table-column prop="TeacherNum" label="教职工数" width min-width="100" align="center"></el-table-column>
        <el-table-column prop="StudentNum" label="学生总数" width min-width="100" align="center"></el-table-column>
        <el-table-column prop="SchoolAdmin" label="校管理员" width min-width="100" align="center"></el-table-column>
        <el-table-column prop="AdminMobile" label="联系电话" width min-width="120" align="center"></el-table-column>
        <el-table-column prop="HeadMaster" label="单位法人" width min-width="100" align="center"></el-table-column>
        <el-table-column prop="MsaterMobile" label="联系电话" width min-width="120" align="center"></el-table-column>
        <el-table-column label="查看" width="240" align="center" fixed="right">
            <template #default="{ row }">
                <el-button type="primary" link @click="HandleUser(row)">用户账户</el-button>
                <el-button type="primary" link @click="HandleDetail(row)">单位明细</el-button>
                <!-- <el-button type="primary" link @click="HandlePlace(row)">场所信息</el-button> -->
            </template>
        </el-table-column>
        <!-- <el-table-column prop="opt" label="操作" width="120" align="center" fixed="right">
            <template #default="{ row }">
                <el-button type="primary" link v-if="row.IsLock == 0" @click="HandleLock(row)">锁定</el-button>
                <el-button type="primary" link v-if="row.IsLock == 1" @click="HandleunLock(row)">解锁</el-button>
                <el-button type="primary" link v-if="processVersion == 0" @click="HandleSetLink(row)">关联</el-button>
            </template>
        </el-table-column> -->
        <template #empty>
            <el-empty description="没有数据"></el-empty>
        </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
        @handleChange="handlePage" />
</template>
<style lang="scss" scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}

.addCustom.el-form {
    width: 90%;
    margin: 0 auto;

    .el-form-item {
        margin-bottom: 10px;
    }

    .el-checkbox-group {
        display: flex;
        flex-wrap: wrap;

        label {
            width: 20%;
        }
    }

    .el-checkbox {
        height: var(--el-checkbox-height, 22px);
    }

}

.taskNameConent {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>