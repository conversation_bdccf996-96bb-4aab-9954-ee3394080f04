<script setup>
import { onMounted, ref, watch } from 'vue'
import {
    PuseruSerchangepass
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import router from '@/router'
import md5 from 'js-md5';
import { useUserStore } from '@/stores';
const userStore = useUserStore()
// 重置密码
const formData = ref({})
const refForm = ref()
const validatePassSec = (rule, value, callback) => {
    if (value === "") {
        callback(new Error("请再次输入密码"));
    } else if (value !== formData.value.NewPwd) {
        callback(new Error("两次输入密码不一致!"));
    } else {
        callback();
    }
};
const refFormRule = {
    OldPwd: [
        { required: true, message: '原密码不能为空', trigger: 'change' },
    ],
    NewPwd: [
        { required: true, message: '新密码不能为空', trigger: 'change' },
        // {
        //     pattern: /^(?!Xxpt@01688$).+$/,
        //     message: '密码不能为：Xxpt@01688。请重新修改！',
        //     trigger: 'blur'
        // }



    ],
    NewPwd2: [
        // { required: true, message: '请再次输入密码', trigger: 'change' },
        { required: true, validator: validatePassSec, trigger: "blur" }
    ]
}

const SubmitReset = () => {

    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        ElMessageBox.confirm('确定修改密码吗?')
            .then(() => {
                let params = {
                    OldPwd: md5(formData.value.OldPwd),
                    NewPwd: md5(formData.value.NewPwd),
                }
                PuseruSerchangepass(params).then((res) => {

                    if (res.data.flag == 1) {
                        ElMessage.success(res.data.msg || '重置成功')
                        // 清除本地的数据 (token + user信息)
                        userStore.logout()
                        router.push('/login')
                    } else {
                        ElMessage.error(res.data.msg)
                    }


                    outer.push('/login')
                })
            })
    })


}
</script>
<template>
    <el-form style="width: 550px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
        :rules="refFormRule" label-width="160px" status-icon>
        <!-- <el-form-item label="昵称" prop="Name">
            <el-input v-model="userStore.userInfo.Name" readonly></el-input>
        </el-form-item> -->
        <el-form-item label="原密码" prop="OldPwd">
            <el-input v-model="formData.OldPwd" show-password auto-complete="off" clearable></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="NewPwd">
            <el-input v-model="formData.NewPwd" show-password auto-complete="off" clearable></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="NewPwd2">
            <el-input v-model="formData.NewPwd2" show-password auto-complete="off" clearable></el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="SubmitReset">
                修改
            </el-button>
        </el-form-item>
    </el-form>

</template>
