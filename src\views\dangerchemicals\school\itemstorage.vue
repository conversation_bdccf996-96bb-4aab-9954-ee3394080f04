<script setup>
defineOptions({
    name: 'dangerchemicalsschoolitemstorage'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Select } from '@element-plus/icons-vue'
import {
    BconfigSetgetPunit, UploadPostfile, DcCompanyComboxFind, DcDepositAddressFind, DcschoolCatalogCommonuseFind, DcCabinetAddressGet,
    DcschoolCatalogFindCommonuseAll, DcSchoolMaterialInsertUpdate, DcSchoolMaterialModelGetByCatalogId, DcSchoolModelBrandGetBrand
} from '@/api/dangerchemicals.js'
import { useUserStore } from '@/stores';
import { ElMessageBox, ElMessage } from 'element-plus'
import { AttachmentUpload } from '@/api/user.js'
import { buildTree, fileDownload, limit } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
const bogetList = ref([])
const addressList = ref([])//存放地点
const brandList = ref([])
const modelList = ref([])
const companyList = ref([])
const cabinetAddressList = ref([])
const dangerchemicalsList = ref([])
const dialogVisible = ref(false)
const refForm = ref()
const userStore = useUserStore()
const bconfigSet = ref(0)
const formData = ref({})
const ruleForm = {
    Name: [
        { required: true, message: '请选择危化品', trigger: 'change' },
    ],
    SchoolMaterialModelId: [
        { required: true, message: '请选择规格属性', trigger: 'change' },
    ],
    SchoolMaterialBrandId: [
        { required: true, message: '请选择品牌', trigger: 'change' },
    ],
    Num: [
        { required: true, message: '请输入数量', trigger: 'change' },
    ],
    Price: [
        { required: true, message: '请输入单价', trigger: 'change' },
    ],
    oldDcCompanyId: [
        { required: true, message: '请选择供应商', trigger: 'change' },
    ],
    DepositAddressId: [
        { required: true, message: '请选择存放地点', trigger: 'change' },
    ],
    ValidDate: [
        { required: true, message: '请选择有效期至', trigger: 'change' },
    ],
}
onMounted(() => {
    BconfigSetgetPunitUser()
    DcDepositAddressFindUser()
    DcschoolCatalogFindCommonuseAllUser()
    DcCompanyComboxFindUser()
    DcCabinetAddressGetUser()
})
// 选择危化品
const HandleAdd = () => {
    dialogVisible.value = true
    isSearch.value = false
}
//输入框限制：输入两位小数
const limitInput = (val, name) => {
    formData.value[name] = limit(val);
}
// 根据TypeCode获取上级单位配置信息
const BconfigSetgetPunitUser = () => {
    BconfigSetgetPunit({ moduleCode: 9, typeCode: 'WHPCLRKGN' }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bconfigSet.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 存放地点-查询
const DcDepositAddressFindUser = () => {
    DcDepositAddressFind({ Statuz: 1, pageIndex: 0, pageSize: ********* }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            addressList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取学校物品类别选择-查询
const DcschoolCatalogFindCommonuseAllUser = () => {
    DcschoolCatalogFindCommonuseAll({ commonUse: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            let arr = rows.data || []
            bogetList.value = buildTree(arr)
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取学校供应商信息-查询
const DcCompanyComboxFindUser = () => {
    DcCompanyComboxFind({ Statuz: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            companyList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
// 获取存储柜地址下拉列表-查询 
const DcCabinetAddressGetUser = () => {
    DcCabinetAddressGet().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            cabinetAddressList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}

// 获取规格属性
const DcSchoolMaterialModelGetByCatalogIdUser = (id) => {
    DcSchoolMaterialModelGetByCatalogId({ schoolCatalodId: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            modelList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取品牌
const DcSchoolModelBrandGetBrandUser = (id) => {
    DcSchoolModelBrandGetBrand({ schoolCatalodId: id, modelId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            brandList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//提交
const HandleSubmit = () => {
    console.log("formData.value", formData.value)
    let paraData = {
        BaseCatalogId: formData.value.BaseCatalogId,
        Brand: formData.value.Brand,
        CabinetAddress: formData.value.CabinetAddress,
        CompanyName: formData.value.CompanyName,
        DcCompanyId: formData.value.DcCompanyId,
        DepositAddressId: formData.value.DepositAddressId,
        Id: '0',
        MsdsFile: formData.value.MsdsFile,
        Name: formData.value.Name,
        Num: formData.value.Num,
        Price: formData.value.Price,
        Remark: formData.value.Remark,
        SchoolCatalogId: formData.value.Id,
        SchoolMaterialBrandId: formData.value.SchoolMaterialBrandId,
        SchoolMaterialModelId: formData.value.SchoolMaterialModelId,
        Statuz: 0,
        UnitName: formData.value.UnitName,
        ValidDate: formData.value.ValidDate,
        WarrantyMonth: formData.value.WarrantyMonth,
    }
    console.log("paraData", paraData)
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcSchoolMaterialInsertUpdate(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '保存成功')
                formData.value = {}
                nextTick(() => {
                    refForm.value.resetFields()
                })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

const activeName = ref('content')
// 品牌选择
const brandChange = (e) => {
    console.log(e)
    let find = brandList.value.filter(item => item.SchoolMaterialBrandId == e)
    if (find.length > 0) {
        formData.value.SchoolMaterialBrandId = e
        formData.value.Brand = find[0].Brand
    } else {
        formData.value.Brand = e
    }
}
// 供应商选择
const companyChange = (e) => {
    let find = companyList.value.filter(item => item.Id == e)
    if (find.length > 0) {
        formData.value.DcCompanyId = e
        formData.value.CompanyName = find[0].Name
    } else {
        formData.value.DcCompanyId = 0
        formData.value.CompanyName = e
    }
}
// 选择危化品
const dangerchemicalsClick = (item) => {
    console.log("item", item)
    formData.value.BaseCatalogId = item.BaseCatalogId
    formData.value.Id = item.Id
    formData.value.Name = item.Name
    formData.value.UnitName = item.UnitsMeasurement
    formData.value.SchoolMaterialModelId = undefined
    formData.value.SchoolMaterialBrandId = undefined
    dialogVisible.value = false
    DcSchoolMaterialModelGetByCatalogIdUser(item.Id)
    DcSchoolModelBrandGetBrandUser(item.Id)

}
const firstName = ref('')
const isSearch = ref(false)

const HandleSearch = () => {
    isSearch.value = true
    if (!firstName.value) return
    let paraData = {
        Name: firstName.value,
        Statuz: 1,
        pageIndex: 0,
        pageSize: 100000,
        sortModel: [{ SortCode: "Code", SortType: "asc" }]
    }
    DcschoolCatalogCommonuseFind(paraData).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerchemicalsList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//附件上传
const uploadFileData = ref([{ FileCategory: 2964, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 1, Memo: "文件小于5M，支持pdf和图片文件", Name: "MSDS：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" }])
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)

// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data

            //formData.MsdsFilePath, formData.Ext, formData.MsdsFileTitle
            formData.value.MsdsFilePath = rows[0].Path;
            formData.value.MsdsFileTitle = rows[0].Title;
            formData.value.Ext = rows[0].Ext;
            formData.value.MsdsFile = rows[0].Path + "|" + rows[0].Title + rows[0].Ext;
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)

// 附件图片预览与文件下载
const lookFileListDownload = (filePath, ext, title) => {
    viewPhotoList.value = [filePath];
    ext = (typeof ext === "string") ? ext.toLowerCase() : "";
    if (ext == ".png" || ext == ".jpg" || ext == ".jpng") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (filePath == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        fileDownload(filePath, title)
    }
}
</script>
<template>
    <div v-if="bconfigSet == 2" class="emptyMsg">
        <span> 存量录入功能已关闭，如需开启请联系教育局主管科室。</span>
    </div>
    <div v-else>
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    存量录入 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 是指上平台前，学校原有危化品存量录入。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-form style="min-width: 100px;margin-top: 10px;" class="mobile-box" @submit.prevent ref="refForm"
            :model="formData" :rules="ruleForm" label-width="180px" status-icon>
            <el-form-item label="危化品名称：" prop="Name">
                <el-input v-model="formData.Name" readonly @click="HandleAdd" style="width: 400px"></el-input>
                <el-button type="success" :icon="Search" @click="HandleAdd" style="margin-left: 10px;">选择</el-button>
            </el-form-item>
            <el-form-item label="规格属性：" prop="SchoolMaterialModelId">
                <el-select v-model="formData.SchoolMaterialModelId" style="width: 400px;">
                    <el-option v-for="item in modelList" :key="item.Id" :label="item.Model" :value="item.Id" />
                </el-select>
                <span class="placeholdertext">你所需的规格属性下拉菜单中如没有，请通知区危化品监管员添加！</span>
            </el-form-item>
            <el-form-item label="品牌：" prop="SchoolMaterialBrandId">
                <el-select v-model="formData.SchoolMaterialBrandId" filterable allow-create default-first-option
                    @change="brandChange" style="width: 400px">
                    <el-option v-for="item in brandList" :key="item.SchoolMaterialBrandId" :label="item.Brand"
                        :value="item.SchoolMaterialBrandId" />
                </el-select>
                <span class="placeholdertext">你所需的品牌下拉菜单中如没有，请自行填写！</span>
            </el-form-item>
            <el-form-item label="数量：" prop="Num">
                <el-input v-model="formData.Num" @input="limitInput($event, 'NUm')" style="width: 400px"></el-input>
            </el-form-item>
            <el-form-item label="单价(元)：" prop="Price">
                <el-input v-model="formData.Price" @input="limitInput($event, 'Price')" style="width: 400px"></el-input>
            </el-form-item>
            <el-form-item label="供应商：" prop="oldDcCompanyId">
                <el-select v-model="formData.oldDcCompanyId" filterable allow-create default-first-option
                    @change="companyChange" style="width: 400px">
                    <el-option v-for="item in companyList" :key="item.Id" :label="item.Name" :value="item.Id" />
                </el-select>
                <span class="placeholdertext">你所需的供应商下拉菜单中如没有，请自行添加，名称须与发票中一致！</span>
            </el-form-item>
            <el-form-item label="存放地点：" prop="DepositAddressId">
                <el-select v-model="formData.DepositAddressId" filterable style="width: 400px">
                    <el-option v-for="item in addressList" :key="item.Id" :label="item.Address" :value="item.Id" />
                </el-select>
            </el-form-item>
            <el-form-item label="储存柜层次：">
                <el-select v-model="formData.CabinetAddress" filterable allow-create default-first-option
                    style="width: 400px">
                    <el-option v-for="item in cabinetAddressList" :key="item.CabinetAddress"
                        :label="item.CabinetAddress" :value="item.CabinetAddress" />
                </el-select>
            </el-form-item>
            <el-form-item label="有效期至：" prop="ValidDate">
                <el-date-picker type="date" placeholder="选择日期" v-model="formData.ValidDate" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD" style="width: 400px"></el-date-picker>
            </el-form-item>
            <el-form-item label="质保期(月)：">
                <el-input v-model="formData.WarrantyMonth" style="width: 400px"></el-input>
                <span class="placeholdertext">如无“质保期”，则免填写！</span>
            </el-form-item>
            <el-form-item label="备注：">
                <el-input v-model="formData.Remark" type="textarea" style="width: 400px"></el-input>
            </el-form-item>
            <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                <template #label>
                    <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                    <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                        <div>
                            <el-icon color="#E6A23C" class="tipIcon">
                                <QuestionFilled />
                            </el-icon>
                        </div>
                    </el-tooltip>
                    <span> {{ item.Name }}： </span>
                </template>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                    :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                    :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                    <el-button type="success" size="small" :icon="UploadFilled">上传</el-button>
                </el-upload>
                <div class="fileFlex">
                    <div v-if="formData.MsdsFile" style="color:#409EFF ;width:200px">
                        <span style="cursor: pointer;"
                            @click="lookFileListDownload(formData.MsdsFilePath, formData.Ext, formData.MsdsFileTitle)">
                            {{ formData.MsdsFileTitle }}
                        </span>
                    </div>
                </div>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
            </el-form-item>

        </el-form>
        <app-box v-model="dialogVisible" :width="960" :lazy="true" title="请选择危化品">
            <template #content>
                <el-tabs tab-position="left" v-model="activeName" style="height: 480px" class="demo-tabs">
                    <el-tab-pane label="搜索" name="search">
                        <div>
                            <el-input v-model.trim="firstName" placeholder="危化品名称" style="width: 280px"> </el-input>
                            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        </div>
                        <div>
                            <div v-if="isSearch && firstName && dangerchemicalsList.length == 0"
                                style="padding: 10px;color: #E6A23C;">危化品不存在，请重新搜索！</div>
                            <ul class="SearchUi" v-else>
                                <li v-for="item in dangerchemicalsList" :key="item.Id"
                                    @click="dangerchemicalsClick(item)">
                                    {{ item.FirstName }} > {{ item.SecondName }} > {{ item.Name }}</li>
                            </ul>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="危化品" name="content">
                        <ul class="whpUi">
                            <li v-for="item in bogetList[0].children" :key="item.Id">
                                <div class="content_left">
                                    {{ item.Name }}
                                </div>
                                <div class="content_right">
                                    <div v-for="item1 in item.children" :key="item1.Id" style="padding: 2px 0;">
                                        <el-divider direction="vertical" />
                                        <span class="text" style="padding: 2px;" @click="dangerchemicalsClick(item1)">
                                            {{ item1.Name }}</span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                </el-tabs>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
.whpUi {
    font-size: 12px;
    background-color: #f9f9f9;
    padding: 5px;
    padding-inline-start: 0px;
    margin: 0;

    li {
        list-style-type: none;
        font-size: 12px;
        display: flex;
        padding: 10px 5px;
        border: 1px solid #f9f9f9;
        border-bottom: 1px dotted #d1cfd0;

        .content_left {
            width: 140px;
            flex-shrink: 0;
            color: #723535;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .content_right {
            display: flex;
            flex-wrap: wrap;
            color: #3c3c3c;

            .text:hover {
                cursor: pointer;
                color: #ff9933 !important;
            }
        }
    }

    li:hover {
        background: #fff;
        border: 1px solid #f93 !important;
    }
}

.SearchUi {
    font-size: 14px;
    // background-color: #f9f9f9;
    padding: 5px;
    height: 500px;
    margin: 0;
    overflow-y: auto;

    li {
        list-style-type: none;
        padding: 5px;
        border-bottom: 1px dotted #d1cfd0;
    }

    li:hover {
        background: #fff;
        color: #ff9933 !important;
        border: 1px solid #f93 !important;
        cursor: pointer;
    }
}

:deep(.el-tabs__nav-scroll) {
    background-color: #f9f9f9;
}

.placeholdertext {
    padding-left: 10px;
    color: #999;
}
</style>