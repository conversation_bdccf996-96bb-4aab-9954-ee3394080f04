import Oidc, { UserManager } from "oidc-client";
import { GetNavigationBar } from '@/api/user.js'
import { getToken } from "../api/auth";
import { useUserStore } from '@/stores'
import router, { addDynamicRoutes } from "@/router";
import { onePage } from "@/utils/index.js"

const userStore = useUserStore()

// OIDC配置
const config = {
  authority: import.meta.env.VITE_OIDC_AUTHORITY,
  client_id: import.meta.env.VITE_OIDC_CLIENT_ID,
  redirect_uri: import.meta.env.VITE_OIDC_REDIRECT_URI,
  response_type: import.meta.env.VITE_OIDC_RESPONSE_TYPE,
  scope: import.meta.env.VITE_OIDC_SCOPE,
  
  // 静默更新相关配置
  automaticSilentRenew: true,
  silent_redirect_uri: import.meta.env.VITE_OIDC_REDIRECT_URI,
  
  // 其他配置
  loadUserInfo: true,
  userStore: new Oidc.WebStorageStateStore({}),
};

const userManager = new UserManager(config);


// 导出userManager实例
export { userManager };