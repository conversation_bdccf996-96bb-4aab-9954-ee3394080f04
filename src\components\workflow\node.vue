<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance();
// 定义父组件传过来的值
const props = defineProps({
    node: Object,
    activeElement: Object,
    disabled: {
        typeof: Boolean,
        default: false
    }
});
const nodeRef = ref()
const isDragging = ref(false)
let startX = 0
let startY = 0
// 节点容器样式
const nodeContainerClass = computed(() => {
    return {
        'ef-node-container': true,
        'ef-node-active': props.activeElement.type == 'node' ? props.activeElement.nodeId === props.node.id : false
    }
})
// 节点容器样式
const nodeContainerStyle = computed(() => {
    return {
        top: props.node.top,
        left: props.node.left
    }
})
const nodeIcoClass = computed(() => {
    var nodeIcoClass = {}
    nodeIcoClass[props.node.ico] = true
    // 添加该class可以推拽连线出来，viewOnly 可以控制节点是否运行编辑
    nodeIcoClass['flow-node-drag'] = props.node.viewOnly ? false : true
    return nodeIcoClass
})

const emit = defineEmits(['changeNodeSite', 'delNode', 'clickNode']);

const isMouseUpHandled = ref(false)
const mousedown = (event) => {
    isDragging.value = false
    startX = event.clientX
    startY = event.clientY
}
// 根据鼠标抬起时的移动像素判断：节点移动/点击节点
const mouseup = (event) => {
    if (event.button !== 0) return;
    if (isDragging.value) {
        changeNodeSite(event)
    } else {
        clickNode(event)
    }
}
// 节点移动像素
const mousemove = (event) => {
    if (Math.abs(event.clientX - startX) > 2 || Math.abs(event.clientY - startY) > 2) {
        isDragging.value = true
    }
}
// 点击节点
const clickNode = (event) => {
    if (event.target.closest('.el-icon-delete')) return; // 如果点击的是删除按钮，不触发
    if (isMouseUpHandled.value) return

    emit('clickNode', props.node.id)
}
// 鼠标移动后抬起
const changeNodeSite = () => {
    // 避免抖动
    if (props.node.left == proxy.$refs.nodeRef.style.left && props.node.top == proxy.$refs.nodeRef.style.top) {
        return;
    }
    emit('changeNodeSite', {
        nodeId: props.node.id,
        left: proxy.$refs.nodeRef.style.left,
        top: proxy.$refs.nodeRef.style.top,
    })
}

const delNode = (event) => {
    event.stopPropagation();
    emit("delNode");
}

</script>
<template>
    <div ref="nodeRef" class="node-item" :style="nodeContainerStyle" @mousedown="mousedown" @mouseup="mouseup"
        @mousemove="mousemove" :class="nodeContainerClass">
        <!-- 最左侧的那条竖线 -->
        <div class="ef-node-left"></div>
        <!-- 节点类型的图标 -->
        <div class="ef-node-left-ico flow-node-drag">
            <i :class="nodeIcoClass"></i>
        </div>
        <!-- 节点名称 -->
        <div class="ef-node-text" :show-overflow-tooltip="true">
            {{ node.name }}
        </div>
        <i @click.stop="delNode" v-if="!disabled" style="display: none" class="el-icon-delete"></i>
        <!-- 节点状态图标 -->
        <!-- <div class="ef-node-right-ico">
            <i class="el-icon-circle-check el-node-state-success" v-if="node.state === 'success'"></i>
            <i class="el-icon-circle-close el-node-state-error" v-if="node.state === 'error'"></i>
            <i class="el-icon-warning-outline el-node-state-warning" v-if="node.state === 'warning'"></i>
            <i class="el-icon-loading el-node-state-running" v-if="node.state === 'running'"></i>
        </div> -->
    </div>
</template>
<style lang="scss" scoped>
@import "./index.css";

.node-item:hover .el-icon-delete {
    display: inline-block !important;
}

.el-icon-delete {
    cursor: pointer;
    position: relative;
    top: -18px;
    padding-left: 5px;
    right: 0;
    color: #f61313;
    height: 20px;
}
</style>
