<script setup>
defineOptions({
    name: 'dangerchemicalswasteauditlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, View
} from '@element-plus/icons-vue'
import {
    DcWasteDisposalAuditFind, DcWasteDisposalGetById, DcWasteDisposalAudit
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { fileDownload } from "@/utils/index.js";//上传附件
import { Getpagedbytype } from '@/api/user.js'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const batchNoList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const RegDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    statuz: [
        { required: true, message: '请选择审核结果', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'BatchNo', label: '处置批次', },
    { value: 'UserName', label: '申请人', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    GetpagedbytypeUser();
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const wasteId = ref('')
const wasteNUm = ref(1)
// 审核
const HandleEdit = (row, e) => {
    dialogVisible.value = true
    wasteId.value = row.Id
    wasteNUm.value = e
    if (e == 1) {
        formData.value = row
        nextTick(() => {
            refForm.value.resetFields()
        })
    } else {
        DcWasteDisposalGetById({ Id: row.Id }).then(res => {
            if (res.data.flag == 1) {
                const { rows } = res.data.data
                formData.value = rows

                //置空附件
                formData.value.ProcessFilePath = undefined;
                formData.value.ProcessFileTitle = undefined;
                formData.value.ProcessFileExt = undefined;
                formData.value.ProcessImgPath = undefined;
                formData.value.ProcessImgTitle = undefined;
                formData.value.ProcessImgExt = undefined;

                //处理附件
                if (rows.ProcessFile && rows.ProcessFile.includes("|")) {
                    //解析出扩展名和名称，路径
                    const fileStrings = rows.ProcessFile.split("|");
                    formData.value.ProcessFilePath = fileStrings[0];
                    formData.value.ProcessFileTitle = fileStrings[1];
                    formData.value.ProcessFileExt = "." + fileStrings[1].split(".").pop();
                }
                if (rows.ProcessImg && rows.ProcessImg.includes("|")) {
                    //解析出扩展名和名称，路径
                    const fileStrings = rows.ProcessImg.split("|");
                    formData.value.ProcessImgPath = fileStrings[0];
                    formData.value.ProcessImgTitle = fileStrings[1];
                    formData.value.ProcessImgExt = "." + fileStrings[1].split(".").pop();
                }

            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }

}
const HandleDetail = (row) => {
    if (row.SourceType == 1) {
        router.push({
            path: "./detaillist", query: {
                Id: row.Id,
                batchNo: row.BatchNo,
                path: '/dangerchemicals/waste/auditlist'
            }
        })
    } else if (row.SourceType == 2) {
        router.push({
            path: "/dangerchemicals/scraped/list", query: {
                Id: row.Id,
                batchNo: row.BatchNo,
                path: '/dangerchemicals/waste/auditlist'
            }
        })
    }
}
const regDategeChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const regDateleChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcWasteDisposalAuditFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.SourceType = undefined
    filters.value.Statuz = undefined
    filters.value.RegDatege = undefined
    filters.value.RegDatele = undefined
    RegDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
//提交
const HandleSubmit = () => {
    let paraData = {
        id: wasteId.value,
        statuz: formData.value.statuz,
        remark: formData.value.remark,
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcWasteDisposalAudit(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '审核成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}


//附件上传
const uploadFileData = ref([])
const fileFile = ref()
const uploadRef = ref()

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 12 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {

                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
            console.log('uploadFileData.value', uploadFileData.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)

// 附件图片预览与文件下载
const lookFileListDownload = (filePath, ext, title) => {
    console.log("----2025-07-17 09:08:28--ext--:", ext);
    viewPhotoList.value = [filePath];
    ext = (typeof ext === "string") ? ext.toLowerCase() : "";
    if (ext == ".png" || ext == ".jpg" || ext == ".jpng") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (filePath == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        fileDownload(filePath, title)
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!RegDatele.value) return false;
    return time >= new Date(RegDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="regDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="RegDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="regDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SourceType" filterable clearable placeholder="来源"
                            style="width: 160px" @change="filtersChange">
                            <el-option v-for="item in batchNoList" :key="item.PurchaseBatchNo"
                                :label="item.PurchaseBatchNo" :value="item.PurchaseBatchNo" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Statuz" filterable clearable placeholder="状态" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in batchNoList" :key="item.PurchaseBatchNo"
                                :label="item.PurchaseBatchNo" :value="item.PurchaseBatchNo" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="BatchNo" label="处置批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="UserName" label="申请人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="RegDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DisposalDate" label="处置时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.DisposalDate ? row.DisposalDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="SourceType" label="来源" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.SourceType == 1 ? '危废物' : row.SourceType == 2 ? '危化品' : '' }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <el-tooltip v-if="row.Statuz == 1 || row.Statuz == 2" class="item" effect="dark"
                        :content="'审核意见：' + row.AuditRemark || '无'" placement="top">
                        <span :style="{ color: row.Statuz == 1 ? 'green' : 'red' }">
                            {{ row.Statuz == 1 ? '审核通过' : '审核不通过' }}
                        </span>
                    </el-tooltip>
                    <span v-else :style="{ color: row.Statuz == 3 ? 'green' : '' }">
                        {{ row.Statuz == 3 ? '完成处置' : '待审核' }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="明细清单" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="处置审核" width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 0" type="primary" link @click="HandleEdit(row, 1)">审核</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="处置信息" width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 3" type="primary" link @click="HandleEdit(row, 2)">查看</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="wasteNUm == 1 ? '处置审核' : '查看处置信息'">
            <template #content>
                <el-form style="min-width: 320px; " class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>

                    <div v-if="wasteNUm == 1">
                        <el-form-item label="项目清单：">
                            <el-button type="primary" :icon="View" size="small"
                                @click="HandleDetail(formData)">查看</el-button>
                        </el-form-item>
                        <el-form-item label="审核结果：" prop="statuz">
                            <el-radio-group v-model="formData.statuz">
                                <el-radio :value="1">通过</el-radio>
                                <el-radio :value="2">不通过</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="审核意见：">
                            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="formData.remark"
                                style="width: 400px"></el-input>
                        </el-form-item>
                    </div>
                    <div v-else>
                        <el-form-item label="处置时间：">
                            <span>{{ formData.DisposalDate ? formData.DisposalDate.substring(0, 10) : '--' }}</span>
                        </el-form-item>
                        <el-form-item label="备注：">
                            <span>{{ formData.DisposalRemark }}</span>
                        </el-form-item>
                        <!-- 附件上传 -->
                        <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                            <template #label>
                                <span> {{ item.Name }}： </span>
                            </template>
                            <div class="fileFlex">
                                <div v-if="item.FileCategory == 2982 && formData.ProcessFile"
                                    style="color:#409EFF ;width:200px">
                                    <span style="cursor: pointer;"
                                        @click="lookFileListDownload(formData.ProcessFilePath, formData.ProcessFileExt, formData.ProcessFileTitle)">
                                        {{ formData.ProcessFileTitle }}
                                    </span>
                                </div>
                                <div v-else-if="item.FileCategory == 2983 && formData.ProcessImg"
                                    style="color:#409EFF ;width:200px">
                                    <span style="cursor: pointer;"
                                        @click="lookFileListDownload(formData.ProcessImgPath, formData.ProcessImgExt, formData.ProcessImgTitle)">
                                        {{ formData.ProcessImgTitle }}
                                    </span>
                                </div>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 确认 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>