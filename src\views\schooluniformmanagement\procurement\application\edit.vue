<script setup>
defineOptions({
    name: 'applicationedit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    QuestionFilled, Select, UploadFilled, Delete, Download
} from '@element-plus/icons-vue'
import {
    AttachmentUpload, Getpagedbytype,
} from '@/api/user.js'
import {
    UniformBuyGetById, UniformBuySave, BuyDelattachmentbyid
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { yearDate, foundReturn, fileDownload, tagsListStore, integerLimit, limit } from "@/utils/index.js";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const formData = ref({})
const refForm = ref()
const filePath = ref('')
const uploadFileData = ref([])
const yearDateList = ref([])
const OrganizationFormList = ref([])
const BidPublicList = ref([])
const MethodList = ref([])
const UniformSchemeIdList = ref([])
const purchaseId = ref()
const PurchaseStatuz = ref(0)//审核不通过或退回  状态值
const FilingExplanation = ref('')//审核不通过或退回原因
const ruleForm = {
    PlanYear: [
        { required: true, message: '请选择年度', trigger: 'change' },
    ],
    UniformSchemeId: [
        { required: true, message: '请选择选用批次', trigger: 'change' },
    ],
    PreparationDate: [
        { required: true, message: '请选择采购需求编制日期', trigger: 'change' },
    ],
    IsNeedBidding: [
        { required: true, message: '请选择是否需要招标', trigger: 'change' },
    ],
    Organizational: [
        { required: true, message: '请选择组织形式', trigger: 'change' },
    ],
    BidPublic: [
        { required: true, message: '请选择招标公告公开', trigger: 'change' },
    ],
    Method: [
        { required: true, message: '请选择采购方式', trigger: 'change' },
    ],
    PublicDate: [
        { required: true, message: '请选择公开日期', trigger: 'change' },
    ],
    Num: [
        { required: true, message: '请输入校服数量', trigger: 'change' },
    ],
    BudgetAmount: [
        { required: true, message: '请输入预算金额', trigger: 'change' },
    ],
    PublicMediaName: [
        { required: true, message: '请输入公开媒体名称', trigger: 'change' },
    ],
    AdoptRatio: [
        { required: true, message: '请输入家长同意选用校服比例', trigger: 'change' },
    ],
    OrganizationRatio: [
        { required: true, message: '请输入选用组织家长和学生代表比例', trigger: 'change' },
    ],
}

onMounted(() => {
    yearDateList.value = yearDate()
    GetpagedbytypeUser()
    if (route.query.isTagRouter) {
        if (route.query.id) {
            purchaseId.value = route.query.id
            if (route.query.id == '0') {
                purchaseId.value = 0
                nextTick(() => {
                    refForm.value.resetFields()

                })
            }
        }
        UniformBuyGetByIdUser(purchaseId.value)
    }
})

onActivated(() => {
    // 修改tag标签名称########################
    if (route.query.title) {
        // 修改pinia数据
        userStore.$patch(state => {
            state.pageTitleObj.applicationTitle = route.query.title
        })
    }
    let tagsList = userStore.tagsList
    // 使用forEach遍历数组  修改tag标签名称
    tagsList.forEach(item => {
        if (item.path == route.path) {
            // 如果path匹配，则修改title  
            item.title = userStore.pageTitleObj.applicationTitle;
        }
    });
    userStore.setTagsList(tagsList)
    // ########################
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)

    fileIdList.value = []
    if (route.query.id) {
        purchaseId.value = route.query.id
        if (route.query.id == '0') {
            purchaseId.value = 0
            nextTick(() => {
                refForm.value.resetFields()

            })
        }
    }
    nextTick(() => {
        if (!route.query.isTagRouter) {
            UniformBuyGetByIdUser(purchaseId.value)
        }
    })
})

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 701 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const UniformBuyGetByIdUser = (id) => {
    UniformBuyGetById({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other, footer } = res.data.data
            uploadFileData.value.forEach(item => {
                item.fileLChildList = []
                item.categoryList = []
            })
            fileIdList.value = []
            refForm.value.resetFields()
            if (rows) {
                formData.value = rows
                if (rows.BidPublic === 0) {
                    formData.value.BidPublic = undefined
                } else {
                    formData.value.BidPublic = String(rows.BidPublic)
                }
                if (rows.Organizational === 0) {
                    formData.value.Organizational = undefined
                } else {
                    formData.value.Organizational = String(rows.Organizational)
                }
                if (rows.Method === 0) {
                    formData.value.Method = undefined
                } else {
                    formData.value.Method = String(rows.Method)
                }
                FilingExplanation.value = rows.FilingExplanation || ''
                PurchaseStatuz.value = rows.PurchaseStatuz
            } else {
                formData.value = {}
                FilingExplanation.value = ''
            }
            let categoryList = footer || [];//附件集合
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
            filePath.value = other.filePath;//文件路径
            BidPublicList.value = other.listBidPublic;//招标公告公开
            MethodList.value = other.listMethod;//采购方式
            OrganizationFormList.value = other.listOrganizational;//采购年度
            UniformSchemeIdList.value = other.listUniformSchemeId;//选用批次
            UniformSchemeIdList.value.unshift({ value: '0', label: '无' })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//提交
const HandleSubmit = (e) => {
    if (!formData.value.PlanYear) {
        ElMessage.error('请先选择年度')
        return
    }
    if (e == 1) {
        // 提交
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            // 判断必传的附件是否已传 
            let fileData = []
            if (formData.value.IsNeedBidding == 1) {
                fileData = uploadFileData.value
            } else {
                fileData = uploadFileData.value.filter(item => item.FileCategory != 107001)
            }
            let found = foundReturn(fileData)
            if (found) {
                return
            }
            UniformBuySaveUser(purchaseId.value || undefined, e)
        })
    } else {
        // 保存不校验 
        UniformBuySaveUser(purchaseId.value || undefined, e)
    }
}
// 采购管理添加保存
const UniformBuySaveUser = (id, ButtonType) => {

    let OrganizationalName = ''
    if (formData.value.Organizational) {
        OrganizationalName = OrganizationFormList.value.find(t => t.value == formData.value.Organizational).label
    }
    let MethodName = ''
    if (formData.value.Method) {
        MethodName = MethodList.value.find(t => t.value == formData.value.Method).label
    }

    let pramsData = {
        Id: id,
        ButtonType: ButtonType,//（0：保存，1：提交）
        PlanYear: formData.value.PlanYear,
        UniformSchemeId: formData.value.UniformSchemeId,
        PreparationDate: formData.value.PreparationDate || undefined,
        Num: Number(formData.value.Num) || undefined,
        BudgetAmount: Number(formData.value.BudgetAmount) || undefined,
        IsNeedBidding: formData.value.IsNeedBidding,
        Organizational: Number(formData.value.Organizational) || undefined,
        OrganizationalName: OrganizationalName,
        BidPublic: Number(formData.value.BidPublic) || undefined,
        Method: Number(formData.value.Method) || undefined,
        MethodName: MethodName,
        OrganizationRatio: formData.value.OrganizationRatio,
        AdoptRatio: formData.value.AdoptRatio,
        PublicDate: formData.value.PublicDate || undefined,
        PublicMediaName: formData.value.PublicMediaName,
        Remark: formData.value.Remark,
        ListAttachmentId: fileIdList.value.map(t => t.Id)
    }
    console.log('pramsData', pramsData)

    UniformBuySave(pramsData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '提交成功')
            formData.value.PurchaseNo = res.data.data.rows.PurchaseNo
            purchaseId.value = res.data.data.rows.Id
            if (ButtonType == 1) {
                // 删除当前tag标签
                let tagsList = userStore.tagsList
                tagsList = tagsList.filter(t => t.path != route.path)
                userStore.setTagsList(tagsList)
                router.push({ path: "./list" })
            } else {
                UniformBuyGetByIdUser(purchaseId.value)
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入保留两位小数
const limitInput = (val, name, len) => {
    formData.value[name] = limit(val, len);
}

//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
// 模板下载
const HandleDownload = () => {
    fileDownload(filePath.value);
}
// /////    附件处理      /////
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.categoryList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: item.FileCategory }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    BuyDelattachmentbyid({ id: purchaseId.value, attid: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}


</script>
<template>
    <el-form style="width: 100%;min-width: 560px;" :inline="true" class="mobile-box" @submit.prevent ref="refForm"
        :model="formData" :rules="ruleForm" label-width="260px" status-icon>
        <el-form-item label=" " label-width="100px" style="width: 100%;  ">
            <span style="color: #E6A23C;">是否需要招标：是指本次采购的校服是否需要组织招投标活动，如果是合同续签则无需招投标！</span>
        </el-form-item>
        <el-form-item label=" " label-width="100px" style="width: 100%;"
            :class="PurchaseStatuz == 11 || PurchaseStatuz == 21 ? '' : 'isBorder'">
            <el-button type="primary" plain :icon="Download" @click="HandleDownload">校服清单模版下载</el-button>
            <span style="color: #E6A23C;padding-left: 10px;">请在此处下载需上传校服清单的模板！</span>
        </el-form-item>
        <el-form-item v-if="PurchaseStatuz == 11 || PurchaseStatuz == 21"
            :label="PurchaseStatuz == 11 ? '审核不通过原因：' : '退回原因：'"
            style="width: 100%; border-bottom: 1px dashed #E4E7ED; ">
            <span style="color: #F56C6C;padding-left: 10px;">{{ FilingExplanation }}</span>
        </el-form-item>
        <el-form-item label="年度：" prop="PlanYear" class="formItem">
            <el-select v-model="formData.PlanYear" placeholder="是指采购年度" class="item_content">
                <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="采购批次：" class="formItem">
            <el-input v-model="formData.PurchaseNo" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="选用批次：" prop="UniformSchemeId" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="如使用本平台征求家长意见的，请选择对应的编号，否则选择“无”" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 选用批次： </span>
            </template>
            <el-select v-model="formData.UniformSchemeId" class="item_content">
                <el-option v-for="item in UniformSchemeIdList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="采购需求编制日期：" prop="PreparationDate" class="formItem">
            <el-date-picker type="date" v-model="formData.PreparationDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="校服数量(套)：" prop="Num" class="formItem">
            <el-input v-model="formData.Num" @input="integerLimitInput($event, 'Num')" placeholder="与需采购的学生数一致"
                class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="预算金额(元)：" prop="BudgetAmount" class="formItem">
            <el-input v-model="formData.BudgetAmount" @input="limitInput($event, 'BudgetAmount', 4)"
                placeholder="是指校服采购的总预算金额" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="选用组织家长和学生代表比例(%)：" prop="OrganizationRatio" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="家长和学生人数不少于80%" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 选用组织家长和学生代表比例(%)： </span>
            </template>
            <el-input v-model="formData.OrganizationRatio" @input="limitInput($event, 'OrganizationRatio')"
                class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="家长同意选用校服比例：" prop="AdoptRatio" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="是否选用校服应征得2/3以上家长同意" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 家长同意选用校服比例(%)： </span>
            </template>
            <el-input v-model="formData.AdoptRatio" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="是否需要招标：" prop="IsNeedBidding" @input="limitInput($event, 'IsNeedBidding')"
            class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="是指本次采购的校服是否需要组织招投标活动，如果是合同续签则无需招投标！" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 是否需要招标： </span>
            </template>
            <el-radio-group v-model="formData.IsNeedBidding">
                <el-radio :value="1"> 是</el-radio>
                <el-radio :value="2"> 否</el-radio>
            </el-radio-group>
        </el-form-item>

        <el-form-item label="组织形式：" prop="Organizational" class="formItem" v-if="formData.IsNeedBidding == 1">
            <template #label>
                <el-tooltip class="item" effect="dark" content="统一采购：教育行政部门为采购主体，其它是学校为采购主体" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 组织形式： </span>
            </template>
            <el-select v-model="formData.Organizational" class="item_content">
                <el-option v-for="item in OrganizationFormList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="招标公告公开：" prop="BidPublic" class="formItem" v-if="formData.IsNeedBidding == 1">
            <el-select v-model="formData.BidPublic" class="item_content">
                <el-option v-for="item in BidPublicList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="采购方式：" prop="Method" class="formItem" v-if="formData.IsNeedBidding == 1">
            <el-select v-model="formData.Method" class="item_content">
                <el-option v-for="item in MethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="公开日期：" prop="PublicDate" class="formItem"
            v-if="formData.IsNeedBidding == 1 && formData.BidPublic == 1">
            <el-date-picker type="date" v-model="formData.PublicDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="公开媒体名称：" prop="PublicMediaName" class="formItem"
            v-if="formData.IsNeedBidding == 1 && formData.BidPublic == 1">
            <el-input v-model="formData.PublicMediaName" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%;">
            <el-input type="textarea" v-model="formData.Remark" :maxlength="300" show-word-limit
                :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
        </el-form-item>
        <!-- 附件上传 -->
        <template v-for="(item, index) in uploadFileData" :key="index">
            <el-form-item class="formItem"
                v-if="item.FileCategory != 107001 || item.FileCategory == 107001 && formData.IsNeedBidding == 1">
                <template #label>
                    <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                    <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                        <div>
                            <el-icon color="#E6A23C" class="tipIcon">
                                <QuestionFilled />
                            </el-icon>
                        </div>
                    </el-tooltip>
                    <span> {{ item.Name }}： </span>
                </template>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                    :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                    :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                    <el-button type="success" size="small" :icon="UploadFilled"
                        @click="MaxFileNumberClick(item)">上传</el-button>
                </el-upload>
                <div class="fileFlex">
                    <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                        <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                            <Delete />
                        </el-icon>
                        <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                            {{ itemCate.Title }}{{ itemCate.Ext }}
                        </span>
                    </div>
                    <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                        <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                            <Delete />
                        </el-icon>
                        <span style="cursor: pointer;" @click="fileListDownload(itemChild, item.fileLChildList)">
                            {{ itemChild.Title }}{{ itemChild.Ext }}
                        </span>
                    </div>
                </div>
            </el-form-item>
        </template>

        <el-form-item label=" " style="width: 100%;">
            <el-button type="primary" :icon="Select" @click="HandleSubmit(0)">保存</el-button>
            <el-button type="primary" :icon="Select" @click="HandleSubmit(1)">提交</el-button>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-right: 0;
}

.item_content {
    // width: 60% !important;
}

:deep(.el-date-editor.el-input) {
    width: 100% !important;
}

.formItem {
    width: 50%;
}

.isBorder {
    border-bottom: 1px dashed #E4E7ED;
    padding-bottom: 10px;
}

@media (max-width: 768px) {
    .formItem {
        width: 100%;
    }
}
</style>