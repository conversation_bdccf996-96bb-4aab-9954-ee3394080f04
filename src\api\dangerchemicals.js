import request from '@/utils/request.js'

// 获取配置文件 hyun/anon/dangerchemicals
export const AnonDangerchemicals = (params) => {
  return request.post('/api/hyun/anon/dangerchemicals', params)
}

// 危化品领用
// 领用填报(标准版) dangerchemicals/apply/fill   ###############################################
// 平台设置：根据TypeCode获取上级单位配置信息    (string moduleCode, string typeCode)
export const BconfigSetgetPunit = (params) => {
  return request.post('/api/hyun/bconfigset/bconfigsetgetpunit', null, {
    params: params
  })
}
// 申请添加 (FromBody)
export const DcapplyAdd = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyadd', params)
}
// 申请批量添加 (FromBody)
export const DcapplyBatchAdd = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplybatchadd', params)
}
// 批量领用申请   (FromBody)
export const DcapplyDirectApply = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplydirectapply', params)
}
// 领用填报列表   (FromBody)
export const DcapplyFillListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyfilllistfind', params)
}
// 获取同领用人、发放人下拉框数据-查询 (int type)
export const DcapplyGrantUserComboGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcapplygrantusercomboget', null, {
    params: params
  })
}
// 获取学校物品类别选择-查询   DcschoolCatalogFindCommonuseAll

// 领用车(标准版)  dangerchemicals/apply/carlist   ###############################################
// 平台设置：根据TypeCode获取上级单位配置信息   BconfigSetgetPunit

// 领用待审核列表   (FromBody)
export const DcapplyCarListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplycarlistfind', params)
}
// 根据Id查询申领数据  (long id)
export const DcapplyFindByid = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyfindbyid', null, {
    params: params
  })
}
// 申领新增修改   (FromBody)
export const DcapplyInsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyinsertupdate', params)
}
// 批量领用申请   (string ids, DateTime useTime, long withUserId)
export const DcapplyBatchApply = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplybatchapply', null, {
    params: params
  })
}
// 根据Id申领删除    (long id)
export const DcapplyDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplydelete', null, {
    params: params
  })
}
// 获取库存数量  (long schoolCatalogId, long schoolMaterialModelId, long schoolMaterialBrandId)
export const DcapplyGetStocknum = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplygetstocknum', null, {
    params: params
  })
}
// 获取同领用人、发放人下拉框数据-查询  DcapplyGrantUserComboGet

// 根据型号获取可用品牌-查询  (long schoolCatalogId, long modelId)
export const DcschoolCatalogBrandGetbyModelidv2 = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogbrandgetbymodelidv2', null, {
    params: params
  })
}
// 物品分类下拉框数据-查询
export const DcschoolCatalogComboboxGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogcomboboxget', params)
}
// 物品分类搜索-查询     (FromBody)
export const DcschoolCatalogCommonuseFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogcommonusefind', params)
}
// 获取学校物品类别选择-查询   DcschoolCatalogFindCommonuseAll

// 获取可使用的物品型号-查询   (long id)
export const DcschoolCatalogModelGetv2 = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogmodelgetv2', null, {
    params: params
  })
}
// 领用查看 dangerchemicals/apply/detailview    ###############################################
// 申请查看   (long id,int viewType=0)
export const DcapplyView = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyview', null, {
    params: params
  })
}
// 已填报物品(标准版) dangerchemicals/applyed/list  ###############################################
// 已填报列表     (FromBody)
export const DcapplyListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplylistfind', params)
}
// 申请确认      (FromBody)
export const DcapplyAfterConfirm = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyafterconfirm', params)
}
// 更新退回数量    (long id, decimal backNum)
export const DcapplyUpdateBackNum = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyupdatebacknum', null, {
    params: params
  })
}
// 根据Id查询申领数据  DcapplyFindByid

// 申领新增修改   DcapplyInsertUpdate

// 领用申请    (FromBody)
export const DcapplyApply = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyapply', params)
}
// 根据Id申领删除  DcapplyDelete

// 根据Id获取领用审批信息   (long id)
export const DcApplyNoPassReasonGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplynopassreasonget', null, {
    params: params
  })
}
// 查询当前领用状态的审核人   (long id)
export const DcApplyLookAuditUser = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplylookaudituser', null, {
    params: params
  })
}
// 批量撤回已填报危化品信息  (string ids)
export const DcApplyRevoke = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyrevoke', null, {
    params: params
  })
}
// 获取退回原因-查询   (long id)
export const DcPurchaseNoPassReasonGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasenopassreasonget', null, {
    params: params
  })
}
// 根据型号获取可用品牌-查询 DcschoolCatalogBrandGetbyModelidv2

// 物品分类下拉框数据-查询   DcschoolCatalogComboboxGet

// 物品分类搜索-查询   DcschoolCatalogCommonuseFind

// 获取学校物品类别选择-查询 DcschoolCatalogFindCommonuseAll

// 获取可使用的物品型号-查询 DcschoolCatalogModelGetv2

// 废弃物基础分类列表-查询
export const DcWasteBaseFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastebasefind', params)
}
// // 领用填报(简易版)  dangerchemicals/apply/filleasy   #############################################
// 平台设置：根据TypeCode获取配置信息   BconfigsetGet

// 平台设置：根据TypeCode获取上级单位配置信息 BconfigSetgetPunit

// 简易申领列表     (FromBody)
export const DcApplyEasyListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyeasylistfind', params)
}
// 简易领用    (FromBody)
export const DcApplyEasyApply = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyeasyapply', params)
}
// 获取同领用人、发放人下拉框数据-查询  DcapplyGrantUserComboGet

// 获取维护品二级分类-查询   DccatalogGetClassTwo

// 已领用物品(简易版)   dangerchemicals/applyed/listeasy  #############################################

// 已填报列表    DcapplyListFind

// 根据Id申领删除 DcapplyDelete

// // 领用审核(标准版) ##########################################################
// // 待审核物品   dangerchemicals/apply/auditlist@p=20   #############################################
// 通用领用待审核列表    (FromBody)
export const DcApplyAuditListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyauditlistfind', params)
}
// 批量领用确认 (string ids, string remark)
export const DcApplyBatchConfirm = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplybatchconfirm', null, {
    params: params
  })
}
// 通用领用待审核列表  DcApplyAuditListFind
// 领用批量审核  (FromBody)
export const DcApplyBatchAudit = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplybatchaudit', params)
}

// // 已审核物品  dangerchemicals/apply/auditedlist@p=20  #############################################
// 领用通用已审核列表  (FromBody)
export const DcApplyAuditedListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyauditedlistfind', params)
}

// // 领用审核 dangerchemicals/apply/audit    #############################################
// 申请查看    DcapplyView

// 领用审核  (FromBody)
export const DcApplyAudit = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyaudit', params)
}
// // 危化品配发(标准版)
// // 待配货物品   dangerchemicals/apply/auditlist@p=30     #############################################
// 通用领用待审核列表   DcApplyAuditListFind

// 批量领用确认   DcApplyBatchConfirm

// 领用批量审核    DcApplyBatchAudit

// // 单条配货 dangerchemicals/apply/confirm  #############################################
// 申请查看    DcapplyView

// 通用领用待审核列表     DcApplyAuditListFind

// 获取学校物品库列表  (FromBody)
export const DcApplySchoolMaterialFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyschoolmaterialfind', params)
}
// 领用配货  (FromBody)
export const DcApplyAdjust = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyadjust', params)
}
// 根据Id删除申领配货信息   (long id)
export const DcApplyConfirmDetailDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetaildelete', null, {
    params: params
  })
}
// 领用配货数据列表   (FromBody)
export const DcApplyAdjustFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyadjustfind', params)
}
// 领用确认 (long id, decimal num, string remark)
export const DcApplyConfirm = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyconfirm', null, {
    params: params
  })
}
// // 待发放物品 dangerchemicals/apply/givelist  #############################################
// 平台设置：根据TypeCode获取上级单位配置信息   BconfigSetgetPunit

// 短信发送   (long id, string mobile, int messageType)
export const DcApplyConfirmDetailResendCode = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetailresendcode', null, {
    params: params
  })
}
// 领用发放    (FromBody)
export const DcApplyConfirmDetailCollar = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetailcollar', params)
}
// 学校领用列表     (FromBody)
export const DcApplyFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyfind', params)
}
// 根据Id查询领用详情信息  (long id)
export const DcApplyConfirmDetailGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyconfirmdetailgetbyid', null, {
    params: params
  })
}
// 获取同领用人、发放人下拉框数据-查询   DcapplyGrantUserComboGet

// // 已发放物品 dangerchemicals/apply/givedlist@t=1    #############################################
//  学校领用列表   DcApplyFind

// // 打印领用单  dangerchemicals/apply/print   #############################################
// 获取领用清单打印列表     (FromBody)
export const DcApplyPrintListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyprintlistfind', params)
}
// // 危化品采购
// // 采购填报  dangerchemicals/purchase/fill    #############################################
// 物品单条加入采购车-保存   (FromBody)
export const DcPurchaseAdd = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseadd', params)
}
// 物品批量添加采购车-保存   (FromBody)
export const DcPurchaseBatchAdd = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasebatchadd', params)
}
// 从基础库添加列表-查询  (FromBody)
export const DcPurchaseByCatalogFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasebycatalogfind', params)
}
// 获取学校物品类别选择-查询 DcschoolCatalogFindCommonuseAll

// // 采购车 dangerchemicals/purchase/list  #############################################
// 物品单条加入采购车-保存   DcPurchaseAdd

// 物品采购申请-保存  (long purchaseOrderId, string ids)
export const DcPurchaseApply = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseapply', null, {
    params: params
  })
}
// 物品采购清单-删除 (long id)
export const DcPurchaseListDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaselistdelete', null, {
    params: params
  })
}
// 采购清单列表-查询 (FromBody)
export const DcPurchaseListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaselistfind', params)
}
// 根据Id查询采购清单详细数据-查询  (long id)
export const DcPurchaseListFindById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaselistfindbyid', null, {
    params: params
  })
}
// 采购物品清单修改-保存   (FromBody)
export const DcPurchaseListUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaselistupdate', params)
}
// 获取退回原因-查询    DcPurchaseNoPassReasonGet

// 物品分类下拉框数据-查询   DcschoolCatalogComboboxGet

// 物品分类搜索-查询   DcschoolCatalogCommonuseFind

// 获取学校物品类别选择-查询 DcschoolCatalogFindCommonuseAll

// 获取物品型号-查询 (long schoolCatalodId)
export const DcSchoolMaterialModelGetByCatalogId = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelgetbycatalogid', null, {
    params: params
  })
}
// // 已填报列表  dangerchemicals/purchase/orderlist    #############################################

// 物品采购单-删除    (long id)
export const DcPurchaseOrderDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseorderdelete', null, {
    params: params
  })
}
// 已填报列表-查询    (FromBody)
export const DcPurchaseOrderFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseorderfind', params)
}
// 物品采购-撤销  (long id)
export const DcPurchaseRevoke = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaserevoke', null, {
    params: params
  })
}

// 查看采购计划-详情  (long id)
export const DcPurchaseEndGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseendgetbyid', null, {
    params: params
  })
}

// // 采购计划  dangerchemicals/purchase/endlist    #############################################
// 采购已生成计划列表-查询   (FromBody)
export const DcPurchaseEndFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseendfind', params)
}

// 物品明细列表  dangerchemicals/purchase/detaillist  #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 采购物品明细列表-查询   (FromBody)
export const DcPurchaseDetailListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasedetaillistfind', params)
}
// 物品采购清单-删除  DcPurchaseListDelete

// 采购审核
// // 待审核列表 dangerchemicals/purchase/auditlist@p=10     #############################################
// 通用物品待审批列表-查询    (FromBody)
export const DcPurchaseAuditFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseauditfind', params)
}
// // 已审核列表 dangerchemicals/purchase/auditedlist@p=10    #############################################
// 通用物品已审批列表-查询   (FromBody)
export const DcPurchaseAuditedFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseauditedfind', params)
}
// 通用物品采购审批-撤销  (long id, long purchaseOrderId)
export const DcPurchaseWithdraw = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasewithdraw', null, {
    params: params
  })
}
// // 采购审批
// // 待审批列表  dangerchemicals/purchase/auditlist@p=20  #############################################
// 通用物品待审批列表-查询   DcPurchaseAuditFind

// // 已审批列表 dangerchemicals/purchase/auditedlist@p=20  #############################################
// 通用物品已审批列表-查询  DcPurchaseAuditedFind

// 通用物品采购审批-撤销  DcPurchaseWithdraw

// // 采购审核  dangerchemicals/purchase/audit #############################################
// 附件上传 UploadPostfile

// 通用物品采购审批-审核   (FromBody)
export const DcPurchaseAudit = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseaudit', params)
}
// // 采购查看 dangerchemicals/purchase/detailview  #############################################

// // 危化品入库
// // 按计划入库 dangerchemicals/input/byplanlist   #############################################
// 采购已生成计划列表-查询   DcPurchaseEndFind

// 结束入库-设置 (long id)
export const DcPurchaseOrderFinishInputStatuz = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseorderfinishinputstatuz', null, {
    params: params
  })
}
// // 存量录入  dangerchemicals/school/itemstorage    #############################################
// 平台设置：根据TypeCode获取上级单位配置信息 BconfigSetgetPunit

// 附件上传
export const UploadPostfile = (params) => {
  return request.post('/api/hyun/upload/postfile', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取学校供应商信息-查询   (FromBody)
export const DcCompanyComboxFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccompanycomboxfind', params)
}
// 存放地点-查询   (FromBody)
export const DcDepositAddressFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcdepositaddressfind', params)
}
// 获取存储柜地址下拉列表-查询   (FromBody)  DcCabinetAddressGet

// 物品分类搜索-查询   DcschoolCatalogCommonuseFind

// 获取学校物品类别选择-查询 DcschoolCatalogFindCommonuseAll

// 物品-保存    (FromBody)
export const DcSchoolMaterialInsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialinsertupdate', params)
}
// 获取物品型号-查询  DcSchoolMaterialModelGetByCatalogId

// 获取物品可使用的品牌-查询  (long schoolCatalogId, long modelId)
export const DcSchoolModelBrandGetBrand = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmodelbrandgetbrand', null, {
    params: params
  })
}
// // 入库记录 dangerchemicals/school/materialstoragelog    #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 获取学校供应商信息-查询  DcCompanyComboxFind

// 入库记录变更记录信息-查询 (long SchoolMaterialId, long PurchaseListId)
export const DcSchoolCatalogRecord = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogrecord', null, {
    params: params
  })
}
// 存量预警查看-查询     (FromBody)
export const DcScrapListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcscraplistfind', params)
}
// // 采购入库审核   dangerchemicals/school/purchasestorageauditlist     #############################################
// 采购已生成计划列表-查询   DcPurchaseEndFind

// // 存量入库审核 dangerchemicals/school/itemstorageauditlist   #############################################
// 附件上传  UploadPostfile

// 获取学校供应商信息-查询  DcCompanyComboxFind

// 存放地点-查询   DcDepositAddressFind

// 获取采购批次下拉框数据-查询
export const DcPurchaseBatchNoFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasebatchnofind', params)
}
// 根据Id采购申请单-查询   (long id)
export const DcPurchaseOrderGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseordergetbyid', null, {
    params: params
  })
}
// 采购申请单上传公安报备文件-保存 (long id, string path)
export const DcPurchaseOrderUploadFile = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchaseorderuploadfile', null, {
    params: params
  })
}
// 物品分类搜索-查询   DcschoolCatalogCommonuseFind

// 获取学校物品类别选择-查询 DcschoolCatalogFindCommonuseAll

// 批量审核-审核     (FromBody)
export const DcSchoolMaterialBatchAudit = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialbatchaudit', params)
}
// 学校库基础库,物品汇总列表-查询    (FromBody)
export const DcSchoolMaterialCatalogFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialcatalogfind', params)
}
// // 根据Id获取"学校物品库"信息-查询  (long Id)
export const DcSchoolMaterialCatalogGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialcataloggetbyid', null, {
    params: params
  })
}
// 入库审核物品删除-删除  (long Id)
export const DcSchoolMaterialDeleteById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialdeletebyid', null, {
    params: params
  })
}
// 物品-保存   DcSchoolMaterialInsertUpdate

// 获取物品型号-查询   DcSchoolMaterialModelGetByCatalogId
// 获取存储柜地址下拉列表-查询   DcCabinetAddressGet
// // 入库审核 下一条显示-查询   (long Id)
export const DcSchoolMaterialNext = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialnext', null, {
    params: params
  })
}
// 获取物品可使用的品牌-查询 DcSchoolModelBrandGetBrand

// // 计划入库 dangerchemicals/input/materialbyplan    #############################################
// 本单位信息管理-单位信息
export const UnitGetInfo = (params) => {
  return request.post('/api/hyun/punit/unitgetinfo', params)
}
// 附件上传  UploadPostfile

// 获取存储柜地址下拉列表-查询
export const DcCabinetAddressGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccabinetaddressget', params)
}
// 获取学校供应商信息-查询  DcCompanyComboxFind

// 存放地点-查询  DcDepositAddressFind

// 按计划录入入库列表-查询        (FromBody)
export const DcInputMaterialByPlanFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcinputmaterialbyplanfind', params)
}
// 按计划录入入库-保存    (FromBody)
export const DcMaterialByPlanInput = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcmaterialbyplaninput', params)
}
// 按计划录入-保存   (FromBody)
export const DcMaterialByPlanSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcmaterialbyplansave', params)
}
// 清除物品入库保存的临时数据-删除   (long Id)
export const DcSchoolMaterialTempDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialtempdelete', null, {
    params: params
  })
}

// 获取物品可使用的品牌-查询   DcSchoolModelBrandGetBrand

// // 打印入库单 dangerchemicals/school/storagelogprint   #############################################
// 存量预警查看-查询  DcScrapListFind

// 入库记录打印，更新打印状态-设置 (FromBody)
export const DcStorageLogPrintUpdateStatuz = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcstoragelogprintupdatestatuz', params)
}

// // 存量数据导入 dangerchemicals/import/schoolmaterial   #############################################

// // 上报公安 dangerchemicals/report/securitylist    #############################################
// 本单位信息管理-单位信息   UnitGetInfo

// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 获取学校供应商信息-查询 DcCompanyComboxFind

// 存量预警查看-查询  DcScrapListFind

// // 危化品库管理
// // 危化品存量库  dangerchemicals/school/materialcataloglist    #############################################
// 存放地点-查询  DcDepositAddressFind

// 批量修改存放地点-设置 (string ids, long addressId, string cabinetAddress)
export const DcMaterialAddressBatchEdit = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcmaterialaddressbatchedit', null, {
    params: params
  })
}
// 学校库基础库,物品汇总列表-查询   (FromBody)
export const DcSchoolMaterialStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialstatisticsfind', params)
}
// 存量预警查看-查询 DcScrapListFind

// // 领用发放(简易版)  dangerchemicals/apply/granteasy   #############################################
// 通用领用待审核列表  DcApplyAuditListFind

// 简易发放   (long id, decimal num, int isNeedSendMessage)
export const DcApplyEasyGrant = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyeasygrant', null, {
    params: params
  })
}
// 物品规格型号，获取实体-查询  (long id)
export const DcSchoolMaterialModelGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelgetbyid', null, {
    params: params
  })
}
// // 已领用物品(简易版) dangerchemicals/apply/givedlist@t=2    #############################################
// 学校领用列表   DcApplyFind

// // 领用退回(简易版) dangerchemicals/apply/backlisteasy    #############################################
// 学校领用列表   DcApplyFind

// 简易退回 /api/hyun/dcdangerchemicalsapply/dcapplyeasyback
export const DcApplyEasyBack = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcapplyeasyback', null, {
    params: params
  })
}

// // 领用退回(标准版)  dangerchemicals/material/backlist    #############################################
// 危化品退回   (long id, int isMayUse)
export const DcMaterialRevertBackConfirm = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcmaterialrevertbackconfirm', null, {
    params: params
  })
}
// 学校危化品退回列表     (FromBody)
export const DcMaterialBackFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicalsapply/dcmaterialbackfind', params)
}
// // 采购退货  dangerchemicals/school/materialbacklist  #############################################

// 退货、盘点、报废审核表新增-保存    (FromBody)
export const DcMaterialsNumAuditAdd = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcmaterialsnumauditadd', params)
}
// 获取采购批次下拉框数据-查询  DcPurchaseBatchNoFind

// 危化品存量库-查询    (FromBody)
export const DcSchoolMaterialOptFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialoptfind', params)
}
// // 已退货清单 dangerchemicals/school/materialbacklog    #############################################
// 获取学校供应商信息-查询   DcCompanyComboxFind

// 已退货清单列表-查询     (FromBody)
export const DcSchoolMaterialBackLogFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialbacklogfind', params)
}
// // 打印退货单  dangerchemicals/school/materialbacklogprint   #############################################
// 已退货清单列表-查询 DcSchoolMaterialBackLogFind

// // 超量预警查看(标准版) dangerchemicals/warning/stocklist  #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 物品预警获取实体信息-查询  DcschoolMaterialModelconfigGetByid

// 存量预警查看-查询  DcScrapListFind

// 物品超量预警列表-查询    (FromBody)
export const DcWarningStockListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwarningstocklistfind', params)
}
// 超期预警查看(标准版) dangerchemicals/warning/validitylist #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 用户库基础型号库管理启用、禁用-设置  (string ids, int statuz)
export const DcSchoolMaterialModelSetStatuz = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelsetstatuz', null, {
    params: params
  })
}
// 物品超期预警列表-查询    (FromBody)
export const DcWarningValidityListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwarningvaliditylistfind', params)
}
// 库存盘点(标准版) dangerchemicals/inventory/list   #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 退货、盘点、报废审核表新增-保存  DcMaterialsNumAuditAdd

// 物品报废-获取物品实体方法-查询   (long id)
export const DcSchoolMaterialGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialgetbyid', null, {
    params: params
  })
}
// 危化品存量库-查询  DcSchoolMaterialOptFind

// 库存盘点记录(标准版) dangerchemicals/inventory/recordlist  #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 存量预警查看-查询    (FromBody)
export const DcInventoryRecordFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcinventoryrecordfind', params)
}
// 危化品报废 dangerchemicals/scrap/list  #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 设置是否可二次使用-设置   (long id, int isMayUse)
export const DcMaterialSetIsMayUse = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcmaterialsetismayuse', null, {
    params: params
  })
}
// 退货、盘点、报废审核表新增-保存  DcMaterialsNumAuditAdd

// 物品报废-获取物品实体方法-查询   DcSchoolMaterialGetById

// 危化品存量库-查询  DcSchoolMaterialOptFind

// 已报废清单 dangerchemicals/scraped/list   #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 报废危化品处置申请-保存  (string ids)
export const DcScrapDisposal = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcscrapdisposal', null, {
    params: params
  })
}
// 存量预警查看-查询    (FromBody)
export const DcScrapedListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcscrapedlistfind', params)
}
// 退货审核 dangerchemicals/materials/numaudit@t=3 #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 盘点、退货、报废审核表-查询 (FromBody)
export const DcMaterialsNumAuditFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcmaterialsnumauditfind', params)
}

// 物品退货-保存  (long id, int statuz)
export const DcSchoolMaterialReturnLibrary = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialreturnlibrary', null, {
    params: params
  })
}
// 盘点审核(标准版) dangerchemicals/materials/numaudit@t=1 #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 物品盘点-盘盈，盘亏-保存  (long id, int statuz)
export const DcInventorySave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcinventorysave', null, {
    params: params
  })
}

// 盘点、退货、报废审核表-查询  DcMaterialsNumAuditFind

// 报废审核 dangerchemicals/materials/numaudit@t=2 #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 盘点、退货、报废审核表-查询  DcMaterialsNumAuditFind

// 物品报废-保存  (long id, int statuz, int wasetStatuz)
export const DcScrapSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicalszb/dcscrapsave', null, {
    params: params
  })
}

// 打印领用单 dangerchemicals/apply/print #############################################
// 获取领用清单打印列表   DcApplyPrintListFind

// 危废物处置
// 危废物存量库 dangerchemicals/waste/list #############################################
// 查询是否存在废弃物-查询
export const DcWasetDisposalGetIsExists = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwasetdisposalgetisexists', params)
}
// 废弃物处置申请-保存     (FromBody)
export const DcWasetDisposalSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwasetdisposalsave', params)
}
// 废弃物处置填报列表-查询
export const DcWasteDisposalFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalfind', params)
}
// 已处置危废物 dangerchemicals/waste/disposallisted #############################################
// 废弃物明细列表-查询    DcWasteDisposalDetailFind

// 获取废弃物分类-查询  (long pid, long id, int depth)
export const DcWasteClassGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwasteclassget', null, {
    params: params
  })
}
// 待处置信息填报  dangerchemicals/waste/reportlist #############################################
// 附件上传  UploadPostfile

// 废弃物处置审核列表-查询    (FromBody)
export const DcWasteDisposalAuditFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalauditfind', params)
}
// 根据Id查询废弃物处置信息-查询 (long id)
export const DcWasteDisposalGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalgetbyid', null, {
    params: params
  })
}
// 处置信息-填报  (long id, DateTime disposalDate, string remark, string processFile, string processImg, string companyName)
export const DcWasteDisposalReport = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalreport', params)
}
// 危废物明细  dangerchemicals/waste/detaillist #############################################
// 废弃物明细列表-查询   (FromBody)
export const DcWasteDisposalDetailFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposaldetailfind', params)
}

// 处置审核 dangerchemicals/waste/auditlist #############################################
// 处置-审核  (long id, int statuz, string remark)
export const DcWasteDisposalAudit = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalaudit', null, {
    params: params
  })
}
// 废弃物处置审核列表-查询 DcWasteDisposalAuditFind

// 根据Id查询废弃物处置信息-查询 DcWasteDisposalGetById

// 危废物详情  dangerchemicals/waste/recordlist #############################################
// 实验后废弃物明细列表-查询  (FromBody)
export const DcWasteRecordFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwasterecordfind', params)
}

// 安全保障要求
// 制度与队伍建设  dangerchemicals/system/teambuild #############################################
// 附件上传  UploadPostfile

// 删除附件-删除  (long id)
export const BaseFieldConfigDeleteFile = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/basefieldconfigdeletefile', null, {
    params: params
  })
}
// 加载制度与队伍建设数据信息-查询    (long id)
export const BaseFieldConfigFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/basefieldconfigfind', null, {
    params: params
  })
}
// 附件信息-保存  (FromBody)
export const BaseFieldConfigSaveFile = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/basefieldconfigsavefile', params)
}

// 批量保存制度与队伍建设文本信息-保存  (FromBody)
export const BaseFieldConfigSaveList = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/basefieldconfigsavelist', params)
}

// 培训与安全教育  dangerchemicals/train/safeeducationlist #############################################
// 附件上传  UploadPostfile

// 删除附件-删除 (long id)
export const AttachmentDeleteFile = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/attachmentdeletefile', null, {
    params: params
  })
}
// 保存附件-保存  (FromBody)
export const AttachmentSaveFile = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/attachmentsavefile', params)
}

// 培训与安全教育-删除    (long Id)
export const TrainSafeEducationDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/trainsafeeducationdelete', null, {
    params: params
  })
}
// 培训与安全教育修改内容获取-查询   (long Id)
export const TrainSafeEducationEdit = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/trainsafeeducationedit', null, {
    params: params
  })
}
// 培训与安全教育列表-查询  (FromBody)
export const TrainSafeEducationFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/trainsafeeducationfind', params)
}
// 保存培训与安全教育信息-保存   (FromBody)
export const TrainSafeEducationInsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/trainsafeeducationinsertupdate', params)
}

// 演练与应急预案 dangerchemicals/emergency/planlist #############################################
// 附件上传  UploadPostfile

// 删除附件-删除 AttachmentDeleteFile

// 保存附件-保存  AttachmentSaveFile

// 培训与安全教育-删除  (long Id)
export const EmergencyPlanDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/emergencyplandelete', null, {
    params: params
  })
}
// 应急预案与演练修改内容获取-查询 (long Id)
export const EmergencyPlanEdit = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/emergencyplanedit', null, {
    params: params
  })
}

// 应急预案与演练列表-查询  (FromBody)
export const EmergencyPlanFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/emergencyplanfind', params)
}

// 保存应急预案与演练信息-保存  (FromBody)
export const EmergencyPlanInsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/emergencyplaninsertupdate', params)
}

// 查看MSDS dangerchemicals/msds/list   #############################################
// 附件上传 UploadPostfile

// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 存量预警查看-查询   DcScrapListFind

// 根据Id修改MSDS文件-保存  (long id, string msdsFile)
export const SchoolMaterialMsdsFileEdit = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/schoolmaterialmsdsfileedit', null, {
    params: params
  })
}
// 工作指导 dangerchemicals/word/guidedist #############################################
// 字典值管理：根据字典编号获取字段信息（超管）  (string typeCode)
export const GetDictionaryCombox = (params) => {
  return request.post('/api/hyun/bdictionary/getdictionarycombox', null, {
    params: params
  })
}
// 根据区县获取学校  Punitgetschoolbycountyid

// 获取存储柜地址下拉列表-查询   DcCabinetAddressGet

// 危化品监管
// 超量预警查看  dangerchemicals/warning/stocklist  #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 物品预警获取实体信息-查询   DcschoolMaterialModelconfigGetByid

// 存量预警查看-查询 DcScrapListFind

// 物品超量预警列表-查询  DcWarningStockListFind

// 超期预警查看 dangerchemicals/warning/validitylist #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 用户库基础型号库管理启用、禁用-设置 DcSchoolMaterialModelSetStatuz

// 物品超期预警列表-查询  DcWarningValidityListFind

// 使用年限预警查看 dangerchemicals/warning/uselifelist #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 用户库基础型号库管理启用、禁用-设置   DcSchoolMaterialModelSetStatuz

// 使用年限预警列表-查询 (FromBody)
export const DcWarningUseLifeListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwarninguselifelistfind', params)
}

// 保障要求查看 dangerchemicals/guarantee/claimlist  #############################################
// 保障要求查看列表-查询  (FromBody)
export const DcGuaranteeClaimListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcguaranteeclaimlistfind', params)
}
// 制度与队伍建设 dangerchemicals/system/teambuild #############################################
// 附件上传 UploadPostfile

// 删除附件-删除 BaseFieldConfigDeleteFile

// 加载制度与队伍建设数据信息-查询  BaseFieldConfigFind

// 附件信息-保存  BaseFieldConfigSaveFile

// 批量保存制度与队伍建设文本信息-保存  BaseFieldConfigSaveList

// 培训与安全教育 dangerchemicals/train/safeeducationlist #############################################
// 附件上传 UploadPostfile

// 删除附件-删除 AttachmentDeleteFile

// 保存附件-保存 AttachmentSaveFile

// 培训与安全教育-删除 TrainSafeEducationDelete

// 培训与安全教育修改内容获取-查询  TrainSafeEducationEdit

// 培训与安全教育列表-查询   TrainSafeEducationFind

// 保存培训与安全教育信息-保存  TrainSafeEducationInsertUpdate

// 演练与应急预案 dangerchemicals/emergency/planlist #############################################
// 附件上传 UploadPostfile

// 删除附件-删除 AttachmentDeleteFile

// 保存附件-保存 AttachmentSaveFile

// 培训与安全教育-删除 EmergencyPlanDelete

// 应急预案与演练修改内容获取-查询  EmergencyPlanEdit

// 应急预案与演练列表-查询 EmergencyPlanFind

// 保存应急预案与演练信息-保存  EmergencyPlanInsertUpdate

// 存放地查看 dangerchemicals/county/depositaddresslist #########################################
// 获取地址配置列表-查询   (FromBody)
export const DcDepositAddressListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcdepositaddresslistfind', params)
}

// 工作指导 dangerchemicals/word/guidelist #############################################
// 附件上传 UploadPostfile

// 删除附件-删除  AttachmentDeleteFile

// 保存附件-保存 AttachmentSaveFile

// 获取工作指导文件列表-查询   (FromBody)
export const DcWorkGuideFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcworkguidefind', params)
}
// 查询统计
// 库存数量统计 dangerchemicals/county/stocknumstatistics #########################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 库存数量统计-查询    (FromBody)
export const DcCountyStockNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccountystocknumstatisticsfind', params)
}
// 存量预警查看-查询 DcScrapListFind

// 按属性分类统计 dangerchemicals/county/classifystatistics #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 按属性统计数量-查询   (FromBody)
export const DcClassifyStockNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcclassifystocknumstatisticsfind', params)
}
// 采购数量统计 dangerchemicals/purchase/yearnumstatistics #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 采购数量统计-查询  (FromBody)
export const DcPurchaseNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcpurchasenumstatisticsfind', params)
}
// 获取采购查询年度列表-查询
export const GetDcPurchaseStatisticsYear = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/getdcpurchasestatisticsyear', params)
}
// 领用数量统计 dangerchemicals/apply/yearnumstatistics #########################################
// 领用数量统计-查询    (FromBody)
export const DcApplyNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcapplynumstatisticsfind', params)
}
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 获取领用查询年度列表-查询
export const GetDcApplyStatisticsYear = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/getdcapplystatisticsyear', params)
}
// 按领用人统计 dangerchemicals/apply/usernumstatistics #########################################
// 物品领用按领用人统计-查询    (FromBody)
export const DcApplyUserNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcapplyusernumstatisticsfind', params)
}
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 获取领用查询年度列表-查询 /api/hyun/dcdangerchemicals/getdcapplystatisticsuser
export const GetDcApplyStatisticsUser = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/getdcapplystatisticsuser', params)
}
// 获取领用查询年度列表-查询   GetDcApplyStatisticsYear

// 危废物存量统计 dangerchemicals/county/wastestatisticslist #########################################
// 获取废弃物分类-查询   DcWasteClassGet

// 废弃物存量统计-查询   (FromBody)
export const DcWasteDisposalStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalstatisticsfind', params)
}
// 处置数量统计  dangerchemicals/waste/disposalnumstatistics #############################################
// 获取废弃物分类-查询v   DcWasteClassGet

// 处置数量统计-查询  (FromBody)
export const DcWasteDisposalNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcwastedisposalnumstatisticsfind', params)
}

// 获取处置数量统计年度-查询
export const GetDcWasteDisposalStatisticsYear = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/getdcwastedisposalstatisticsyear', params)
}
// 库存数量统计 dangerchemicals/city/stocknumstatistics #########################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo
// 单位管理：获取下属单位集合。  (long CityId)
export const PUnitGetCountyByCityId = (params) => {
  return request.post('/api/hyun/punit/punitgetcountybycityid', null, {
    params: params
  })
}

// 市级库存数量统计-查询 (FromBody)
export const DcCityStockNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccitystocknumstatisticsfind', params)
}
// 按属性分类统计 dangerchemicals/city/classifystatistics #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 市级按属性统计数量-查询  (FromBody)
export const DcCityClassifyStockNumStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccityclassifystocknumstatisticsfind', params)
}
// 采购数量统计 dangerchemicals/city/purchaseyearnumstatistics    #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 采购数量统计-查询 DcPurchaseNumStatisticsFind

// 获取采购查询年度列表-查询 GetDcPurchaseStatisticsYear

// 领用数量统计 dangerchemicals/city/applyyearnumstatistics #############################################
// 领用数量统计-查询  DcApplyNumStatisticsFind

// 获取维护品二级分类-查询    DccatalogGetClassTwo
// 获取领用查询年度列表-查询  GetDcApplyStatisticsYear

// 危废物存量统计 dangerchemicals/city/wastestatisticslist #############################################
// 市级废弃物存量统计-查询   (FromBody)
export const DcCityWasteDisposalStatisticsFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccitywastedisposalstatisticsfind', params)
}

// 获取废弃物分类-查询   DcWasteClassGet

// 处置数量统计 dangerchemicals/city/wastedisposalnumstatistics #############################################
// 获取废弃物分类-查询  DcWasteClassGet

// 处置数量统计-导出  DcWasteDisposalNumStatisticsExport

// 处置数量统计-查询 DcWasteDisposalNumStatisticsFind

// 获取处置数量统计年度-查询  GetDcWasteDisposalStatisticsYear

// 学校库存数量统计 dangerchemicals/county/stocknumstatistics #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 库存数量统计-导出  DcCountyStockNumStatisticsExort

// 库存数量统计-查询 DcCountyStockNumStatisticsFind

// 存量预警查看-查询   DcScrapListFind

// 学校按属性分类统计 dangerchemicals/county/classifystatistics #############################################
// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 按属性统计数量-导出  DcClassifyStockNumStatisticsExport

// 按属性统计数量-查询  DcClassifyStockNumStatisticsFind

// 学校采购数量统计 dangerchemicals/purchase/yearnumstatistics #########################################
// 获取维护品二级分类-查询      DccatalogGetClassTwo

// 采购数量统计-查询 DcPurchaseNumStatisticsFind

// 获取采购查询年度列表-查询 GetDcPurchaseStatisticsYear

// 学校领用数量统计 dangerchemicals/apply/yearnumstatistics #########################################
// 领用数量统计-查询  DcApplyNumStatisticsFind

// 获取维护品二级分类-查询   DccatalogGetClassTwo

// 获取领用查询年度列表-查询 GetDcApplyStatisticsYear

// 学校危废物存量统计 dangerchemicals/county/wastestatisticslist #########################################
// 获取废弃物分类-查询   DcWasteClassGet

// 废弃物存量统计-查询  DcWasteDisposalStatisticsFind

// 学校处置数量统计 dangerchemicals/waste/disposalnumstatistics #########################################
// 获取废弃物分类-查询   DcWasteClassGet

// 处置数量统计-导出 DcWasteDisposalNumStatisticsExport

// 处置数量统计-查询 DcWasteDisposalNumStatisticsFind

// 获取处置数量统计年度-查询 GetDcWasteDisposalStatisticsYear

// 上报日志(常州专属) dangerchemicals/report/detonatepoisonlog@t=17 #############################################
// 危化品看板 dangerchemicals/kanban/index  #############################################
// 配置信息
// 危化品信息  dangerchemicals/school/materialmodelset #############################################
// 根据上级Id获取分类-查询
// /api/hyun/dcdangerchemicals/dccataloggetbypid

// 获取维护品二级分类-查询    DccatalogGetClassTwo

// 查询物品型号预警设置列表-查询 DcschoolMaterialModelConfigFind

// 物品预警获取实体信息-查询  DcschoolMaterialModelconfigGetByid

// 物品型号配置参数保存-保存 DcschoolMaterialModelConfigSave

// 添加物品规格型号-保存 DcschoolMaterialModelSave

// 供应商信息 dangerchemicals/company/list #############################################
// 平台设置：根据TypeCode获取配置信息   BconfigsetGet
// 所有单位信息管理-获取单位   UnitFindidName
// 用户管理：获取企业单位用户列表  UserFindCompany
// 用户管理：获取用户详情信息 UserGetpubByid
// 获取个人信息  UserGetInfo
// 供应商删除-删除 UserGetInfo
// 供应商启用禁用-设置 DccompanyenAbled
// 供应商管理列表-查询  DccompanyFind
// 根据Id获取供应商信息-查询  DccompanyGetByid
// 供应商新增修改-保存   DccompanyInsertUpdate
// 单位许可证信息-查询 DcunitlicenseinfoGetCommany
// 保存供应商、学校单位证书信息-保存 DcunitlicenseinfoSave

// 本单位信息维护(常州专属) dangerchemicals/school/unitinfo  #############################################
// 单位许可证信息-查询   DcunitlicenseinfoGetCommany

// 保存供应商、学校单位证书信息-保存   DcunitlicenseinfoSave

// 领用人配置 dangerchemicals/apply/grantuserset@t=1 #########################################
// 用户管理：获取用户集合 (string rids)
export const UserFindMyUnitUserNameByRoleId = (params) => {
  return request.post('/api/hyun/puser/userfindmyunitusernamebyroleid', null, {
    params: params
  })
}
// 添加领用人、发放人-保存    (FromBody)
export const DcApplyGrantUserAdd = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcapplygrantuseradd', params)
}
// 删除领用人、发放人-删除 (long id)
export const DcApplyGrantUserDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcapplygrantuserdelete', null, {
    params: params
  })
}
// 获取领用人、发送人列表-查询  (FromBody)
export const DcApplyGrantUserFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcapplygrantuserfind', params)
}
// 发放人配置 dangerchemicals/apply/grantuserset@t=2 #########################################
// 用户管理：获取用户集合 UserFindMyUnitUserNameByRoleId

// 添加领用人、发放人-保存 DcApplyGrantUserAdd

// 删除领用人、发放人-删除 DcApplyGrantUserDelete

// 获取领用人、发送人列表-查询 DcApplyGrantUserFind

// 领用参数配置 dangerchemicals/base/configset@m=9&opt=0&t=领用参数配置 #########################################
// 平台设置：项目配置信息
export const Bconfigsetgetbyunit = (params) => {
  return request.post('/api/hyun/bconfigset/bconfigsetgetbyunit', null, { params: params })
}
// 平台设置：项目配置信息保存
export const Bconfigsetsavebyunit = (params) => {
  return request.post('/api/hyun/bconfigset/bconfigsetsavebyunit', params)
}
// 存放地点设置 dangerchemicals/deposit/addresslist #############################################
// 附件上传 UploadPostfile

// 删除附件-删除 AttachmentDeleteFile

// 保存附件-保存 AttachmentSaveFile

// 存放地点-删除  (long id)
export const DcDepositAddressDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcdepositaddressdelete', null, {
    params: params
  })
}
// 获取地址信息-查询  (long id)
export const DcDepositAddressGetById = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcdepositaddressgetbyid', null, {
    params: params
  })
}
// 存放地点设置-保存  (FromBody)
export const DcDepositAddressInsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcdepositaddressinsertupdate', params)
}
// 获取地址配置列表-查询 DcDepositAddressListFind

// 危化品管理设置
// 基础库配置 dangerchemicals/school/cataloginitlist #############################################
// 基础库配置-根据区县获取学校 (long CountyId)
export const Punitgetschoolbycountyid = (params) => {
  return request.post('/api/hyun/punit/punitgetschoolbycountyid', null, { params: params })
}
// 基础库配置-单位管理：获取区县集合。
export const PunitGetCountybyAdmin = (params) => {
  return request.post('/api/hyun/punit/punitgetcountybyadmin', params)
}
// 基础库配置-学校基础库管理-查询 (FromBody)
export const DcschoolCataloginitFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcataloginitfind', params)
}
// 基础库配置- 学校基础库-保存 (long id)
export const DcschoolCataloginitSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcataloginitsave', null, {
    params: params
  })
}
// 基础库配置-修改学校基础库-保存 (long id)
export const DcschoolCataloginitUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcataloginitupdate', null, {
    params: params
  })
}

// 危化品选配 dangerchemicals/school/cataloglist #########################################
// 危化品选配-获取维护品二级分类-查询 (long unitId)
export const DccatalogGetClassTwo = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccataloggetclasstwo', null, { params: params })
}
// 危化品选配-用户库基础库,物品管理启用、禁用、是否归还-查询 (FromBody)
export const DcschoolCatalogFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogfind', params)
}
// 危化品选配-获取学校物品类别选择-查询 (int commonUse = 0)
export const DcschoolCatalogFindCommonuseAll = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogfindcommonuseall', null, {
    params: params
  })
}
// 危化品选配-根据id获取物品详细信息-查询  (long id)
export const DcschoolCatalogGetByid = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcataloggetbyid', null, {
    params: params
  })
}
// 危化品选配-学校物品分类添加-保存 (FromBody)
export const DcschoolCataloginsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcataloginsertupdate', params)
}
// 危化品选配-物品一二级分类下拉框数据-查询
export const DcschoolCatalogparentGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogparentget', params)
}
// 危化品选配-危化品选品-设置 (string ids, int isCommonUse)
export const DcschoolCatalogsetisCommonuse = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogsetiscommonuse', null, {
    params: params
  })
}
// 危化品选配-用户库基础库,物品管理启用、禁用-设置 (string ids, int statuz)
export const DcschoolCatalogsetStatuz = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolcatalogsetstatuz', null, {
    params: params
  })
}

// 规格参数设置 dangerchemicals/school/materialmodelset #########################################
// 规格参数设置-根据上级Id获取分类-查询 (long pid)
export const DccataloggetBypid = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccataloggetbypid', null, { params: params })
}
// 规格参数设置-获取维护品二级分类-查询  DccatalogGetClassTwo

// 规格参数设置-查询物品型号预警设置列表-查询 (FromBody)
export const DcschoolMaterialModelConfigFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigfind', params)
}
// 规格参数设置-物品预警获取实体信息-查询  (long id)
export const DcschoolMaterialModelconfigGetByid = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfiggetbyid', null, {
    params: params
  })
}
// 规格参数设置-物品型号配置参数保存-保存  (long[] arrId, int type, string setValue)
export const DcschoolMaterialModelConfigSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelconfigsave', params)
}
// 规格参数设置-添加物品规格型号-保存   (FromBody)
export const DcschoolMaterialModelSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelsave', params)
}

// 规格品牌配置 dangerchemicals/school/modelbranddisablelist #########################################
// 附件上传 UploadPostfile

// 获取维护品二级分类-查询   DccatalogGetClassTwo

// 添加物品详细描述-设置   (int id, string imageUrl, string remark)
export const DcSchoolModelBrandSetDetail = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmodelbrandsetdetail', null, {
    params: params
  })
}
// 规格品牌配置 dangerchemicals/school/modelbrand #############################################
// 字典值管理：根据字典编号获取字段信息（超管） GetDictionaryCombox

// 获取维护品二级分类-查询   DccatalogGetClassTwo

// 用户库基础库,物品管理启用、禁用、是否归还-查询 DcschoolCatalogFind

// 获取物品库品牌信息-查询  (int catelogId, DcApplyParam param, int isNoSet)
export const DcSchoolMaterialBrandGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialbrandget', null, {
    params: params
  })
}
// 获取物品库品牌信息-查询2  (int catelogId, int modelId)
export const DcSchoolMaterialBrandGet2 = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialbrandgetv2', null, {
    params: params
  })
}
// 获取物品规格型号-查询 (int catelogId, DcApplyParam param, int isNoSet)
export const DcSchoolMaterialModelGet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelget', null, {
    params: params
  })
}
// 获取物品规格型号-查询2  (int catelogId)
export const DcSchoolMaterialModelGetV2 = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmaterialmodelgetv2', null, {
    params: params
  })
}
// 添加物品规格型号-保存 DcschoolMaterialModelSave

// 物品规格品牌配置-设置  (int schoolCatalogId, int modelId, int baseModelId, string brandIds)
export const DcSchoolModelBrandSet = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolmodelbrandset', null, {
    params: params
  })
}
// 供应商管理 dangerchemicals/company/list #############################################

// 供应商管理-平台设置：根据TypeCode获取配置信息 (string moduleCode, string typeCode)
export const BconfigsetGet = (params) => {
  return request.post('/api/hyun/bconfigset/bconfigsetget', null, { params: params })
}
// 供应商管理-所有单位信息管理-获取单位  (FromBody)
export const UnitFindidName = (params) => {
  return request.post('/api/hyun/punit/unitfindidname', params)
}
// 供应商管理-用户管理：获取企业单位用户列表 (FromBody)
export const UserFindCompany = (params) => {
  return request.post('/api/hyun/puser/userfindcompany', params)
}
// 供应商管理-用户管理：获取用户详情信息 (int id)
export const UserGetpubByid = (params) => {
  return request.post('/api/hyun/puser/usergetpubbyid', null, { params: params })
}
// 供应商管理-获取个人信息
export const UserGetInfo = (params) => {
  return request.post('/api/hyun/puser/usergetinfo', params)
}
// 供应商管理-供应商删除-删除 (long Id)
export const DccompanyDeleteByid = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccompanydeletebyid', null, { params: params })
}
// 供应商管理-供应商启用禁用-设置 (long Id)
export const DccompanyenAbled = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccompanyenabled', null, { params: params })
}
// 供应商管理-供应商管理列表-查询 (FromBody)
export const DccompanyFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccompanyfind', params)
}
// 供应商管理-根据Id获取供应商信息-查询  (long Id)
export const DccompanyGetByid = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccompanygetbyid', null, { params: params })
}
// 供应商管理-供应商新增修改-保存  (FromBody)
export const DccompanyInsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dccompanyinsertupdate', params)
}
// 供应商管理-单位许可证信息-查询  (long id, int unittype)
export const DcunitlicenseinfoGetCommany = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcunitlicenseinfogetcommany', null, {
    params: params
  })
}
// 供应商管理-保存供应商、学校单位证书信息-保存  (FromBody)
export const DcunitlicenseinfoSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcunitlicenseinfosave', params)
}

// 领用审核设置 dangerchemicals/audit/conditionset@t=2 #############################################
// 审核审批配置-查询 DcauditconditionFind

// 审核审批设置-保存 DcauditconditioninsertUpdate

// 采购审核审批设置 dangerchemicals/audit/conditionset@t=1 #########################################
// 采购审核审批设置-审核审批配置-查询
export const DcauditconditionFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcauditconditionfind', params)
}
// 采购审核审批设置-审核审批设置-保存
export const DcauditconditioninsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcauditconditioninsertupdate', params)
}

// 后台配置 dangerchemicals/base/configset@m=9&opt=0&t=后台配置 #############################################
// 平台设置：项目配置信息  Bconfigsetgetbyunit

// 平台设置：项目配置信息保存 Bconfigsetsavebyunit

// 基础分类配置 dangerchemicals/base/cataloglist #############################################

// 基础分类配置-获取危化品分类一二级下拉框-查询 (int depth, int pid)
export const DcbaseCatalogCmboget = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasecatalogcmboget', null, { params: params })
}
// 基础分类配置-基础分类-删除 (long id)
export const DcbaseCatalogDelete = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasecatalogdelete', null, { params: params })
}
// 基础分类配置-基础分类数据列表-查询
export const DcbaseCatalogFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasecatalogfind', params)
}
// 基础分类配置-保存危化品分类-保存  (FromBody)
export const DcbaseCataloginsertUpdate = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasecataloginsertupdate', params)
}
// 基础分类配置-分类启用、禁用-设置  (long id, int statuz)
export const DcbaseCatalogSetStatuz = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasecatalogsetstatuz', null, {
    params: params
  })
}

// 基础规格配置 dangerchemicals/base/modellist #########################################
// 基础规格配置-字典值管理：获取字典列表信息（超管） (FromBody)
export const DictionaryFind = (params) => {
  return request.post('/api/hyun/bdictionary/dictionaryfind', params)
}
// 基础规格配置-获取危化品分类一二级下拉框-查询  DcbaseCatalogCmboget

// 基础规格配置-基础规格参数列表-查询 (FromBody)
export const DcbaseModelextensionFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasemodelextensionfind', params)
}
// 基础规格配置-获取基础规格参数信息-查询 (long id)
export const DcbaseModelextensiongetByid = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasemodelextensiongetbyid', null, {
    params: params
  })
}
// 基础规格配置-添加、修改基础规格参数-保存 (FromBody)
export const DcbaseModelextensionSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcbasemodelextensionsave', params)
}
// 基础规格配置-基础规格配置参数-保存 (string arrId, int type, string setValue)
export const DcdcbaseModelextensionconfigSave = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcdcbasemodelextensionconfigsave', null, {
    params: params
  })
}

// 学校权限控制 dangerchemicals/base/configset@m=9&opt=0&t=学校权限控制   #########################################
// 平台设置：项目配置信息 Bconfigsetgetbyunit

// 平台设置：项目配置信息保存  Bconfigsetsavebyunit

// 台账打印
// 采购台账 dangerchemicals/standbook/purchase #########################################
// 采购台账-查询 (string month)
export const DcStandbookPurchaseFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcstandbookpurchasefind', params)
}
// 采购台账打印 dangerchemicals/standbook/purchaseprint #############################################
// 采购台账-查询 DcStandbookPurchaseFind

// 领用台账 dangerchemicals/standbook/apply #############################################
// 学校领用列表   DcApplyFind

// 领用台账打印 dangerchemicals/standbook/applyprint #############################################
// 学校领用列表   DcApplyFind

// 存量台账 dangerchemicals/standbook/stock #############################################
// 存量台账-查询  DcStandbookStockFind(int attribute, string date)
export const DcStandbookStockFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcstandbookstockfind', params)
}
// 存量台账打印 dangerchemicals/standbook/stockprint #############################################
// 存量台账-查询 DcStandbookStockFind

// 处置台账 dangerchemicals/standbook/waste #############################################
// 处置台账-查询 (FromBody)
export const DcStandbookWasteFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcstandbookwastefind', params)
}
// 处置台账打印 dangerchemicals/standbook/wasteprint #############################################
// 处置台账-查询 DcStandbookWasteFind

// 采购汇总列表-查询   (FromBody)
export const DcSchoolPurchaseSummaryListFind = (params) => {
  return request.post('/api/hyun/dcdangerchemicals/dcschoolpurchasesummarylistfind', params)
}
