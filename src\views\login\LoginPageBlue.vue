<script setup>
// 接口
import {
  user<PERSON>og<PERSON>, Get<PERSON><PERSON><PERSON>B<PERSON>, teacherGetvalidatecode, teacherUser<PERSON>ogin, LoginRefreshcaptchaimage
} from '@/api/user.js'
import {
  ArticleGetbottomcatetype, ArticleGetoperator, AnonGetconfigbymodule
} from '@/api/home.js'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { useUserStore } from '@/stores';
import router from '@/router'
import { addDynamicRoutes } from '@/router'
import md5 from 'js-md5';
import { integerLimit, onePage } from "@/utils/index.js";

const isAdmin = ref(true)
// 登录信息
const formData = ref({
  name: '',
  pass: ''
})
const userStore = useUserStore()
const imageUrl = ref('')
const imgUuid = ref('')
onMounted(() => {

  if (userStore.token) {
    if (userStore.platformType == 1) {
      router.push('/')
    } else if (userStore.platformType == 2 || userStore.platformType == 3) {
      const path = onePage(userStore.menu)
      router.push('/' + path)
    }
  }
  noticeChecked.value = userStore.noticeChecked
  // 是否记住账号密码
  if (userStore.isRemember) {
    formData.value.name = userStore.name
    formData.value.pass = atob(userStore.pass)
  }
  if (userStore.isImgCode == 5) {
    // LoginRefreshcaptchaimageUser()
    imageUrl.value = `data:image/jpg;base64,${userStore.captchaimage.Img}`;
    imgUuid.value = userStore.captchaimage.Uuid
  }
  ArticleGetoperatorUser({ code: 1010 })
  if (userStore.platformType == 2 || userStore.platformType == 3) {
    ArticleGetbottomcatetypeUser()
    AnonGetconfigbymoduleUser()
  }
  window.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress)
})
// 回车键登录
const handleKeyPress = (event) => {
  if (event.key === 'Enter') {
    if (isAdmin.value) {
      login()
    } else {
      teacherLogin
    }
  }
}
// 监听变量
watch(() => userStore.isRemember, () => {
  userStore.setName('')
  userStore.setPass('')
})
// 登录禁用
const isLogin = computed(() => {
  let isLogin = true
  if (formData.value.name && formData.value.pass) isLogin = false
  return isLogin
})
// 班主任登录禁用
const isTeacherLogin = computed(() => {
  let isLogin = true
  if (formData.value.phoneNumber && formData.value.validateCode) isLogin = false
  return isLogin
})
// 校验信息
const formRules = {
  name: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  pass: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  imgCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
  phoneNumber: [{ required: true, message: '请输入手机号', trigger: 'blur' },
  {
    pattern: /^1[0-9]{10}$/,
    message: '请输入正确11位手机号码',
    trigger: 'blur'
  }],
  validateCode: [{ required: true, message: '请输入验证码', trigger: 'blur' },
  {
    pattern: /^[0-9]{4,6}$/,
    message: '请输入正确的验证码',
    trigger: 'blur'
  }],
}
//输入整数
const integerLimitInput = (val, name) => {
  formData.value[name] = integerLimit(val);
}
const refForm = ref()
// 登录
const login = () => {
  console.log('登录', noticeChecked)
  if (noticeList.value.length > 0 && !noticeChecked.value) {
    ElMessage.error('请先阅读并同意用户条款')
    return
  }
  refForm.value
    .validate()
    .then((res) => {
      // console.info('表单验证成功', res)
      const loginParams = ref({
        name: formData.value.name,
        pass: md5(formData.value.pass),
      })
      if (userStore.isImgCode == 5) {
        loginParams.value.Code = formData.value.imgCode
        loginParams.value.Uuid = imgUuid.value
      }
      userLogin(loginParams.value).then((resUser) => {
        if (resUser.data.flag == 5) {
          userStore.setImgCode(resUser.data.flag)
          // LoginRefreshcaptchaimageUser()
          imageUrl.value = `data:image/jpg;base64,${resUser.data.data.footer.Img}`;
          imgUuid.value = resUser.data.data.footer.Uuid
          userStore.setCaptchaImage(resUser.data.data.footer)
          ElMessage.error(resUser.data.msg)
        } else if (resUser.data.flag == 6) {
          // resUser.data.flag == 6  判断为特定密码时，直接进入修改密码页面
          const { rows, footer } = resUser.data.data

          userStore.setToken(rows.token)
          userStore.setTokenType(rows.token_type)
          userStore.setExpiresin(rows.expires_in * 1000)
          userStore.setUserInfo(footer)

          // 记住账号密码
          if (userStore.isRemember) {
            userStore.setName(formData.value.name)
            userStore.setPass(btoa(formData.value.pass))
          }
          userStore.setNoticeChecked(true)

          router.replace('/user/my/changepass')

          userStore.setImgCode(0)
        } else if (resUser.data.flag == 1) {
          // console.info("登录信息", resUser.data.data)
          // console.info("用户信息", resUser.data.data.footer)
          const { rows, footer } = resUser.data.data

          userStore.setToken(rows.token)
          userStore.setTokenType(rows.token_type)
          userStore.setExpiresin(rows.expires_in * 1000)
          userStore.setUserInfo(footer)

          // 记住账号密码
          if (userStore.isRemember) {
            userStore.setName(formData.value.name)
            userStore.setPass(btoa(formData.value.pass))
          }
          userStore.setNoticeChecked(true)

          if (footer.UnitStatus == 1) {
            // 已认证，调用菜单
            GetNavigationBarUser(footer.Id)
          } else if (footer.UnitStatus == 2) {
            // 需要认证：跳到新页面申请认证
            router.replace('/unitauthentic')
          }
          userStore.setImgCode(0)
        } else {
          ElMessage.error(resUser.data.msg)
        }
      })
        .catch((errUser) => {
          // 在这里处理登录失败的额外操作
          console.log("errUser", errUser)
          ElMessage.error(errUser.msg);
        })
    })
    .catch((err) => {
      ElMessage.error('请填写信息');
      console.info('表单验证失败', err)
    })
}
// 班主任登录
const teacherLogin = () => {
  if (!noticeChecked.value) {
    ElMessage.error('请先阅读并同意用户条款')
    return
  }
  // 班主任注册登录
  let paraData = {
    phoneNumber: formData.value.phoneNumber, //'手机号
    validateCode: formData.value.validateCode, //验证码
    uuid: Uuid.value, //验证码uid
    userType: 6, //账号类型：5:家长；6：班主任,
  }
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    teacherUserLogin(paraData).then(res => {
      if (res.data.flag == 1) {
        const { rows, footer } = res.data.data
        userStore.setToken(rows.token)
        userStore.setTokenType(rows.token_type)
        userStore.setExpiresin(rows.expires_in * 1000)
        userStore.setUserInfo(footer)
        userStore.setNoticeChecked(true)
        if (!footer.UnitStatus || footer.UnitStatus == 1) {
          // 已认证，调用菜单
          GetNavigationBarUser(footer.Id)
        } else if (footer.UnitStatus == 2) {
          // 需要认证：跳到新页面申请认证
          router.replace('/unitauthentic')
        }
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })
}
// 切换账号
const inputDemoAccount = (name, pass) => {
  formData.value.name = name
  formData.value.pass = pass
}
// 注册
const reg = () => {
  router.replace('/reg')
}
// 切换登录方法
const tabClick = (e) => {
  isAdmin.value = e
  // noticeChecked.value = false
}

const codeText = ref('获取验证码')
const isCode = ref(false)
const CodeLength = ref(6)
const IsExistAccount = ref(false)
const Uuid = ref()
const time = ref(Date.now() + 1000 * 60)
// 获取校验码
const getCode = () => {
  refForm.value.validateField(['phoneNumber'], valid => {
    if (!valid) {
      return
    }

    // 班主任获取验证码
    let paraData = {
      phoneNumber: formData.value.phoneNumber, //手机号
      validateType: 3, //家长、教师登录
      userType: 6, //账号类型：5:家长；6：班主任,
    }

    teacherGetvalidatecode(paraData).then(res => {
      if (res.data.flag == 1) {
        const { rows } = res.data.data
        CodeLength.value = rows.CodeLength; //验证码长度
        IsExistAccount.value = rows.IsExistAccount; //是否存在账号
        Uuid.value = rows.Uuid; //是否存在账号
        time.value = Date.now() + 1000 * rows.WaitSecond
        isCode.value = true
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  });
}
// 校验码倒计时结束
const finishChange = () => {
  // console.log('倒计时结束')
  codeText.value = '重新获取'
  isCode.value = false
}

const GetNavigationBarUser = (uid) => {
  GetNavigationBar({ uid: uid }).then((res) => {
    // console.log("菜单res", res)
    // console.log("菜单res", res.data.response.children)
    if (res.data.response.children) {
      userStore.setMenu(res.data.response.children)
      // 添加vue router路由
      addDynamicRoutes(userStore.menu)
    }
    // console.log("userStore.curPage.path",userStore.curPage.path)
    const routerPath = userStore.curPage.path
    if (routerPath && !['/login', '/reg', '/exhibition', '/information', '/articlelist', '/articledetail', '/unitauthentic', '/preview', '/'].includes(routerPath)) {
      router.replace(userStore.curPage.path)
    } else {
      let path = ''
      if (userStore.platformType == 2) {
        if (userStore.userInfo.UnitType === 0 || (userStore.userInfo.UnitType > 0 && !['1200', '2200', '3200'].some(t => userStore.userInfo.RoleIds.includes(t)))) {
          path = onePage(userStore.menu)
        } else {
          // 工作流管理平台：首页
          path = 'approval/home/<USER>'
        }
      } else {
        path = onePage(userStore.menu)
      }
      // 获取当前账号第一个权限页面路径
      // const path = onePage(userStore.menu)
      console.log("获取当前账号第一个权限页面路径", path)
      userStore.setOnePage(path)//存储起来用于tag标签
      // 跳转路由
      // router.replace('/')
      router.replace('/' + path)
    }
  })
}

// 切换图片验证码
const imgCodeChange = () => {
  LoginRefreshcaptchaimageUser()
}
// 获取图片验证码 
const LoginRefreshcaptchaimageUser = () => {
  LoginRefreshcaptchaimage().then((res) => {
    console.log('获取验证码', res)
    const { footer } = res.data.data
    imageUrl.value = `data:image/jpg;base64,${footer.Img}`;
    imgUuid.value = footer.Uuid
  })
}
const noticeList = ref([])
const noticeChecked = ref(false)
// 获取用户条款
const ArticleGetoperatorUser = (parData) => {
  ArticleGetoperator(parData).then(res => {
    // console.log("资讯列表", res)
    if (res.data.flag == 1) {
      const { other } = res.data.data
      noticeList.value = other || []
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 查看用户须知
const detailClick = (item) => {
  const { href } = router.resolve({
    path: "/articledetail",
    query: { Id: item.Id }
  });
  window.open(href, "_blank");
}
//底部资讯分类信息
const ArticleGetbottomcatetypeUser = () => {
  ArticleGetbottomcatetype({ topCount: 3 }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      let articleFooterList = rows || [];//底部资讯分类信息
      userStore.setArticleFooter(articleFooterList)
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 获取配置信息
const AnonGetconfigbymoduleUser = () => {
  AnonGetconfigbymodule({ moduleCode: 8002 }).then(res => {
    let obj = {}
    const { rows } = res.data.data
    rows.map(item => {
      obj[item.TypeCode] = item.ConfigValue
    })
    userStore.setDefaultSet(obj)
  })
}

</script>
<template>
  <div class="login-box">
    <!-- 主登录容器 -->
    <div class="main-container">
      <!-- 左侧插图区域 -->
      <div class="illustration-section">
        <div class="illustration-content">
          <!-- 教育主题插图 -->
          <div class="education-showcase">
            <div class="main-visual">
              <div class="education-icon">
                <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                  <!-- 书本图标 -->
                  <rect x="20" y="30" width="80" height="60" rx="4" fill="rgba(255,255,255,0.2)"
                    stroke="rgba(255,255,255,0.4)" stroke-width="2" />
                  <rect x="25" y="35" width="70" height="50" rx="2" fill="rgba(255,255,255,0.1)" />
                  <!-- 页面线条 -->
                  <line x1="35" y1="45" x2="85" y2="45" stroke="rgba(255,255,255,0.6)" stroke-width="1" />
                  <line x1="35" y1="55" x2="75" y2="55" stroke="rgba(255,255,255,0.6)" stroke-width="1" />
                  <line x1="35" y1="65" x2="80" y2="65" stroke="rgba(255,255,255,0.6)" stroke-width="1" />
                  <line x1="35" y1="75" x2="70" y2="75" stroke="rgba(255,255,255,0.6)" stroke-width="1" />
                  <!-- 装饰元素 -->
                  <circle cx="30" cy="20" r="3" fill="rgba(255,255,255,0.3)" />
                  <circle cx="90" cy="25" r="2" fill="rgba(255,255,255,0.4)" />
                  <circle cx="85" cy="100" r="2.5" fill="rgba(255,255,255,0.3)" />
                </svg>
              </div>
            </div>
            <div class="platform-info">
              <h3 class="platform-name"> {{ userStore.defaultSet['8002_DLYM_BJ_LEFT1'] || '' }}</h3>
              <p class="platform-desc">{{ userStore.defaultSet['8002_DLYM_BJ_LEFT2'] || '' }}</p>
              <div class="feature-tags">
                <span class="tag">{{ userStore.defaultSet['8002_DLYM_BJ_LEFT31'] || '' }}</span>
                <span class="tag">{{ userStore.defaultSet['8002_DLYM_BJ_LEFT32'] || '' }}</span>
                <span class="tag">{{ userStore.defaultSet['8002_DLYM_BJ_LEFT33'] || '' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧登录表单区域 -->
      <div class="login-container">
        <!-- 系统标题和Logo -->
        <div class="system-header">
          <div class="logo-section">
            <div class="logo-icon">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <rect width="32" height="32" rx="8" fill="url(#gradient)" />
                <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" fill="white" />
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#4a90e2" />
                    <stop offset="100%" style="stop-color:#357abd" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <div class="system-info">
              <h2 class="system-title">{{ userStore.defaultSet['8002_DLYM_BJ_YC'] || '' }}</h2>
              <p class="system-subtitle">{{ userStore.defaultSet['8002_DLYM_BJ_YCF'] || '' }}</p>
            </div>
          </div>
        </div>

        <!-- 登录标题区域 -->
        <div class="login-header">
          <div v-if="userStore.platformType == 1" class="login-tabs">
            <div v-if="isAdmin" class="tab-switch">
              <!-- <span class="current-tab">账号登录</span> -->
              <span class="switch-link" @click="tabClick(false)">班主任登录 ></span>
            </div>
            <div v-else class="tab-switch">
              <!-- <span class="current-tab">班主任登录</span> -->
              <span class="switch-link" @click="tabClick(true)">账号登录 ></span>
            </div>
          </div>
          <h3 class="login-title">{{ isAdmin ? '账号登录' : '班主任登录' }}</h3>
        </div>
        <!-- 登录表单区域 -->
        <div class="form-section">
          <!-- 账号登录 -->
          <div v-if="isAdmin" class="login-form">
            <el-form @submit.prevent ref="refForm" :rules="formRules" :model="formData" label-position="left"
              label-width="80px">
              <el-form-item label="用户名" prop="name">
                <el-input v-model.trim="formData.name" :prefix-icon="User" clearable auto-complete="off"
                  placeholder="请输入用户名" class="custom-input"></el-input>
              </el-form-item>
              <el-form-item label="密码" prop="pass">
                <el-input @keyup.enter="login" v-model.trim="formData.pass" :prefix-icon="Lock" clearable
                  auto-complete="off" show-password placeholder="请输入密码" class="custom-input"></el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="imgCode" v-if="userStore.isImgCode == 5">
                <div class="captcha-container">
                  <el-input v-model="formData.imgCode" auto-complete="off" placeholder="请输入验证码"
                    class="captcha-input"></el-input>
                  <div class="captcha-image" @click="imgCodeChange">
                    <img v-if="imageUrl" :src="imageUrl" alt="验证码" />
                  </div>
                </div>
              </el-form-item>

              <el-form-item label-width="0px">
                <div class="form-options">
                  <el-checkbox class="remember-checkbox" v-model="userStore.isRemember">记住密码</el-checkbox>
                  <div @click="reg" v-if="userStore.platformType == 1" class="register-link">
                    立即注册
                  </div>
                </div>
              </el-form-item>

              <!-- 快速登录测试账号 -->
              <!-- <el-form-item label-width="0px">
                <div style="margin-bottom: 20px" class="count-test">
                  <el-radio-group>
                    <el-radio-button value="" label="学校"
                      @click="inputDemoAccount('yucai', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="区县"
                      @click="inputDemoAccount('浦口教育局', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="市级"
                      @click="inputDemoAccount('南京技装', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="管理员"
                      @click="inputDemoAccount('超级管理员', '123456')"></el-radio-button>
                    <el-radio-button value="" label="企业"
                      @click="inputDemoAccount('junfei', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="lisi1"
                      @click="inputDemoAccount('lisi1', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="quxian1"
                      @click="inputDemoAccount('quxian1', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="quxian2"
                      @click="inputDemoAccount('quxian2', '123456@cneefix')"></el-radio-button>
                    <el-radio-button value="" label="lyy"
                      @click="inputDemoAccount('lyy002', '123456@cneefix')"></el-radio-button>
                  </el-radio-group>
                </div>
              </el-form-item> -->
              <el-form-item class="login-button-item" label-width="0px">
                <el-button type="primary" class="login-button" @click="login" :disabled="isLogin">
                  <span>登录</span>
                </el-button>
              </el-form-item>

              <!-- 用户协议 -->
              <el-form-item v-if="noticeList.length > 0" class="agreement-item" label-width="0px">
                <div class="agreement-content">
                  <el-checkbox v-model="noticeChecked" class="agreement-checkbox">
                    <span class="agreement-text">阅读并接受: </span>
                  </el-checkbox>
                  <span class="agreement-links" v-for="(item, index) in noticeList" :key="item.Id">
                    <span class="agreement-link" @click="detailClick(item)">《{{ item.Title }}》</span>
                    <span v-if="index < noticeList.length - 1">、</span>
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </div>
          <!-- 班主任登录  -->
          <div v-else class="login-form">
            <div class="teacher-login-tip">未注册将自动创建并登录</div>
            <el-form @submit.prevent ref="refForm" :rules="formRules" :model="formData" label-position="left"
              label-width="80px">
              <el-form-item label="手机号码" prop="phoneNumber">
                <el-input v-model="formData.phoneNumber" clearable @input="integerLimitInput($event, 'phoneNumber')"
                  auto-complete="off" placeholder="请输入手机号码" class="custom-input"></el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="validateCode">
                <div class="code-container">
                  <el-input v-model="formData.validateCode" clearable @input="integerLimitInput($event, 'validateCode')"
                    auto-complete="off" placeholder="请输入验证码" @keyup.enter="teacherLogin" class="code-input"></el-input>
                  <el-button type="primary" plain :disabled="isCode" @click="getCode" class="code-button">
                    <span v-if="!isCode">{{ codeText }}</span>
                    <el-countdown v-else format="ss" :value="time" @finish="finishChange" suffix="秒后重新获取"
                      :value-style="{ color: '#606266', fontSize: '14px' }" />
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item class="login-button-item" label-width="0px">
                <el-button type="primary" class="login-button" @click="teacherLogin" :disabled="isTeacherLogin">
                  <span>登录</span>
                </el-button>
              </el-form-item>

              <!-- 用户协议 -->
              <el-form-item v-if="noticeList.length > 0" class="agreement-item" label-width="0px">
                <div class="agreement-content">
                  <el-checkbox v-model="noticeChecked" class="agreement-checkbox">
                    <span class="agreement-text">阅读并接受: </span>
                  </el-checkbox>
                  <span class="agreement-links" v-for="(item, index) in noticeList" :key="item.Id">
                    <span class="agreement-link" @click="detailClick(item)">《{{ item.Title }}》</span>
                    <span v-if="index < noticeList.length - 1">、</span>
                  </span>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
// 主容器样式
.login-box {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4a90e2 100%);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }
}

// 主登录容器
.main-container {
  position: relative;
  display: flex;
  width: 90%;
  max-width: 1000px;
  min-height: 600px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(20px);
  overflow: hidden;
  z-index: 2;
}

// 左侧插图区域
.illustration-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
  position: relative;
  overflow: hidden;
}

.illustration-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
}

// 教育展示区域
.education-showcase {
  text-align: center;
  color: white;
  max-width: 400px;
}

.main-visual {
  margin-bottom: 40px;
}

.education-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.platform-info {
  .platform-name {
    margin: 0 0 16px 0;
    font-size: 28px;
    font-weight: 600;
    color: white;
    line-height: 1.2;
  }

  .platform-desc {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.85);
    line-height: 1.5;
  }
}

.feature-tags {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

.tag {
  padding: 6px 16px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }
}

// 右侧登录容器
.login-container {
  flex: 0 0 420px;
  display: flex;
  flex-direction: column;
  padding: 50px 40px;
  background: #fff;
  position: relative;
}

// 系统标题区域
.system-header {
  margin-bottom: 25px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.system-info {
  flex: 1;
}

.system-title {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: -0.5px;
}

.system-subtitle {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  font-weight: 400;
}

// 登录标题区域
.login-header {
  margin-bottom: 20px;
}

.login-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 500;
  color: #1a1a1a;
}

.login-tabs {
  display: flex;
  justify-content: flex-end;
}

.tab-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.current-tab {
  color: #2c3e50;
  font-weight: 500;
}

.switch-link {
  color: #4a90e2;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #357abd;
  }
}

// 表单区域
.form-section {
  flex: 1;
}

.login-form {
  width: 100%;
}

// 自定义输入框样式
:deep(.custom-input) {
  .el-input__wrapper {
    border-radius: 6px;
    box-shadow: none;
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;
    background: #fff;

    &:hover {
      border-color: #4a90e2;
    }

    &.is-focus {
      border-color: #4a90e2;
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
    }
  }

  .el-input__inner {
    height: 40px;
    font-size: 14px;
    color: #333;
    padding: 0 12px;

    &::placeholder {
      color: #999;
    }
  }
}

// 验证码容器
.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.captcha-input {
  width: 60%;
}

.captcha-image {
  width: 100px;
  height: 36px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #e1e8ed;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 验证码容器（班主任登录）
.code-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.code-input {
  width: 60%;
}

.code-button {
  height: 36px;
  border-radius: 8px;
  font-size: 13px;
  min-width: 110px;
}

// 表单选项
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
}

.remember-checkbox {
  :deep(.el-checkbox__label) {
    color: #7f8c8d;
    font-size: 14px;
  }
}

.register-link {
  color: #4a90e2;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #357abd;
  }
}

// 班主任登录提示
.teacher-login-tip {
  margin-bottom: 20px;
  color: #7f8c8d;
  font-size: 14px;
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4a90e2;
}



.demo-title {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 10px;
  text-align: center;
}



// 登录按钮
.login-button-item {
  margin: 24px 0 20px 0;
}

.login-button {
  width: 100% !important;
  height: 44px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  background: #4a90e2;
  border: none;
  box-shadow: none;
  transition: all 0.3s ease;

  &:hover {
    background: #357abd;
    transform: none;
  }

  &:active {
    background: #2a5298;
  }

  &.is-disabled {
    background: #d9d9d9;
    color: #999;

    &:hover {
      background: #d9d9d9;
    }
  }

  span {
    position: relative;
    z-index: 1;
  }
}

// 用户协议
.agreement-item {
  margin-bottom: 0;
}

.agreement-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  line-height: 1.5;
}

.agreement-checkbox {
  :deep(.el-checkbox__label) {
    color: #7f8c8d;
    font-size: 13px;
    line-height: 1.5;
  }
}

.agreement-text {
  color: #7f8c8d;
}

.agreement-links {
  color: #4a90e2;
  line-height: 1.5;
}

.agreement-link {
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #357abd;
    text-decoration: underline;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    width: 95%;
    min-width: 450px;
    min-height: auto;
  }

  .illustration-section {
    height: 200px;
    flex: none;
  }

  .illustration-3d {
    width: 200px;
    height: 200px;
  }

  .login-container {
    padding: 30px 25px;
    margin: 20px auto;
  }

  .system-title {
    font-size: 20px;
  }

  .main-visual {
    display: none;
  }
}

:deep(.el-statistic__suffix) {
  font-size: 14px;
}

:deep(.el-form-item__content) {
  align-items: center;
  justify-content: space-between;

  .el-button {
    width: 120px;
  }

  img {
    height: 100%;
  }
}

// 表单项间距调整
:deep(.el-form-item) {
  margin-bottom: 20px;

  .el-form-item__label {
    color: #333;
    font-weight: 500;
    font-size: 14px;
    line-height: 40px;
    padding-bottom: 4px;
  }
}

// 复选框样式优化
:deep(.el-checkbox) {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #4a90e2;
    border-color: #4a90e2;
  }

  .el-checkbox__inner:hover {
    border-color: #4a90e2;
  }
}

// 倒计时样式
:deep(.el-statistic) {
  .el-statistic__content {
    font-size: 14px;
    color: #6c757d;
  }
}
</style>
