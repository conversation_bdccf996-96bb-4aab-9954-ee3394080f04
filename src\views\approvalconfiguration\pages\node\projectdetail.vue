<script setup>
defineOptions({
  name: 'nodeprojectprojectdetail'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
  FindHistoryProjectList
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { ElMessageBox, ElMessage } from 'element-plus'
import { Refresh, Search, Back } from '@element-plus/icons-vue'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { tagsListStore } from "@/utils/index.js";
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, key: '' })
const tableTotal = ref(0)
const tableData = ref([])// 表格数据
const columnData = ref([])//列表头数据
const searchList = ref([])//查询条件：下拉、时间等 
//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})
// 返回上一页
const HandleBack = () => {
  let tagsList = userStore.tagsList
  tagsList = tagsList.filter(t => t.path != route.path)
  userStore.setTagsList(tagsList)
  if (route.query.page == 'detail') {
    router.push({ path: './detail' + route.query.routerUrl, query: { id: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode } })

  } else if (route.query.page == 'treasurydetail') {
    router.push({ path: '../treasury/detail' + route.query.routerUrl, query: { id: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode, ProcessId: route.query.ProcessId } })
  } else if (route.query.page == 'examine') {
    router.push({ path: './examine' + route.query.routerUrl, query: { id: route.query.ProjectDeclarationId, FieldCode: route.query.FieldCode } })
  }
}
const approvalNoList = ref([])//历史清单数据集合
const projectApprovalNoId = ref('')//选择的历史清单数据
// 选择历史数据
const HandleChange = (e) => {
  projectApprovalNoId.value = e
  HandleTableData()
}
//列表
const HandleTableData = (isFirst) => {

  filters.value.ProjectDeclarationId = route.query.ProjectDeclarationId
  filters.value.ProcessId = route.query.ProcessId
  filters.value.ProcessNodeId = route.query.ProcessNodeId
  filters.value.FieldCode = route.query.FieldCode
  if (isFirst) {
    filters.value.isFirst = isFirst
    filters.value.ProjectApprovalNoId = undefined
  } else {
    filters.value.isFirst = undefined
    filters.value.ProjectApprovalNoId = projectApprovalNoId.value
  }
  console.log('filters.value', filters.value)
  FindHistoryProjectList(filters.value).then(res => {
    console.log('res', res)
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      tableData.value = rows.data || [];//表格数据
      tableTotal.value = rows.dataCount || 0
      // console.log('tableData.value', tableData.value)
      if (isFirst && other) {
        columnData.value = other.listColumn || [];//表头（列表字段）
        searchList.value = other.listSearch || []//查询条件
        approvalNoList.value = other.listApprovalNo || []//查询条件
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.Code = undefined
  filters.value.key = undefined
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}

</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item class="flexItem">
            <el-button :icon="Back" @click="HandleBack">返回</el-button>
          </el-form-item>
          <!-- :label="route.query.type == 'projectexaminelist' ? '历史审批:' : '历史清单'" -->
          <el-form-item class="flexItem" v-if="approvalNoList.length > 1">
            <el-select v-model="filters.ProjectApprovalNoId" @change="HandleChange" style="width: 160px"
              :placeholder="route.query.type == 'projectexaminelist' ? '历史审批:' : '历史清单'">
              <el-option v-for="item in approvalNoList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="flexItem" v-if="searchList.length > 0">
            <el-input v-model.trim="filters.key" placeholder="请输入" style="width: 240px" class="input-with-select">
              <template #prepend>
                <el-select v-model="filters.Code" style="width: 120px">
                  <el-option v-for="item in searchList" :key="item.FieldValue" :label="item.Title"
                    :value="item.FieldValue" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column v-for="column in columnData" :key="column.FieldValue" :prop="column.FieldValue"
        :label="column.Title" :min-width="column.Width" :align="column.ContentStyle">
        <template #default="{ row }">
          {{ row[column.FieldValue] }}
        </template>
      </el-table-column>
      <el-table-column v-if="route.query.type == 'projectexaminelist'" prop="AuditRemark" label="审核意见"
        show-overflow-tooltip min-width="200">
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>

</template>
<style lang="scss" scoped>
.el-table {
  :deep(.el-input__wrapper) {
    border: none !important;
    box-shadow: none !important;
  }

  .input-focused {
    border: 1px solid #409EFF;
  }
}
</style>
