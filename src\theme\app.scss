/* 初始化样式
------------------------------- */
// * {
// 	margin: 0;
// 	padding: 0;
// 	box-sizing: border-box;
// 	outline: none !important;
// }

:root {
	--next-background-color-white: #ffffff;
	--next-color-white: #ffffff;
	--next-bg-main-color: #f8f8f8;
	--next-bg-color: #f5f5ff;
	--next-border-color-light: #f1f2f3;
	--next-color-primary-lighter: #ecf5ff;
	--next-color-success-lighter: #f0f9eb;
	--next-color-warning-lighter: #fdf6ec;
	--next-color-danger-lighter: #fef0f0;
	--next-color-dark-hover: #0000001a;
	--next-color-menu-hover: rgba(0, 0, 0, 0.2);
	--next-color-user-hover: rgba(0, 0, 0, 0.04);
	--next-color-seting-main: #e9eef3;
	--next-color-seting-aside: #d3dce6;
	--next-color-seting-header: #b3c0d1;
	--next-bg-menuBar: #041d8f; 
	// --next-bg-topBar: #041d8f; 
	--next-bg-topBar: #ffffff; 
	--next-bg-menuBarColor: #ffffff; 
	--next-bg-menuBarActiveColor: #031772;
	// --next-bg-topBarColor: #ffffff;
	--next-bg-topBarColor: #2f2f2f;
}

html,
body,
#app {
	background-color: var(--next-bg-main-color);
}

/* 主布局样式
------------------------------- */
.layout-container {


	.el-scrollbar {
		width: 100%;
	}

}

/* element plus 全局样式
------------------------------- */
.layout-breadcrumb-seting {
	.el-divider {
		background-color: rgb(230, 230, 230);
	}
}

/* nprogress 进度条跟随主题颜色
------------------------------- */
#nprogress {
	.bar {
		background: var(--el-color-primary) !important;
		z-index: 9999999 !important;
	}
}





/* 颜色值
------------------------------- */
.color-primary {
	color: var(--el-color-primary);
}

.color-success {
	color: var(--el-color-success);
}

.color-warning {
	color: var(--el-color-warning);
}

.color-danger {
	color: var(--el-color-danger);
}

.color-info {
	color: var(--el-color-info);
}