<script setup>
defineOptions({
    name: 'dangerchemicalsmsdslist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { UploadFilled, Search, Refresh } from '@element-plus/icons-vue'
import { DcScrapListFind, SchoolMaterialMsdsFileEdit, DcSchoolMaterialGetById, DccatalogGetClassTwo } from '@/api/dangerchemicals.js'
import { AttachmentUpload } from '@/api/user.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, tagsListStore } from "@/utils/index.js";
import { ElMessageBox, ElMessage } from 'element-plus';
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const route = useRoute()
const userStore = useUserStore()
const SchoolId = ref(0)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1, StockNumgt: 0, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const bogetList = ref([])//搜索条件
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const regDategeChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const RegDatele = ref("");
const regDateleChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}
//
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bogetList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//加载数据
onMounted(() => {
    SchoolId.value = route.query.SchoolId || 0
    if (route.query.isTagRouter) {
        HandleTableData();
        DccatalogGetClassTwoUser();
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    SchoolId.value = route.query.SchoolId || 0
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData();
            DccatalogGetClassTwoUser();
        }
    })
})

const dialogData = ref({})
const refForm = ref()//
const dialogVisible = ref(false)


// 修改弹出窗体
const HandleEdit = (row, e) => {
    dialogVisible.value = true
    uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
    });
    DcSchoolMaterialGetById({ Id: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            dialogData.value = rows
            if (rows.MsdsFile && rows.MsdsFile.length > 0) {
                const extension = rows.MsdsFile.match(/\.\w+$/)?.[0] || '';
                uploadFileData.value[0].categoryList.push({ Id: row.Id, Path: rows.MsdsFile, Ext: extension, Title: row.Name });
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交
const HandleSubmit = () => {
    const attIdArr = [];
    uploadFileData.value.map(function (item, index) {
        if (item.categoryList.length > 0) {
            item.categoryList.map(function (olditem, oldindex) {
                attIdArr.push(olditem.Path);
            });
        }
        if (item.fileLChildList.length > 0) {
            item.fileLChildList.map(function (newitem, newindex) {
                attIdArr.push(newitem.Path);
            });
        }
    });
    const param = { id: dialogData.value.Id, msdsFile: attIdArr[0] || "" };
    console.log(param);
    SchoolMaterialMsdsFileEdit(param).then(res => {
        if (res.data.flag == 1) {
            dialogVisible.value = false
            ElMessage.success(res.data.msg || '保存成功')
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 列表
const HandleTableData = () => {
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcScrapListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {

    filters.value.pageIndex = 1;
    filters.value.RegDatege = undefined;
    filters.value.RegDatele = undefined;
    filters.value.Name = undefined;
    filters.value.TwoCatalogId = undefined
    RegDatele.value = '';
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

//附件上传
const uploadFileData = ref([{ FileCategory: 2964, fileLChildList: [], categoryList: [], IsFilled: 0, FileSize: 10, MaxFileNumber: 1, Memo: "文件小于5M，支持pdf和图片文件", Name: "msds：", UploadFileType: ".pdf.jpg.jpeg.png", UploadFileTypeAccept: ".pdf,.jpg,.jpeg,.png" }])
const fileFile = ref()
const uploadRef = ref()
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.fileLChildList.length + item.categoryList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    console.log("item", item, "index", index)
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下危险化学品管理制度
            uploadFileData.value[0].fileLChildList.push(rows[0])
            //uploadFileData.value[index].fileLChildList.push({Id:rows[0].Id,Path:rows[0].Path,Ext:rows[0].Ext,Title:rows[0].Title})
            // console.log("uploadFileData.value", uploadFileData.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    let path = e.FieldValue;
    viewPhotoList.value = imgList.map(t => t.FieldValue)
    e.Ext = (typeof e.Ext === "string") ? e.Ext.toLowerCase() : "";
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.FieldName + e.Ext
        fileDownload(e.FieldValue, title)
    }

}

// 附件图片预览与文件下载
const lookFileListDownload = (filePath, ext, title) => {
    viewPhotoList.value = [filePath];
    ext = (typeof ext === "string") ? ext.toLowerCase() : "";
    if (ext == ".png" || ext == ".jpg" || ext == ".jpng") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (filePath == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        fileDownload(filePath, title)
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!RegDatele.value) return false;
    return time >= new Date(RegDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="入库时间"
                            @change="regDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="RegDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="入库时间" @change="regDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in bogetList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="140" align="center"></el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="MSDS" min-width="160" align="center">
                <template #default="{ row }">
                    <span v-if="row.MsdsFile" style="cursor: pointer;color:#409eff;"
                        @click="lookFileListDownload(row.MsdsFile, row.Ext, row.Name)">查看</span>
                    <el-button v-else type="primary" link @click="HandleEdit(row)">上传</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="560" :lazy="true" title="MSDS上传">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" label-width="140px" status-icon>
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                            :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                            :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                            <el-button type="success" size="small" :icon="UploadFilled"
                                @click="MaxFileNumberClick(item)">上传</el-button>
                        </el-upload>
                        <div class="fileFlex">
                            <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id"
                                style="color:#409EFF ;width:200px">
                                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                                    <Delete />
                                </el-icon>
                                <span style="cursor: pointer;"
                                    @click="lookFileListDownload(itemCate.Path, itemCate.Ext, itemCate.Title)">
                                    {{ dialogData.Name }}(MSDS)
                                </span>
                            </div>
                            <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id" style="width:200px">
                                <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                                    <Delete />
                                </el-icon>
                                {{ dialogData.Name }}(MSDS)
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>