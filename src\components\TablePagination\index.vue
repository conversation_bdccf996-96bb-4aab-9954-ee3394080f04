<script setup>
import { ref } from 'vue'
const props = defineProps({
    //总数量
    total: {
        type: Number,
        default: 1
    },
    //当前页码
    pageIndex: {
        type: Number,
        default: 1
    },
    //当前页数
    pageSize: {
        type: Number,
        default: 10
    },
    //选择分页条数
    pageSizes: {
        type: Array,
        default: () => [10, 20, 30, 40]
    },
    //布局
    layout: {
        type: String,
        default: 'total, prev, pager, next, sizes, jumper'
    },
    background: {
        type: Boolean,
        default: true
    }
})
const currentPage = ref(props.pageIndex)

const emit = defineEmits(['handleChange', 'update:pageIndex', 'update:pageSize'])
const handleSizeChange = (val) => {
    //修改pageSize的值
    emit('update:pageSize', val)
    //调用请求
    emit('handleChange')
}
const handleCurrentChange = (val) => {
    // console.log("修改pageIndex的值", val)
    //修改pageIndex的值
    emit('update:pageIndex', val)
    //调用请求
    emit('handleChange')
}
</script>

<template>
    <!-- 分页 -->

    <el-row v-if="props.total > 0">
        <el-col class="flexBox">
            <el-pagination class="flexItem" size="small" background  :current-page="pageIndex"
                :page-size="props.pageSize" :layout="props.layout" :total="props.total"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </el-col>
    </el-row>
</template>

<style scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}
</style>