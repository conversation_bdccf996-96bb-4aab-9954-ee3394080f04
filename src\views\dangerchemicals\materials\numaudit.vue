<script setup>
defineOptions({
    name: 'dangerchemicalsmaterialsnumaudit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcInventorySave, DccatalogGetClassTwo, DcMaterialsNumAuditFind, DcSchoolMaterialReturnLibrary, DcScrapSave
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import { limit, pageQuery } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const StatuzSolicitedList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Statuz", SortType: "ASC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    auditStatuz: [
        { required: true, message: '请选择审核结果', trigger: 'change' },
    ],
}
const routerObject = ref({})//成页面携带的参数对象 
const options = ref([
    { value: 'Name', label: '危化品名称' },
    { value: 'Model', label: '规格属性' },
    { value: 'Brand', label: '品牌' },
])
const filtersKey = ref('')
const filtersValue = ref('Name')
const auditId = ref('')
//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

const stockNum = ref('')
// 审核
const HandleEdit = (row) => {
    dialogVisible.value = true
    auditId.value = row.Id
    formData.value.auditStatuz = undefined
    nextTick(() => {
        refForm.value.resetFields()
    })
}

// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    filters.value.OptType = routerObject.value.t;
    DcMaterialsNumAuditFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.TwoCatalogId = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
//报废提交
const HandleSubmit = () => {
    if (routerObject.value.t == 1) {
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            DcInventorySave({ id: auditId.value, statuz: formData.value.auditStatuz }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '盘点成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    } else if (routerObject.value.t == 2) {
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            DcScrapSave({ id: auditId.value, statuz: formData.value.auditStatuz }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '报废成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    } else if (routerObject.value.t == 3) {
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            DcSchoolMaterialReturnLibrary({ id: auditId.value, statuz: formData.value.auditStatuz }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '退货成功')
                    dialogVisible.value = false
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    }


}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="OptNum" :label="routerObject.t == 1 ? '盈亏数量' : routerObject.t == 2 ? '报废数量' : '退货数量'"
                min-width="100" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Price" label="单价" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Brand || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="OptTime"
                :label="routerObject.t == 1 ? '盘点时间' : routerObject.t == 2 ? '报废时间' : '退货时间'" min-width="120"
                align="center">
                <template #default="{ row }">
                    {{ row.OptTime ? row.OptTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="OptReason"
                :label="routerObject.t == 1 ? '情况说明' : routerObject.t == 2 ? '报废原因' : '退货理由'" min-width="120"
                align="center" show-overflow-tooltip>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.Statuz == 0"> 待审核</span>
                    <span v-else-if="row.Statuz == 1" style="color: green"> 审核通过 </span>
                    <span v-else style="color: #F56C6C"> 审核不通过 </span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 0" type="primary" link @click="HandleEdit(row)">审核</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="报废">
            <template #content>
                <el-form style="min-width: 320px; " class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="审核结果：" prop="auditStatuz">
                        <el-radio-group v-model="formData.auditStatuz">
                            <el-radio value="1">通过</el-radio>
                            <el-radio value="2">不通过</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 确认 </el-button>
                </span>
            </template>
        </app-box>

    </div>
</template>
<style lang="scss" scoped></style>