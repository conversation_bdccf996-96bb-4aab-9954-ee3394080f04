import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import { nextTick } from 'vue'
import { useUserStore } from '@/stores/modules/user'

// createRouter 创建路由实例
// createWebHistory history模式:地址栏不带#号
// createWebHashHistory hash模式:地址栏带#号
// const userStore1 = useUserStore()
// console.log('userStore1', userStore1)

// 因router初始化之前无法访问 useUserStore(),在这里重新设置 platformType，需根据平台修改
const platformType = 2 //平台类型 1:校服平台 2:工作流管理平台（审批配置平台）3：危化品
let routes = []
if (platformType === 1) {
  routes = [
    {
      path: '/',
      component: () => import('@/views/HomePage/Index.vue'),
      meta: { requireAuth: false, title: '首页' }
    },
    {
      path: '/oauth/login',
      component: () => import('@/views/Login/SilentLogin.vue'),
      meta: { requireAuth: false, title: '静默登录' }
    },
    {
      path: '/oauth/login-success',
      component: () => import('@/views/Login/LoginCallback.vue'),
      meta: { requireAuth: false, title: '登录回调' }
    },
    {
      name: 'Login', // 确保新路由有相同的名称
      path: '/login',
      component: () => import('@/views/login/LoginPage.vue'),
      meta: { requireAuth: false, title: '登录' }
    },
    {
      path: '/reg',
      component: () => import('@/views/login/regPage.vue'),
      meta: { requireAuth: false, title: '注册' }
    },
    {
      path: '/exhibition',
      component: () => import('@/views/HomePage/exhibition.vue'),
      meta: { requireAuth: false, title: '校服展示' }
    },
    {
      path: '/information',
      component: () => import('@/views/HomePage/information.vue'),
      meta: { requireAuth: false, title: '平台资讯' }
    },
    {
      path: '/articlelist',
      component: () => import('@/views/HomePage/articlelist.vue'),
      meta: { requireAuth: false, title: '资讯列表' }
    },
    {
      path: '/articledetail',
      component: () => import('@/views/HomePage/articledetail.vue'),
      meta: { requireAuth: false, title: '详情资讯' }
    },
    {
      path: '/unitauthentic',
      component: () => import('@/views/HomePage/unitauthentic.vue'),
      meta: { requireAuth: false, title: '单位认证审核' }
    },
    {
      path: '/preview',
      component: () => import('@/views/preview/preview.vue'),
      meta: { requireAuth: false, title: '预览' }
    },
    {
      path: '/uniform',
      component: () => import('@/views/layout/LayoutContainer.vue'),
      redirect: '/uniform/home',
      meta: { requireAuth: false },
      children: [
        {
          path: '/uniform/home/<USER>',
          component: () => import('@/views/schooluniformmanagement/home/<USER>'),
          meta: { requireAuth: true, title: '首页' }
        }
      ]
    },
    {
      path: '/user',
      component: () => import('@/views/layout/LayoutContainer.vue'),
      redirect: '/user/my',
      meta: { requireAuth: false },
      children: [
        {
          path: '/user/my/useredit',
          component: () => import('@/views/user/my/useredit.vue'),
          meta: { requireAuth: true, title: '个人信息' }
        },
        {
          path: '/user/my/changepass',
          component: () => import('@/views/user/my/changepass.vue'),
          meta: { requireAuth: true, title: '修改密码' }
        }
      ]
    },
    // {
    //   path: '/base',
    //   component: () => import('@/views/layout/LayoutContainer.vue'),
    //   redirect: '/base/config',
    //   meta: { requireAuth: false },
    //   children: [
    //     {
    //       path: '/base/config/ceshi',
    //       component: () => import('@/views/base/config/ceshi.vue'),
    //       meta: { requireAuth: true, title: '测试页面' }
    //     }
    //   ]
    // },
    // 空白路由重定向到首页页面
    {
      path: '/:catchAll(.*)',
      component: () => import('@/views/HomePage/Index.vue'),
      meta: { requireAuth: false, title: '首页' }
    }
    // 空白路由重定向到404页面
    // {
    //   path: '/:catchAll(.*)',
    //   component: () => import('@/views/error/404.vue'),
    //   meta: { requireAuth: false, title: '404错误' }
    // }
  ]
} else if (platformType === 2) {
  routes = [
    {
      path: '/approval',
      component: () => import('@/views/layout/LayoutContainer.vue'),
      redirect: '/approval/home',
      meta: { requireAuth: false },
      children: [
        {
          path: '/approval/home/<USER>',
          component: () => import('@/views/approvalconfiguration/home/<USER>'),
          meta: { requireAuth: true, title: '首页' }
        }
      ]
    },
    {
      name: 'Login', // 确保新路由有相同的名称
      path: '/',
      component: () => import('@/views/login/LoginPageBlue.vue'),
      meta: { requireAuth: false, title: '登录' }
    },
    {
      path: '/oauth/login',
      component: () => import('@/views/Login/SilentLogin.vue'),
      meta: { requireAuth: false, title: '静默登录' }
    },
    {
      path: '/oauth/login-success',
      component: () => import('@/views/Login/LoginCallback.vue'),
      meta: { requireAuth: false, title: '登录回调' }
    },
    {
      path: '/information',
      component: () => import('@/views/HomePage/information.vue'),
      meta: { requireAuth: false, title: '平台资讯' }
    },
    {
      path: '/articlelist',
      component: () => import('@/views/HomePage/articlelist.vue'),
      meta: { requireAuth: false, title: '资讯列表' }
    },
    {
      path: '/articledetail',
      component: () => import('@/views/HomePage/articledetail.vue'),
      meta: { requireAuth: false, title: '详情资讯' }
    },
    {
      path: '/user',
      component: () => import('@/views/layout/LayoutContainer.vue'),
      redirect: '/user/my',
      meta: { requireAuth: false },
      children: [
        {
          path: '/user/my/useredit',
          component: () => import('@/views/user/my/useredit.vue'),
          meta: { requireAuth: true, title: '个人信息' }
        },
        {
          path: '/user/my/changepass',
          component: () => import('@/views/user/my/changepass.vue'),
          meta: { requireAuth: true, title: '修改密码' }
        }
      ]
    },
    // 空白路由重定向到登录页面
    {
      path: '/:catchAll(.*)',
      component: () => import('@/views/login/LoginPageBlue.vue'),
      meta: { requireAuth: false, title: '登录' }
    }
    // 空白路由重定向到404页面
    // {
    //   path: '/:catchAll(.*)',
    //   component: () => import('@/views/error/404.vue'),
    //   meta: { requireAuth: false, title: '404错误' }
    // }
  ]
} else if (platformType === 3) {
  routes = [
    {
      path: '/',
      component: () => import('@/views/login/LoginPage.vue'),
      meta: { requireAuth: false, title: '登录' }
    },
    {
      path: '/oauth/login',
      component: () => import('@/views/Login/SilentLogin.vue'),
      meta: { requireAuth: false, title: '静默登录' }
    },
    {
      path: '/oauth/login-success',
      component: () => import('@/views/Login/LoginCallback.vue'),
      meta: { requireAuth: false, title: '登录回调' }
    },
    {
      path: '/information',
      component: () => import('@/views/HomePage/information.vue'),
      meta: { requireAuth: false, title: '平台资讯' }
    },
    {
      path: '/articlelist',
      component: () => import('@/views/HomePage/articlelist.vue'),
      meta: { requireAuth: false, title: '资讯列表' }
    },
    {
      path: '/articledetail',
      component: () => import('@/views/HomePage/articledetail.vue'),
      meta: { requireAuth: false, title: '详情资讯' }
    },
    {
      path: '/user',
      component: () => import('@/views/layout/LayoutContainer.vue'),
      redirect: '/user/my',
      meta: { requireAuth: false },
      children: [
        {
          path: '/user/my/useredit',
          component: () => import('@/views/user/my/useredit.vue'),
          meta: { requireAuth: true, title: '个人信息' }
        },
        {
          path: '/user/my/changepass',
          component: () => import('@/views/user/my/changepass.vue'),
          meta: { requireAuth: true, title: '修改密码' }
        }
      ]
    },
    // 空白路由重定向到404页面
    {
      path: '/:catchAll(.*)',
      component: () => import('@/views/error/404.vue'),
      meta: { requireAuth: false, title: '404错误' }
    }
  ]
}
// nextTick(() => {
//   const userStore1 = useUserStore()
//   console.log('userStore1', userStore1)
// })
// import.meta.env.BASE_URL 是vite中的环境变量 就是 vite.config.js 中的 base 配置项
// console.log('import.meta.env.BASE_URL', import.meta.env.BASE_URL)
const router = createRouter({
  // history: createWebHistory(import.meta.env.BASE_URL),
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: routes
})

// // 异步获取动态路由配置
// export async function setupRouter() {
//   // 等待 Pinia 初始化
//   await new Promise((resolve) => setTimeout(resolve, 0))
//   const userStore = useUserStore()
//   // 获取当前所有路由
//   const currentRoutes = router.getRoutes()

//   if (userStore.platformType == 1) {
//     const loginRoute = currentRoutes.find((route) => route.path === '/login')
//     if (loginRoute) {
//       // 中小学校服管理与备案平台 11

//       // 移除现有的登录路由
//       router.removeRoute('Login')
//       // // 添加新的登录路由
//       if (userStore.defaultSet['8002_DLYM_BJ' == '中小学校服管理与备案平台 11']) {
//         router.addRoute({
//           name: 'Login', // 确保新路由有相同的名称
//           path: '/login',
//           component: () => import('@/views/login/LoginPage.vue'),
//           meta: { requireAuth: false, title: '登录' }
//         })
//       } else {
//         router.addRoute({
//           name: 'Login', // 确保新路由有相同的名称
//           path: '/login',
//           component: () => import('@/views/login/LoginPageBlue.vue'),
//           meta: { requireAuth: false, title: '登录' }
//         })
//       }
//     }
//   } else {
//     const loginRoute = currentRoutes.find((route) => route.path === '/')
//     if (loginRoute) {
//       // 移除现有的登录路由
//       router.removeRoute('Login')
//       // // 添加新的登录路由
//       router.addRoute({
//         name: 'Login', // 确保新路由有相同的名称
//         path: '/',
//         component: () => import('@/views/login/LoginPageBlue.vue'),
//         meta: { requireAuth: false, title: '登录' }
//       })
//     }
//   }
//   return router
// }

// vue文件列表
export const vueFilePathList = {
  ...import.meta.glob('@/views/*'),
  ...import.meta.glob('@/views/*/*'),
  ...import.meta.glob('@/views/*/*/*'),
  ...import.meta.glob('@/views/*/*/*/*'),
  ...import.meta.glob('@/views/*/*/*/*/*')
}

// 动态添加路由
export const addRoute = (item, parent) => {
  // console.log('动态添加路由item', item.path)

  let path = item.path
  if (item.path.indexOf('@') > -1) {
    const url = item.path
    const separatorIndex = url.indexOf('@')
    path = url.substring(0, separatorIndex) // 路径
  }
  // console.log('动态添加路由path', path)
  // console.log('动态添加路由item。path', item.path)

  let meta = { ...item.meta, ...{ parent } }
  router.addRoute({
    path: '/' + item.path + '_self',
    name: item.id + '_self',
    component: () => import('@/views/layout/LayoutContainer.vue'),
    redirect: '/' + item.path,
    children: [
      {
        path: '/' + item.path,
        name: item.id,
        component: vueFilePathList['/src/views' + '/' + path + '.vue'],
        meta: meta
      }
    ]
  })
}
// 动态路由
export const addDynamicRoutes = (menu, parent) => {
  for (let index = 0; index < menu.length; index++) {
    const item = menu[index]
    if (item.children) {
      for (let cIndex = 0; cIndex < item.children.length; cIndex++) {
        //二级路由
        const child = item.children[cIndex]
        if (child.children) {
          //递归获取
          addDynamicRoutes(child.children, child)
        } else if (child.path != '@url') {
          // 添加路由
          addRoute(child, item)
        } else {
          //
        }
      }
    } else if (item.path != '@url') {
      // 添加路由
      addRoute(item, parent)
    } else {
      //
    }
  }
}

// 前置守卫
// 返回undefined/true直接放行
// 返回false 拦回from的地址页面
router.beforeEach((to, from) => {
  const userStore = useUserStore()
  // 路径内存在汉字时，浏览器路径会被编译，需要转译成汉字，重定向到正确的页面
  if (to.path.indexOf('%') > -1) {
    to.path = decodeURIComponent(to.path)
    to.fullPath = decodeURIComponent(to.fullPath)
    to.href = decodeURIComponent(to.href)
    // console.log('路由变化事件', to, from)
    return to
  }
  // console.log('路由变化事件', userStore.menu)
  // console.log('路由变化事件decodeURIComponent', decodeURIComponent(to.path))

  // 如果没有token,且访问的是非登录页面,拦截到登录页面
  if (userStore.platformType === 1) {
    if (to.meta.title)
      document.title = userStore.defaultSet['8002_DLYM_BJ'] || '中小学校服管理与备案平台'
    if (to.meta.requireAuth === true && !userStore.token && to.path != '/login') {
      return '/login'
    }
    //路由变化事件

    if (to.path != '/login' && to.path != '/reg') {
      userStore.setOneActiveTag(to.path, from.path)
    }
  } else if (userStore.platformType === 2) {
    if (to.meta.title) document.title = userStore.defaultSet['8002_DLYM_BJ'] || '审批配置平台'
    if (to.meta.requireAuth === true && !userStore.token && to.path != '/') {
      return '/'
    }
    //路由变化事件
    if (to.path != '/') {
      userStore.setOneActiveTag(to.path, from.path)
    }
  } else if (userStore.platformType === 3) {
    if (to.meta.title) document.title = userStore.defaultSet['8002_DLYM_BJ'] || '危化品平台'
    if (to.meta.requireAuth === true && !userStore.token && to.path != '/') {
      return '/'
    }
    //路由变化事件
    if (to.path != '/') {
      userStore.setOneActiveTag(to.path, from.path)
    }
  }
  // console.log('to.path', to)
  // console.log('from.path', from.fullPath)
  userStore.setPreviousPath(from.fullPath)
  // console.log('路由变化事件', to, from)
})

export default router
