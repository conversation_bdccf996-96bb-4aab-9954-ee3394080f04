<script setup>
defineOptions({
  name: 'treasurylist'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
  FindPagedeList
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { pageQuery } from "@/utils/index.js";
import router from '@/router'
const route = useRoute()
const treasuryData = ref([])
const routerObject = ref({})
const imageImports = import.meta.glob(`@/assets/img/*.png`);
//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(async () => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

//获取数据
const HandleTableData = (isFirst) => {
  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
  FindPagedeList({ moduleId: routerObject.value.moduleId }).then(res => {
    console.log('res', res)
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      treasuryData.value = rows || [];
      treasuryData.value.forEach(item => {
        // 动态加载图片路径
        const imagePath = `/src/assets/img/${item.Logo}`;
        if (imageImports[imagePath]) {
          imageImports[imagePath]().then(img => {
            item.defaultImage = img.default;
          }).catch(e => {
            item.defaultImage = new URL('@/assets/img/collaraudit.png', import.meta.url).href; // 使用默认图片
          });
        } else {
          item.defaultImage = new URL('@/assets/img/collaraudit.png', import.meta.url).href; // 使用默认图片
        }
      })
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 跳转查看对应的查询统计列表
const handlePage = (item) => {
  console.log("routerObject.value.moduleId", routerObject.value.moduleId)
  router.push({ path: "/approvalconfiguration/pages/treasury/listitem" + '@id=' + item.Id, query: { moduleId: routerObject.value.moduleId } })
}
</script>
<template>
  <div class="viewContainer" style=" display: flex;flex-wrap: wrap;">
    <div v-for="item in treasuryData" :key="item.Id" @click="handlePage(item)" style="cursor:pointer">
      <div style="width: 150px;  margin: 20px;">
        <div style="width: 100px;margin: 0 auto;">
          <img :src="item.defaultImage" alt="" style="width: 100%;">
        </div>
        <div class="item-txt">
          {{ item.ShowName }}
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.item-txt {
  width: 100%;
  // font-size: 12px;
  // color: #333;
  text-align: center;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
