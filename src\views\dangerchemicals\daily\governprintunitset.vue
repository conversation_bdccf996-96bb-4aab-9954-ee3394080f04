<script setup>
defineOptions({
    name: 'dangerchemicalsdailygovernprintunitset'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Select, Search
} from '@element-plus/icons-vue'
import { DcGovernSetGet, DcGovernUnitSetSave } from '@/api/daily.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    GetGovernSetData();
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

//验证
const ruleForm = {
    UnitName: [
        { required: true, message: '请填写填报单位名称', trigger: 'change' },
    ],
    UserName: [
        { required: true, message: '请填写填报人姓名', trigger: 'change' },
    ],
    UserPhoneNumber: [
        { required: true, message: '请填写填报人手机', trigger: 'change' },
        {
            pattern: /^1\d{10}$/,
            message: '手机号码不正确',
            trigger: 'blur'
        }
    ]
}
const refForm = ref()
const formData = ref({})
const GetGovernSetData = () => {
    DcGovernSetGet().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data;
            formData.value = rows;
            nextTick(() => {
                refForm.value.resetFields()
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        if (formData.value.UnitName && formData.value.UnitName.length > 100) {
            ElMessage.error("填报单位名称不能大于100个字符。");
            return;
        }
        if (formData.value.UserName && formData.value.UserName.length > 60) {
            ElMessage.error("填报人姓名不能大于63个字符。");
            return;
        }
        DcGovernUnitSetSave(formData.value).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '设置成功')
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

</script>
<template>
    <div class="viewContainer">
        <el-form style="width: 80%; min-width: 600px;" class="mobile-box" @submit.prevent ref="refForm"
            :rules="ruleForm" :model="formData" label-width="180px" status-icon>
            <el-form-item label="填报单位名称：" prop="UnitName">
                <el-input v-model="formData.UnitName" style="width: 300px;"></el-input>
                <span style="line-height: normal;color: #999999;font-size: 14px;margin-left: 5px;">（须与申报单位公章一致） </span>
            </el-form-item>
            <el-form-item label="填报人姓名：" prop="UserName">
                <el-input v-model="formData.UserName" style="width: 300px;"></el-input>
            </el-form-item>
            <el-form-item label="填报人手机：" prop="UserPhoneNumber">
                <el-input v-model="formData.UserPhoneNumber" style="width: 300px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<style lang="scss" scoped></style>