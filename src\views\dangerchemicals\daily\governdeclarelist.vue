<script setup>
defineOptions({
    name: 'dangerchemicalsdailygoverndeclarelist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import { DcGovernDeclareSummaryWeekFind, DcGovernDeclareSummaryMonthFind } from '@/api/daily.js'
import { UserFindMyUnitUserNameByRoleId } from '@/api/dangerchemicals.js'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { ElMessageBox, ElMessage } from 'element-plus'
import { pageQuery } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const routerObject = ref({})//成页面携带的参数对象 

const declareStatuz = [
    { Id: 1, Name: '已申报' },
    { Id: 0, Name: '未申报' },
];
const constweekArr = ref([]);
const constMontnhArr = ref([]);
const loadWeekData = (row) => {
    if (routerObject.value.t == 1) {
        let date = new Date();
        let year = date.getFullYear();
        let weekMax = 53;
        if (year % 4 == 0) {
            weekMax = 54;
        }
        for (let index = 1; index < weekMax; index++) {
            constweekArr.value.push({ Id: index, Name: '第' + index + '周' });
        }
    } else if (routerObject.value.t == 2) {
        let months = "一,二,三,四,五,六,七,八,九,十,十一,十二";
        let monthArr = months.split(',');
        for (let i = 0; i < monthArr.length; i++) {
            constMontnhArr.value.push({ Id: (i + 1), Name: (monthArr[i] + '月份') });
        }
    }
}
const declareYearArr = ref([]);
const loadYearData = (row) => {
    const date = new Date();
    const year = date.getFullYear();
    declareYearArr.value.push({ Id: (year + 1), Name: (year + 1) });

    for (let index = year; index > (year - 9); index--) {
        declareYearArr.value.push({ Id: index, Name: index });
    }
}

//加载数据
onMounted(() => {
    routerObject.value = pageQuery(route.path)
    if (!routerObject.value.c || routerObject.value.c != 1) {
        routerObject.value.c = 0;
    }
    loadWeekData();
    loadYearData();
    filters.value.GovernYear = new Date().getFullYear();
    if (route.query.isTagRouter) {
    }

    HandleTableData();
    DeclareUserload();
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

//表格参数
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "ASC" }, { SortCode: "NumberCyclez", SortType: "ASC" }] })

//搜索条件
const declareUserList = ref([])
const filtersChange = (row) => {
    filters.value.pageIndex = 1;
    if (!filters.value.GovernYear) {
        filters.value.GovernYear = new Date().getFullYear();
    }
    HandleTableData()
}
// 加载申报人
const DeclareUserload = () => {
    UserFindMyUnitUserNameByRoleId({ rids: 355 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            declareUserList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}


const declareSelectId = ref();
// 列表
const HandleTableData = () => {
    filters.value.Idgt = undefined;
    filters.value.Id = undefined;
    if (declareSelectId.value == 1) {
        filters.value.Idgt = 0;
    } else if (declareSelectId.value == 0) {
        filters.value.Id = 0;
    }
    if (routerObject.value.t == 2) {
        DcGovernDeclareSummaryMonthFind(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, total } = res.data.data
                tableData.value = rows
                tableTotal.value = Number(total)
            } else {
                ElMessage.error(res.data.msg)
            }
        });
    }
    else if (routerObject.value.t == 1) {

        DcGovernDeclareSummaryWeekFind(filters.value).then(res => {
            if (res.data.flag == 1) {
                const { rows, total } = res.data.data
                tableData.value = rows
                tableTotal.value = Number(total)
            } else {
                ElMessage.error(res.data.msg)
            }
        });
    }
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.UserId = undefined;
    filters.value.GovernYear = new Date().getFullYear();
    filters.value.NumberCyclez = undefined;
    filters.value.Idgt = undefined;
    filters.value.Id = undefined;
    declareSelectId.value = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

//查看上报详情
const HandleDetail = (row, e) => {
    router.push({ path: "./governdetail", query: { SchoolId: row.SchoolId, Id: row.Id, c: routerObject.value.c, t: routerObject.value.t } })
}

const getMonthName = (month) => {
    var html = "";
    switch (month) {
        case 1:
            html = "一";
            break;
        case 2:
            html = "二";
            break;
        case 3:
            html = "三";
            break;
        case 4:
            html = "四";
            break;
        case 5:
            html = "五";
            break;
        case 6:
            html = "六";
            break;
        case 7:
            html = "七";
            break;
        case 8:
            html = "八";
            break;
        case 9:
            html = "九";
            break;
        case 10:
            html = "十";
            break;
        case 11:
            html = "十一";
            break;
        case 12:
            html = "十二";
            break;
    }
    html = (html + '月份');
    return html
}

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">

                    <el-form-item class="flexItem">
                        <el-select v-model="filters.GovernYear" clearable placeholder="年度" style="width: 120px"
                            @change="filtersChange">
                            <el-option v-for="item in declareYearArr" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-if="routerObject.t == 1" v-model="filters.NumberCyclez" clearable placeholder="周次"
                            style="width: 80px" @change="filtersChange">
                            <el-option v-for="item in constweekArr" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                        <el-select v-else-if="routerObject.t == 2" v-model="filters.NumberCyclez" clearable
                            placeholder="月份" style="width: 80px" @change="filtersChange">
                            <el-option v-for="item in constMontnhArr" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.UserId" clearable placeholder="申报人" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in declareUserList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="declareSelectId" clearable placeholder="申报结果" style="width: 100px"
                            @change="filtersChange">
                            <el-option v-for="item in declareStatuz" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="id" default-expand-all
            header-cell-class-name="headerClassName">
            <el-table-column prop="GovernYear" label="年度" width="100" align="center"></el-table-column>
            <el-table-column prop="NumberCyclez" :label="routerObject.t == 2 ? '月份' : '周次'" width="80" align="center">
                <template #default="{ row }">
                    <span v-if="row.Id && routerObject.t == 2">{{ getMonthName(row.NumberCyclez) }}</span>
                    <span v-else-if="row.Id">第{{ row.NumberCyclez }}周</span>
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="申报时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="UserName" label="申报人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="Statuz" label="申报结果" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.Id && row.Id > 0">已申报</span>
                    <span v-else-if="routerObject.t == 2 && row.CurrentMonthNum <= row.NumberCyclez">--</span>
                    <span v-else-if="routerObject.t != 2 && row.CurrentMonthNum < row.NumberCyclez">--</span>
                    <span v-else>未申报</span>
                </template>
            </el-table-column>
            <el-table-column prop="ProblemNum" label="发现问题（个）" min-width="130" align="center">
                <template #default="{ row }">
                    <span v-if="row.Id && row.Id > 0">{{ row.ProblemNum }}</span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="RectifyProblemNum" label="已整改问题（个）" min-width="140" align="center">
                <template #default="{ row }">
                    <span v-if="row.Id && row.Id > 0">{{ row.RectifyProblemNum }}</span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="DangerNum" label="发现隐患（个）" min-width="130" align="center">
                <template #default="{ row }">
                    <span v-if="row.Id && row.Id > 0">{{ row.DangerNum }}</span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column prop="RectifyDangerNum" label="已整改隐患（个）" min-width="140" align="center">
                <template #default="{ row }">
                    <span v-if="row.Id && row.Id > 0">{{ row.RectifyDangerNum }}</span>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" min-width="160" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Id && row.Id > 0" type="primary" link @click="HandleDetail(row)">查看</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>