<script setup>
import { onMounted, ref } from 'vue';
import {
  Plus, Select
} from '@element-plus/icons-vue'
import {
  PuserSetuserinfo, PuserGetuserinfo, UploadPosthead
} from '@/api/user.js'
import { useUserStore } from '@/stores';
import { ElMessageBox, ElMessage } from 'element-plus'

const refForm = ref()
const userStore = useUserStore()
const formData = ref({})
const ruleForm = {
  Name: [
    { required: true, message: '姓名不能为空', trigger: 'blur' },
  ],
  Mobile: [
    { required: true, message: '手机号不能为空', trigger: 'blur' }, {
      pattern: /^1[0-9]{10}$/,
      message: '请输入11位手机号码',
      trigger: 'blur'
    }
  ],
}
onMounted(() => {
  PuserGetuserinfoUser()
})

// 
// 获取个人信息
const PuserGetuserinfoUser = () => {
  PuserGetuserinfo().then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      formData.value = rows
      // 修改pinia数据
      // userStore.$patch(state => {
      //   state.userInfo.Name = rows.Name
      //   state.userInfo.Mobile = rows.Mobile
      // })
    } else {
      ElMessage.error(res.data.msg)
    }
  }).catch((err) => {
  })
}

//提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    ElMessageBox.confirm('确定保存吗?')
      .then(() => {
        PuserSetuserinfo(formData.value).then((res) => {
          if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '保存成功')
            // 修改pinia数据
            userStore.$patch(state => {
              state.userInfo.Name = formData.value.Name
              state.userInfo.Mobile = formData.value.Mobile
              if (formData.value.HeadPortrait) {
                state.userInfo.HeadPortrait = formData.value.HeadPortrait
              }

            })
          } else {
            ElMessage.error(res.data.msg)
          }
        })
      })
      .catch((err) => {
      })
  })
}
const imageUrl = ref('')
const fileFile = ref()

const handleAvatarSuccess = (
  response,
  uploadFile
) => {
  // console.log("response", response, "uploadFile", uploadFile)
  imageUrl.value = URL.createObjectURL(uploadFile.raw)
}

const beforeAvatarUpload = (rawFile) => {
  console.log("rawFile", rawFile)
  fileFile.value = rawFile
  if (rawFile.size / 1024 / 1024 > 5) {
    ElMessage.error('上传头像图片大小不能超过 5MB!')
    return false
  }
  return true
}
// 附件上传
const httpRequest = () => {
  UploadPosthead({ file: fileFile.value }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      imageUrl.value = rows

      formData.value.HeadPortrait = rows

    } else {
      formData.value.HeadPortrait = undefined
      ElMessage.error(res.data.msg)
    }

  })
}

</script>
<template>
  <el-form style="width: 800px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData" :rules="ruleForm"
    label-width="180px" status-icon>
    <el-form-item label="登录帐号">
      <el-input v-model="formData.AccoutName" readonly></el-input>
    </el-form-item>
    <el-form-item label="姓名" prop="Name">
      <el-input v-model="formData.Name" auto-complete="off"></el-input>
    </el-form-item>
    <el-form-item label="手机号码" prop="Mobile">
      <el-input v-model="formData.Mobile" auto-complete="off"></el-input>
    </el-form-item>
    <el-form-item label="微信号">
      <el-input v-model="formData.Wechat" auto-complete="off"></el-input>
    </el-form-item>
    <el-form-item label="头像">
      <el-upload class="avatar-uploader" :show-file-list="false" action="#" accept="image/png,image/jpg,image/jpeg"
        :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :http-request="httpRequest">
        <img v-if="formData.HeadPortrait" :src="formData.HeadPortrait" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon">
          <Plus />
        </el-icon>
      </el-upload>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" :icon="Select" @click="HandleSubmit">保存</el-button>
    </el-form-item>

  </el-form>

</template>
<style lang="scss" scoped>
.fillText {
  margin-bottom: 5px !important;
}

.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
</style>