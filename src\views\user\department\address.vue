<script setup>
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd, Delete, Upload
} from '@element-plus/icons-vue'
import {
    AddressFind, AddressGetById, AddressInsertUpdate, UploadPostexecl, UploadAddressFile, AddressDelBatch
} from '@/api/user.js'

import { ElMessage, ElMessageBox } from 'element-plus'
import { fileDownload } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 100000, isFirst: true })
const selectRows = ref([])
const parentAddressList = ref([])
const filePath = ref('')
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const dialogVisible = ref(false)
const Pid = ref('0')//类型
const isEdit = ref(false)//是否修改
const dialogData = ref({})
const title = ref('添加字典分类')
const refForm = ref()
const ruleForm = {
    Pid: [
        { required: true, message: '请选择所属楼宇场馆', trigger: 'change' },
    ],
    Name: [
        { required: true, message: '请输入名称', trigger: 'change' },
    ]
}

//加载数据
onMounted(() => {
    HandleTableData(true);
})
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}



// 修改状态
const HandleSwitchChange = (e, row) => {
    DictionarysetStatuz({ id: row.Id }).then((res) => {
        // console.log(res)
        // if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '设置成功')
        // } else {
        //     ElMessage.error(res.data.msg)
        // }
    }).catch((err) => {
        console.info(err)
    })
}



//列表
const HandleTableData = () => {
    AddressFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows;
            parentAddressList.value = other.listParentAddress
            filePath.value = other.filePath;
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    // filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    // filters.value.pageIndex = 1
    filters.value.Key = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const editType = ref(0)
// 添加场所（室）
const HandleAdd = (e, row) => {
    editType.value = e
    isEdit.value = false
    title.value = '添加房间'

    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            Pid: row.Id
        }
    })
}
// 修改字典  楼宇场馆||添加场所（室）
const HandleEdit = (e, row) => {
    console.log(e, row)
    editType.value = e
    isEdit.value = true
    title.value = '修改'

    Pid.value = row.Pid

    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            Pid: row.Pid,
            Id: row.Id,
            Name: row.Name,
            Sort: row.Sort,
            Code: row.Code,
            Memo: row.Memo,
        }
    })
}
// 添加楼宇场馆||添加场所（室）
const HandleAddAll = (e) => {
    editType.value = e
    if (e == 1) {
        title.value = '添加楼宇场馆'
    } else {
        title.value = '添加场所（室）'
    }
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {}
    })
}
//创建/修改选用组织  提交
const HandleSubmit = () => {
    console.log('提交', dialogData.value)
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        AddressInsertUpdate(dialogData.value).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '提交成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}
// 删除
const HandleDel = (row) => {
    AddressDelBatchUser(row.Id, row.Name)
}
//批量删除
const HandleDelAll = () => {
    let ids = selectRows.value.map(t => t.Id).join(',')
    let name = selectRows.value.map(t => t.Name).join(',')
    AddressDelBatchUser(ids, name)

}
const AddressDelBatchUser = (ids, name) => {
    ElMessageBox.confirm(`确认删除用户[${name}]吗?`,
        '删除确认', { type: 'warning', })
        .then(() => {
            AddressDelBatch({ ids: ids }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 模板下载
const HandleDownload = () => {
    fileDownload(filePath.value);
}
const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
// 导入
const HandleStudentUpload = () => {
    uploadVisible.value = true
}
const fileFile = ref()
const uploadRef = ref()
// 导入前校验
const beforeAvatarUpload = (file) => {
    fileFile.value = file
    const extension = file.name.split('.')[1] === 'xls'
    const extension2 = file.name.split('.')[1] === 'xlsx'
    if (!extension && !extension2) {
        ElMessage({
            message: '上传模板只能是 xls、xlsx格式!',
            type: 'error'
        })
    }
    return extension || extension2
}
const httpRequest = () => {
    UploadPostexecl([fileFile.value]).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            UploadAddressFile({ FilePath: rows }).then(res1 => {
                if (res1.data.flag == 1) {
                    ElMessage.success(res1.data.msg || '导入成功')
                    HandleTableData()
                    setTimeout(function () {
                        uploadVisible.value = false
                    }, 1000);
                } else {
                    ElMessage({
                        showClose: true,
                        message: res1.data.msg,
                        type: 'error',
                        duration: 5000
                    })
                }
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 多选框禁止勾选
const selectable = (row) => {
    return row.AddressType != 1
}
// 修改排序值
const sortChange = (e, row, i) => {
    if (e) {

        console.log(e, row, i, typeof (e))
        let foemData = {
            Pid: row.Pid,
            Id: row.Id,
            Name: row.Name,
            Sort: e,
            Code: row.Code,
            Memo: row.Memo,
        }
        AddressInsertUpdate(foemData).then((res) => {
            if (res.data.flag == 1) {
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAddAll(1)">添加楼宇场馆</el-button>
                        <el-button type="success" :icon="FolderAdd" @click="HandleAddAll(2)">添加场所（室）</el-button>
                        <el-button type="danger" :icon="Delete" @click="HandleDelAll"
                            :disabled="selectRows.length == 0">批量删除</el-button>
                        <el-button type="success" :icon="Upload" @click="HandleStudentUpload">批量导入</el-button>
                    </el-form-item>
                    <!-- <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="名称" style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item> -->
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="Id"
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <!-- <el-table-column prop="Id" label="Id" min-width="120" align="center"></el-table-column> -->
            <el-table-column type="index" width="50" />
            <el-table-column type="selection" width="50" :selectable="selectable"></el-table-column>
            <el-table-column prop="Name" label="地点信息" min-width="140"></el-table-column>
            <el-table-column prop="Sort" label="排序" min-width="80" align="center">
                <template #default="{ row }">
                    <span v-if="row.AddressType == 1">{{ row.Sort }}</span>
                    <el-input v-else type="number" v-model="row.Sort" @change="sortChange($event, row)"></el-input>
                </template>
            </el-table-column>
            <!-- <el-table-column prop="DepartmentName" label="所属部门" min-width="80" align="center"></el-table-column> -->
            <!-- <el-table-column prop="UserName" label="管理人" min-width="180" show-overflow-tooltip></el-table-column> -->
            <el-table-column label="操作" fixed="right" width="220" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Pid == 0 && row.AddressType != 1" type="primary" link
                        @click="HandleAdd(3, row)">添加场所(室)</el-button>
                    <el-button v-if="row.AddressType != 1" type="primary" link
                        @click="HandleEdit(4, row)">修改</el-button>
                    <el-button v-if="row.AddressType != 1" type="primary" link @click="HandleDel(row)">删除</el-button>

                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="620" :lazy="true" :title="title">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="160px"
                    status-icon style="padding-right: 40px;">
                    <!-- editType -->
                    <el-form-item label="所属楼宇场馆：" prop="Pid"
                        v-if="editType == 2 || editType == 3 || (editType == 4 && Pid && Pid !== '0')">
                        <el-select v-model="dialogData.Pid">
                            <el-option v-for="item in parentAddressList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        :label="editType == 2 || editType == 3 || (editType == 4 && Pid && Pid !== '0') ? '场所(室)名称：' : '楼宇场馆名称：'"
                        prop="Name">
                        <el-input v-model="dialogData.Name"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="所属部门" prop="DepartmentId">
                        <el-select v-model="dialogData.DepartmentId">
                            <el-option v-for="item in managerList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="管理人" prop="Name">
                        <el-select v-model="dialogData.ManagerUserId">
                            <el-option v-for="item in managerList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item> -->
                    <el-form-item label="排序：">
                        <el-input type="number" v-model="dialogData.Sort"></el-input>
                    </el-form-item>
                    <el-form-item
                        :label="editType == 2 || editType == 3 || (editType == 4 && Pid && Pid !== '0') ? '场所(室)别名：' : '楼宇场馆别名：'">
                        <el-input v-model="dialogData.Code" placeholder="请输入别名"></el-input>
                    </el-form-item>
                    <el-form-item
                        :label="editType == 2 || editType == 3 || (editType == 4 && Pid && Pid !== '0') ? '场所(室)备注：' : '楼宇场馆备注：'">
                        <el-input v-model="dialogData.Memo" placeholder="请输入备注"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
        <!-- 导入弹窗 -->
        <el-dialog v-model="uploadVisible" title="批量导入" width="480px">
            <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" label-width="120px" status-icon>
                <el-form-item>
                    <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                        style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload"
                        :http-request="httpRequest">
                        <el-button type="primary" :icon="UploadFilled">批量导入</el-button>
                    </el-upload>
                    <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}

.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>