<script setup>
defineOptions({
  name: 'workextension'
});
import { onMounted, ref, nextTick, onActivated, getCurrentInstance } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import FlowPanel from '@/components/workflow/panel.vue'
import AppBox from '@/components/Approve/AppBox.vue';
import { tagsListStore } from "@/utils/index.js";
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const processId = ref(route.query.id)
const { proxy } = getCurrentInstance();
const model = ref(true)
const height = ref(500)
const width = ref(1200)
const row = ref()
const flow = ref(null)
const isAdd = ref(false)

onMounted(() => {
  height.value = document.body.clientHeight - 140;
  let w = document.body.clientWidth * 0.9;
  width.value = w > 1800 ? 1800 : w;
  // console.log("flow",flow.value.dataReload())
  open([])
})
onActivated(() => {
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  processId.value = route.query.id
})

const open = (row) => {
  // row.value = row;
  model.value = true;
  isAdd.value = Object.keys(row).length == 0;

}


</script>
<template>
  <!-- <app-box :lazy="false" v-model="model" :title="isAdd ? '新建流程' : '编辑流程'" :width="width" :padding="0">
    <div :style="{ height: height + 'px' }">
      <flow-panel ref="flow"></flow-panel>
    </div>
    <template #footer>
      <div style="text-align: center;">
        <el-button type="default" size="mini" @click="model = false">取消</el-button>
        <el-button type="primary" size="mini" @click="save">保存</el-button>
      </div>
    </template>
</app-box> -->
  <div :style="{ height: height + 'px' }">
    <flow-panel ref="flow" :processId="processId"></flow-panel>
  </div>
</template>