<script setup>
import {
  Search, Refresh, FolderAdd
} from '@element-plus/icons-vue'
import { onMounted, ref, watch, nextTick } from 'vue'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
import {
  Userfindmyunit, Userdelbatch, Rolefindmyunit, Usermyunitupdateuserstatuz, Useraccountunlock, Usergetbyid, Usersavemyunit, GetAllDepartment, Departmentuserbatchset,
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { integerLimit, roleObjList } from "@/utils/index.js";
import AppBox from "@/components/Approve/AppBox.vue";
import md5 from 'js-md5';
// 表格初始化
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filtersKey = ref({ key: '', value: 'Name', departmentId: 0 })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const options = ref([
  { value: 'Name', label: '姓名', },
  { value: 'Mobile', label: '手机', },
  { value: 'AcctName', label: '账号', },
  { value: 'RoleName', label: '角色', }
])
const hUnitType = ref(userStore.userInfo.UnitType)
const rolefindList = ref([])//角色列表
const roleTableData = ref([])//角色列表
const strRoleIdsList = ref([])//当前账号的角色集合
const departmentList = ref([])//部门集合
const departmentDataList = ref([])//部门集合
const amenddepartmentList = ref([])//部门集合 
// 翻页
watch(() => filters.value.pageIndex, () => {
  HandleSearch()
})
watch(() => filters.value.pageSize, () => {
  filters.value.pageIndex = 1
  HandleSearch()
})

//加载数据
onMounted(() => {
  HandleSearch()
  RolefindmyunitUser()
  GetAllDepartmentUser()
})
//输入整数
const integerLimitInput = (val, name) => {
  formData.value[name] = integerLimit(val);
}

//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const isAdd = ref(true)
const refForm = ref()
const ruleForm = {
  Name: [
    { required: true, message: '姓名不能为空', trigger: 'blur' },
  ],
  AcctName: [
    { required: true, message: '账号不能为空', trigger: 'blur' },
  ],
  Mobile: [
    { required: true, message: '手机号码不能为空', trigger: 'blur' },
    {
      pattern: /^1[0-9]{10}$/,
      message: '请输入11位手机号码',
      trigger: 'blur'
    }
  ],
  initPwd: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    {
      pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
      message: '密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种',
      trigger: 'blur'
    }
  ]
}

// 添加账户
const HandleAdd = () => {
  isAdd.value = true
  dialogVisible.value = true
  nextTick(() => {
    refForm.value.resetFields()
    formData.value = { Id: '0' }
  })
  strRoleIdsList.value = []
}
//修改弹窗
const HandleEdit = (row) => {
  isAdd.value = false
  formData.value.Id = row.Id
  // 获取拥有角色信息，实现反选
  Usergetbyid({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      formData.value = rows
      formData.value.Sex = Number(rows.Sex)
      if (formData.value.DepartmentIds) {
        formData.value.DepartmentIds = formData.value.DepartmentIds.split(',')
      }

      strRoleIdsList.value = rows.StrRoleIds.split(',')
      strRoleIdsList.value = strRoleIdsList.value.map(t => Number(t))
    } else {
      ElMessage.error(res.data.msg)
    }
  }).catch((err) => {
    console.info(err)
  })
  dialogVisible.value = true
}
//删除
const HandleDel = (row) => {
  ElMessageBox.confirm(`确认删除用户[${row.Name}]吗?`)
    .then(() => {
      Userdelbatch([row.Id]).then((res) => {
        if (res.data.flag == 1) {
          HandleSearch()
          ElMessage.success('删除成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      }).catch((err) => {
        console.info(err)
      })
    })
}

// 修改状态
const HandleSwitchChange = (e, row) => {
  Usermyunitupdateuserstatuz({ userId: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      if (e == 1) {
        ElMessage.success('启用成功')
      } else {
        ElMessage.success('禁用成功')
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  }).catch((err) => {
    console.info(err)
  })
}

// 解冻
const HandleUnLock = (row) => {
  Useraccountunlock({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      HandleSearch()
      ElMessage.success('解冻成功')
    } else {
      ElMessage.error(res.data.msg)
    }
  }).catch((err) => {
    console.info(err)
  })
}

//获取列表 与 搜索
const HandleSearch = (page) => {
  if (page) filters.value.pageIndex = page
  filters.value.Name = undefined;
  filters.value.Mobile = undefined;
  filters.value.AcctName = undefined;
  filters.value.RoleName = undefined;

  // 按类型搜索
  if (filtersKey.value.key) {
    filters.value[filtersKey.value.value] = filtersKey.value.key;
  } else {
    filters.value[filtersKey.value.value] = undefined;
  }
  //选择部门
  if (filtersKey.departmentId == 0) {
    filters.value.DepartmentId = undefined;
  } else {
    filters.value.DepartmentId = filtersKey.value.departmentId;
  }

  Userfindmyunit(filters.value).then(res => {
    if (res.data.flag == 1) {
      tableData.value = res.data.data.rows;
      tableTotal.value = Number(res.data.data.total);
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 重置
const HandleReset = (page) => {
  filtersKey.value.key = ''
  HandleSearch(page)
}

// 根据单位类型加载角色信息
const RolefindmyunitUser = (StrRoleIds) => {
  Rolefindmyunit().then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      rolefindList.value = rows
      roleTableData.value = roleObjList(rolefindList.value)
    } else {
      ElMessage.error(res.data.msg)
    }
  }).catch((err) => {
    console.info(err)
  });
}
// 新增或修改提交
const HandleSubmit = (formEl) => {
  if (!strRoleIdsList.value.length) {
    ElMessage.warning('请至少配置一个角色')
    return
  }
  if (formData.value.initPwd) {
    formData.value.Pwd = md5(formData.value.initPwd)
  } else {
    formData.value.Pwd = ''
  }
  // formData.value.StrRoleIds = newStrRoleIds.value
  formData.value.StrRoleIds = strRoleIdsList.value.join(',')
  if (hUnitType.value == 3 && formData.value.DepartmentIds) {
    formData.value.DepartmentIds = formData.value.DepartmentIds.join(',')
  }
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    let data = JSON.parse(JSON.stringify(formData.value))
    data.initPwd = undefined
    Usersavemyunit(data).then(res => {
      if (res.data.flag == 1) {
        HandleSearch()
        ElMessage.success('保存成功')
        dialogVisible.value = false
      } else {
        ElMessage.error(res.data.msg)
      }
    }).catch((err) => {
      console.info(err)
    })
  })
}

// 获取部门
const GetAllDepartmentUser = () => {
  GetAllDepartment().then(res => {
    if (res.data.flag == 1) {
      // console.log("获取部门res", res.data.data)
      const { rows } = res.data.data
      amenddepartmentList.value = JSON.parse(JSON.stringify(rows))
      departmentDataList.value = JSON.parse(JSON.stringify(rows))
      rows.unshift({
        Id: 0,
        Name: '全部'
      })
      departmentList.value = rows
    } else {
      ElMessage.error(res.data.msg)
    }
  }).catch((err) => {
    console.info(err)
  });
}

</script>
<template>
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filtersKey" class="flexBox">
        <el-form-item class="flexItem">
          <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
        </el-form-item>
        <div class="verticalIdel"></div>
        <el-form-item class="flexItem">
          <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="width: 240px" class="input-with-select">
            <template #prepend>
              <el-select v-model="filtersKey.value" style="width: 80px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" :icon="Search" @click="HandleSearch(1)">搜索</el-button>
          <el-button :icon="Refresh" @click="HandleReset(1)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
  <!-- 内容 -->
  <el-table ref="refTable" :data="tableData" highlight-current-row border stripe>
    <el-table-column type="index" width="50" align="center"></el-table-column>
    <el-table-column prop="Name" label="姓名" min-width="160" align="center"></el-table-column>
    <el-table-column prop="Mobile" label="手机号码" min-width="180" align="center"></el-table-column>
    <el-table-column prop="AcctName" label="登录账号" min-width="180" align="center"></el-table-column>
    <el-table-column prop="RoleNames" label="角色" min-width="200" show-overflow-tooltip align="center">
    </el-table-column>
    <el-table-column label="状态" min-width="120" align="center">
      <template #default="{ row }">
        <span v-if="row.AcctId == userStore.userInfo.AcctId">
          启用
        </span>
        <el-switch v-else v-model="row.Statuz" :active-value="1" :inactive-value="0"
          style="--el-switch-off-color: #ff4949" inline-prompt active-text="启" inactive-text="禁"
          @change="HandleSwitchChange($event, row)" />
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="160" align="center">
      <template #default="{ row }">
        <span>
          <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
          <el-button type="primary" link @click="HandleDel(row)"
            v-if="row.AcctId != userStore.userInfo.AcctId">删除</el-button>
          <el-button type="primary" link @click="HandleUnLock(row)"
            v-if="row.AcctId != userStore.userInfo.AcctId">解冻</el-button>
        </span>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>
  <el-row>
    <el-col class="flexBox">
      <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
        :total="tableTotal" v-model:current-page="filters.pageIndex" v-model:page-size="filters.pageSize" />
    </el-col>
  </el-row>
  <!-- 添加修改单位用户信息 -- 弹窗 -->
  <app-box v-model="dialogVisible" :height="600" :width="960" :lazy="true" :title="isAdd ? '添加单位用户信息' : '修改单位用户信息'">
    <template #content>
      <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="80px" status-icon
        class="addCustom">
        <fieldset>
          <legend>基本信息</legend>
          <div class="dialogFlexBox">
            <el-form-item label="姓名" prop="Name">
              <el-input v-model="formData.Name" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="Mobile">
              <el-input v-model="formData.Mobile" auto-complete="off"
                @input="integerLimitInput($event, 'Mobile')"></el-input>
            </el-form-item>
            <el-form-item label="登录账号" prop="AcctName">
              <el-input v-model="formData.AcctName" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="密码" :prop="isAdd ? 'initPwd' : ''">
              <el-input v-model="formData.initPwd" show-password auto-complete="off" clearable
                :placeholder="isAdd ? '' : '如不修改密码，请留空'"></el-input>
            </el-form-item>
          </div>
        </fieldset>
        <fieldset>
          <legend>角色信息</legend>
          <el-form-item label="" label-width="0">
            <!-- <el-checkbox-group v-model="strRoleIdsList" style="width: 100%;">
            <el-checkbox v-for="item in rolefindList" :key="item.RoleId" :label="item.Name" :value="item.RoleId" />
          </el-checkbox-group> -->

            <el-table :data="roleTableData" border header-cell-class-name="headerClassName">
              <el-table-column prop="ModuleName" label="角色分类" min-width="160" align="center"></el-table-column>
              <el-table-column label="角色名称" width="640">
                <template #default="{ row }">
                  <el-checkbox-group v-model="strRoleIdsList">
                    <el-checkbox v-for="item in row.children" :key="item.RoleId" :label="item.Name" :value="item.RoleId"
                      :disabled="(hUnitType == 3 && item.RoleId == 30 || hUnitType == 1 && item.RoleId == 10) && userStore.userInfo.Id === formData.Id"
                      style="width: 20%;">
                    </el-checkbox>
                  </el-checkbox-group>
                </template>
              </el-table-column>
            </el-table>

          </el-form-item>
        </fieldset>
        <fieldset>
          <legend>更多信息</legend>
          <div class="dialogFlexBox2">
            <el-form-item label="部门" prop="DepartmentIds" v-if="hUnitType == 3">
              <el-select v-model="formData.DepartmentIds">
                <el-option class="flexItem" v-for="item in departmentDataList" :key="item.Id" :label="item.Name"
                  :value="item.Id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="性别" prop="Sex">
              <el-radio-group v-model="formData.Sex">
                <el-radio class="radio" :value="1">男</el-radio>
                <el-radio class="radio" :value="0">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="QQ">
              <el-input v-model="formData.Qq" auto-complete="off"></el-input>
            </el-form-item>
            <el-form-item label="电邮">
              <el-input v-model="formData.Email" auto-complete="off"></el-input>
            </el-form-item>
          </div>
        </fieldset>
      </el-form>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit(refForm)"> 确定 </el-button>
      </span>
    </template>
  </app-box>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}

.addCustom.el-form {
  width: 90%;
  margin: 0 auto;

  .el-form-item {
    margin-bottom: 10px;
  }

  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;

    label {
      width: 20%;
    }
  }

  .el-checkbox {
    height: var(--el-checkbox-height, 22px);
  }

}

.dialogFlexBox,
.dialogFlexBox2 {
  display: flex;
  flex-wrap: wrap;

  .el-form-item {
    width: 50%;
  }
}

.dialogFlexBox {
  .el-form-item {
    margin-bottom: 18px !important;

    &:nth-child(3) {
      margin-bottom: 30px !important;
    }

    &:nth-child(4) {
      margin-bottom: 30px !important;
    }

  }
}

.dialogFlexBox2 {
  .el-form-item {
    margin-bottom: 5px !important;
  }
}

.el-row .el-form-item {
  margin-top: 0px;
  margin-bottom: 10px;
}

fieldset {
  // color: #333;
  border: #ccc dashed 1px;
  padding: 10px;
  margin: 10px 0;

  legend {
    font-size: 16px;
  }
}
</style>