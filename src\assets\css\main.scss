:root {  
  --body-height: 100%; 
  --body-background-color: #ffffff; 
  --body--webkit-scrollbar: 6px; 

  // 其他变量...  
}  
html,
body {
  margin: 0;
  padding: 0;
  height: var(--body-height);
  min-height: 100%;
  background-color:  var(--body-background-color);
  scrollbar-width: none;
}

#app {
  height: 100%;
}

// 手机端自适应
@media screen and (max-width:600px) {

  // 弹窗宽度
  .el-dialog {
    width: 90% !important;
  }
  // 移动端宽度
  .mobile-box{
    
    width: 90% !important;
  }

  // 登录框
  .login-container {
    width: 65% !important;
  }
}

// 滚动条美化
/* 滚动槽 */
::-webkit-scrollbar {
  width: var(--body--webkit-scrollbar);
  height: 6px;
}
::-webkit-scrollbar-track {
  border-radius: 3px;
  background: rgba(0,0,0,0.06);
  -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.08);
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0,0,0,0.12);
  -webkit-box-shadow: inset 0 0 10px rgba(0,0,0,0.2);
}
 /* 适用于 Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0,0,0,0.12) rgba(0,0,0,0.06);
}