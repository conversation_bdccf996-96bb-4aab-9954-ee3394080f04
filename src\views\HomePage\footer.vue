<script setup>
import { ElMessage } from 'element-plus'
import {
    ArticleGetbottomcatetype, AnonGetconfigbymodule
} from '@/api/home.js'
import { useUserStore } from '@/stores';
import router from '@/router'
import { ref, onMounted, nextTick } from 'vue';
import { useRoute } from 'vue-router'
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
const userStore = useUserStore()
const route = useRoute()
const articleFooterList = ref([])
const defaultSet = ref({})
const year = ref(new Date().getFullYear())
const serviceData = ref({})
const aboutData = ref({})
const qrPreviewText = ref('')

onMounted(() => {
    let url = window.location.protocol + '//' + window.location.host
    qrPreviewText.value = url + "/ui/#";
    if (route.path == '/') {
        ArticleGetbottomcatetypeUser()
        nextTick(() => {
            GetwebsiteconfigUser()
        });
    }
    articleFooterList.value = userStore.articleFooter
    defaultSet.value = userStore.defaultSet
    serviceData.value = userStore.serviceData
    aboutData.value = userStore.aboutData
    // console.log("userStore.defaultSet", userStore.defaultSet)
})

// 子组件传参
const emit = defineEmits(['footerData']);
// 查看资讯列表
const listClick = (item, num) => {
    // 是否在资讯列表页面
    if (route.path == '/articlelist') {
        if (num == 1) {
            emit('footerData', { Id: item.Id, IsMany: item.IsMany });
        } else {
            emit('footerData', { Id: item.Cid, IsMany: item.IsMany });
        }
    } else {
        if (num == 1) {
            const { href } = router.resolve({
                path: "/articlelist",
                query: { Id: item.Id, IsMany: item.IsMany }
            });
            window.open(href, "_blank");
        } else {
            const { href } = router.resolve({
                path: "/articlelist",
                query: { Id: item.Cid, IsMany: item.IsMany }
            });
            window.open(href, "_blank");
        }
    }
}
// 其他
const handlePage = async (key) => {
    if (key == route.path) return
    const { href } = router.resolve({
        path: key
    });
    window.open(href, "_blank");
}
//底部资讯分类信息
const ArticleGetbottomcatetypeUser = () => {
    ArticleGetbottomcatetype({ topCount: 3 }).then(res => {
        // console.log("资讯列表", res)
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            articleFooterList.value = rows || [];//底部资讯分类信息
            userStore.setArticleFooter(articleFooterList.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}

// 获取配置信息
const GetwebsiteconfigUser = () => {
    AnonGetconfigbymodule({ moduleCode: 8002 }).then(res => {
        let obj = {}
        // console.log("获取配置信息", res.data)
        const { rows } = res.data.data
        rows.map(item => {
            obj[item.TypeCode] = item.ConfigValue
        })
        // console.log("获取配置信息", obj)
        if (userStore.platformType == 1) {
            defaultSet.value.SupportUnit = obj['8002_DLYM_BJ'] || '中小学校服管理与备案平台'

        } else if (userStore.platformType == 2) {
            defaultSet.value.SupportUnit = obj['8002_DLYM_BJ'] || '审批配置平台'

        } else if (userStore.platformType == 3) {
            defaultSet.value.SupportUnit = obj['8002_DLYM_BJ'] || '危化品平台'

        }
        defaultSet.value.RecordNumber = obj['8002_DLYM_ICPBA']
        userStore.setDefaultSet(obj)
    })
}


</script>
<template>
    <div class="footerDiv">
        <div>
            <ul>
                <li v-if="userStore.platformType == 1"><span class="lihover" @click="handlePage('/')">首页</span> 丨 </li>
                <li v-if="userStore.platformType == 1"><span class="lihover"
                        @click="handlePage('/exhibition')">校服展示</span></li>
                <li v-if="userStore.platformType != 1"><span class="lihover"
                        @click="handlePage('/information')">平台资讯</span></li>
                <li v-for="item in userStore.articleFooter" :key="item.Id">
                    <span>丨</span>
                    <span class="lihover" @click="listClick(item, 1)">
                        {{ item.Name }}</span>
                </li>
            </ul>
            <div class="footerDLYM">
                <span> <span>©2024{{ year == 2024 ? '' : '-' + year }}&nbsp;
                    </span>
                    <a :href="userStore.defaultSet['8002_DLYM_JSZCDZ']" target="_blank">
                        {{ userStore.defaultSet['8002_DLYM_JSZC'] }}</a> </span>
                <span
                    v-if="userStore.defaultSet['8002_DLYM_ICPBA'] && userStore.defaultSet['8002_DLYM_ICPBA'] != 'NULL'">&nbsp;&nbsp;&nbsp;
                    备案号：
                    <span>
                        <a :href="userStore.defaultSet['8002_DLYM_ICPBADZ']" target="_blank">
                            {{ userStore.defaultSet['8002_DLYM_ICPBA'] }}</a> </span></span>
            </div>
        </div>
        <div v-if="userStore.platformType == 1" style="margin-left: 20px;">
            <vue-qr :text="qrPreviewText" :size="54" :level="'H'" :margin="5"></vue-qr>
        </div>
    </div>

</template>
<style lang="scss" scoped>
.footerDiv {
    min-width: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
}

ul {
    padding-inline-start: 0px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin: 5px 10px;

    li {
        list-style-type: none;
        font-size: 12px;

        span {
            padding: 0 5px;

            &:nth-child(2):hover {
                cursor: pointer;
                color: var(--el-color-primary);
                text-decoration: underline;
            }
        }

        .lihover:hover {
            cursor: pointer;
            color: var(--el-color-primary);
            text-decoration: underline;
        }

    }
}

.relation:hover {
    cursor: pointer;
    color: var(--el-color-primary);
    text-decoration: underline;
}

.footerDLYM {
    text-align: center;
    font-size: 12px;

    a {
        text-decoration: none; //去掉下划线
        color: inherit;
    }

    a:hover {
        color: var(--el-color-primary);
        text-decoration: underline;
    }
}
</style>
