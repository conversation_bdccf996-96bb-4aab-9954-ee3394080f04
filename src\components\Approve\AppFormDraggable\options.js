import { id } from 'element-plus/es/locales.mjs'

export const componentsList = [
  {
    pid: 1,
    text: '通用组件',
    components: [
      {
        id: 24,
        name: '项目名称',
        label: '项目名称',
        type: 'projectname',
        icon: 'el-icon-edit',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        rules: 'maxLength',
        arg: 120,
        code: 'ProjectName',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 25,
        name: '项目金额',
        label: '项目金额',
        type: 'projectamount',
        icon: 'el-icon-coin',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        rules: 'decimals',
        arg: 4,
        code: 'ProjectAmount',
        Controlled: 0,
        IsAmountControl: 2,
        isCondition: false,
        isRedact: false
      },
      {
        id: 1,
        name: '',
        label: '单行输入',
        type: 'text',
        value: '',
        icon: 'el-icon-document',
        DefaultValue: '',
        code: '',
        Controlled: 0,
        IsAmountControl: 2,
        isCondition: false,
        isRedact: false
      },
      {
        id: 2,
        name: '',
        label: '多行输入',
        type: 'textarea',
        maxLength: 500,
        value: '',
        icon: 'el-icon-c-scale-to-original',
        DefaultValue: '',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 5,
        name: '',
        label: '单选下拉框',
        type: 'select',
        value: null,
        key: '',
        data: [{ label: '请设置数据源', value: '1' }],
        icon: 'el-icon-arrow-down',
        DefaultValue: '',
        code: '',
        MasterControl: 0,
        Controlled: 0,
        isCondition: false,
        isRedact: false,
        ParentCode: ''
      },
      {
        id: 6,
        name: '',
        label: '多选下拉框',
        type: 'selectList',
        key: '',
        value: [],
        data: [{ label: '请设置数据源', value: '1' }],
        icon: 'el-icon-arrow-down',
        DefaultValue: [],
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 3,
        name: '',
        label: '单选',
        type: 'radio',
        icon: 'el-icon-aim',
        value: 0,
        data: [
          { label: '0', value: '否' },
          { label: '1', value: '是' }
        ],
        key: '',
        DefaultValue: '',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 4,
        name: '',
        label: '多选',
        type: 'checkbox',
        value: [],
        key: '',
        data: [
          { label: '多选1', value: '多选1' },
          { label: '多选2', value: '多选2' }
        ],
        icon: 'el-icon-circle-check',
        DefaultValue: [],
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },

      {
        id: 7,
        name: '',
        label: '级联',
        type: 'cascader',
        icon: 'el-icon-share',
        value: [],
        key: '',
        data: [
          {
            value: '请配置数据源',
            label: '请配置数据源',
            children: [
              {
                value: '具体',
                label: '菜单:下拉框绑定设置'
              },
              {
                value: 'color',
                label: '可参照字典编号[tree_roles]'
              }
            ]
          }
        ],
        DefaultValue: '',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 8,
        name: '',
        label: '日期',
        type: 'date',
        icon: 'el-icon-date',
        value: null,
        DefaultValue: '',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 9,
        name: '',
        label: '时间',
        type: 'time',
        icon: 'el-icon-date',
        value: '',
        DefaultValue: '',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 10,
        name: '',
        label: '日期时间',
        type: 'datetime',
        icon: 'el-icon-date',
        value: null,
        DefaultValue: '',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 12,
        name: '',
        label: '附件上传',
        type: 'upload',
        icon: 'el-icon-upload',
        buttonTitle: '上传',
        DefaultValue: [],
        DefaultName: [],
        code: '',
        Controlled: 0,
        categoryList: [],
        fileLChildList: [],
        UploadFileType: [],
        isCondition: false,
        isRedact: false
      },
      // {
      //   id: 14,
      //   name: '模板下载',
      //   type: 'download',
      //   TemplateUrl: '',
      //   icon: 'el-icon-download',
      //   buttonTitle: '模板下载',
      //   DefaultValue: '',
      //   code: '',
      // Controlled: 0,
      //   isCondition: false,
      //   isRedact: false
      // },
      // {
      //   id: 15,
      //   name: '按钮',
      //   type: 'button',
      //   TemplateUrl: '',
      //   icon: 'el-icon-thumb',
      //   buttonTitle: '按钮',
      //   DefaultValue: '',
      //   code: '',
      // Controlled: 0,
      //   isCondition: false,
      //   isRedact: false
      // },
      // {
      //   id: 16,
      //   name: '开关',
      //   type: 'switch',
      //   icon: 'el-icon-turn-off',
      //   value: 0,
      //   DefaultValue: '',
      //   code: '',
      // Controlled: 0,
      //   isCondition: false,
      //   isRedact: false
      // },
      {
        id: 28,
        name: '',
        label: '描述文字',
        type: 'descriptivetext',
        icon: 'el-icon-chat-dot-square',
        DefaultValue: '',
        DefaultName: '',
        dividerTextColor: '#999999',
        labelWidth: 5,
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 17,
        name: '',
        label: '分割线',
        type: 'line',
        icon: 'el-icon-minus',
        DefaultValue: '',
        isCenter: 'left',
        isDashed: 'solid',
        dividerColor: '#dedfe6',
        dividerTextColor: '#303133',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 21,
        name: '',
        label: '资金分配',
        type: 'fundallocation',
        icon: 'el-icon-coin',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        rules: 'decimals',
        arg: 4,
        code: 'FundAllocation_',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 29,
        name: '项目编号',
        label: '项目编号',
        type: 'projectnumber',
        icon: 'el-icon-s-operation',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: '',
        isDetail: false,
        readonly: true,
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 30,
        name: '单位地址',
        label: '单位地址',
        type: 'unitaddress',
        icon: 'el-icon-school',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'UnitAddress',
        isDetail: false,
        readonly: true,
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 34,
        name: '单位名称',
        label: '单位名称',
        type: 'unitname',
        icon: 'el-icon-office-building',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'UnitName',
        isDetail: false,
        readonly: true,
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 31,
        name: '单位学段',
        label: '单位学段',
        type: 'unitperiod',
        icon: 'el-icon-s-grid',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'UnitPeriod',
        isDetail: false,
        readonly: true,
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 32,
        name: '单位所属街道',
        label: '单位所属街道',
        type: 'unitstreettown',
        icon: 'el-icon-office-building',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'UnitStreetTown',
        isDetail: false,
        readonly: true,
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 33,
        name: '主体性质',
        label: '主体性质',
        type: 'subjectnature',
        icon: 'el-icon-office-building',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'SubjectNature',
        isDetail: false,
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      // {
      //   id: 18,
      //   name: '排序',
      //   label: '排序',
      //   type: 'sort',
      //   icon: 'el-icon-sort',
      //   value: 0,
      //   DefaultValue: 0,
      //   DefaultName: 0,
      //   code: '',
      // Controlled: 0,
      //   isCondition: false,
      //   isRedact: false
      // },
      {
        id: 35,
        name: '申报人',
        label: '申报人(填报)',
        type: 'auditusername',
        icon: 'el-icon-user',
        value: '',
        DefaultValue: '',
        isDetail: true,
        readonly: true,
        code: 'ReportUserName',
        Controlled: 0,
        isCondition: false
      },
      {
        id: 36,
        name: '申报时间',
        label: '申报时间(填报)',
        type: 'auditdate',
        icon: 'el-icon-date',
        value: '',
        DefaultValue: '',
        isDetail: true,
        readonly: true,
        code: 'ReportDate',
        Controlled: 0,
        isCondition: false
      },
      {
        id: 22,
        name: '审批人',
        label: '审批人(审批)',
        type: 'auditusername',
        icon: 'el-icon-user',
        value: '',
        DefaultValue: '',
        isDetail: true,
        readonly: true,
        code: 'AuditUserName',
        Controlled: 0,
        isCondition: false
      },
      {
        id: 23,
        name: '审批时间',
        label: '审批时间(审批)',
        type: 'auditdate',
        icon: 'el-icon-date',
        value: '',
        DefaultValue: '',
        isDetail: true,
        readonly: true,
        code: 'AuditDate',
        Controlled: 0,
        isCondition: false
      },
      {
        id: 20,
        name: '审批结果',
        label: '审批结果',
        type: 'auditstatuz',
        icon: 'el-icon-document-checked',
        value: 0,
        DefaultName: '',
        code: 'AuditStatuz',
        Controlled: 0,
        isCondition: false,
        isRedact: false,
        required: true,
        data: [
          { label: '通过', value: '1' },
          { label: '不通过', value: '2' }
        ]
      },
      {
        id: 21,
        name: '审批内容',
        label: '审批内容',
        type: 'auditremark',
        icon: 'el-icon-tickets',
        value: '',
        DefaultValue: '',
        maxLength: 500,
        code: 'AuditRemark',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 19,
        name: '',
        label: '资金来源',
        type: 'statisticstable',
        changeType: 'radio',
        isAssignment: false,
        CanUseAmount: '可用金额',
        tabs: false,
        columns: [
          {
            FieldName: '单位名称',
            FieldCode: 'field1',
            isShow: true,
            Width: 120,
            ContentStyle: 'center'
          },
          {
            FieldName: '日期',
            FieldCode: 'field2',
            isShow: true,
            Width: 120,
            ContentStyle: 'center'
          },
          {
            FieldName: '项目金额',
            FieldCode: 'amount',
            isShow: true,
            Width: 120,
            ContentStyle: 'center'
          },
          {
            FieldName: '实际金额',
            FieldCode: 'actualAmount',
            isShow: true,
            Width: 120,
            ContentStyle: 'center'
          }
        ],
        tableData: [],
        // tableData: [
        //   {
        //     Id: 1,
        //     field1: '字段1',
        //     field2: '字段2',
        //     field3: '字段3',
        //     amount: 2000,
        //     actualAmount: 2000,
        //     field6: '字段6',
        //     radio: false
        //   },
        //   {
        //     Id: 2,
        //     field1: '字段11',
        //     field2: '字段22',
        //     field3: '字段33',
        //     amount: 8000,
        //     actualAmount: 8000,
        //     field6: '字段66',
        //     radio: false
        //   },
        //   {
        //     Id: 3,
        //     field1: '字段111',
        //     field2: '字段222',
        //     field3: '字段333',
        //     amount: 60000,
        //     actualAmount: 60000,
        //     field6: '字段666',
        //     radio: false
        //   }
        // ],
        height: 150,
        icon: 'el-icon-c-scale-to-original',
        url: null,
        index: false, //item.index,
        height: 200,
        index: false,
        columnIndex: false,
        ck: true,
        buttons: [
          { name: '添加行', ck: false, icon: 'el-icon-plus', value: 'add' },
          { name: '删除行', ck: false, icon: 'el-icon-delete', value: 'del' },
          { name: '刷新', ck: false, icon: 'el-icon-refresh-right', value: 'ref' }
        ],
        value: [],
        DefaultValue: '',
        code: '',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 26,
        name: '',
        label: '预算清单(编辑)',
        type: 'projectlist',
        icon: 'el-icon-thumb',
        buttonTitle: '添加',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'ProjectList',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 27,
        name: '',
        label: '预算清单(审核)',
        type: 'projectexaminelist',
        icon: 'el-icon-thumb',
        buttonTitle: '审核',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'ProjectList',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 26,
        name: '预算清单总金额',
        label: '预算清单总金额',
        type: 'projectallamount',
        icon: 'el-icon-coin',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'ProjectAllAmount',
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 37,
        name: '指定审核人',
        label: '指定审核人(单选)',
        type: 'AppointAuditUserSelect',
        icon: 'el-icon-arrow-down',
        value: null,
        key: '100000_40',
        DefaultValue: '',
        code: 'AppointAuditUser',
        data: [],
        Controlled: 0,
        isCondition: false,
        isRedact: false
      },
      {
        id: 38,
        name: '指定审核人',
        label: '指定审核人(多选)',
        type: 'AppointAuditUserSelectList',
        icon: 'el-icon-arrow-down',
        value: null,
        key: '100000_40',
        DefaultValue: [],
        code: 'AppointAuditUser',
        data: [],
        Controlled: 0,
        isCondition: false,
        isRedact: false
      }

      //   {
      //     id: 22,
      //     name: '编辑器',
      //     type: 'editor',
      //     value: '',
      //     url: '',
      //     height: '200',
      //     icon: 'el-icon-notebook-2',
      //     DefaultValue: '',
      //     code: '',
      //     Controlled: 0,
      //     isCondition: false,
      //     isRedact:false
      //   },
    ]
  },
  {
    pid: 2,
    text: '南通平台定制组件',
    components: [
      {
        id: 1001,
        name: '一级分类',
        label: '一级分类',
        type: 'OneClassId',
        icon: 'el-icon-edit',
        value: null,
        key: '',
        data: [{ label: '请设置数据源', value: '1' }],
        DefaultValue: '',
        DefaultName: '',
        code: 'OneClassId',
        Controlled: 0,
        isCondition: false,
        isRedact: false,
        required: true
      },
      {
        id: 1002,
        name: '二级分类',
        label: '二级分类',
        type: 'TwoClassId',
        icon: 'el-icon-coin',
        value: null,
        key: '',
        data: [{ label: '请设置数据源', value: '1' }],
        DefaultValue: '',
        DefaultName: '',
        code: 'TwoClassId',
        Controlled: 0,
        isCondition: false,
        isRedact: false,
        required: true
      },
      {
        id: 1003,
        name: '是否新建事项',
        label: '是否新建事项',
        type: 'isnewcreation',
        icon: 'el-icon-coin',
        value: 0,
        DefaultName: '',
        code: 'isnewcreation',
        key: '16000_2',
        Controlled: 0,
        isCondition: false,
        isRedact: false,
        required: true,
        data: [
          { label: '是', value: '1' },
          { label: '否', value: '2' }
        ]
      },
      {
        id: 1004,
        name: '事项名称',
        label: '事项名称(输入)',
        type: 'creationnameinput',
        icon: 'el-icon-coin',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'ProjectName',
        Controlled: 0,
        isCondition: false,
        isRedact: false,
        required: true
      },
      {
        id: 1005,
        name: '事项名称',
        label: '事项名称(下拉)',
        type: 'creationnameselect',
        icon: 'el-icon-coin',
        value: null,
        key: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'ProjectName',
        data: [],
        Controlled: 0,
        isCondition: false,
        isRedact: false,
        required: true
      },
      {
        id: 1006,
        name: '事项编号',
        label: '事项编号',
        type: 'ProjectCode',
        icon: 'el-icon-coin',
        value: '',
        DefaultValue: '',
        DefaultName: '',
        code: 'ProjectCode',
        isDetail: false,
        readonly: true,
        Controlled: 0,
        isCondition: false,
        isRedact: false
      }
    ]
  }
]

export const tableOption = [
  {
    FieldCode: 'FieldCode',
    FieldName: '字段Code',
    type: 'text',
    Width: 140,
    ContentStyle: 'center'
  },
  {
    FieldCode: 'FieldName',
    FieldName: '字段名称',
    type: 'text',
    Width: 140,
    ContentStyle: 'center'
  },
  {
    FieldCode: 'isShow',
    FieldName: '是否显示',
    type: 'switch',
    Width: 120,
    ContentStyle: 'center'
  },
  { FieldCode: 'Width', FieldName: '列宽度', type: 'input', Width: 120, ContentStyle: 'center' }
]
