import request from '@/utils/request.js'

// 装备分类查询接口-查询   (FromBody)
export const ThEquipmentCategoryGetPaged = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategoryfind', params)
}

// 装备分类实体查询接口-查询   (FromBody)
export const ThEquipmentCategoryById = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategorybyid', null, {
    params: params
  })
}
// 装备分类获取分类下拉集合-查询   (FromBody)
export const ThEquipmentCategory = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategory', params)
}

// 装备分类设置状态-设置   (FromBody)
export const ThEquipmentCategorySetStatuz = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategorysetstatuz', params)
}
// 装备分类实体保存-保存   (FromBody)
export const ThEquipmentCategoryInsertUpdate = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategoryinsertupdate', params)
}
// 装备分类删除-删除   (FromBody)
export const ThEquipmentCategoryDelById = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategorydelbyid', null, {
    params: params
  })
}

// 装备分类导入-导入   (FromBody)
export const ThEquipmentCategoryUploadFile = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategoryuploadfile', params)
}

// 装备分类查询接口-查询   (FromBody)
export const ThEquipmentCategoryGetSchoolPaged = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategoryschoolfind', params)
}

// 装备分类查询接口-查询   (FromBody)
export const ThEquipmentCategorySchoolSave = (params) => {
  return request.post('/api/hyun/thequipmentcategory/thequipmentcategoryschoolsave', params)
}