<script setup>
defineOptions({
    name: 'dangerchemicalscompanylist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    FolderAdd, Setting, Search, Refresh
} from '@element-plus/icons-vue'
import {
    UnitFindidName, UserFindCompany, UserGetpubByid, DccompanyDeleteByid, DccompanyenAbled, DccompanyFind, DccompanyGetByid,
    DccompanyInsertUpdate, DcunitlicenseinfoGetCommany, DcunitlicenseinfoSave, AnonDangerchemicals
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const supplierList = ref([])
const linkUserList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const dialogVisible1 = ref(false)
const dialogData = ref({})
const refForm = ref()
const refForm1 = ref()
const ruleForm = {
    CompanyId: [
        { required: true, message: '请选择供应商', trigger: 'change' },
    ],
    UnitName: [
        { required: true, message: '请输入对方单位名称', trigger: 'change' },
    ],
    UserName: [
        { required: true, message: '请输入对方经办人姓名', trigger: 'change' },
    ],
    LicenseName: [
        { required: true, message: '请输入对方单位许可证', trigger: 'change' },
    ],
    LicenseNo: [
        { required: true, message: '请输入对方单位许可证编号', trigger: 'change' },
    ],
    IdNo: [
        { required: true, message: '请输入对方经办人身份证件号码', trigger: 'change' },
    ],
    Mobile: [
        { required: true, message: '请输入对方经办人手机号码', trigger: 'change' },
    ],
}
const dangerChemicalsLevel = ref('')
const unitType = ref(userStore.userInfo.UnitType)
const options = ref([
    { value: 'Name', label: '供应商', },
    { value: 'LinkUser', label: '联系人', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    AnonDangerchemicalsUser()
    UnitFindidNameUser()
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const isAdd = ref(true)
// 添加
const HandleAdd = (row, e) => {
    num.value = e
    dialogVisible.value = true
}
const num = ref(0)
// 修改
const HandleEdit = (row, e) => {
    num.value = e
    if (e == 3) {
        dialogVisible1.value = true
        DcunitlicenseinfoGetCommany({ unittype: 4, Id: row.Id }).then(res => {
            if (res.data.flag == 1 || res.data.flag == 2) {
                const { rows } = res.data.data
                dialogData.value = rows
                nextTick(() => {
                    refForm1.value.resetFields()
                })
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        isAdd.value = false
        dialogVisible.value = true
        DccompanyGetByid({ Id: row.Id }).then(res => {
            if (res.data.flag == 1) {
                const { rows } = res.data.data
                dialogData.value = rows

                if (dialogData.value.CompanyId == '0') dialogData.value.CompanyId = dialogData.value.Name
                //  supplierList.value 
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}
// 删除
const HandleDel = (row) => {
    ElMessageBox.confirm('确定要删除此供应商信息吗?')
        .then(() => {
            DccompanyDeleteByid({ Id: row.Id }).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 提交
const HandleSubmit = () => {
    dialogData.value.UnitType = '4'
    dialogData.value.IdType = '01'
    if (num.value == 3) {
        refForm1.value.validate((valid, fields) => {
            if (!valid && fields) return;

            DcunitlicenseinfoSave(dialogData.value).then(res => {
                if (res.data.flag == 1) {
                    dialogVisible.value = false
                    dialogVisible1.value = false
                    ElMessage.success(res.data.msg || '保存成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    } else {
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;

            DccompanyInsertUpdate(dialogData.value).then(res => {
                if (res.data.flag == 1) {
                    dialogVisible.value = false
                    dialogVisible1.value = false
                    ElMessage.success(res.data.msg || '保存成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
    }


}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const HandleSetSchool = () => {
    router.push({ path: "../base/configset@m=9&opt=0&t=学校权限控制", query: { id: '' } })
}
// 修改状态
const HandleSwitchChange = (e, row) => {
    DccompanyenAbled({ Id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取供应商对应的联系人
const nameChange = (e) => {
    dialogData.value.Name = supplierList.value.filter(item => item.Id == e)[0].Name
    UserFindCompany({ orderBy: "", pageLength: 100000, start: 0, UnitId: e }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            linkUserList.value = rows?.data || []
            dialogData.value.LinkUser = ''
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 选择获取联系人手机号
const linkUserChange = (e) => {
    let LinkUserId = linkUserList.value.filter(item => item.Name == e)[0].Id
    UserGetpubByid({ id: LinkUserId }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // linkUserList.value = rows?.data || []
            dialogData.value.Tel = rows.Tel
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DccompanyFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取供应商单位
const UnitFindidNameUser = () => {
    UnitFindidName({ UnitType: 4 }).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            supplierList.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取配置文件
const AnonDangerchemicalsUser = () => {
    AnonDangerchemicals().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            dangerChemicalsLevel.value = rows.Level
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    供应商列表 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill" v-if="unitType == 3">
                    <li> 学校只能从下表的供应商采购危化品；</li>
                    <li> 如你所需的供应商不在下表中，请联系教育局主管部门。</li>
                </ol>
                <ol class="rowFill" v-else>
                    <li> 供应商是用于学校危化品入库，不添加学校将无法入库；</li>
                    <li> 所添加的供应商必须是地方危化品供应入围企业。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem" v-if="dangerChemicalsLevel == unitType">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="dangerChemicalsLevel == unitType"></div>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                    <div class="verticalIdel" v-if="dangerChemicalsLevel == unitType"></div>
                    <el-form-item class="flexItem" v-if="dangerChemicalsLevel == unitType">
                        <el-button type="primary" :icon="Setting" @click="HandleSetSchool">学校权限控制</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe row-key="id" default-expand-all
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="供应商全称" min-width="180"></el-table-column>
            <el-table-column prop="LinkUser" label="联系人" min-width="100" align="center"></el-table-column>
            <el-table-column prop="Tel" label="联系电话" min-width="140" align="center"></el-table-column>
            <el-table-column prop="QQ" label="QQ号" min-width="120" align="center"></el-table-column>
            <el-table-column prop="BusinessContent" label="主要经营内容" min-width="160" align="center"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
                <template #default="{ row }">
                    <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="0" inline-prompt active-text="启"
                        inactive-text="禁" style="--el-switch-off-color: #ff4949"
                        @change="HandleSwitchChange($event, row)" />
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="120" align="center"
                v-if="dangerChemicalsLevel == unitType">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="信息维护" width="110" align="center" v-if="unitType == 3">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row, 3)">修改</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" title="设置状态">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="140px"
                    status-icon>
                    <el-form-item label="供应商全称：" prop="CompanyId">
                        <el-select v-model="dialogData.CompanyId" filterable @change="nameChange" style="width: 80%">
                            <el-option v-for="item in supplierList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="联系人：">
                        <el-select v-model="dialogData.LinkUser" filterable allow-create default-first-option
                            @change="linkUserChange" style="width: 80%">
                            <el-option v-for="item in linkUserList" :key="item.Name" :label="item.Name"
                                :value="item.Name" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="联系电话：">
                        <el-input v-model="dialogData.Tel" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="QQ号：">
                        <el-input v-model="dialogData.QQ" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="主要经营内容：">
                        <el-input type="textarea" v-model="dialogData.BusinessContent" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
        <app-box v-model="dialogVisible1" :width="680" :lazy="true" title="信息维护" v-if="unitType == 3">
            <template #content>
                <el-form @submit.prevent ref="refForm1" :model="dialogData" :rules="ruleForm" label-width="200px"
                    status-icon>
                    <el-form-item label="对方单位名称：" prop="UnitName">
                        <el-input v-model="dialogData.UnitName" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="对方单位地址：">
                        <el-input v-model="dialogData.Addresz" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="对方经办人姓名：" prop="UserName">
                        <el-input v-model="dialogData.UserName" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="对方单位许可证：" prop="LicenseName">
                        <el-input v-model="dialogData.LicenseName" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="对方单位许可证编号：" prop="LicenseNo">
                        <el-input v-model="dialogData.LicenseNo" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="对方经办人身份证件号码：" prop="IdNo">
                        <el-input v-model="dialogData.IdNo" style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="对方经办人手机号码：" prop="Mobile">
                        <el-input v-model="dialogData.Mobile" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible1 = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>