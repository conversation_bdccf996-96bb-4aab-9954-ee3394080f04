<script setup>
defineOptions({
    name: 'userschooldetail'
});
import { onMounted, ref, onActivated, nextTick } from 'vue';

import {
    Schoolinfogetbyid
} from '@/api/user.js'
import { tagsListStore } from "@/utils/index.js";
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const formData = ref({})
onMounted(() => {
    if (route.query.isTagRouter) {
        SchoolinfogetbyidUser();
    }
})
onActivated(() => {
    nextTick(() => {
        // tag标签添加参数
        tagsListStore(userStore.tagsList, route)
        if (!route.query.isTagRouter) {
            SchoolinfogetbyidUser();
        }
    })
})
const SchoolinfogetbyidUser = () => {
    Schoolinfogetbyid({ id: route.query.Id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            formData.value = rows[0]
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>

    <el-form style="width: 1000px;" class="mobile-box" :model="formData" label-width="140px" status-icon>
        <fieldset>

            <legend>{{ route.query.Name }}基本信息</legend>
            <el-form-item label="单位代码：">
                <span class="blueSpan">{{ formData.Code }}</span>
            </el-form-item>
            <el-form-item label="单位名称：">
                <span class="blueSpan">{{ formData.Name }}</span>
            </el-form-item>
            <el-form-item label="单位简称：">
                <span :class="formData.Brief ? 'blueSpan' : 'redSpan'">
                    {{ formData.Brief ? formData.Brief : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="单位属性：">
                <span class="blueSpan"> {{ formData.StageName }} </span>
            </el-form-item>
            <el-form-item label="校长：">
                <span :class="formData.HeadMaster ? 'blueSpan' : 'redSpan'">
                    {{ formData.HeadMaster ? formData.HeadMaster : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="校长联系号码：">
                <span :class="formData.MsaterMobile ? 'blueSpan' : 'redSpan'">
                    {{ formData.MsaterMobile ? formData.MsaterMobile : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="单位管理员：">
                <span :class="formData.SchoolAdmin ? 'blueSpan' : 'redSpan'">
                    {{ formData.SchoolAdmin ? formData.SchoolAdmin : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="管理员联系号码：">
                <span :class="formData.AdminMobile ? 'blueSpan' : 'redSpan'">
                    {{ formData.AdminMobile ? formData.AdminMobile : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="单位网址：">
                <span :class="formData.url ? 'blueSpan' : 'redSpan'">
                    {{ formData.url ? formData.url : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="单位地址：">
                <span :class="formData.Address ? 'blueSpan' : 'redSpan'">
                    {{ formData.Address ? formData.Address : '暂无信息' }}</span>
            </el-form-item>
        </fieldset>
        <fieldset>
            <legend>{{ route.query.Name }}扩展信息</legend>
            <el-form-item label="区县单位：">
                <span class="blueSpan">{{ formData.unitCounty }}</span>
            </el-form-item>
            <el-form-item label="市级单位：">
                <span class="blueSpan">{{ formData.unitCity }}</span>
            </el-form-item>
            <el-form-item label="单位性质：">
                <span class="blueSpan"> {{ formData.SchoolNature == 2 ? '民办' : '公办' }}</span>
            </el-form-item>
            <el-form-item label="占地面积：">
                <span :class="formData.FloorArea ? 'blueSpan' : 'redSpan'">
                    {{ formData.FloorArea ? formData.FloorArea : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="建筑面积：">
                <span :class="formData.BuildArea ? 'blueSpan' : 'redSpan'">
                    {{ formData.BuildArea ? formData.BuildArea : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="班级数：">
                <span :class="formData.ClassNum ? 'blueSpan' : 'redSpan'">
                    {{ formData.ClassNum ? formData.ClassNum : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="在校学生数：">
                <span :class="formData.StudentNum ? 'blueSpan' : 'redSpan'">
                    {{ formData.StudentNum ? formData.StudentNum : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="教职工人数：">
                <span :class="formData.TeacherNum ? 'blueSpan' : 'redSpan'">
                    {{ formData.TeacherNum ? formData.TeacherNum : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="邮编：">
                <span :class="formData.ZipCode ? 'blueSpan' : 'redSpan'">
                    {{ formData.ZipCode ? formData.ZipCode : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="邮箱：">
                <span :class="formData.Email ? 'blueSpan' : 'redSpan'">
                    {{ formData.Email ? formData.Email : '暂无信息' }}</span>
            </el-form-item>
            <el-form-item label="说明：">
                <span :class="formData.Memo ? 'blueSpan' : 'redSpan'">
                    {{ formData.Memo ? formData.Memo : '暂无信息' }}</span>
            </el-form-item>
        </fieldset>

    </el-form>
</template>
<style lang="scss" scoped>
.dialogFlexBox {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
        width: 50%;
        margin-bottom: 10px;
    }
}

.blueSpan {
    color: blue;
}

.redSpan {
    color: #F56C6C;
}

.el-form {
    display: flex;

    fieldset {
        width: 500px;
        color: #333;
        border: #ccc dashed 1px;
        padding: 20px;
        margin-right: 20px;

        legend {
            font-size: 15px;
            padding-left: 20px;
            color: #666;
            font-weight: 800;
        }
    }

    .el-form-item {
        margin-bottom: 10px;
    }
}
</style>