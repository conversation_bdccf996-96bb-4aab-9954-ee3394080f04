<script setup>
defineOptions({
    name: 'managementedit'
});
import { onMounted, ref, nextTick, onActivated } from 'vue';
import {
    QuestionFilled, Select, UploadFilled, Delete
} from '@element-plus/icons-vue'
import {
    AttachmentUpload,
    Getpagedbytype,
} from '@/api/user.js'
import {
    PurchaseGeteditbyid, PurchaseSaveadd, PurchaseSaveedit, Delattachmentbyid
} from '@/api/purchase.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { yearDate, foundReturn, fileDownload, tagsListStore, integerLimit, limit } from "@/utils/index.js";
import { useUserStore } from '@/stores'
import { useRoute } from 'vue-router'
import router from '@/router'
const route = useRoute()
const userStore = useUserStore()
const formData = ref({})
const refForm = ref()
const uploadFileData = ref([])
const yearDateList = ref([])
const IsRenewalStatuzList = ref([])
const SupplierListList = ref([])
const contractMainBodyList = ref([])
const payMethodList = ref([])
const supplierLocationList = ref([])
const BuyList = ref([])
const StageList = ref([])
const FilingStatuz = ref(0)//审核不通过或退回 状态值
const FilingExplanation = ref('')//审核不通过或退回原因
const Id = ref('')
const ruleForm = {
    PurchaseYear: [
        { required: true, message: '请选择年度', trigger: 'change' },
    ],
    UniformBuyId: [
        { required: true, message: '请选择采购批次', trigger: 'change' },
    ],
    IsContractRenewal: [
        { required: true, message: '请选择续签合同', trigger: 'change' },
    ],
    ContractMainBodyId: [
        { required: true, message: '请选择合同签订主体', trigger: 'change' },
    ],
    ContractSignDate: [
        { required: true, message: '请选择合同签订日期', trigger: 'change' },
    ],
    ContractStartDate: [
        { required: true, message: '请选择合同开始日期', trigger: 'change' },
    ],
    ContractEndDate: [
        { required: true, message: '请选择合同终止日期', trigger: 'change' },
    ],
    PayMethodId: [
        { required: true, message: '请选择费用支付方式', trigger: 'change' },
    ],
    SupplierId: [
        { required: true, message: '请选择供应商', trigger: 'change' },
    ],
    SupplierLocationId: [
        { required: true, message: '请选择供应商归属地', trigger: 'change' },
    ],
    ContractEndDate: [
        { required: true, message: '请选择送货日期', trigger: 'change' },
    ],
    ContractEndDate: [
        { required: true, message: '请选择验收日期', trigger: 'change' },
    ],
    ContractEndDate: [
        { required: true, message: '请选择供应商送检日期', trigger: 'change' },
    ],
    ContractEndDate: [
        { required: true, message: '请选择学校送检日期', trigger: 'change' },
    ],
    ContractPersonNum: [
        { required: true, message: '请输入订购总人数', trigger: 'change' },
    ],
    ContractAmount: [
        { required: true, message: '请输入合同金额', trigger: 'change' },
    ],
    GoodsDeadline: [
        { required: true, message: '请输入供货期', trigger: 'change' },
    ],
    WarrantyMonth: [
        { required: true, message: '请输入质保期', trigger: 'change' },
    ],
    DeliveryDate: [
        { required: true, message: '请选择送货日期', trigger: 'change' },
    ],
    AcceptanceDate: [
        { required: true, message: '请选择验收日期', trigger: 'change' },
    ],
    SupplierSendTestDate: [
        { required: true, message: '请选择供应商送检日期', trigger: 'change' },
    ],
    SchoolSendTestDate: [
        { required: true, message: '请选择学校送检日期', trigger: 'change' },
    ],
}

onMounted(() => {
    yearDateList.value = yearDate()
    GetpagedbytypeUser()
    Id.value = route.query.id
    if (route.query.isTagRouter) {
        PurchaseGeteditbyidUser()
    }
})

onActivated(() => {
    // 修改tag标签名称########################
    if (route.query.title) {
        // 修改pinia数据
        userStore.$patch(state => {
            state.pageTitleObj.managementTitle = route.query.title
        })
    }
    let tagsList = userStore.tagsList
    // 使用forEach遍历数组  修改tag标签名称
    tagsList.forEach(item => {
        if (item.path == route.path) {
            // 如果path匹配，则修改title  
            item.title = userStore.pageTitleObj.managementTitle;
        }
    });
    userStore.setTagsList(tagsList)
    // ########################
    // tag标签添加参数
    tagsListStore(userStore.tagsList, route)
    fileIdList.value = []
    Id.value = route.query.id
    nextTick(() => {
        if (!route.query.isTagRouter) {
            PurchaseGeteditbyidUser()
        }
    })
})

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 102 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 信息详情
const PurchaseGeteditbyidUser = () => {
    PurchaseGeteditbyid({ id: Id.value }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            uploadFileData.value.forEach(item => {
                item.fileLChildList = []
                item.categoryList = []
            })
            fileIdList.value = []
            refForm.value.resetFields()
            if (rows) {
                formData.value = rows
                if (!rows.IsContractRenewal) {
                    formData.value.IsContractRenewal = undefined
                } else {
                    formData.value.IsContractRenewal = String(rows.IsContractRenewal)
                }
                if (!rows.ContractMainBodyId) {
                    formData.value.ContractMainBodyId = undefined
                } else {
                    formData.value.ContractMainBodyId = String(rows.ContractMainBodyId)
                }
                if (!rows.SupplierLocationId) {
                    formData.value.SupplierLocationId = undefined
                } else {
                    formData.value.SupplierLocationId = String(rows.SupplierLocationId)
                }
                if (!rows.PayMethodId) {
                    formData.value.PayMethodId = undefined
                } else {
                    formData.value.PayMethodId = String(rows.PayMethodId)
                }
                FilingExplanation.value = rows.FilingExplanation || ''
                FilingStatuz.value = rows.FilingStatuz
            } else {
                formData.value = {}
                FilingExplanation.value = ''
            }
            let categoryList = other.AttachmentList || [];//附件集合
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }
            IsRenewalStatuzList.value = other.IsRenewalStatuz || [];//是否为续签合同 
            SupplierListList.value = other.SupplierList || [];//供应商
            contractMainBodyList.value = other.ContractMainBody || [];//合同签订主体
            payMethodList.value = other.PayMethod || [];//费用支付方式
            supplierLocationList.value = other.SupplierLocation || [];//供应商属地
            StageList.value = other.StageList || [];//学段
            BuyList.value = other.BuyList || [];//采购批次

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//提交
const HandleSubmit = (e) => {
    if (e == 3) {
        // 提交
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) return;
            // 判断必传的附件是否已传  
            let found = foundReturn(uploadFileData.value)
            if (found) {
                return
            }
            PurchaseSaveUser(e)
        })
    } else {
        // 保存不校验 
        PurchaseSaveUser(e)
    }
}
// 采购管理添加保存
const PurchaseSaveUser = (e) => {

    if (!formData.value.UniformBuyId) {
        ElMessage.error('请先选择采购批次')
        return
    }
    if (!formData.value.PurchaseYear) {
        ElMessage.error('请先选择年度')
        return
    }

    let pramsData = {
        OptType: e,
        Id: Id.value,
        UniformBuyId: formData.value.UniformBuyId,
        PurchaseYear: formData.value.PurchaseYear,
        IsContractRenewal: Number(formData.value.IsContractRenewal) || undefined,//是否续签合同
        ContractMainBodyId: Number(formData.value.ContractMainBodyId) || undefined,//合同签订主体
        ContractPersonNum: Number(formData.value.ContractPersonNum) || undefined,//订购人数
        ContractSignDate: formData.value.ContractSignDate,//合同签订日期
        ContractStartDate: formData.value.ContractStartDate,//合同开始日期
        ContractEndDate: formData.value.ContractEndDate,//合同终止时间
        ContractAmount: Number(formData.value.ContractAmount) || undefined,//合同金额
        PayMethodId: Number(formData.value.PayMethodId) || undefined,//费用支付方式Id
        SupplierId: formData.value.SupplierId,//供应商
        SupplierLocationId: Number(formData.value.SupplierLocationId) || undefined,//供应商归属地
        GoodsDeadline: Number(formData.value.GoodsDeadline) || undefined,//供货期
        WarrantyMonth: Number(formData.value.WarrantyMonth) || undefined,//质保月
        Memo: formData.value.Memo,//备注
        DeliveryDate: formData.value.DeliveryDate,//送货日期
        AcceptanceDate: formData.value.AcceptanceDate,//验收日期
        SupplierSendTestDate: formData.value.SupplierSendTestDate,//供应商送检日期
        SchoolSendTestDate: formData.value.SchoolSendTestDate,//学校送检日期
        AttachmentIdList: fileIdList.value.map(t => t.Id),
        xd100001: formData.value.xd100001 || undefined,
        xd100002: formData.value.xd100002 || undefined,
        xd100003: formData.value.xd100003 || undefined,
    }
    if (Id.value === '0') {
        // 新增
        PurchaseSaveadd(pramsData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '提交成功')
                Id.value = res.data.data.rows.Id
                formData.value.PurchaseNo = res.data.data.rows.PurchaseNo
                if (e == 3) {
                    // 提交
                    let tagsList = userStore.tagsList
                    tagsList = tagsList.filter(t => t.path != route.path)
                    userStore.setTagsList(tagsList)
                    router.push({ path: "./list" })
                } else {
                    PurchaseGeteditbyidUser(Id.value)
                }
            } else if (res.data.flag == 2) {
                ElMessage.warning(res.data.msg)
                Id.value = res.data.data.rows.Id
                formData.value.PurchaseNo = res.data.data.rows.PurchaseNo
                PurchaseGeteditbyidUser(Id.value)
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    } else {
        // 修改
        PurchaseSaveedit(pramsData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '提交成功')
                if (e == 3) {
                    // 提交
                    let tagsList = userStore.tagsList
                    tagsList = tagsList.filter(t => t.path != route.path)
                    userStore.setTagsList(tagsList)
                    router.push({ path: "./list" })
                } else {
                    PurchaseGeteditbyidUser(Id.value)
                }
            } else if (res.data.flag == 2) {
                ElMessage.warning(res.data.msg)
                PurchaseGeteditbyidUser(Id.value)
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }

}

//输入保留两位小数
const limitInput = (val, name, len) => {
    formData.value[name] = limit(val, len);
}

//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}
// /////    附件处理      /////
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.categoryList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: item.FileCategory }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    Delattachmentbyid({ id: Id.value, attid: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);

    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}

</script>
<template>
    <el-form style="width: 100%;min-width: 560px;" :inline="true" class="mobile-box" @submit.prevent ref="refForm"
        :model="formData" :rules="ruleForm" label-width="180px" status-icon>
        <el-form-item v-if="FilingStatuz == 11 || FilingStatuz == 21" :label="FilingStatuz == 11 ? '审核不通过原因：' : '退回原因：'"
            style="width: 100%; border-bottom: 1px dashed #E4E7ED; ">
            <span style="color: #F56C6C;padding-left: 10px;">{{ FilingExplanation }}</span>
        </el-form-item>
        <el-form-item label="采购批次：" prop="UniformBuyId" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="是指采购申请的中“采购批次”；一个采购批次可对应多个“合同履约”，请正确选择，否则会影响后期数据统计。"
                    placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 采购批次： </span>
            </template>
            <el-select v-model="formData.UniformBuyId" class="item_content">
                <el-option v-for="item in BuyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item class="formItem"> </el-form-item>
        <el-form-item label="年度：" prop="PurchaseYear" class="formItem">
            <el-select v-model="formData.PurchaseYear" placeholder="是指采购年度" class="item_content">
                <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="合同批次：" style="width: 50%;" class="formItem">
            <el-input v-model="formData.PurchaseNo" disabled class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="续签合同：" prop="IsContractRenewal" class="formItem">
            <el-radio-group v-model="formData.IsContractRenewal">
                <el-radio v-for="item in IsRenewalStatuzList" :key="item.value" :value="item.value">
                    {{ item.label }}</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="合同签订主体：" prop="ContractMainBodyId" class="formItem">
            <el-select v-model="formData.ContractMainBodyId" class="item_content">
                <el-option v-for="item in contractMainBodyList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="订购总人数：" prop="ContractPersonNum" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="是指订购校服的学生总人数" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 订购总人数： </span>
            </template>
            <el-input v-model="formData.ContractPersonNum" @input="integerLimitInput($event, 'ContractPersonNum')"
                class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="其中：" class="formItem" v-if="StageList.length > 1">
            <el-form-item v-for="item in StageList" :key="item.value" :label="item.label" label-width="60px"
                :prop="item.value" :rules="[{ required: true, message: `请输入`, trigger: 'change' }]"
                style="width: 33.3%;">
                <el-input v-model="formData[item.value]" @input="integerLimitInput($event, item.value)"
                    class="item_content"></el-input>
            </el-form-item>
        </el-form-item>
        <el-form-item label="合同签订日期：" prop="ContractSignDate" class="formItem">
            <el-date-picker type="date" v-model="formData.ContractSignDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="合同开始日期：" prop="ContractStartDate" class="formItem">
            <el-date-picker type="date" v-model="formData.ContractStartDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="合同终止日期：" prop="ContractEndDate" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="保证时间正确，否则影响校服征订和家长追订" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 合同终止日期： </span>
            </template>
            <el-date-picker type="date" v-model="formData.ContractEndDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="合同金额（元）：" prop="ContractAmount" class="formItem">
            <el-input v-model="formData.ContractAmount" @input="limitInput($event, 'ContractAmount', 4)"
                placeholder="是指校服采购的总金额" class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="费用支付方式：" prop="PayMethodId" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="“学校集中代收取代支付”是指由学校财务部门作为代收费管理；“纳入学校账户管理”是指纳入学校专户或学校账户管理。"
                    placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 费用支付方式： </span>
            </template>
            <el-select v-model="formData.PayMethodId" class="item_content">
                <el-option v-for="item in payMethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="供应商：" prop="SupplierId" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="如下拉框没有你所需的供应商名称，请通知供应商注册" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 供应商： </span>
            </template>
            <el-select v-model="formData.SupplierId" filterable placeholder="输入关键字筛选" class="item_content">
                <el-option v-for="item in SupplierListList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="供应商归属地：" prop="SupplierLocationId" class="formItem">
            <el-select v-model="formData.SupplierLocationId" class="item_content">
                <el-option v-for="item in supplierLocationList" :key="item.value" :label="item.label"
                    :value="item.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="供货期（天）：" prop="GoodsDeadline" class="formItem">
            <el-input v-model="formData.GoodsDeadline" @input="limitInput($event, 'GoodsDeadline')"
                class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="质保期（月）：" prop="WarrantyMonth" class="formItem">
            <el-input v-model="formData.WarrantyMonth" @input="limitInput($event, 'WarrantyMonth')"
                class="item_content"></el-input>
        </el-form-item>
        <el-form-item label="送货日期：" prop="DeliveryDate" class="formItem">
            <el-date-picker type="date" v-model="formData.DeliveryDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="验收日期：" prop="AcceptanceDate" class="formItem">
            <el-date-picker type="date" v-model="formData.AcceptanceDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="供应商送检日期：" prop="SupplierSendTestDate" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="请填写检测报告上的检测日期" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 供应商送检日期： </span>
            </template>
            <el-date-picker type="date" v-model="formData.SupplierSendTestDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="学校送检日期：" prop="SchoolSendTestDate" class="formItem">
            <template #label>
                <el-tooltip class="item" effect="dark" content="请填写检测报告上的检测日期" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> 学校送检日期： </span>
            </template>
            <el-date-picker type="date" v-model="formData.SchoolSendTestDate" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"></el-date-picker>
        </el-form-item>
        <el-form-item label="备注：" style="width: 100%;">
            <el-input type="textarea" v-model="formData.Memo" :maxlength="300" show-word-limit
                :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
        </el-form-item>
        <!-- 附件上传 -->
        <el-form-item v-for="(item, index) in uploadFileData" :key="index" style="width: 100%;">
            <template #label>
                <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                    <div>
                        <el-icon color="#E6A23C" class="tipIcon">
                            <QuestionFilled />
                        </el-icon>
                    </div>
                </el-tooltip>
                <span> {{ item.Name }}： </span>
            </template>
            <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" :accept="item.UploadFileTypeAccept"
                :before-upload="beforeAvatarUpload.bind(null, item)" :http-request="httpRequest.bind(null, item, index)"
                :disabled="numberDisabled">
                <el-button type="success" size="small" :icon="UploadFilled"
                    @click="MaxFileNumberClick(item)">上传</el-button>
            </el-upload>
            <div class="fileFlex">
                <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                    <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                        {{ itemCate.Title }}{{ itemCate.Ext }}
                    </span>
                </div>
                <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                    <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                        <Delete />
                    </el-icon>
                    <span style="cursor: pointer;" @click="fileListDownload(itemChild, item.fileLChildList)">
                        {{ itemChild.Title }}{{ itemChild.Ext }}
                    </span>
                </div>
            </div>
        </el-form-item>
        <el-form-item label=" ">
            <el-button type="primary" :icon="Select" @click="HandleSubmit(1)">保存</el-button>
            <el-button type="primary" :icon="Select" @click="HandleSubmit(3)" v-if="Id && Id !== '0'">提交</el-button>
        </el-form-item>
    </el-form>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-right: 0;
}

.item_content {
    // width: 60% !important;
}

:deep(.el-date-editor.el-input) {
    width: 100% !important;
}

.formItem {
    width: 50%;
}

@media (max-width: 768px) {
    .formItem {
        width: 100%;
    }
}
</style>