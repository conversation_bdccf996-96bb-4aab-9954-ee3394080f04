<script setup>
defineOptions({
    name: 'dangerchemicalswastelist'
});
import { onMounted, ref, nextTick, onActivated, computed } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    DcWasteDisposalFind, DcWasetDisposalGetIsExists, DcWasetDisposalSave
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { limit, disposalDate } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ sortModel: [{ SortCode: "Sort", SortType: "ASC" }] })
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DcWasetDisposalGetIsExistsUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const selectRows = ref([])//选中的行
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

// 列表
const HandleTableData = () => {
    DcWasteDisposalFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//处置申请
const HandleDisposal = (e) => {
    if (e == 1) {
        console.log(selectRows.value)
        let find = selectRows.value.find(t => !t.Num)
        if (find) {
            ElMessage.error('已选择的危化品请全部填写处置数量')
            return
        }
        let ids = selectRows.value.map(t => t.Id).join(',')
        let list = selectRows.value.map(t => {
            return {
                BaseWasteId: t.Id,
                Num: t.Num,
                Remark: t.Remark
            }
        })
        let data = { o: list, type: e, ids: ids };
        ElMessageBox.confirm('您确认处置所选择的危化品吗？')
            .then(() => {
                DcWasetDisposalSave(data).then((res) => {
                    if (res.data.flag == 1) {
                        ElMessage.success(res.data.msg || '处置成功')
                        HandleTableData()
                    } else {
                        ElMessage.error(res.data.msg)
                    }
                })
            })
            .catch((err) => {
                console.info(err)
            })
    } else {
        let arr = tableData.value.filter(t => t.Num)
        let list = arr.map(t => {
            return {
                BaseWasteId: t.Id,
                Num: t.Num,
                Remark: t.Remark
            }
        })
        let data = { o: list, type: e, ids: '' };
        DcWasetDisposalSave(data).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '处置成功')
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }
}
const isExists = ref(false)
// 查询是否存在废弃物
const DcWasetDisposalGetIsExistsUser = () => {
    DcWasetDisposalGetIsExists().then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            if (rows.IsExistApplyWaset == 1 || rows.IsExistScrapWaset == 1) {
                isExists.value = true
            } else {
                isExists.value = false
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//输入保留两位小数
const limitInput = (val, row, index, name) => {
    tableData.value[index][name] = limit(val);
}
const isNum = computed(() => {
    return tableData.value.filter(t => t.Num)
})
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    危废物存量库 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>请根据系统提示或实际产生的危废物，及时更新本库数量；</li>
                    <li>本库中数量是当前累计贮存的数量；</li>
                    <li>更新数量后，请点击【更新危废物数量】；</li>
                    <li>如要处理危废物，请点击【处置申请】，处置申请后数量被清零。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="EditPen" :disabled="selectRows.length == 0"
                            @click="HandleDisposal(1)">处置申请</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Refresh" :disabled="isNum.length == 0"
                            @click="HandleDisposal(0)">更新危废物数量</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div v-if="isExists" style="text-align: center;color: #F56C6C;font-size: 16px;padding: 10px;">
            <span>当前有危废物需要您添加！</span>
        </div>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="OneClassName" label="一级分类" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="二级分类" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"> </el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right" :class-name="'requiredColumn'">
                <template #header>
                    <span style="color: #F56C6C;">*数量</span>
                </template>
                <template #default="{ row, $index }">
                    <el-input v-model="row.Num" @input="limitInput($event, row, $index, 'Num')"
                        :class="row.Num ? '' : 'requiredInput'"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="200" show-overflow-tooltip>
                <template #default="{ row }">
                    <el-input v-model="row.Remark"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="RegDate" label="更新时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ disposalDate(row.RegDate) }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>

    </div>
</template>
<style lang="scss" scoped>
:deep(.requiredColumn) {
    .cell {
        padding: 0 2px !important;
    }
}

.requiredInput {
    :deep(.el-input__wrapper) {
        box-shadow: none !important;
        border: 1px solid red !important;
    }
}
</style>