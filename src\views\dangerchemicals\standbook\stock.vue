<script setup>
defineOptions({
    name: 'dangerchemicalsstandbookstock'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcStandbookStockFind
} from '@/api/dangerchemicals.js'
import { ExcelDownload } from "@/utils/index.js"
import {
    ExportDcStandbookStockFind
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Path", SortType: "ASC" }] })
const attribute = ref()
const date = ref(new Date())
//加载数据
onMounted(() => {

    if (route.query.isTagRouter) {
    }
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
    date.value = currentYear + '-' + currentMonth;
    filters.value.Year = parseInt(currentYear, 10);
    filters.value.Month = parseInt(currentMonth, 10);
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    attribute.value = ''

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
    date.value = currentYear + '-' + currentMonth;
    filters.value.Year = parseInt(currentYear, 10);
    filters.value.Month = parseInt(currentMonth, 10);

    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const dateChange = (val) => {
    if (!val) {
        filters.value.Year = undefined;
        filters.value.Month = undefined;
    } else {
        const [year, month] = val.split('-');
        filters.value.Year = parseInt(year, 10);
        filters.value.Month = parseInt(month, 10);
    }

    HandleTableData()
}

const bindList = [
    { Id: 'IsDetonate', Name: '易制爆' },
    { Id: 'IsPoison', Name: '易制毒' },
    { Id: 'IsHyperToxic', Name: '剧毒' }]
// 列表
const HandleTableData = () => {
    filters.value.IsDetonate = undefined;
    filters.value.IsPoison = undefined;
    filters.value.IsHyperToxic = undefined;
    if (attribute.value) {
        filters.value[attribute.value] = 1
    } else {
        filters.value[attribute.value] = undefined;
    }
    DcStandbookStockFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const HandlePrint = (e) => {
    router.push({
        path: "./stockprint",
        query: {
            attribute: attribute.value,
            Year: filters.value.Year,
            Month: filters.value.Month,
            date: date.value
        }
    })
}
//导出
const HandleExport = () => {
    ExportDcStandbookStockFind(filters.value).then(res => {
        ExcelDownload(res)
    });
}
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
} 
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    操作提示 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 打印与导出请先选择统计时间（月份）； </li>
                    <li> 有数据才可进行打印或导出。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" :disabled="!date || tableData.length == 0"
                            @click="HandlePrint">打印</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" :disabled="!date || tableData.length == 0"
                            @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="date" type="month" format="YYYY-MM" value-format="YYYY-MM"
                            :clearable="false" placeholder="统计时间" @change="dateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="attribute" clearable placeholder="危化品属性" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in bindList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" id="printArea" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="right"> </el-table-column>
            <el-table-column prop="LastStockNum" label="上月存量" min-width="100" align="right"> </el-table-column>
            <el-table-column prop="PurchaseNum" label="采购数量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="ApplyNum" label="领用数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="BackNum" label="退货数量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="ScrapNum" label="报废数量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="StockNum" label="当月存量" min-width="110" align="right"></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>