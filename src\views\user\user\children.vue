<script setup>
import { nextTick, onMounted, ref, watch } from 'vue'
import {
    UploadFilled, Upload, Search, Refresh, FolderAdd, Download
} from '@element-plus/icons-vue'
import {
    Userfindchildren, Rolefindbyunittype, Unitfindchildrenidname, Usergetbyid, Useraccountunlock, UserRoleChildUnitBatchSet,
    Usersavechildren, userupdateuserstatuz, UploadPostexecl, UploadUserFile, RoleGetPlatformRoleList, ExportChildUser
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { integerLimit, fileDownload, roleObjList } from "@/utils/index.js";
import md5 from 'js-md5';
import { useUserStore } from '@/stores';
import router from '@/router'

const userStore = useUserStore()
// const departmentDataList = ref([])//部门集合
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const strRoleIdsList = ref([])//当前账号的角色集合
const rolefindList = ref([])//角色列表
const unitList = ref([])//单位列表
const selectRows = ref([])
const excelUrl = ref('')
const filtersKey = ref({ key: '', value: 'UnitName', departmentId: 0 })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Sort", SortType: "ASC" }, { SortCode: "UnitId", SortType: "ASC" }] })
const options = ref([
    { value: 'UnitName', label: '单位', },
    { value: 'Name', label: '姓名', },
    { value: 'Mobile', label: '手机', },
    { value: 'AcctName', label: '账号', },
])

const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

//加载数据
onMounted(() => {
    HandleTableData()
    RolefindbyunittypeUser()
    UnitfindchildrenidnameUser()
})
//输入整数
const integerLimitInput = (val, name) => {
    formData.value[name] = integerLimit(val);
}

//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const isAdd = ref(true)
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '姓名不能为空', trigger: 'blur' },
    ],
    AcctName: [
        { required: true, message: '账号不能为空', trigger: 'blur' },
    ],
    UnitId: [
        { required: true, message: '请选择单位', trigger: 'change' },
    ],
    Mobile: [
        { required: true, message: '手机号码不能为空', trigger: 'blur' },
        {
            pattern: /^1[0-9]{10}$/,
            message: '请输入11位手机号码',
            trigger: 'blur'
        }
    ],
    initPwd: [
        { required: true, message: '密码不能为空', trigger: 'blur' },
        {
            pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,30}$/,
            message: '密码至少8位，必须包含大写字母，小写字母，数字，特殊字符四种中的三种',
            trigger: 'blur'
        }
    ]
}

// 添加账户
const HandleAdd = () => {
    isAdd.value = true
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        formData.value = { Id: '0' }
    })
    strRoleIdsList.value = []

}
//修改
const HandleEdit = (row) => {
    isAdd.value = false
    formData.value.Id = row.Id
    // 获取拥有角色信息，实现反选
    Usergetbyid({ id: row.Id }).then((res) => {
        const { rows } = res.data.data
        formData.value = rows
        formData.value.Sex = Number(rows.Sex)
        strRoleIdsList.value = rows.StrRoleIds.split(',')
        strRoleIdsList.value = [...new Set(strRoleIdsList.value)]
        strRoleIdsList.value = strRoleIdsList.value.map(item => Number(item))
        console.log('strRoleIdsList.value', strRoleIdsList.value)
    }).catch((err) => {
        console.info(err)
    })
    dialogVisible.value = true
}
// 修改状态
const HandleSwitchChange = (e, row) => {
    userupdateuserstatuz({ userId: row.Id }).then((res) => {
        // console.log(res)
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '设置成功')
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}

// 根据单位类型加载角色信息
const RolefindbyunittypeUser = () => {
    let unitType = 3
    if (userStore.userInfo.UnitType == 1) {
        unitType = 2
    }
    Rolefindbyunittype({ unitType: unitType }).then(res => {
        if (res.data.flag == 1) {
            rolefindList.value = res.data.data.rows

            // console.log('res.data.data.rows', roleObjList(rolefindList.value))
            roleTableData.value = roleObjList(rolefindList.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    });
}
// 获取单位
const UnitfindchildrenidnameUser = () => {
    Unitfindchildrenidname().then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            unitList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    });
}
//获取列表 与 搜索
const HandleTableData = (page) => {
    filters.value.Name = undefined;
    filters.value.Mobile = undefined;
    filters.value.AcctName = undefined;
    filters.value.UnitName = undefined;

    // 按类型搜索
    if (filtersKey.value.key) {
        filters.value[filtersKey.value.value] = filtersKey.value.key;
    } else {
        filters.value[filtersKey.value.value] = undefined;
    }
    Userfindchildren(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { other, rows, total } = res.data.data
            tableData.value = rows;
            excelUrl.value = other;
            tableTotal.value = Number(total);
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value.key = ''
    HandleTableData()
}
// // 下属单位用户导入
// const importExecl = () => {
//     router.push({ path: "./import" })
// }
// 下属单位导出 
//导出
const HandleExport = () => {
    filters.value.Name = undefined;
    filters.value.Mobile = undefined;
    filters.value.AcctName = undefined;
    filters.value.UnitName = undefined;

    // 按类型搜索
    if (filtersKey.value.key) {
        filters.value[filtersKey.value.value] = filtersKey.value.key;
    } else {
        filters.value[filtersKey.value.value] = undefined;
    }
    ExportChildUser(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            // let url = '/' + rows;
            let index = rows.lastIndexOf('_');
            let title = rows.substring(index + 1);
            fileDownload(rows, title);
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}


// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 设置
const HandleSet = (row) => {
    listUserIds.value = row.Id
    RoleGetPlatformRoleList({ userId: row.Id }).then(res => {
        if (res.data.flag == 1) {
            const { other, rows, total } = res.data.data
            roleTableData.value = rows
            tableVisible.value = true
            // nextTick(() => {
            strRoleIdsList.value = other
            strRoleIdsList.value = [...new Set(strRoleIdsList.value)]
            // strRoleIdsList.value = strRoleIdsList.value.map(item => Number(item))
            console.log('strRoleIdsList.value', strRoleIdsList.value)
            // })


        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
        console.info(err)
    })
}
// 新增或修改提交
const HandleSubmit = (e) => {
    if (e == 1) {
        if (!strRoleIdsList.value.length) {
            ElMessage.warning('请至少配置一个角色')
            return
        }
        if (formData.value.initPwd) {
            formData.value.Pwd = md5(formData.value.initPwd)
        } else {
            formData.value.Pwd = ''
        }
        formData.value.StrRoleIds = strRoleIdsList.value.join(',')
        refForm.value.validate((valid, fields) => {
            if (!valid && fields) {
                return;
            }
            let data = JSON.parse(JSON.stringify(formData.value))
            data.initPwd = undefined
            Usersavechildren(data).then(res => {
                if (res.data.flag == 1) {
                    HandleTableData()

                    ElMessage.success('保存成功')
                    dialogVisible.value = false
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
    } else {
        // UserRoleChildUnitBatchSet
        console.log('strRoleIdsList.value', strRoleIdsList.value)
        UserRoleChildUnitBatchSet({ listRoleIds: strRoleIdsList.value.join(','), listUserIds: listUserIds.value }).then(res => {
            if (res.data.flag == 1) {
                HandleTableData()

                ElMessage.success('保存成功')
                tableVisible.value = false
            } else {
                ElMessage.error(res.data.msg)
            }
        }).catch((err) => {
            console.info(err)
        })

    }

}
const tableVisible = ref(false)
const roleTableData = ref([])
const listUserIds = ref()



// 解冻
const HandleUnLock = (row) => {
    ElMessageBox.confirm(`确定要解冻该账户吗?`)
        .then(() => {
            Useraccountunlock({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    HandleTableData()
                    ElMessage.success('解冻成功')
                } else {
                    ElMessage.error(res.data.msg)
                }
            }).catch((err) => {
                console.info(err)
            })
        })
}
// 是否显示所有用户
const handleFilter = (e) => {
    console.log(e)
    if (e) {
        filters.value.IsShowAllRole = e
    } else {
        filters.value.IsShowAllRole = undefined
    }
    HandleTableData()
}

const uploadVisible = ref(false)
const uploadData = ref({})
const uploadRefForm = ref()
//  下属单位用户导入
const importExecl = () => {
    uploadVisible.value = true
}
// 模板下载
const HandleDownload = () => {
    fileDownload(excelUrl.value);

}
const fileFile = ref()
const uploadRef = ref()

// 导入前校验
const beforeAvatarUpload = (file) => {
    fileFile.value = file
    const extension = file.name.split('.')[1] === 'xls'
    const extension2 = file.name.split('.')[1] === 'xlsx'
    if (!extension && !extension2) {
        ElMessage({
            message: '上传模板只能是 xls、xlsx格式!',
            type: 'error'
        })
    }
    return extension || extension2
}
const httpRequest = () => {
    UploadPostexecl([fileFile.value]).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data

            UploadUserFile({ FilePath: rows }).then(res1 => {
                if (res1.data.flag == 1) {
                    ElMessage.success(res1.data.msg || '导入成功')
                    setTimeout(function () {
                        HandleTableData()
                        uploadVisible.value = false
                    }, 1000);
                } else {
                    ElMessage({
                        showClose: true,
                        message: res1.data.msg,
                        type: 'error',
                        duration: 5000
                    })
                }
            })
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <el-row>
        <el-col>
            <el-form @submit.prevent :inline="true" :model="filtersKey" class="flexBox">
                <el-form-item class="flexItem">
                    <el-button type="success" :icon="FolderAdd" @click="HandleAdd">添加</el-button>
                    <el-button type="primary" :icon="UploadFilled" @click="importExecl">下属单位用户导入</el-button>
                    <el-button type="primary" :icon="Upload" @click="HandleExport"
                        :disabled="tableData.length == 0">下属单位用户导出</el-button>
                </el-form-item>
                <div class="verticalIdel"></div>
                <el-form-item label="" class="flexItem" label-width="60">
                    <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="max-width: 300px"
                        class="input-with-select">
                        <template #prepend>
                            <el-select v-model="filtersKey.value" style="width: 100px">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-checkbox v-model="filtersKey.IsShowAllRole" label="显示所有用户" size="large"
                        @change="handleFilter" />
                </el-form-item>
                <el-form-item class="flexItem">
                    <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                    <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                </el-form-item>

            </el-form>
        </el-col>
    </el-row>
    <!-- 内容 -->
    <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" border
        stripe header-cell-class-name="headerClassName">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column prop="Code" label="单位代码" min-width="160" align="center"></el-table-column>
        <el-table-column prop="UnitName" label="单位名称" min-width="160"></el-table-column>
        <el-table-column prop="Name" label="姓名" min-width="160" align="center"></el-table-column>
        <el-table-column prop="Mobile" label="手机号码" min-width="140" align="center"></el-table-column>
        <el-table-column prop="AcctName" label="登录账号" min-width="140" align="center"></el-table-column>
        <el-table-column prop="RoleNames" label="角色" min-width="160" show-overflow-tooltip align="center">
        </el-table-column>
        <el-table-column label="状态" min-width="120" align="center">
            <template #default="{ row }">
                <el-switch v-model="row.Statuz" :active-value="1" :inactive-value="0" inline-prompt active-text="启"
                    inactive-text="禁" style="--el-switch-off-color: #ff4949"
                    @change="HandleSwitchChange($event, row)" />
            </template>
        </el-table-column>
        <!-- <el-table-column fixed="right" label="权限" min-width="100" align="center">
            <template #default="{ row }">
                <el-button type="primary" link @click="HandleSet(row)">设置</el-button>
            </template>
        </el-table-column> -->
        <el-table-column prop="opt" fixed="right" label="操作" width="120" align="center">
            <template #default="{ row }">
                <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                <el-button type="primary" link @click="HandleUnLock(row)">解冻</el-button>
            </template>
        </el-table-column>
        <template #empty>
            <el-empty description="没有数据"></el-empty>
        </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
        @handleChange="handlePage" />
    <!-- 添加修改单位用户信息 -- 弹窗 -->
    <app-box v-model="dialogVisible" :height="600" :width="960" :lazy="true"
        :title="isAdd ? '添加下属单位用户信息' : '修改下属单位用户信息'">
        <template #content>
            <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="80px" status-icon
                class="addCustom">
                <fieldset>
                    <legend>基本信息</legend>
                    <div class="dialogFlexBox">
                        <el-form-item label="姓名" prop="Name">
                            <el-input v-model="formData.Name" auto-complete="off"></el-input>
                        </el-form-item><el-form-item label="手机号码" prop="Mobile">
                            <el-input v-model="formData.Mobile" auto-complete="off"
                                @input="integerLimitInput($event, 'Mobile')"></el-input>
                        </el-form-item>
                        <el-form-item label="登录账号" prop="AcctName">
                            <el-input v-model="formData.AcctName" auto-complete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="密码" :prop="isAdd ? 'initPwd' : ''">
                            <el-input v-model="formData.initPwd" show-password auto-complete="off" clearable
                                :placeholder="isAdd ? '' : '如不修改密码，请留空'"></el-input>
                        </el-form-item>
                    </div>
                    <el-form-item label="单位" prop="UnitId" style="margin-bottom: 18px !important;">
                        <el-select v-model="formData.UnitId" filterable>
                            <el-option v-for="item in unitList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                </fieldset>
                <fieldset>
                    <legend>权限设置</legend>
                    <el-form-item label="" label-width="0">
                        <!-- <el-checkbox-group v-model="strRoleIdsList" style="width: 100%;">
                            <el-checkbox v-for="item in rolefindList" :key="item.Id" :label="item.Name"
                                :value="item.RoleId" />
                        </el-checkbox-group> -->
                        <el-table :data="roleTableData" border header-cell-class-name="headerClassName">
                            <el-table-column prop="ModuleName" label="角色分类" min-width="160"
                                align="center"></el-table-column>
                            <el-table-column label="角色名称" width="640">
                                <template #default="{ row }">
                                    <el-checkbox-group v-model="strRoleIdsList">
                                        <el-checkbox v-for="item in row.children" :key="item.RoleId" :label="item.Name"
                                            :value="item.RoleId" style="width: 20%;">
                                        </el-checkbox>
                                    </el-checkbox-group>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </fieldset>
                <fieldset>
                    <legend>更多信息</legend>

                    <div class="dialogFlexBox2">
                        <!-- <el-form-item label="部门" prop="DepartmentIds">
                            <el-select v-model="formData.DepartmentIds" multiple>
                                <el-option class="flexItem" v-for="item in departmentDataList" :key="item.Id"
                                    :label="item.Name" :value="item.Id">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
                        <el-form-item label="性别" prop="Sex">
                            <el-radio-group v-model="formData.Sex">
                                <el-radio class="radio" :value="1">男</el-radio>
                                <el-radio class="radio" :value="0">女</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="QQ">
                            <el-input v-model="formData.Qq" auto-complete="off"></el-input>
                        </el-form-item>
                        <el-form-item label="电邮">
                            <el-input v-model="formData.Email" auto-complete="off"></el-input>
                        </el-form-item>
                    </div>
                </fieldset>
            </el-form>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="HandleSubmit(1)"> 提交 </el-button>
            </span>
        </template>
    </app-box>
    <app-box v-model="tableVisible" :height="600" :width="960" :lazy="true" title="权限设置">
        <template #content>

            <el-table :data="roleTableData" border header-cell-class-name="headerClassName">
                <el-table-column prop="ModuleName" label="角色分类" width="200" align="center"></el-table-column>
                <el-table-column label="角色名称" min-width="680">
                    <template #default="{ row }">
                        <el-checkbox-group v-model="strRoleIdsList">
                            <el-checkbox v-for="item in row.ListRole" :key="item.RoleId" :label="item.Name"
                                :value="item.RoleId" style="width: 20%;">
                            </el-checkbox>
                        </el-checkbox-group>
                    </template>
                </el-table-column>

            </el-table>


        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="tableVisible = false">取消</el-button>
                <el-button type="primary" @click="HandleSubmit(2)"> 提交 </el-button>
            </span>
        </template>
    </app-box>
    <!-- 导入用户 -->
    <el-dialog v-model="uploadVisible" title="下属单位导入" width="480px">
        <el-form @submit.prevent ref="uploadRefForm" :model="uploadData" label-width="80px" status-icon>
            <el-form-item>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false" accept=".xlsx,.xls"
                    style="line-height: normal;margin: 0 10px;" :before-upload="beforeAvatarUpload"
                    :http-request="httpRequest">
                    <el-button type="primary" :icon="UploadFilled">下属单位用户导入 </el-button>
                </el-upload>
                <el-button type="primary" plain :icon="Download" @click="HandleDownload">模版下载</el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
    display: flex;
    flex-wrap: wrap;

    .flexItem {
        color: #fff;
        font-size: 25px;
        margin-top: 8px;
        margin-right: 5px;
        cursor: pointer;
        flex-wrap: wrap;
    }

    .flexContent {
        width: 200px;
    }
}

.addCustom.el-form {
    width: 90%;
    margin: 0 auto;

    .el-form-item {
        margin-bottom: 10px;
    }

    .el-checkbox-group {
        display: flex;
        flex-wrap: wrap;

        label {
            width: 20%;
        }
    }

    .el-checkbox {
        height: var(--el-checkbox-height, 22px);
    }

}

.taskNameConent {
    width: 100%;
    /* 具体宽度，例如 200px 或 100% */
    ;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.dialogFlexBox,
.dialogFlexBox2 {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
        width: 50%;
    }
}

.dialogFlexBox {
    .el-form-item {
        margin-bottom: 18px !important;

        &:nth-child(3) {
            margin-bottom: 30px !important;
        }

        &:nth-child(4) {
            margin-bottom: 30px !important;
        }

    }
}

.dialogFlexBox2 {
    .el-form-item {
        margin-bottom: 5px !important;
    }
}

.el-row .el-form-item {
    margin-top: 0px;
    margin-bottom: 10px;
}

fieldset {
    // color: #333;
    border: #ccc dashed 1px;
    padding: 10px;
    margin: 10px 0;

    legend {
        font-size: 16px;
    }
}
</style>