<script setup>
defineOptions({
    name: 'dangerchemicalspurchasedetaillist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    DcPurchaseDetailListFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'

const route = useRoute()
const userStore = useUserStore()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [] })
const CatalogSecond = ref([])
const summation = ref({})

const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        filters.value.PurchaseOrderId = route.query.Id
        HandleTableData(true);
    }

})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            filters.value.PurchaseOrderId = route.query.Id
            HandleTableData(true)
        }
    })
})
// 列表
const HandleTableData = (isFirst) => {
    if (isFirst) {
        filters.value.isFirst = isFirst
    } else {
        filters.value.isFirst = undefined
    }
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcPurchaseDetailListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (other) {
                CatalogSecond.value = other.listCatalog
            }
            if (res.data.data.rows.Statistics) {
                summation.value = res.data.data.rows.Statistics[0]
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.SecondId = undefined
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        else if (index == 5) {
            sums[index] = summation.value.Sum ? '￥' + Number(summation.value.Sum).toFixed(2) : '--'
            return;
        }
    });
    return sums;
}

const filtersChange = () => { HandleTableData() }

</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SecondId" clearable placeholder="危化品分类" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in CatalogSecond" :key="item.id" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <div style="font-size: 14px;color: #606266;margin-bottom: 10px;">采购批次：{{ route.query.batchNo }}
        </div>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="right"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Price" label="参考单价" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Sum" label="参考金额" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row.Sum ? '￥' + Number(row.Sum).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Remark" label="备注" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="StockNum" label="库存量" min-width="80" align="center"></el-table-column>
            <el-table-column prop="Limited" label="预警量" min-width="80" align="center"></el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>