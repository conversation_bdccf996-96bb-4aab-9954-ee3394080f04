<script setup>
defineOptions({
  name: 'treasurylistitem'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
  FindSourceFundList
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, Search } from '@element-plus/icons-vue'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { pageQuery, urlQuery, tagsListStore, formatNumberWithCommas, formatNumber } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores'
const userStore = useUserStore()
const route = useRoute()
const routerObject = ref({})
const routerUrl = ref('')
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const tableTotal = ref(0)
const tableData = ref([])// 表格数据
const columns = ref([])// 表头数据
const columnData = ref([])//列表头数据
const buttonData = ref([])//按钮数据
const inputList = ref([])//查询条件:输入
const searchList = ref([])//查询条件：下拉、时间等
const moduleId = ref(route.query.moduleId)//模块Id
const amountName = ref('')
const displayColumn = ref()
const sumList = ref([])
//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  // tag标签添加参数
  tagsListStore(userStore.tagsList, route)
  // routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
  routerUrl.value = urlQuery(route.path); // 参数
  // console.log("routerUrl", routerUrl.value)
  filters.value.ListCondition = []
  filtersSeachList.value = []
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

const Handle = (row, item) => {
  if (item.CalculateFun == 'detail') {
    // 详情
    router.push({ path: "./detail@moduleId=" + moduleId.value, query: { id: row.Id, ProcessId: row.ProcessId } })
  }
}
const filtersKey = ref('')
const filtersValue = ref()
//列表
const HandleTableData = (isFirst) => {
  routerObject.value = pageQuery(route.path)// 获取生成页面携带的参数对象
  filters.value.ProcessId = routerObject.value.processId
  filters.value.isFirst = isFirst

  // 输入框查询条件格式化
  if (filtersKey.value) {
    // 提取 inputList.value 的所有 FieldCode 值
    const listCodes = inputList.value.map(item => item.FieldCode);
    // 过滤 filters.value.ListCondition list2Codes 中的项
    filters.value.ListCondition = filters.value.ListCondition.filter(item => !listCodes.includes(item.Code));
    filters.value.ListCondition.push({ Code: filtersValue.value, Value: filtersKey.value })
  }

  FindSourceFundList(filters.value).then(res => {
    console.log('res', res)
    if (res.data.flag == 1) {
      const { rows, other } = res.data.data
      tableData.value = rows.data || [];
      tableTotal.value = rows.dataCount || 0
      filters.value.pageSize = rows.PageSize || 10
      // console.log('tableData.value', tableData.value)
      sumList.value = rows.Other?.listSum || [];//获取合计行数据
      displayColumn.value = rows.Other?.displayColumn || undefined;//获取合计行合计名称位置
      if (isFirst && other) {
        amountName.value = other.amountName || '';//获取合计行合计名称
        columns.value = other.listColumn || [];
        // 区分是否为按钮列
        columnData.value = columns.value.filter(t => !t.CalculateFun)
        buttonData.value = columns.value.filter(t => t.CalculateFun)
        let listSearch = other?.listWhere || []//查询条件
        let listDataSource = other?.listDataSource || []//查询条件数据源
        // 数据源匹配查询条件
        listSearch.forEach((item, index) => {
          const data = listDataSource.find(t => t.label == item.FieldCode);
          if (data) {
            item.data = JSON.parse(data.value);
          }
        });
        // searchList.value = listSearch
        searchList.value = listSearch.filter(t => t.ControlType != 'text' && t.ControlType != 'projectname')
        inputList.value = listSearch.filter(t => t.ControlType == 'text' || t.ControlType == 'projectname' || !t.ControlType)
        console.log('searchList.value', searchList.value)
        console.log('inputList.value', inputList.value)
        if (inputList.value.length > 1) {
          filtersValue.value = inputList.value[0].FieldName //查询条件：输入框
        }
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });

}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.Key = undefined
  // 重置搜索条件
  filters.value.ListCondition = []
  filtersSeachList.value = []
  searchList.value.forEach(item => {
    item.value = undefined
  })
  inputList.value.forEach(item => {
    item.value = undefined
  })
  startDate.value = undefined
  endDate.value = undefined
  filtersKey.value = undefined
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}

const startDate = ref()
const endDate = ref()
const filtersSeachList = ref([])
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
  if (!endDate.value) return false;
  return time >= new Date(endDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
  if (!startDate.value) return false;
  return time < new Date(startDate.value + ' 00:00:00');
};
// 查询条件选择
const filtersChange = (e, item, type) => {
  if (e) {
    if (type == 'select') {
      // 选择下拉项时添加进数组内
      filtersSeachList.value.push({ Code: item.FieldCode, Value: item.data.find(t => t.value == e).value })
    } else if (type == 'text') {
      filtersSeachList.value.push({ Code: item.FieldCode, Value: e })
    }
    // 根据Code去除数组内重复项，并保留Code相同的最后一项
    filters.value.ListCondition = removeLastClean(filtersSeachList.value)
  } else {
    // 清空下拉框时删除数组内对应Code的项
    filters.value.ListCondition = filtersSeachList.value.filter(t => item.FieldCode != t.Code);
  }
  if (type == 'date') {
    let start = ''
    let end = ''
    if (startDate.value) {
      start = startDate.value + ' 00:00:00'
    } else {
      start = ''
    }
    if (endDate.value) {
      end = endDate.value + ' 23:59:59'
    } else {
      end = ''
    }
    let strDate = start + '|' + end
    if (!start && !end) {
      filters.value.ListCondition = filtersSeachList.value.filter(t => item.FieldCode != t.Code);
    } else {
      console.log('strDate', strDate)
      filtersSeachList.value.push({ Code: item.FieldCode, Value: strDate })
      // 根据Code去除数组内重复项，并保留Code相同的最后一项
      filters.value.ListCondition = removeLastClean(filtersSeachList.value)
    }

  }
  if (type == 'select' || type == 'date') {
    HandleTableData()
  }
}
//  数组去重： 根据Code去除数组内重复项，并保留Code相同的最后一项
const removeLastClean = (arr) => {
  const seenIds = {};
  for (const item of arr) {
    seenIds[item.Code] = item; // 直接更新为当前对象，覆盖之前的（如果有的话）
  }
  return Object.values(seenIds);
}
const found = ref(true)//合计行合计名称位置是否存在
// 总计
const getSummaries = (param) => {
  const { columns, data } = param;
  const sums = [];
  // 单元格赋值
  columns.forEach((column, index) => {
    // 判断合计行合计名称位置是否存在
    if (column.rawColumnKey === displayColumn.value) {
      sums[index] = amountName.value;
      found.value = false;
      return;
    }
    if (found.value) {
      sums[0] = amountName.value;
      // return;
    }
    // 需要合计的字段位置与值
    let isAmount = sumList.value.filter(t => t.Code == column.rawColumnKey)
    if (isAmount.length > 0) {
      let arr = columnData.value.filter(t => t.FieldCode == isAmount[0].Code)
      if (arr[0].ColumnFieldType == 1) {
        sums[index] = formatNumber(isAmount[0].SumValue)
      } else if (arr[0].ColumnFieldType == 2) {
        sums[index] = formatNumberWithCommas(isAmount[0].SumValue)
      } else {
        sums[index] = isAmount[0].SumValue
      }
      return;
    }
  });
  return sums;
}

</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox" v-if="searchList.length > 0 || inputList.length > 0">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="" class="flexItem" v-for="(item, index) in searchList" :key="item.Id">
            <el-select
              v-if="['select', 'radio', 'subjectnature', 'OneClassId', 'TwoClassId', 'creationnameselect'].includes(item.ControlType)"
              v-model="item.value" clearable :placeholder="item.ConditionWord || item.FieldName"
              @change="filtersChange($event, item, 'select')" :style="{ 'width': item.Width + 'px' }">
              <el-option v-for="item in item.data" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <!-- 日期 -->
            <el-date-picker v-else-if="item.ControlType == 'date' && item.IsDateRange != 1" v-model="item.value"
              type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable
              :placeholder="item.ConditionWord || item.FieldName" @change="filtersChange($event, item, 'text')"
              :style="{ 'width': item.Width + 'px' }">
            </el-date-picker>
            <!-- 日期区间 -->
            <div v-else-if="item.ControlType == 'date' && item.IsDateRange == 1">
              <el-date-picker v-model="startDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable
                :placeholder="item.ConditionWord || item.FieldName" :disabled-date="disabledStartDate"
                @change="filtersChange($event, item, 'date', 'startDate')" :style="{ 'width': item.Width + 'px' }">
              </el-date-picker>
              <span style="color: #333;"> 至 </span>
              <el-date-picker v-model="endDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD" clearable
                :placeholder="item.ConditionWord || item.FieldName" :disabled-date="disabledEndDate"
                @change="filtersChange($event, item, 'date', 'endDate')" :style="{ 'width': item.Width + 'px' }">
              </el-date-picker>
            </div>
            <!-- 日期时间 -->
            <el-date-picker v-else-if="item.ControlType == 'datetime'" v-model="item.value" type="datetime"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" clearable
              :placeholder="item.ConditionWord || item.FieldName" @change="filtersChange($event, item, 'text')"
              :style="{ 'width': item.Width + 'px' }">
            </el-date-picker>
            <!-- 时间 -->
            <el-time-picker v-else-if="item.ControlType == 'time'" v-model="item.value" format="HH:mm:ss"
              value-format="HH:mm:ss" clearable :placeholder="item.ConditionWord || item.FieldName"
              @change="filtersChange($event, item, 'text')" :style="{ 'width': item.Width + 'px' }">
            </el-time-picker>
          </el-form-item>
          <!-- 输入框 -->
          <el-form-item class="flexItem" v-if="inputList.length > 0">
            <!-- 单个条件 -->
            <el-input v-if="inputList.length == 1" v-model.trim="inputList[0].value" clearable
              @change="filtersChange($event, inputList[0], 'text')" :placeholder="inputList[0].FieldName"
              :style="{ 'width': inputList[0].Width || 180 + 'px' }"></el-input>
            <!-- 多个条件 -->
            <el-input v-else v-model.trim="filtersKey" placeholder="请输入" style="width: 280px" class="input-with-select">
              <template #prepend>
                <el-select v-model="filtersValue" style="width: 120px">
                  <el-option v-for="item in inputList" :key="item.Id" :label="item.FieldName" :value="item.FieldCode" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :show-summary="sumList.length > 0"
      :summary-method="getSummaries" header-cell-class-name="headerClassName">
      <el-table-column v-for="column in columnData" :key="column.FieldCode" :prop="column.FieldCode"
        :label="column.FieldName" :min-width="column.Width" :align="column.ContentStyle">
        <template #default="{ row }">
          <span v-if="column.ColumnFieldType == 4">
            {{ row[column.FieldCode] ? row[column.FieldCode].substring(0, column.DateDisplay || 10) : '' }}
          </span>
          <span v-else-if="column.ColumnFieldType == 1">
            {{ row[column.FieldCode] ? formatNumber(row[column.FieldCode]) : '' }}
          </span>
          <span v-else-if="column.ColumnFieldType == 2">
            {{ row[column.FieldCode] ? formatNumberWithCommas(row[column.FieldCode]) : '' }}
          </span>
          <span v-else> {{ row[column.FieldCode] }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column fixed="right" label="操作" min-width="120" align="center">
        <template #default="{ row }">
          <span v-for="item in buttonData" :key="item.FieldCode">
            <el-button type="primary" link @click="Handle(row, item)" v-if="row[item.FieldCode]">
              {{ row[item.FieldCode] }}</el-button>
          </span>
        </template>
      </el-table-column> -->
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>
