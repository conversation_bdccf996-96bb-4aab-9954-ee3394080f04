worker_processes auto;
#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;
 
#pid        logs/nginx.pid;
 
 
events {
    worker_connections  1024;
}
 
 
http {
    include       mime.types;
    default_type  application/octet-stream;

    gzip  on;
    gzip_min_length 5k; 
    gzip_buffers 4 16k; 
    gzip_comp_level 8; 
 
    sendfile        on;
    #tcp_nopush     on; 

    keepalive_timeout  600;
 
    #gzip  on;
 
    client_max_body_size   20m;

    server {
        listen       80;
        server_name  localhost;
  
        location / {
        	root   /usr/share/nginx/html;
        	index  index.html index.htm;
        	try_files $uri $uri/ /index.html;
        }
         
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
 
        
    }
 
}