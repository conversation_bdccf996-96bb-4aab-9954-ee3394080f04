import request from '@/utils/request.js'
// 获取选用方案列表
export const SchemeGetpaged = (params) => {
  return request.post('/api/hyun/xuniformscheme/getpaged', params)
}
// 选用方案获取详情
export const SchemeGetbyid = (params) => {
  return request.get('/api/hyun/xuniformscheme/getbyid', { params: params })
}
// 选用方案-复制
export const SchemeSavecopy = (params) => {
  return request.put('/api/hyun/xuniformscheme/savecopy', null, { params: params })
}
// 获取区县启用的采购方式
export const Getpurchasemethod = (params) => {
  return request.get('/api/hyun/xuniformscheme/getpurchasemethod', { params: params })
}
// 选用方案添加保存
export const SchemeSaveadd = (params) => {
  return request.post('/api/hyun/xuniformscheme/saveadd', params)
}
// 选用方案修改保存
export const SchemeSaveedit = (params) => {
  return request.post('/api/hyun/xuniformscheme/saveedit', params)
}
// 选用方案发布
export const SchemeSavepublish = (params) => {
  return request.post('/api/hyun/xuniformscheme/savepublish', params)
}
//  选用方案撤销发布
export const SchemePublishcancel = (params) => {
  return request.put('/api/hyun/xuniformscheme/publishcancel', null, { params: params })
}
// 方案选用-删除附件
export const Delattachmentbyid = (params) => {
  return request.post('/api/hyun/xuniformscheme/delattachmentbyid', null, { params: params })
}
// 选用方案删除
export const SchemeDelbyid = (params) => {
  return request.delete('/api/hyun/xuniformscheme/delbyid', { params: params })
}
// 征求结果提交备案
export const SchemeSavefiling = (params) => {
  return request.put('/api/hyun/xuniformscheme/savefiling', null, { params: params })
}
//意见详情列表
export const SchemeGetopinionpaged = (params) => {
  return request.post('/api/hyun/xuniformscheme/getopinionpaged', params)
}
// 方案选用-区县审核
export const SchemeConfirmfiling = (params) => {
  return request.put('/api/hyun/xuniformscheme/confirmfiling', null, { params: params })
}
// 方案选用-区县退回
export const SchemeFilingbackout = (params) => {
  return request.put('/api/hyun/xuniformscheme/filingbackout', null, { params: params })
}
// 导出
// 校服选用-征求结果-意见详情导出
export const SchemeExportopinion = (params) => {
  return request.post('/api/hyun/xuniformscheme/exportopinion', params)
}
//  方案选用-区县撤回
export const FilingRevoked = (params) => {
  return request.put('/api/hyun/xuniformscheme/filingrevoked', null, { params: params })
}
