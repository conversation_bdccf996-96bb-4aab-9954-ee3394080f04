<script setup>
defineOptions({
    name: 'dangerchemicalsschoolmaterialstoragelog'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy
} from '@element-plus/icons-vue'
import {
    DccatalogGetClassTwo, DcCompanyComboxFind, DcSchoolCatalogRecord, DcScrapListFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const bogetList = ref([])
const companyList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const dialogVisible = ref(false)
const filters = ref({ pageIndex: 1, pageSize: 10, statuz: 1, InputType: 1, sortModel: [{ SortCode: "RegDate", SortType: "DESC" }] })
const RegDatele = ref()
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'Brand', label: '品牌', },
    { value: 'PurchaseBatchNo', label: '采购批次', }
])
const filtersKey = ref('')
const filtersValue = ref('Name')
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
    DcCompanyComboxFindUser()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const selectRows = ref([])
// 列表多选
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}

const dialogData = ref([])
const Remark = ref('')
// 查看
const HandleDetail = (row, e) => {
    DcSchoolCatalogRecord({ SchoolMaterialId: row.Id, PurchaseListId: row.PurchaseListId }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            let data = rows.data
            dialogData.value = [
                { prop: '危化品名称', oldAttribute: data[1].Name, newAttribute: data[0].Name, isChange: data[0].Name != data[1].Name },
                { prop: '规格属性', oldAttribute: data[1].Model, newAttribute: data[0].Model, isChange: data[0].Model != data[1].Model },
                { prop: '品牌', oldAttribute: data[1].Brand, newAttribute: data[0].Brand, isChange: data[0].Brand != data[1].Brand },
                { prop: '数量', oldAttribute: data[1].Num, newAttribute: data[0].Num, isChange: data[0].Num != data[1].Num },
                { prop: '单位', oldAttribute: data[1].UnitName, newAttribute: data[0].UnitName, isChange: data[0].UnitName != data[1].UnitName },
                { prop: '单价(元)', oldAttribute: data[1].Price, newAttribute: data[0].Price, isChange: data[0].Price != data[1].Price }
            ]
            Remark.value = data[0].Remark
            console.log(dialogData)
            dialogVisible.value = true
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 列表
const HandleTableData = () => {
    filters.value.PurchaseBatchNo = undefined;
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.Brand = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcScrapListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.TwoCatalogId = undefined
    filters.value.LvCompanyId = undefined
    filters.value.RegDatege = undefined
    filters.value.RegDatele = undefined
    RegDatele.value = undefined
    filtersKey.value = ''
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const regDategeChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const regDateleChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            bogetList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取学校供应商信息-查询
const DcCompanyComboxFindUser = () => {
    DcCompanyComboxFind({ Statuz: 1 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            companyList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    }).catch((err) => {
    })
}
const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 打印
const HandlePrint = (e) => {
    let ids = undefined
    if (selectRows.value.length > 0) {
        ids = selectRows.value.map(item => item.Id).join(',')
    }
    router.push({
        path: "./storagelogprint",
        query: {
            companyid: filters.value.LvCompanyId,
            dtBegin: filters.value.RegDatege,
            dtEnd: filters.value.RegDatele,
            datefiled: filtersValue.value,
            keyword: filtersKey.value,
            ids: ids,
            path: './materialbacklog',
        }
    })
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!RegDatele.value) return false;
    return time >= new Date(RegDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    入库记录 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 第一步，选择供应商和入库时间段； </li>
                    <li> 第二步，点击【打印入库单】。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy"
                            :disabled="!filters.LvCompanyId || tableData.length == 0"
                            @click="HandlePrint">打印入库单</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="regDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="RegDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="regDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.TwoCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in bogetList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.LvCompanyId" clearable filterable placeholder="供应商"
                            style="width: 160px" @change="filtersChange">
                            <el-option v-for="item in companyList" :key="item.Id" :label="item.Name" :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Num" label="数量" min-width="120" align="center"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="140" align="center"></el-table-column>
            <el-table-column prop="Price" label="单价" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row.Price ? '￥' + Number(row.Price).toFixed(2) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="Brand" label="品牌" min-width="120" align="center"> </el-table-column>
            <el-table-column prop="RegDate" label="入库时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="CompanyName" label="供应商" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="PurchaseBatchNo" label="采购批次" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.PurchaseBatchNo || '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="IsChange" label="采购变更" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsChange == 1" style="color: #F56C6C">有变更</span>
                    <span v-else> </span>
                </template>
            </el-table-column>
            <el-table-column prop="IsPrint" label="是否已打印" min-width="120" align="center">
                <template #default="{ row }">
                    <span v-if="row.IsPrint == 1" style="color: #F56C6C">已打印</span>
                    <span v-else>未打印</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="80" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.IsChange == 1" type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="600" :lazy="true" title="变更信息">
            <template #content>
                <el-table ref="refTableData" :data="dialogData" border stripe header-cell-class-name="headerClassName">
                    <el-table-column prop="prop" label="项目名称" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="oldAttribute" label="原采购需求" min-width="120" align="center"></el-table-column>
                    <el-table-column prop="newAttribute" label="实际采购" min-width="120" align="center">
                        <template #default="{ row }">
                            <span v-if="row.isChange" style="color: #F56C6C">{{ row.newAttribute }}</span>
                            <span v-else>{{ row.newAttribute }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style></style>