<script setup>
import { onMounted, ref } from 'vue';
import {
    Getpaged, Setbyid
} from '@/api/user.js'
import { ElMessage } from 'element-plus'
const formData = ref({})
const tableData = ref([])
const IsShowPrice = ref(1)

//加载数据
onMounted(() => {
    GetpagedUser()
})
// 校服展示价格
const GetpagedUser = () => {
    Getpaged({ code: 4000 }).then((res) => {
        if (res.data.flag == 1) {
            // console.log("校服展示价格", res.data)
            const { rows } = res.data.data
            tableData.value = rows
            IsShowPrice.value = rows[0].ValueNum
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 修改状态
const HandleSwitchChange = (e) => {
    console.log(e)
    Setbyid({ id: e.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '执行成功')
            if (e.ValueNum == 0) {
                IsShowPrice.value = 0
            } else {
                IsShowPrice.value = 1
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

</script>
<template>

    <el-form style="width: 900px;" class="mobile-box" :model="formData" label-width="280px" status-icon>
        <el-form-item label="校服在本平台展示时是否显示价格：" v-for="item in tableData" :key="item.Id">
            <el-radio-group v-model="item.ValueNum" @change="HandleSwitchChange(item)">
                <el-radio :value="0">不显示</el-radio>
                <el-radio :value="1">显示</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label-width="0px">
            <div class="img">
                <span style="padding-left: 10px;">价格是否显示示例 :</span>
                <img v-if="IsShowPrice == 1" src="@/assets/img/price01.png" alt="">
                <img v-else src="@/assets/img/price02.png" alt="">
            </div>
        </el-form-item>
    </el-form>
</template>
<style lang="scss" scoped>
.img {
    padding-left: 30px;
}
</style>