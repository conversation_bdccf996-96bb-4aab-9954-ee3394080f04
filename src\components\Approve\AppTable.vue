<script setup>
import { watch, onMounted, ref, nextTick, computed, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import AppUpload from '@/components/Approve/AppUpload.vue';
import AppBox from '@/components/Approve/AppBox.vue';
import AppTableRender from '@/components/Approve/AppTable/AppTableRender';
import AppImageViewer from '@/components/Approve/AppImageViewer.vue';

const { proxy } = getCurrentInstance();
const _errMsg = ref()
const emit = defineEmits(['loadBefore', 'loadAfter', 'rowChange', 'rowClick', 'rowDbClick', 'selectionChange']);
const props = defineProps({
  rowKey: {
    // 树形结构的主键字段，如果设置值默认会开启树形table；注意rowKey字段的值必须是唯一（2021.05.02）
    typeof: String,
    default: undefined
  },
  loadTreeChildren: {
    // 树形结构加载子节点
    type: Function,
    default: (tree, treeNode, resolve) => {
      if (resolve) {
        return resolve([])
      }
    }
  },
  textInline: {
    // 表格内容超出后是否换行显示（2020.01.16）
    type: Boolean,
    default: true
  },
  tableData: {
    // 表数据源,配置了url就不用传这个参数了
    type: Array,
    default: () => {
      return []
    }
  },
  columns: {
    type: Array,
    default: []
  },
  height: {
    type: Number,
    default: 0
  },
  maxHeight: {
    type: Number,
    default: 0
  },
  linkView: {
    type: Function,
    default: function () {
      return 1
    }
  },
  pagination: {
    type: Object,
    default: function () {
      return { total: 0, size: 30, sortName: '' }
    }
  },
  url: {
    type: String,
    default: ''
  },
  paginationHide: {
    type: Boolean,
    default: true
  },
  color: {
    type: Boolean,
    default: true
  },
  index: {
    // 是否创建索引号,如果需要表格编辑功能，这里需要设置为true
    type: Boolean,
    default: false
  },
  allowEmpty: {
    // 表格数据为空时是否默认为--
    type: Boolean,
    default: true
  },
  defaultLoadPage: {
    // 传入了url，是否默认加载表格数据
    type: Boolean,
    default: true
  },
  loadKey: {
    // 是否自动从后台加载数据源
    type: Boolean,
    default: true
  },
  single: {
    type: Boolean, // 是否单选
    default: false
  },
  doubleEdit: {
    type: Boolean, // 是否双击启用编辑功能
    default: true
  },
  beginEdit: {
    // 编辑开始
    type: Function,
    default: function (row, column, index) {
      return true
    }
  },
  endEditBefore: {
    // 结束编辑前
    type: Function,
    default: function (row, column, index) {
      return true
    }
  },
  endEditAfter: {
    // 结束编辑前
    type: Function,
    default: function (row, column, index) {
      return true
    }
  },
  ck: {
    // 是否显示checkbox
    type: Boolean,
    default: true
  },
  columnIndex: {
    // 是否显示行号(2020..11.1)
    type: Boolean,
    default: true
  },
  highlightCurrentRow: {
    //增加选中行高亮显示(2022.10.07)
    type: Boolean,
    default: true
  },

  selectable: {
    type: Function,
    default: (row, index) => {
      return true
    }
  },
  spanMethod: {
    type: Function,
    default: ({ row, column, rowIndex, columnIndex }) => { }
  },
  lazy: {
    //树形表格是否默认延迟加载
    type: Boolean,
    default: true
  },
  defaultExpandAll: {
    //树形表格是否展开所有
    type: Boolean,
    default: false
  },
  expandRowKeys: {
    //默认展开行
    type: Array,
    default: () => {
      return []
    }
  },
  rowParentField: {
    //树形表格父级id
    type: String,
    default: ''
  },
  reserveSelection: {
    //分页或者刷新表格数据后是否保留复选框选择状态
    type: Boolean,
    default: true
  }
});

const fixed = ref(false); //是固定行号与checkbox
const clickEdit = ref(true);//置为点击行结束编辑
const randomTableKey = ref(1);
const key = ref('');
const enableEdit = ref(false); // 是否启表格用编辑功能
//const  defaultImg= new URL('@/assets/imgs/error-img.png', import.meta.url).href,
const loading = ref(false);
const formatConfig = ref({});
const colors = ref(['primary', 'success', 'info', 'warning', 'danger']);
const rule = ref({
  phone: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
  decimal: /(^[\-0-9][0-9]*(.[0-9]+)?)$/,
  number: /(^[\-0-9][0-9]*([0-9]+)?)$/
})
const rowData = ref([]);
const paginations = ref({
  sort: '',
  order: 'desc',
  Foots: '',
  total: 0,
  sizes: [30, 60, 100, 120],// 自定义分页条大小
  size: 30, // 默认分页大小
  Wheres: [],
  page: 1,
  rows: 30
})
const errorFiled = ref(''); //错误字段
const edit = ref({ columnIndex: -1, rowIndex: -1 }); // 当前双击编辑的行与列坐标
const summary = ref(false); // 是否显示合计
const summaryData = ref([]);// 目前只支持从后台返回的summaryData数据
const summaryIndex = ref({}); //合计列
const remoteColumns = ref([]);  // 需要每次刷新或分页后从后台加载字典数据源的列配置
const cellStyleColumns = ref([]); // 有背景颜色的配置
const fxRight = ref(false); // 是否有右边固定表头
const selectRows = ref([]);//当前选中的行
const isChrome = ref(false);
const useTag = ref(true);//app-table带数据源的单元格是否启用tag标签(下拉框等单元格以tag标签显示)
const currentRow = ref({});
const currentColumn = ref([]);
const fileInfo = ref([]);
const uploadUrl = ref('');
const uploadModel = ref(false);
const smallCell = ref(true);
watch(() => props.tableData.length, (newLen, oldLen) => {
  watchRowSelectChange(newLen, oldLen)
});
watch(() => rowData.value.length, (newLen, oldLen) => {
  watchRowSelectChange(newLen, oldLen)
});
const filterColumns = computed(() => {
  return props.columns.filter((x, index) => {
    if (!x.field) {
      x.field = x.title + index
    }
    return !x.hidden
  })
})
onMounted(() => {
  console.log('tableData', props.tableData)
  console.log('filterColumns', filterColumns.value)

  // try {
  //    useTag.value =  $global.table &&  $global.table.useTag
  //    smallCell.value =  $global.table &&  $global.table.smallCell
  // } catch (error) {
  //   console.log(error.message)
  // }

  fxRight.value = props.columns.some((x) => {
    return x.fixed == 'right'
  })
  //移除强制固定行号与checkbox列
  if (
    props.columns.some((x) => {
      return x.fixed && x.fixed != 'right'
    })
  ) {
    fixed.value = true
  }

  // 从后台加下拉框的[是否启用的]数据源
  let keys = []
  let columnBind = []
  summaryData.value.push('合计')
  if (props.columnIndex) {
    summaryData.value.push(' ')
  }
  initCellStyleColumns()
  props.columns.forEach((x, _index) => {
    if (!x.hidden) {
      // 修复求和列错位的问题
      summaryData.value.push('')
      summaryIndex.value[x.field] = summaryData.value.length - 1
    }
    // 求和
    if (x.summary && !summary.value) {
      summary.value = true
    }
    if (x.children && Array.isArray(x.children)) {
      x.children.forEach((cl) => {
        if (cl.bind && cl.bind.key && (!cl.bind.data || cl.bind.data.length == 0)) {
          keys.push(cl.bind.key)
          cl.bind.valueType = cl.type
          columnBind.push(cl.bind)
        }
      })
    } else if (x.bind && x.bind.key && (!x.bind.data || x.bind.data.length == 0)) {
      // 写入远程
      if (!x.bind.data) x.bind.data = []
      if (x.bind.remote) {
        remoteColumns.value.push(x)
      } else if (props.loadKey) {
        keys.push(x.bind.key)
        x.bind.valueType = x.type
        if (x.edit && x.edit.type) {
          x.bind.editType = x.edit.type
        }
        columnBind.push(x.bind)
      }
    }
  })
  if (keys.length > 0) {
    // http.post('/api/Sys_Dictionary/GetVueDictionary', keys).then((dic) => {
    //   dic.forEach((x) => {

    //     const arrType = ['cascader', 'treeSelect']
    //     columnBind.forEach((c) => {
    //       if (arrType.indexOf(c.valueType) != -1 || arrType.indexOf(c.editType) != -1) {
    //         props.columns.forEach((col) => {
    //           if (col.bind && col.bind.key == c.key) {
    //             col.bind.orginData = JSON.parse(JSON.stringify(x.data))
    //           }
    //         })
    //         x.data = base.value.convertTree(x.data, (node, data, isRoot) => {
    //           if (!node.inited) {
    //             node.inited = true
    //             node.label = node.value
    //             node.value = node.key + ''
    //           }
    //         })
    //       }
    //       // 转换数据源的类型与列的类型一致(2020.04.04)
    //       else if (c.key == x.dicNo && (c.valueType == 'int' || c.valueType == 'sbyte')) {
    //         x.data.forEach((d) => {
    //           // 2020.09.01增加对数字类型的二次判断
    //           if (!isNaN(d.key)) {
    //             d.key = ~~d.key
    //           }
    //         })
    //       }
    //       if (c.key == x.dicNo) c.data.push(...x.data)
    //     })
    //   })
    // })
  }

  paginations.value.sort = props.pagination.sortName
  // 2020.08.29增加自定义分页条大小
  Object.assign(paginations.value, props.pagination)
  if (props.pagination.size) {
    paginations.value.rows = props.pagination.size
  }
  enableEdit.value = props.columns.some((x) => {
    return x.hasOwnProperty('edit')
  })
  let keyColumn = props.columns.find((x) => {
    return x.isKey
  })
  if (keyColumn) {
    key.value = keyColumn.field
  }
  props.defaultLoadPage && load()

})

const initCellStyleColumns = () => {
  props.columns.forEach((x) => {
    if (x.cellStyle) {
      cellStyleColumns.value[x.field] = x.cellStyle
    }
  })
}
const watchRowSelectChange = (newLen, oldLen) => {
  if (!props.reserveSelection && newLen < oldLen && selectRows.value.length) {
    selectRows.value = []
    proxy.$refs.table.clearSelection()
  }
  if (!props.reserveSelection && isPageLoad.value) {
    isPageLoad.value = false
    return
  }
}
const switchChange = (val, row, column) => {
  //这里在初始化的时候也会触发change事件
  if (Object.keys(row).length <= 1) {
    return
  }
  if (column.onChange) {
    column.onChange(val, row, column)
  }
}
const inputKeyPress = (row, column, $event, $e) => {
  column.onKeyPress && column.onKeyPress(row, column, $event)
  getInputSummaries(null, null, $event, column)
}
const extraClick = (row, column) => {
  column.extra.click &&
    column.extra.click(row, column, props.url ? rowData.value : props.tableData)
}
const headerClick = (column, event) => {
  if (clickEdit.value && edit.value.rowIndex != -1) {
    if (
      rowEndEdit(
        props.url ? rowData.value[edit.value.rowIndex] : props.tableData[edit.value.rowIndex],
        column
      )
    ) {
      edit.value.rowIndex = -1
    }
  }
}
//双击行事件
const rowDbClick = (row, column, event) => {
  emit('rowDbClick', { row, column, event })
}
//点击时表格参数判断
const rowClick = (row, column, event) => {
  if (!column) {
    return
  }
  //正在编辑时，禁止触发rowClick事件
  if (edit.value.rowIndex == -1) {
    emit('rowClick', { row, column, event })
  }
  // 点击行事件
  if (!props.doubleEdit) {
    return
  }
  // 点击其他行时，如果点击的行与正在编辑的行相同，保持编辑状态
  if (clickEdit.value && edit.value.rowIndex != -1) {
    if (row.elementIndex == edit.value.rowIndex) {
      // 点击的单元格如果不可以编辑，直接结束编辑
      let _col = props.columns.find((x) => {
        return x.field == ((event && event.property) || column.property)
      })
      if (_col && (!_col.edit || _col.readonly)) {
        if (rowEndEdit(row, event)) {
          edit.value.rowIndex = -1
        }
      }
      return
    }
    if (rowEndEdit(row, event && event.property ? event : column)) {
      edit.value.rowIndex = -1
    }
    //当正在编辑，且点击到其他行时，在原编辑的行结束编辑后，触发新行的rowClick事件
    //正在编辑时，禁止出发rowClick事件
    if (edit.value.rowIndex == -1) {
      emit('rowClick', { row, column, event })
    }
  }
  rowBeginEdit(row, column)
}
const dowloadFile = (file) => {
  // base.value.dowloadFile(
  //   file.path,
  //   file.name,
  //   {
  //     Authorization: $store.getters.getToken()
  //   },
  // http.ipAddress
  // )
}
const getFilePath = (pathSring, column) => {
  // 获取表的图片与文件显示
  if (!pathSring) return []
  // 增加图片自定义操作
  // 返回格式必须是[{name:"文件名",path:"图片全路径或base64格式"}]
  if (column.formatter) {
    return column.formatter(pathSring)
  }
  let filePath
  if (column.base64 && pathSring.indexOf('data') != -1) {
    filePath = (',' + pathSring)
      .split(',data')
      .filter((x) => {
        return x
      })
      .map((m) => {
        return 'data' + m
      })
  } else {
    filePath = pathSring.replace(/\\/g, '/').split(',')
  }

  let fileInfo = []
  for (let index = 0; index < filePath.length; index++) {
    let file = filePath[index]
    // 2020.12.19增加base64图片显示
    if (column.base64) {
      fileInfo.push({
        name: '',
        path: (file.indexOf('data') == -1 ? 'data:image/png;base64,' : '') + file
      })
    } else if (file.indexOf('.') != -1) {
      let splitFile = file.split('/')
      if (splitFile.length > 0) {
        fileInfo.push({
          name: splitFile[splitFile.length - 1],
          // path: base.value.isUrl(file) ? file : http.ipAddress + file
          path: file
        })
      }
    }
  }
  return fileInfo
}

const getHeight = () => {
  // 没有定义高度与最大高度，使用table默认值
  if (!props.height && !props.maxHeight) {
    return null
  }
  // 定义了最大高度则不使用高度
  if (props.maxHeight) {
    return null
  }
  // 使用当前定义的高度
  return props.height
}
const getMaxHeight = () => {
  // 没有定义高度与最大高度，使用table默认值
  if (!props.height && !props.maxHeight) {
    return null
  }
  // 定义了最大高度使用最大高度
  if (props.maxHeight) {
    return props.maxHeight
  }
  // 不使用最大高度
  return null
}

const formatterClick = (row, column, event) => {
  if (column.click) {
    column.click(row, column, event)
    event.stopPropagation && event.stopPropagation()
  } else {
    rowClick(row, column, event)
  }
}
const initIndex = ({ row, rowIndex }) => {
  if (props.index) {
    row.elementIndex = rowIndex
  }

  return
}

const rowBeginEdit = (row, column) => {
  if (edit.value.rowIndex != -1) {
    return
  }
  let _row = props.columns.find((x) => x.field == column.property)
  if (_row) {
    if (_row.readonly) {
      return
    }
    if (
      //不能编辑的字段、switch，点击不开启启编辑功能
      !_row.edit ||
      (_row.edit.keep && _row.edit.type == 'switch')
    ) {
      return
    }
  }
  if (!enableEdit.value) return
  _errMsg.value = ''
  // 编辑前
  props.columns
    .filter((x) => {
      return x.bind && x.bind.data && x.bind.data.length
    })
    .forEach((column) => {
      let val = row[column.field]
      if (typeof column.bind.data[0].key == 'string') {
        if (typeof val == 'number') {
          row[column.field] = row[column.field] + ''
        }
      } else {
        //多选或者级联编辑回写,2023.01.06
        if (Array.isArray(val)) {
          val = val.map((v) => {
            return v * 1
          })
          row[column.field] = val
        } else if (typeof val == 'string' && val) {
          let _val = val * 1
          if (_val + '' === val) {
            row[column.field] = _val
          }
        }
      }
    })
  if (!props.beginEdit(row, column, row.elementIndex)) return
  if (row.hasOwnProperty('elementIndex')) {
    if (edit.value.rowIndex == row.elementIndex) {
      return
    }
    edit.value.rowIndex = row.elementIndex
  }
  let col = props.columns.find((x) => {
    return x.field == (column.field || column.property)
  })
  if (col && col.edit) {
    nextTick(() => {
      let refEl = proxy.$refs[(column.field || column.property) + row.elementIndex]
      if (refEl && Array.isArray(refEl)) {
        refEl[0].focus && refEl[0].focus()
      }

    });

  }
}
const rowEndEdit = (row, column, event) => {
  if (clickEdit.value && event) {
    return true
  }
  if (!enableEdit.value) {
    if (!errorFiled.value) {
      if (edit.value.rowIndex != -1 && !props.endEditAfter(row, column, edit.value.rowIndex)) {
        return false
      }
      edit.value.rowIndex = -1
    }
    return true
  }
  if (!props.doubleEdit && event) {
    return true
  }
  let _row = props.url ? rowData.value[edit.value.rowIndex] : props.tableData[edit.value.rowIndex]
  // 结束编辑前
  if (!props.endEditBefore(_row, column, edit.value.rowIndex)) return false
  if (edit.value.rowIndex != -1) {
    //2022.06.26修复表格内容切换后行数不一致时不能编辑的问题
    if (edit.value.rowIndex - 1 > (rowData.value || props.tableData).length) {
      edit.value.rowIndex = -1
      return
    }
    let row = (props.url ? rowData.value : props.tableData)[edit.value.rowIndex]
    for (let index = 0; index < props.columns.length; index++) {
      const _column = props.columns[index]
      if (_column.edit) {
        if (!validateRow(row, _column)) {
          return
        }
      }
    }
  }
  if (!props.endEditAfter(_row, column, edit.value.rowIndex)) return false
  edit.value.rowIndex = -1
  return true
}
const validateRow = (row, option1) => {
  if (!validateColum(option1, row)) {
    errorFiled.value = option1.field
    //修改错误信息重复的问题
    ElMessage.error(option1.title + _errMsg.value)
    return false
  }
  errorFiled.value = ''
  return true
}
const validateColum = (option, data) => {
  if (option.hidden || option.bind || !data) return true
  let val = data[option.field]
  if (option.require || option.required) {
    if (val != '0' && (val === '' || val === undefined)) {
      if (!errorFiled.value) {
        _errMsg.value = '不能为空'
      }
      return false
    }
  }
  if (!option.edit) {
    return true
  }
  let editType = option.edit.type
  // 验证数字
  if (editType == 'int' || editType == 'decimal' || editType == 'number') {
    if (val == '' || val == undefined) return true
    if (editType == 'decimal') {
      if (!rule.value.decimal.test(val)) {
        _errMsg.value = '只能是数字'
        return false
      }
    } else if (!rule.value.decimal.test(val)) {
      _errMsg.value = '只能是数字'
      return false
    }
    if (
      option.edit.min != undefined &&
      typeof option.edit.min === 'number' &&
      val < option.edit.min
    ) {
      _errMsg.value = '不能小于' + option.edit.min
      return false
    }
    if (
      option.edit.max != undefined &&
      typeof option.edit.max === 'number' &&
      val > option.edit.max
    ) {
      _errMsg.value = '不能大于' + option.edit.max
      return false
    }
    return true
  }

  // 验证字符串
  if (val && (editType == 'text' || editType == 'string')) {
    if (
      option.edit.min != undefined &&
      typeof option.edit.min === 'number' &&
      val.length < option.edit.min
    ) {
      _errMsg.value = '至少' + option.edit.min + '个字符'
      return false
    }
    if (
      option.edit.max != undefined &&
      typeof option.edit.max === 'number' &&
      val.length > option.edit.max
    ) {
      _errMsg.value = '最多' + option.edit.max + '个字符'
      return false
    }
  }
  return true
}
const delRow = () => {
  let rows = getSelected()
  if (rows.length == 0) return ElMessage.error('请选择要删除的行!')

  let data = props.url ? rowData.value : props.tableData
  let indexArr = getSelectedIndex()
  if (indexArr.length == 0) {
    return ElMessage.error("删除操作必须设置AppTable的属性index='true'")
  }
  // if (indexArr.length == 0 || ! key.value) {
  //   return  ElMessage.error(
  //     "请设置index=true属性或指columns的字段为key"
  //   );
  // }
  if (indexArr.length == 0) {
    // let keyValues=[]
    // rows.forEach(x=>{
    //   if (x[ key.value]) {
    //   }
    //   keyValues.push(x[ key.value])
    // })
    // data.find(x=>)
  } else {
    for (let i = data.length - 1; i >= 0; i--) {
      if (indexArr.indexOf(i) != -1) {
        data.splice(i, 1)
      }
    }
  }
  edit.value.rowIndex = -1
  return rows
}

const viewImg = (row, column, url, $event, index) => {
  $event.stopPropagation()
  const imgs = getFilePath(row[column.field], column).map((x) => {
    return x.path
  })
  proxy.$refs.viewer.show(imgs, index)
  // base.value.previewImg(url);
  // window.open(row[column.field]);
}
const link = (row, column, $e) => {
  $e.stopPropagation()
  props.linkView(row, column)
}
const getSelected = () => {
  return selectRows.value
}
const getSelectedIndex = () => {
  if (!props.index) {
    // 只有设置了属性index才有索引行
    return []
  }
  let indexArr = selectRows.value.map((x) => {
    return x.elementIndex
  })
  return indexArr || []
}
const GetTableDictionary = (rows) => {
  // 分页或刷新或重新绑定数据源
  if (remoteColumns.value.length == 0 || !rows || rows.length == 0) return
  let remoteInfo = {}
  for (let index = 0; index < remoteColumns.value.length; index++) {
    const column = remoteColumns.value[index]
    //  column.bind.data.splice(0);
    let key = column.bind.key
    let data = []
    rows.forEach((row) => {
      if (row[column.field] || row[column.field] == '0') {
        if (data.indexOf(row[column.field]) == -1) {
          data.push(row[column.field])
        }
      }
    })
    if (data.length > 0) {
      remoteInfo[key] = data
    }
  }
  if (remoteInfo.length == 0) return
  // http.post('/api/Sys_Dictionary/GetTableDictionary', remoteInfo).then((dic) => {
  //   dic.forEach((x) => {
  //     remoteColumns.value.forEach((column) => {
  //       if (column.bind.key == x.key) {
  //         column.bind.data = Object.assign([], column.bind.data, x.data)
  //         // column.bind.data.push(...x.data);
  //       }
  //     })
  //   })
  //    emit('dicInited', dic)
  // })
}
const load = (query, isResetPage) => {
  // isResetPage重置分页数据
  if (!props.url) return
  if (isResetPage) {
    resetPage()
  }
  let param = {
    page: paginations.value.page,
    rows: props.paginationHide ? 1000 : paginations.value.rows,
    sort: paginations.value.sort,
    order: paginations.value.order,
    wheres: [] // 查询条件，格式为[{ name: "字段", value: "xx" }]
  }
  let status = true
  // 合并查询信息(包查询分页、排序、查询条件等)
  if (query) {
    param = Object.assign(param, query)
  }
  /* 查询前处理(如果需要查询条件，实现组件方法loadBefore方法即可:
    loadBefore=(param, callBack)=>{
      param.wheres = [{ name: "PhoneNo", value: "13419098211" }];
      callBack(true);
    })
  */
  emit('loadBefore', param, (result) => {
    status = result
  })
  if (!status) return

  if (param.wheres && param.wheres instanceof Array) {
    param.wheres = JSON.stringify(param.wheres)
  }
  loading.value = true
  let url = param.url || props.url
  param.url = undefined
  // http.post(url, param).then(
  //   (data) => {
  //     //2021.06.04修复tree不刷新的问题
  //     if (props.rowKey) {
  //       randomTableKey.value++
  //       rowData.value.splice(0)
  //     }
  //     loading.value = false
  //     let rows = data.rows || []
  //     // 查询返回结果后处理
  //     // 2020.10.30增加查询后返回所有的查询信息
  //      emit(
  //       'loadAfter',
  //       rows,
  //       (result) => {
  //         status = result
  //         rows = data.rows;
  //       },
  //       data
  //     )
  //     if (!status) return
  //     GetTableDictionary(data.rows)

  //     if (props.rowParentField) {
  //       rows = base.value.convertTree(rows, null, props.rowKey, props.rowParentField)
  //     }
  //     rowData.value = rows
  //     paginations.value.total = data.total
  //     // 合计
  //     getSummaries(data)
  //     //设置分页后记录默认选中行2024.09.10
  //     if (props.reserveSelection && props.rowKey) {
  //       //  isPageLoad = false;
  //       nextTick(() => {
  //         isPageLoad.value = true
  //         rows.forEach((row) => {
  //           if (
  //             selectRows.value.some((c) => {
  //               return c[props.rowKey] === row[props.rowKey]
  //             })
  //           ) {
  //             proxy.$refs.table.toggleRowSelection(row)
  //           }
  //         })
  //         isPageLoad.value = false
  //       })
  //     }
  //   },
  //   (error) => {
  //     loading.value = false
  //     //  ElMessage.error(error || "网络异常");
  //   }
  // )
}
// 获取统计
const getSummaries = (data) => {
  if (!summary.value || !data.summary) return
  summaryData.value.splice(0)
  // 开启了行号的，+1
  if (props.columnIndex) {
    summaryData.value.push('')
  }
  // 如果有checkbox，应该算作是第一行
  if (props.ck) {
    summaryData.value.push('')
  }

  props.columns.forEach((col) => {
    if (col.children && col.children.length) {
      col.children.forEach((item) => {
        getColumnSummaries(item, data)
      })
    } else {
      getColumnSummaries(col, data)
    }
  })
  if (summaryData.value.length > 0 && summaryData.value[0] == '') {
    summaryData.value[0] = '合计'
  }
}
const getColumnSummaries = (col, data) => {
  if (!col.hidden) {
    if (data.summary.hasOwnProperty(col.field)) {
      let sum = data.summary[col.field]
      //2024.01.07增加自定义合计格式化
      if (col.summaryFormatter) {
        sum = col.summaryFormatter(sum, col, data, summaryData.value)
      } else if (sum) {
        sum = (sum * 1.0).toFixed(col.numberLength || 2).replace('.00', '') * 1.0
      }
      summaryData.value.push(sum)
    } else {
      summaryData.value.push('')
    }
  }
}
const handleSizeChange = (val) => {
  paginations.value.size = val
  paginations.value.rows = val
  load()
}
const handleCurrentChange = (val) => {
  paginations.value.page = val
  load()
}
const sortChange = (sort) => {
  paginations.value.sort = sort.prop
  paginations.value.order = sort.order == 'ascending' ? 'asc' : 'desc'
  load()
}
const resetPage = () => {
  // 重置查询分页
  //  paginations.value.rows = 30;
  paginations.value.page = 1
}
const selectionChange = (selection) => {
  // // console.log(selection);
  // // 选择行事件,只有单选才触发
  //  selectRows.value = selection;
  // if (props.single) {
  //   if (selection.length == 1) {
  //       emit('rowChange', selection[0]);
  //   }
  //   if (selection.length > 1) {
  //     let _row = selection[selection.length - 1];
  //     proxy.$refs.table.toggleRowSelection(selection[0]);
  //      selectRows.value = [_row];
  //   }
  // }
  // // 将selectionchange暴露出去
  //   emit('selectionChange', selection);

  // console.log(selection);
  // 选择行事件,只有单选才触发
  if (props.reserveSelection && props.rowKey) {
    //分页或者刷新时不重置选中的数据
    if (!isPageLoad.value) {
      //获取当前页选中的行数据并且未添加的数据
      const selectNewRows = selection.filter((c) => {
        return !selectRows.value.some((x) => {
          return x[props.rowKey] === c[props.rowKey]
        })
      })
      //当前面新添加的数据
      selectRows.value.push(...selectNewRows)
      //获取当前页移除(未选中)的数据
      let unselectIds = (props.url ? rowData.value : props.tableData)
        .filter((x) => {
          return !selection.some((c) => {
            return x[props.rowKey] === c[props.rowKey]
          })
        })
        .map((c) => {
          return c[props.rowKey]
        })
      selectRows.value = selectRows.value.filter((c) => {
        return !unselectIds.some((x) => x === c[props.rowKey])
      })
      //  console.log( selectRows.value);
    }
  } else {
    selectRows.value = selection
  }
  if (props.single) {
    if (selection.length == 1) {
      emit('rowChange', selection[0])
    }
    if (selection.length > 1) {
      let _row = selection[selection.length - 1]
      proxy.$refs.table.toggleRowSelection(selection[0])
      selectRows.value = [_row]
    }
  } else {
    emit('rowChange', selection)
  }
  emit('selectionChange', selection)
}
const getColor = (row, column) => {
  let val = row[column.field]
  if (column.getColor && typeof column.getColor === 'function') {
    let _color = column.getColor(row, column)
    if (_color) {
      return _color
    }
  }
  if (!val && val != '0') {
    return undefined
  }
  if (!formatConfig.value[column.field]) {
    formatConfig.value[column.field] = [val]
    return colors.value[0]
  }
  let index = formatConfig.value[column.field].indexOf(val)
  if (index != -1) {
    return colors.value[index]
  }
  if (formatConfig.value[column.field].length > 5) {
    return ''
  }

  if (index == -1) {
    formatConfig.value[column.field].push(val)
    index = formatConfig.value[column.field].length - 1
  }
  return colors.value[index] || 'info'
}
const formatterDate = (row, column) => {
  return (row[column.field] || '').substr(0, 10)
}
const formatter = (row, column, template) => {
  if (!template) return row[column.property]
  let val = row[column.field]
  if (!val && val != 0) return val
  // 是否值
  if (column.edit && column.edit.type == 'switch') {
    return val ? '是' : '否'
  }
  if (!column.bind || !column.bind.data) {
    return row[column.field]
  }

  if (
    column.edit &&
    (column.edit.type == 'selectList' ||
      column.edit.type == 'treeSelect' ||
      column.bind.type == 'cascader' ||
      column.bind.type == 'treeSelect')
  ) {
    if (!Array.isArray(val)) {
      row[column.field] = (val + '').split(',')
    } else {
      val = val.join(',')
    }
    return getSelectFormatter(column, val)
  }
  // 编辑多选table显示
  //  if (
  //   column.bind.type == "selectList" ||
  //   column.bind.type == "checkbox" ||
  //   column.bind.type == "cascader" ||
  //   column.bind.type == "treeSelect"
  // ) {
  if (typeof val === 'string' && val.indexOf(',') != -1) {
    return getSelectFormatter(column, val)
  }
  //}
  let source = column.bind.data.filter((x) => {
    // return x.key != "" && x.key == val;
    // 2020.06.06修复单独使用table组件时,key为数字0时转换成文本失败的问题
    return x.key !== '' && x.key !== undefined && x.key + '' === val + ''
  })
  if (source && source.length > 0) val = source[0].label || source[0].value
  return val
}
const getSelectFormatter = (column, val) => {
  // 编辑多选table显示
  let valArr = (val + '').split(',')
  for (let index = 0; index < valArr.length; index++) {
    ; (column.bind.orginData && column.bind.orginData.length
      ? column.bind.orginData
      : column.bind.data
    ).forEach((x) => {
      // 2020.06.06修复数据源为selectList时,key为数字0时不能转换文本的问题
      if (x.key !== '' && x.key !== undefined && x.key + '' == valArr[index] + '') {
        valArr[index] = x.label || x.value
      }
    })
  }
  return valArr.join(',')
}
const onChange = (scope, val, event, column) => {
  // 2020.09.03修复onChange不触发的问题
  let row = scope.row
  if (column.onChange && !column.onChange(row, val, event)) {
    return
  }
  // 输入框求和实时计算
  getInputSummaries(scope, val, event, column)
}
// input输入实时求和
const getInputSummaries = (scope, val, event, column) => {
  // column列设置了summary属性的才计算值
  if (!column.summary) return
  let sum = 0
    //  let _index = 0;
    ; (props.url ? rowData.value : props.tableData).forEach((x, index) => {
      if (x.hasOwnProperty(column.field) && !isNaN(x[column.field])) {
        // _index = index;
        sum += x[column.field] * 1
      }
    })
  if (sum) {
    if (column.summary == 'avg') {
      sum = sum / (rowData.value.length || props.tableData.length || 1)
    }
    sum = (sum * 1.0).toFixed(column.numberLength || 2).replace('.00', '') * 1.0
  }
  summaryData.value[summaryIndex.value[column.field]] = sum
}
const getSummaryData = ({ columns, data }) => {
  return summaryData.value
}
const getCellStyle = (row) => {
  // 2020.12.13增加设置单元格颜色
  if (row.column.property) {
    return (
      cellStyleColumns.value[row.column.property] &&
      cellStyleColumns.value[row.column.property](row.row, row.rowIndex, row.columnIndex)
    )
  }
}
const compareDate = (date1, date2) => {
  if (!date2) {
    return true
  }
  return date1.valueOf() < (typeof date2 == 'number' ? date2 : new Date(date2).valueOf())
}
const getDateOptions = (date, item) => {
  //2021.07.17设置时间可选范围
  if ((!item.min && !item.max) || !date) {
    return false
  }
  if (item.min && item.min.indexOf(' ') == -1) {
    //不设置时分秒，后面会自动加上 08:00
    item.min = item.min + ' 00:00:000'
  }
  return compareDate(date, item.min) || !compareDate(date, item.max)
}
const getDateFormat = (column) => {
  if (column.format) {
    return column.format
  }
  if (column.edit.type == 'month') {
    return 'YYYY-MM'
  }
  //见https://day.js.org/docs/zh-CN/display/format
  return column.edit.type == 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'
}
const userSelect = (selection, row) => {
  //  selectRows.value = selection;
  // if (!props.single) {
  //     emit('rowChange', { row, selection });
  // }
}
const isEmptyTag = (row, column) => {
  if (!row[column.field] && row[column.field] != '0') {
    return 'empty-tag'
  }
  return ''
}
const filterChildrenColumn = (children) => {
  if (!children) {
    return []
  }
  return children.filter((x) => {
    return !x.hidden
  })
}
const initColumnDisabled = (row, column) => {
  return column.getDisabled && column.getDisabled(row, column)
}
const showUpload = (row, column) => {
  fileInfo.value = (row[column.field] || '')
    .split(',')
    .filter((x) => {
      return x
    })
    .map((item) => {
      return { path: item, name: '' }
    })
  currentRow.value = row
  currentColumn.value = column
  if (currentColumn.value.edit.autoUpload === undefined) {
    currentColumn.value.edit.autoUpload = true
  }
  if (currentColumn.value.edit.multiple === undefined) {
    currentColumn.value.edit.multiple = false
  }

  if (currentColumn.value.edit.url === undefined) {
    uploadUrl.value = 'api/' + (props.url || '').replace('/api', 'api').split('/')[1] + '/upload'
  } else {
    uploadUrl.value = currentColumn.value.edit.url
  }
  uploadModel.value = true
}
const uploadAfter = (result, files) => {
  currentColumn.value.uploadAfter && currentColumn.value.uploadAfter(result, files)
  return true
}
const uploadBefore = (files, params) => {
  currentColumn.value.uploadBefore &&
    currentColumn.value.uploadBefore(files, currentRow.value, params)
  return true
}
const saveUpload = () => {
  //生成保存后返回的路径
  let arr = fileInfo.value.map((x) => {
    if (x.path) {
      return x.path
    }
    return result.data + x.name
  })

  currentRow.value[currentColumn.value.field] = arr.join(',')
  uploadModel.value = false
  return true
}
const expandChange = (row, expandedRows) => {
  //	当用户对某一行展开或者关闭的时
  if (!props.defaultExpandAll && !props.lazy) {
    if (expandedRows) {
      if (props.expandRowKeys.indexOf(row[props.rowKey]) == -1) {
        props.expandRowKeys.push(row[props.rowKey])
      }
    } else {
      let _index = props.expandRowKeys.findIndex((x) => {
        return x == row[props.rowKey]
      })
      if (_index != -1) {
        props.expandRowKeys.splice(_index, 1)
      }
    }
  }
}
const handleImageError = ($e) => {
  // $e.target.src =  defaultImg.value
}
const cellSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  return props.spanMethod(
    { row, column, rowIndex, columnIndex },
    props.url ? rowData.value : props.tableData
  )
}
// }
// })

// 使用 defineExpose 来暴露方法给父组件
defineExpose({
  delRow,
});
</script>
<template>

  <div class="app-table"
    :class="[textInline ? 'text-inline' : '', fxRight ? 'fx-right' : '', isChrome ? 'chrome' : '', smallCell ? 'small-table' : '']">
    <div class="mask" v-show="loading"></div>
    <div class="message" v-show="loading">加载中.....</div>

    <el-table :show-summary="summary" :summary-method="getSummaryData" :row-key="rowKey" :key="randomTableKey"
      :lazy="lazy" :defaultExpandAll="defaultExpandAll" :expand-row-keys="rowKey ? expandRowKeys : undefined" stripe
      :load="loadTreeChildren" @select="userSelect" @select-all="userSelect" @selection-change="selectionChange"
      @row-dblclick="rowDbClick" @row-click="rowClick" @header-click="headerClick"
      :highlight-current-row="highlightCurrentRow" ref="table" class="v-table" @sort-change="sortChange"
      tooltip-effect="dark" :height="getHeight()" :max-height="getMaxHeight()" :data="url ? rowData : tableData" border
      :row-class-name="initIndex" :cell-style="getCellStyle" style="width: 100%" :scrollbar-always-on="true"
      :span-method="cellSpanMethod" @expand-change="expandChange">
      <el-table-column v-if="columnIndex" type="index" :fixed="fixed" width="55"></el-table-column>
      <el-table-column v-if="ck" type="selection" :fixed="fixed" :selectable="selectable" width="55"></el-table-column>

      <!-- 2020.10.10移除table第一行强制排序 -->
      <el-table-column v-for="(column, cindex) in filterColumns" :prop="column.field" :label="column.title"
        :min-width="column.width" :formatter="formatter" :fixed="column.fixed" :key="column.field + cindex"
        :align="column.align" :sortable="column.sort ? 'custom' : false"
        :show-overflow-tooltip="column.showOverflowTooltip">
        <template #header>
          <span v-if="(column.require || column.required) && column.edit" class="column-required">*</span>
          {{ column.title }}
        </template>

        <template #default="scope">
          <!-- 2022.01.08增加多表头，现在只支持常用功能渲染，不支持编辑功能(涉及到组件重写) -->
          <el-table-column style="border: none" v-for="columnChildren in filterChildrenColumn(column.children)"
            :key="columnChildren.field" :min-width="columnChildren.width" :class-name="columnChildren.class"
            :prop="columnChildren.field" :align="columnChildren.align" :label="columnChildren.title">
            <template #default="scopeChildren">
              <a href="javascript:void(0);" style="text-decoration: none"
                @click="link(scopeChildren.row, columnChildren, $event)" v-if="columnChildren.link"
                v-text="scopeChildren.row[columnChildren.field]"></a>
              <app-table-render v-else-if="columnChildren.render && typeof columnChildren.render == 'function'"
                :row="scopeChildren.row" key="rd-01" :index="scope.$index" :column="columnChildren"
                :render="columnChildren.render"></app-table-render>
              <div v-else-if="columnChildren.formatter" @click="
                columnChildren.click &&
                columnChildren.click(scopeChildren.row, columnChildren, scopeChildren.$index)
                " v-html="columnChildren.formatter(scopeChildren.row, columnChildren, scopeChildren.$index)
                  "></div>
              <div v-else-if="columnChildren.bind">
                {{ formatter(scopeChildren.row, columnChildren, true) }}
              </div>
              <span v-else-if="columnChildren.type == 'date'">
                {{ formatterDate(scopeChildren.row, columnChildren) }}
              </span>
              <template v-else>
                {{ scopeChildren.row[columnChildren.field] }}
              </template>
            </template>
          </el-table-column>
          <!-- 2020.06.18增加render渲染自定义内容 -->
          <app-table-render v-if="column.render && typeof column.render == 'function'" :row="scope.row" key="rd-01"
            :index="scope.$index" :column="column" :render="column.render"></app-table-render>
          <!-- 启用双击编辑功能，带编辑功能的不会渲染下拉框文本背景颜色 -->
          <!-- @click="rowBeginEdit(scope.$index,cindex)" -->
          <!-- 2021.09.21增加编辑时对readonly属性判断 -->
          <template
            v-else-if="column.edit && !column.readonly && ['file', 'img', 'excel'].indexOf(column.edit.type) != -1">
            <div style="display: flex; align-items: center" @click.stop>
              <i v-if="!column.showUpload || column.showUpload(scope.row, column)"
                style="padding: 3px; margin-right: 10px; color: #8f9293; cursor: pointer"
                @click="showUpload(scope.row, column)" class="el-icon-upload"></i>
              <template v-if="column.edit.type == 'img'">
                <img v-for="(file, imgIndex) in getFilePath(scope.row[column.field], column)" :key="imgIndex"
                  @error="handleImageError" @click="viewImg(scope.row, column, file.path, $event, imgIndex)"
                  class="table-img" :src="file.path" />
              </template>
              <a style="margin-right: 8px" v-else class="t-file"
                v-for="(file, fIndex) in getFilePath(scope.row[column.field], column)" :key="fIndex"
                @click="dowloadFile(file)">
                {{ file.name }}</a>
            </div>
          </template>
          <div class="edit-el"
            v-else-if="column.edit && !column.readonly && (column.edit.keep || edit.rowIndex == scope.$index)">
            <div @click.stop class="e-item">
              <div>
                <el-date-picker clearable size="default" style="width: 100%"
                  v-if="['date', 'datetime'].indexOf(column.edit.type) != -1" v-model="scope.row[column.field]"
                  @change="(val) => { column.onChange && column.onChange(scope.row, column, val) }"
                  :type="column.edit.type" :placeholder="column.placeholder || column.title"
                  :disabledDate="(val) => getDateOptions(val, column)" :value-format="getDateFormat(column)"
                  :disabled="initColumnDisabled(scope.row, column)" :ref="column.field + scope.$index">
                </el-date-picker>
                <el-time-picker clearable size="default" style="width: 100%" v-else-if="column.edit.type == 'time'"
                  v-model="scope.row[column.field]" :disabled="initColumnDisabled(scope.row, column)"
                  @change="(val) => { column.onChange && column.onChange(scope.row, column, val) }"
                  :placeholder="column.placeholder || column.title" :value-format="column.format || 'HH:mm:ss'">
                </el-time-picker>
                <el-switch v-else-if="column.edit.type == 'switch'" v-model="scope.row[column.field]"
                  active-color="#0f84ff" inactive-color="rgb(194 194 194)" active-text="是" inactive-text="否"
                  inline-prompt @change="(val) => { switchChange(val, scope.row, column) }"
                  :active-value="typeof scope.row[column.field] == 'boolean' ? true : typeof scope.row[column.field] == 'string' ? '1' : 1"
                  :inactive-value="typeof scope.row[column.field] == 'boolean' ? false : typeof scope.row[column.field] == 'string' ? '0' : 0"
                  :disabled="initColumnDisabled(scope.row, column)">
                </el-switch>
                <template v-else-if="['select', 'selectList'].indexOf(column.edit.type) != -1">
                  <el-select size="default" style="width: 100%" v-model="scope.row[column.field]"
                    :filterable="column.filter || column.bind.data.length > 10 ? true : false"
                    :multiple="column.edit.type == 'select' ? false : true"
                    :placeholder="column.placeholder || column.title" :allow-create="column.autocomplete"
                    @change="column.onChange && column.onChange(scope.row, column)" clearable
                    :ref="column.field + scope.$index" :disabled="initColumnDisabled(scope.row, column)">
                    <el-option v-for="item in column.bind.data" :key="item.value" v-show="!item.hidden"
                      :disabled="item.disabled" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
                <!-- <el-tree-select style="width: 100%"
                  v-else-if="column.edit.type == 'treeSelect' || column.edit.type == 'cascader'"
                  v-model="scope.row[column.field]" :data="column.bind.data"
                  :multiple="column.multiple === undefined ? true : column.multiple" :render-after-expand="false"
                  :show-checkbox="true"
                  :check-strictly="column.checkStrictly === undefined ? true : column.checkStrictly" check-on-click-node
                  node-key="key" @change="column.onChange && column.onChange(scope.row, column)"
                  :props="{ label: 'label' }" :ref="column.field + scope.$index">
                  <template #default="{ data, node }"> {{ data.label }}</template>
                </el-tree-select> -->

                <el-input v-else-if="column.edit.type == 'textarea'" type="textarea"
                  :placeholder="column.placeholder || column.title" v-model="scope.row[column.field]"
                  :disabled="initColumnDisabled(scope.row, column)" :ref="column.field + scope.$index">
                </el-input>
                <el-input v-else-if="!column.summary && !column.onKeyPress" v-model.lazy="scope.row[column.field]"
                  :placeholder="column.placeholder || column.title" :disabled="initColumnDisabled(scope.row, column)"
                  :ref="column.field + scope.$index" />
                <el-input v-else @change="inputKeyPress(scope.row, column, $event)"
                  @input="inputKeyPress(scope.row, column, $event)"
                  @keyup.enter="inputKeyPress(scope.row, column, $event)" size="default"
                  v-model="scope.row[column.field]" :ref="column.field + scope.$index"
                  :placeholder="column.placeholder || column.title"
                  :disabled="initColumnDisabled(scope.row, column)"></el-input>
              </div>
              <div class="extra" v-if="column.extra && edit.rowIndex == scope.$index">
                <a :style="column.extra.style" style="text-decoration: none" @click="extraClick(scope.row, column)">
                  <i v-if="column.extra.icon" :class="[column.extra.icon]" />
                  {{ column.extra.text }}
                </a>
              </div>
            </div>
          </div>
          <!--没有编辑功能的直接渲染标签-->
          <template v-else>
            <a href="javascript:void(0)" style="text-decoration: none; line-height: 1.3"
              @click="link(scope.row, column, $event)" v-if="column.link" v-text="scope.row[column.field]"></a>
            <img v-else-if="column.type == 'img'"
              v-for="(file, imgIndex) in getFilePath(scope.row[column.field], column)" :key="imgIndex"
              @error="handleImageError" @click="viewImg(scope.row, column, file.path, $event, imgIndex)"
              class="table-img" :src="file.path" />
            <a style="margin-right: 8px" v-else-if="column.type == 'file' || column.type == 'excel'" class="t-file"
              v-for="(file, fIndex) in getFilePath(scope.row[column.field], column)" :key="fIndex"
              @click="dowloadFile(file)">{{ file.name }}</a>
            <span v-else-if="column.type == 'date'">{{ formatterDate(scope.row, column) }}</span>
            <div v-else-if="column.formatter" @click="formatterClick(scope.row, column, $event)"
              v-html="column.formatter(scope.row, column)"></div>
            <!-- 2021.11.18修复table数据源设置为normal后点击行$event缺失的问题 -->
            <div v-else-if="column.bind && (column.normal || column.edit)"
              @click="formatterClick(scope.row, column, $event)"
              :style="column.getStyle && column.getStyle(scope.row, column)">
              {{ formatter(scope.row, column, true) }}
            </div>
            <div v-else-if="column.click && !column.bind" @click="formatterClick(scope.row, column)">
              {{ scope.row[column.field] }}
            </div>
            <div v-else-if="column.bind" @click="() => { column.click && formatterClick(scope.row, column) }">
              <el-tag v-if="useTag" class="cell-tag" :class="[isEmptyTag(scope.row, column)]"
                :type="getColor(scope.row, column)" :effect="column.effect">
                {{ formatter(scope.row, column, true) }}
              </el-tag>
              <template v-else>{{ formatter(scope.row, column, true) }}</template>
            </div>

            <span v-else>{{ formatter(scope.row, column, true) }}</span>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <template v-if="!paginationHide">
      <div class="block pagination" key="pagination-01" style="display: flex">
        <div style="flex: 1"></div>
        <el-pagination key="pagination-02" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="paginations.page" :page-sizes="paginations.sizes" :page-size="paginations.size"
          layout="total, sizes, prev, pager, next, jumper" :total="paginations.total"></el-pagination>
      </div>
    </template>
  </div>

  <app-box v-model="uploadModel" title="上传" :height="228" :width="500" :padding="15" lazy>
    <!-- 上传图片、excel或其他文件、文件数量、大小限制都可以，参照volupload组件api -->
    <div style="height: 200px; display: flex; align-items: center">
      <app-upload style="text-align: center" :autoUpload="currentColumn.edit.autoUpload"
        :multiple="currentColumn.edit.multiple" :url="uploadUrl" :max-file="currentColumn.edit.maxFile"
        :img="currentColumn.edit.type == 'img'" :excel="currentColumn.edit.type == 'excel'"
        :fileTypes="currentColumn.edit.fileTypes ? currentColumn.edit.fileTypes : []" :fileInfo="fileInfo"
        :upload-after="uploadAfter" :upload-before="uploadBefore" :append="currentColumn.edit.accept">
        <div>{{ currentColumn.message }}</div>
      </app-upload>
    </div>
    <template #footer>
      <div style="text-align: center">
        <el-button type="default" size="small" @click="uploadModel = false">关闭</el-button>
        <el-button type="primary" size="small" @click="saveUpload">保存</el-button>
      </div>
    </template>
  </app-box>
  <app-image-viewer ref="viewer"></app-image-viewer>
</template>
<style lang="scss" scoped>
.app-table {
  position: relative;

  .mask {
    opacity: 0.2;
    position: absolute;
    width: 100%;
    height: 100%;
    background: #d0d0d0;
    z-index: 100;
  }

  .message {
    text-align: center;
    color: #635c5c;
    font-size: 15px;
    font-weight: 600;
    background: #eee;
    transform: translateY(-50%);
    top: 50%;
    position: absolute;
    z-index: 200;
    left: 0;
    right: 0;
    width: 150px;
    margin: 0 auto;
    line-height: 40px;
    border-radius: 4px;
    border: 1px solid #a09e9e;
  }
}

.e-item {
  display: flex;

  >div:first-child {
    flex: 1;
  }
}

.app-table :deep(.el-pager .number) {
  padding: 0 7px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  margin-left: 8px;
  font-weight: 500;
  min-width: 28px;
  height: 27px;
}

.app-table :deep(.el-pager .number.active) {
  background: #ed4014;
  color: #fff;
}

.app-table :deep(.el-pagination .el-input__wrapper) {
  height: 27px;
}

.app-table .t-file {
  color: #1e8cff;
  cursor: pointer;
  border-bottom: 1px solid;
  padding-bottom: 2px;
}

.app-table .empty-tag {
  border: none;
  background: none;
}

.v-table :deep(.el-date-editor .el-icon-date),
.v-table :deep(.el-date-editor .el-icon-time) {
  width: 10px;
}

.column-required {
  position: relative;
  color: #f20303;
  font-size: 14px;
  top: 2px;
  right: 2px;
}
</style>

<style scoped>
/* .v-table :deep(.even-row){
  background: rgb(245,247,250);
} */
.pagination {
  text-align: right;
  padding: 2px 28px;
  border: 1px solid #eee;
  border-top: 0px;
}

/* .v-table :deep(.el-input .el-input__inner) {
  padding: 0 7px;
} */
.v-table :deep(.el-table__header th) {
  /* padding: 0px !important; */
  background-color: #f8f8f9 !important;
  font-size: 13px;
  /* height: 46px; */
  color: #616161;
}

.v-table :deep(.el-table__header th.is-sortable) {
  padding: 3px !important;
}

.app-table.text-inline :deep(.el-table__body .cell),
.app-table.text-inline :deep(.el-table__header-wrapper .cell) {
  word-break: inherit !important;
  white-space: nowrap !important;
}

/* .v-table  :deep(.el-table__body td) {
  padding: 9px 0 !important;
} */

.v-table :deep(.el-table__footer td) {
  padding: 7px 0 !important;
}

.app-table :deep(.el-table-column--selection .cell) {
  display: inline;
}

.app-table.text-inline :deep(.el-table th > .cell) {
  white-space: nowrap !important;
}

.app-table .table-img {
  height: 40px;
  border-radius: 5px;
  margin-right: 10px;
  width: 40px;
  object-fit: cover;
}

.app-table .table-img:hover {
  cursor: pointer;
}

.app-table :deep(.cell) {
  padding: 2px 10px;
}

.app-table :deep(.cell .el-tag) {
  padding: 5px 9px;
}



.small-table :deep(.el-pagination .el-input__wrapper) {
  height: 27px;
}

.small-table :deep(.el-table__cell) {
  padding: 6px 0;
  font-size: 13px;
}

.small-table :deep(.cell-tag) {
  padding: 0 5px !important;
  height: 19px;
}
</style>
