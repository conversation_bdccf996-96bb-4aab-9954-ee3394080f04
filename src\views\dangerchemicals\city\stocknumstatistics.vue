<script setup>
defineOptions({
    name: 'dangerchemicalscitystocknumstatistics'
});
import {
    Refresh, Search, Position
} from '@element-plus/icons-vue'
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DccatalogGetClassTwo, PUnitGetCountyByCityId, DcCityStockNumStatisticsFind
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { ExcelDownload } from "@/utils/index.js"
import {
    DcCityStockNumStatisticsExport
} from '@/api/directdata.js'
import { fileDownload } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "AreaId", SortType: "ASC" }, { SortCode: "TwoCatalogId", SortType: "ASC" }, { SortCode: "SchoolCatalogId", SortType: "ASC" }] })
const summation = ref({})
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
    DccatalogGetClassTwoUser()
    PUnitGetCountyByCityIdUser()

})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值  
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        if (column.property == 'StockNum') {
            sums[index] = summation.value.StockNum
            return;
        }
    });
    return sums;
}

// 列表
const HandleTableData = () => {
    DcCityStockNumStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            summation.value = other[0]
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.CountyId = undefined
    filters.value.TwoBaseCatalogId = undefined
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}

const filtersChange = () => { HandleTableData() }
const StatuzSolicitedList = ref([])
const cityList = ref([])
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取下属单位集合。
const PUnitGetCountyByCityIdUser = () => {
    PUnitGetCountyByCityId({ CityId: 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            cityList.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//导出  
const HandleExport = () => {
    filters.value.pageIndex = undefined;
    filters.value.pageSize = undefined;
    DcCityStockNumStatisticsExport(filters.value).then(res => {
        ExcelDownload(res)
    });
}
// 区县名称查看
const HandleLook = (row) => {
    router.push({
        path: '/dangerchemicals/county/stocknumstatistics',
        query: {
            countyId: row.CountyId,
            countyName: row.AreaName,
            path: '/dangerchemicals/city/stocknumstatistics',
        }
    })
}
// 明细查看  
const HandleDetail = (row) => {
    router.push({
        path: '/dangerchemicals/county/stocknumstatistics',
        query: {
            countyId: row.CountyId,
            twoCatalogId: row.TwoCatalogId,
            model: row.Model,
            countyName: row.AreaName,
            name: row.Name,
            path: '/dangerchemicals/city/stocknumstatistics',
        }
    })
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" @click="HandleExport">导出</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in cityList" :key="item.UnitId" :label="item.CountyName"
                                :value="item.UnitId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.TwoBaseCatalogId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="危化品名称"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Model" clearable placeholder="规格属性"
                            style="width: 180px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="AreaName" label="区县名称" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.SchoolMaterialModelId" type="primary" link @click="HandleLook(row)">
                        {{ row.AreaName }}</el-button>
                    <span v-else>{{ row.AreaName }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="TwoCatalogName" label="危化品分类" min-width="120" align="center"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column prop="StockNum" label="库存数量" min-width="120" align="right"></el-table-column>
            <el-table-column fixed="right" label="明细" width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>