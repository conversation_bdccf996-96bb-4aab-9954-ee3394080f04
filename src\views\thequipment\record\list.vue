<script setup>
defineOptions({
    name: 'dangerchemicalstrainsafeeducationlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import { FolderAdd, UploadFilled, Setting, Search, Refresh } from '@element-plus/icons-vue'
import { ThEquipmentCategoryGetSchoolPaged, ThEquipmentCategorySchoolSave } from '@/api/thequipment.js'
import { Getpagedbytype, AttachmentUpload,Getdictionarycombox } from '@/api/user.js'
import AppBox from "@/components/Approve/AppBox.vue";
import { fileDownload, tagsListStore ,limit} from "@/utils/index.js";//上传附件
import { ElMessageBox, ElMessage } from 'element-plus';
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const SchoolId = ref(0)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 1000000, sortModel: [{ SortCode: "Sort", SortType: "ASC" }] })
const categoryListData = ref([])
const schoolstageListData = ref([])

//加载数据
onMounted(() => {
    SchoolId.value = route.query.SchoolId || 0
    if (route.query.isTagRouter) { 
    }

    HandleTableData(true);
    LoadSchoolStageData();
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    SchoolId.value = route.query.SchoolId || 0
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
        }
    })
})

// 加载学段集合
const LoadSchoolStageData = () => {
    Getdictionarycombox({ TypeCode: "100000" }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            if(rows){
                rows.unshift({DicValue:0,DicName:"全部"});
            }
            schoolstageListData.value = rows
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = (row) => {
    filters.value.pageIndex = 1
    HandleTableData()
}
const regDategeChange = (val) => {
    if (!val) filters.value.BeginDatege = undefined
    HandleTableData()
}
const EndDatele = ref("");
const regDateleChange = (val) => {
    if (val) {
        filters.value.EndDatele = val + " 23:59:59"
    } else {
        filters.value.EndDatele = undefined
    }
    HandleTableData()
}

// 列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    ThEquipmentCategoryGetSchoolPaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows , other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
            if (isFirst) {
                categoryListData.value = other.CategoryList;
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData(false)
} // 重置
const HandleReset = () => {
    filters.value.pageIndex = 1;
    filters.value.BeginDatege = undefined;
    filters.value.EndDatele = undefined;
    filters.value.Name = undefined;
    filters.value.CategoryName = undefined;
    filters.value.SchoolStageName = undefined;
    EndDatele.value = '';
    HandleTableData(false)
}
// 分页
const handlePage = (val) => {
    HandleTableData(false)
}

// 提交
const HandleSubmit = (row) => { 
        ElMessageBox.confirm('确定要提交保存该数据信息吗?').then(() => {
            ThEquipmentCategorySchoolSave([{EquipmentCategoryId:row.EquipmentCategoryId,StandardNum:row.StandardNum,StockNum:row.StockNum,DifferenceNum:row.DifferenceNum}]).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '保存成功')
                    row.UpdateTime = res.data.data.rows;
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        }) 
}

const HandleAllSave = () => {
     try {
         ElMessageBox.confirm('确定全部保存吗?').then(() => {
            const list = tableData.value.map(item => ({
                EquipmentCategoryId: item.EquipmentCategoryId,
                StandardNum: item.StandardNum,
                StockNum: item.StockNum,
                DifferenceNum: item.DifferenceNum
            }));

            if (window.__isSaving) return;
            window.__isSaving = true;

            ThEquipmentCategorySchoolSave(list).then(res => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '保存成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        }) 
    } catch (err) {
        console.error(err);
        ElMessage.error('保存失败: ' + err.message);
    }
    finally {
        // 确保解锁
        window.__isSaving = false;
    }
}

// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDatele.value) return false;
    return time >= new Date(EndDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDatege) return false;
    return time < new Date(filters.value.BeginDatege + ' 00:00:00');
};

//输入整数
const limitInput = (val, index ,name) => {
  tableData.value[index][name] = limit(val);
}

const limitChange =(row)=>{
     // 确保数值处理（空值/非数字转为0）
    const stock = Number(row.StockNum) || 0;
    const standard = Number(row.StandardNum) || 0;
    let diff = stock - standard;
    row.DifferenceNum = diff;  // 绝对值
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Setting" @click="HandleAllSave">全部保存</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="开始更新时间"
                            @change="regDategeChange" style="width: 140px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDatele" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledEndDate" clearable placeholder="截止更新时间"
                            @change="regDateleChange" style="width: 140px;">
                        </el-date-picker>
                    </el-form-item>
                        <el-form-item class="flexItem">
                        <el-select v-model="filters.CategoryName" clearable placeholder="装备分类" style="width: 120px" @change="filtersChange">
                            <el-option v-for="item in categoryListData" :key="item.CategoryName" :label="item.CategoryName" :value="item.CategoryName" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-select v-model="filters.SchoolStageName" clearable placeholder="适用学段" style="width: 120px"  @change="filtersChange">
                            <el-option v-for="item in schoolstageListData" :key="item.DicName" :label="item.DicName" :value="item.DicName" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="名称" style="width: 160px">
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
            <el-table ref="refTable" :data="tableData" highlight-current-row border stripe header-cell-class-name="headerClassName">
            <el-table-column prop="CategoryName" label="装备分类" min-width="100" align="center"></el-table-column>
            <el-table-column prop="EquipmentName" label="名称" min-width="180"></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="60" align="center"></el-table-column>
            <el-table-column prop="StandardNum" label="配备标准数量" min-width="100" align="center"> 
                <template #default="{ row ,$index}"> 
                    <el-input v-model="row.StandardNum"  auto-complete="off" @input="limitInput($event, $index,'StandardNum')" @change="limitChange(row)"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="StockNum" label="存量" min-width="100" align="center"> 
                <template #default="{ row , $index}"> 
                    <el-input v-model="row.StockNum" auto-complete="off" @input="limitInput($event,$index, 'StockNum')" @change="limitChange(row)"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="DifferenceNum" label="差额" min-width="100" align="center"> 
                 <template #default="{ row }"> 
                    {{row.DifferenceNum??"--"}}
                </template>
                <!-- <template #default="{ row , $index}"> 
                    <el-input v-model="row.DifferenceNum" auto-complete="off" @input="limitInput($event, $index,'DifferenceNum')"></el-input>
                </template> -->
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleSubmit(row)">保存</el-button>
                </template>
            </el-table-column>
            <el-table-column prop="UpdateTime" label="更新时间" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.UpdateTime ? row.UpdateTime.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page v-if="tableTotal > filters.pageSize" :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize" @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>