<script setup>
defineOptions({
  name: 'auditauditlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  QuestionFilled, Refresh, Search
} from '@element-plus/icons-vue'
import {
  ShelfGetwaitauditpaged, PurchaseGetpurchaseno, ShelfAudit
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const SupplierList = ref([])
const purchasenoList = ref([])
const filters = ref({ pageIndex: 1, pageSize: 10 })
const dialogVisible = ref(false)
const dialogData = ref({ Type: '1' })
const refForm = ref()
const ruleForm = {
  statuz: [
    { required: true, message: '请选择审核结果', trigger: 'change' },
  ],
  purchaseid: [
    { required: true, message: '请选择合同批次', trigger: 'change' },
  ],
  explain: [
    { required: true, message: '请输入审核意见', trigger: 'change' },
  ],
}

//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    PurchaseGetpurchasenoUser()
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      PurchaseGetpurchasenoUser()
      HandleTableData(true);
    }
  })
})

// 品名查看
const HandleDetail = (row) => {
  // 电脑预览
  const { href } = router.resolve({
    path: "/preview",
    query: { id: row.Id, requestNum: 2 }
  });
  window.open(href, "_blank");
}
// 审核
const HandleExamine = (row) => {
  dialogData.value.id = row.Id
  // 表单重置
  dialogData.value.statuz = undefined
  dialogData.value.purchaseid = undefined
  dialogData.value.explain = ''

  dialogVisible.value = true
}
// 提交审核
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    ShelfAuditUser()
  })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  ShelfGetwaitauditpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        SupplierList.value = other.SupplierList || [];//供应商
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.SupplierId = undefined
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }
// 获取合同批次
const PurchaseGetpurchasenoUser = () => {
  PurchaseGetpurchaseno().then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      purchasenoList.value = rows;
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 提交审核
const ShelfAuditUser = () => {
  ShelfAudit(dialogData.value).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '提交审核成功')
      HandleTableData()
      dialogVisible.value = false
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

</script>
<template>
  <div class="viewContainer">
    <el-collapse>
      <el-collapse-item>
        <template #title>
          操作提示 &nbsp;
          <el-icon color="#E6A23C" :size="16">
            <QuestionFilled />
          </el-icon>
        </template>
        <ol class="rowFill">
          <li style="color: #F56C6C;"> 是指审核供应商按采购合同清单录入的校服参数及价格；</li>
          <li> 因涉及校服征订价格、规格，请认真审核； </li>
          <li> 点击【审核】按钮，按采购合同清单中校服参数逐条审核。 </li>
        </ol>
      </el-collapse-item>
    </el-collapse>
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SupplierId" clearable filterable placeholder="供应商" @change="filtersChange"
              style="width: 240px">
              <el-option v-for="item in SupplierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="Uniformtype" label="种类" min-width="100" align="center"></el-table-column>
      <el-table-column prop="Name" label="品名" min-width="120" align="center">
        <template #default="{ row }">
          <span style="color: #66ccff;cursor: pointer;" @click="HandleDetail(row)">{{ row.Name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="Price" label="单价（元）" min-width="120" align="right"></el-table-column>
      <el-table-column label="适合性别" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.Sex == 1 ? '男' : row.Sex == 2 ? '女' : row.Sex == 3 ? '男/女' : '未知' }}
        </template>
      </el-table-column>
      <el-table-column prop="StandardNum" label="标配数量" min-width="120" align="center"></el-table-column>
      <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
      <el-table-column prop="SupplierName" label="供应商" min-width="180"></el-table-column>
      <el-table-column prop="ModifyTime" label="提交时间" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.ModifyTime ? row.ModifyTime.substring(0, 10) : '--' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="100" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleExamine(row)">审核</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
    <el-dialog v-model="dialogVisible" title="校服审核" draggable width="600px" :close-on-click-modal="false">
      <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="100px" status-icon>
        <el-form-item label="审核结果：" prop="statuz">
          <el-radio-group v-model="dialogData.statuz">
            <el-radio :value="1">审核通过</el-radio>
            <el-radio :value="2">审核不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="合同批次：" prop="purchaseid" v-if="dialogData.statuz == 1">
          <template #label>
            <el-tooltip class="item" effect="dark" content="请正确选择，否则影响校服征订清单！" placement="top">
              <div>
                <el-icon color="#E6A23C" class="tipIcon">
                  <QuestionFilled />
                </el-icon>
              </div>
            </el-tooltip>
            <span> 合同批次: </span>
          </template>
          <el-select v-model="dialogData.purchaseid" clearable filterable placeholder="合同批次">
            <el-option v-for="item in purchasenoList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="dialogData.statuz == 2" label="审核意见：" prop="explain">
          <el-input type="textarea" v-model="dialogData.explain" :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入审核意见"></el-input>
        </el-form-item>
        <el-form-item v-else label="审核意见：">
          <el-input type="textarea" v-model="dialogData.explain" :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入审核意见"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>
<style lang="scss" scoped>
.dialog-content {
  height: 500px;
  overflow: auto;
}
</style>