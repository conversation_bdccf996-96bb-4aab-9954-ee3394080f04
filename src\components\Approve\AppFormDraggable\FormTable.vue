<script setup>
import { watch, onMounted, ref } from 'vue'
import { integerLimit } from "@/utils/index.js";
const props = defineProps({
  tableData: {
    type: Array,
    default: () => {
      return []
    }
  },
  columns: {
    type: Array,
    default: []
  },
  height: {
    type: Number,
    default: 0
  },
  currentItem: {
    type: Object,
    default: () => {
      return {}
    }
  },
  tableModel: {
    type: Boolean,
    default: false
  }
});
const filterColumns = ref([])
const filterTableData = ref([])
onMounted(() => {
  filterColumns.value = props.columns
  filterTableData.value = props.tableData
  console.log('filterTableData', props.tableData)
  console.log('filterColumns', props.columns)
})
watch(() => props.tableData, (newLen, oldLen) => {
  filterTableData.value = newLen
});
watch(() => props.columns, (newLen, oldLen) => {
  filterColumns.value = newLen
});
const emit = defineEmits(['update:tableModel', 'confirm']);
const saveConfigOptions = () => {
  emit('confirm', filterTableData.value)
}
// 关闭弹窗
const beforeClose = () => {
  emit('update:tableModel', false)
}
// 弹窗全屏
const fullscreen = ref(false);
const handleFullScreen = () => {
  fullscreen.value = !fullscreen.value;
}
//金额输入限制：最多4位小数
const integerLimitInput = (val, row, name) => {
  row[name] = integerLimit(val);
}
</script>
<template>
  <div>
    <el-dialog :model-value="tableModel" :height="600" :width="1100" :fullscreen="fullscreen"
      :before-close="beforeClose">
      <template #header>
        {{ currentItem.name }}
        <button class="el-dialog__headerbtn" type="button" style="right: 35px; color: var(--el-color-info)"
          @click="handleFullScreen">
          <i class="el-icon el-icon-full-screen"></i>
        </button>
      </template>
      <el-scrollbar>
        <div style="min-height: 50px;padding: 1px;" class="srcoll-content">
          <div class="app-table" style="height:560px;padding:5px 10px;">
            <el-table stripe ref="refTable" :data="filterTableData" highlight-current-row border
              header-cell-class-name="headerClassName">
              <el-table-column v-for="(column, cindex) in filterColumns" :key="column.FieldCode"
                :prop="column.FieldCode" :label="column.FieldName" :min-width="column.Width"
                :align="column.ContentStyle">
                <template #default="{ row }">
                  <el-switch v-if="column.type == 'switch'" v-model="row[column.FieldCode]" active-color="#0f84ff"
                    inactive-color="rgb(194 194 194)" active-text="是" inactive-text="否" inline-prompt
                    :active-value="true" :inactive-value="false">
                  </el-switch>
                  <el-input v-else-if="column.type == 'input'" v-model="row[column.FieldCode]"
                    @input="integerLimitInput($event, row, column.FieldCode)"> </el-input>
                  <span v-else>{{ row[column.FieldCode] }}</span>
                </template>
              </el-table-column>


              <!-- <el-table-column label="字段Code" min-width="160" align="center">
                <template #default="{ row }">
                  <span>{{ row.FieldCode }}</span>
                </template>
              </el-table-column>
              <el-table-column label="字段名称" min-width="160" align="center">
                <template #default="{ row }">
                  <span>{{ row.FieldName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="是否显示" min-width="120" align="center">
                <template #default="{ row }">
                  <el-switch v-model="row.isShow" active-color="#0f84ff" inactive-color="rgb(194 194 194)"
                    active-text="是" inactive-text="否" inline-prompt :active-value="true" :inactive-value="false">
                  </el-switch>
                </template>
              </el-table-column>
              <el-table-column label="列宽度" min-width="120" align="center">
                <template #default="{ row }">
                  <el-input v-model="row.Width"> </el-input>
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </div>
      </el-scrollbar>
      <template #footer>
        <div style="text-align: center">
          <el-button size="small" @click="beforeClose"><i class="el-icon-close"></i>关闭</el-button>
          <el-button type="primary" size="small" @click="saveConfigOptions"><i class="el-icon-check"></i>保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-dialog__header) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 0px 13px;
  line-height: 53px;
  border-bottom: 1px solid #e6e6e6;
  height: 50px;
  color: rgb(79, 79, 79);
  font-weight: bold;
  font-size: 14px;
  margin: 0;
}

:deep(.el-dialog__headerbtn) {
  top: 0;
  padding-top: 8px;
  height: 50px;
  width: 0;
  padding-right: 30px;
  padding-left: 5px;
}

.app-table :deep(.el-pager .number) {
  padding: 0 7px;
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  margin-left: 8px;
  font-weight: 500;
  min-width: 28px;
  height: 27px;
}
</style>
