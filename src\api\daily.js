import request from '@/utils/request.js'

// 保存申报时间设置-保存 (FromBody)
export const DcGovernDateTimeSave = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndatetimesetsave', params)
}
// 查询填报详情-查询 (FromBody)
export const DcGovernDeclareDetailFind = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndeclaredetailfind', params)
}
// 危化品治理 - 区、市周、月统计列表-查询 (FromBody)
export const DcGovernDeclareSummaryFind = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndeclaresummaryfind', params)
}
// 查询月申报列表-查询 (FromBody)
export const DcGovernDeclareSummaryMonthFind = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndeclaresummarymonthfind', params)
}
// 查询周申报列表-查询 (FromBody)
export const DcGovernDeclareSummaryWeekFind = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndeclaresummaryweekfind', params)
}
// 批量设置学校上报-保存批量 (FromBody)
export const DcGovernDeclareUnitConfigBatchSet = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndeclareunitconfigbatchset', null, {
    params: params
  })
}
// 申报单位管理列表-查询 (FromBody)
export const DcGovernDeclareUnitConfigFind = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndeclareunitconfigfind', params)
}
// 设置单位是否需要上报-保存 (FromBody)
export const DcGovernDeclareUnitConfigSet = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverndeclareunitconfigset', null, {
    params: params
  })
}
// 获取危化品治理打印表数据-查询 (FromBody)
export const DcGovernItemInfo = (params) => {
  return request.post('/api/hyun/dcgovern/dcgoverniteminfo', params)
}
// 查询周天排查列表-查询 (FromBody)
export const DcGovernItemReportGetById = (params) => {
  return request.post('/api/hyun/dcgovern/dcgovernitemreportgetbyid', params)
}
// 整改申报-保存 (FromBody)
export const DcGovernItemReportSave = (params) => {
  return request.post('/api/hyun/dcgovern/dcgovernitemreportsave', params)
}
// 获取申报整改信息-查询 (FromBody)
export const DcGovernRectifyGetById = (params) => {
  return request.post('/api/hyun/dcgovern/dcgovernrectifygetbyid', null, {
    params: params
  })
}
// 问题隐患清单-查询 (FromBody)
export const DcGovernRectifyListFind = (params) => {
  return request.post('/api/hyun/dcgovern/dcgovernrectifylistfind', params)
}
// 危化品整改-保存 (FromBody)
export const DcGovernRectifyRectifySave = (params) => {
  return request.post('/api/hyun/dcgovern/dcgovernrectifyrectifysave', params)
}
// 危化品打印输出单位信息-查询 (FromBody)
export const DcGovernSetGet = (params) => {
  return request.post('/api/hyun/dcgovern/dcgovernsetget', params)
}
// 危化品打印输出单位信息-保存 (FromBody)
export const DcGovernUnitSetSave = (params) => {
  return request.post('/api/hyun/dcgovern/dcgovernunitsetsave', params)
}
// 问题与隐患清单检查记录表打印-查询 (FromBody)
export const DcProblemGovernRectifyListPrint = (params) => {
  return request.post('/api/hyun/dcgovern/dcproblemgovernrectifylistprint', params)
}
// 查看检查说明-查询 (FromBody)
export const GovernDeclareDetailGetById = (params) => {
  return request.post('/api/hyun/dcgovern/governdeclaredetailgetbyid', params)
}

// 已创建检查记录列表-查询 (FromBody)
export const GovernTaskedListFind = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskedlistfind', params)
}
// 危化品检查结束任务-修改 (FromBody)
export const GovernTaskFinish = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskfinish', null, {
    params: params
  })
}
// 添加学校-保存添加 (FromBody)
export const GovernTaskSchoolAdd = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskschooladd', params)
}
// 批量添加学校-添加批量 (FromBody)
export const GovernTaskSchoolBatchAdd = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskschoolbatchadd', params)
}

// 删除学校-删除 (FromBody)
export const GovernTaskSchoolDelete = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskschooldelete', params)
}

// 选择学校列表-查询 (FromBody)
export const GovernTaskSchoolListFind = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskschoollistfind', params)
}

// 获取当前危化品治理任务信息-查询 (FromBody)
export const GovernTaskUnitByTaskNameFindByid = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskunitbytasknamefindbyid', params)
}
// 获取危化品治理任务单位数据-查询 (FromBody)
export const GovernTaskUnitGetById = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskunitgetbyid', null, {
    params: params
  })
}

// 被检查学校列表-查询 (FromBody)
export const GovernTaskUnitListFind = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskunitlistfind', params)
}

// 学校危险化学品安全综合治理检查记录表打印-打印 (FromBody)
export const GovernTaskUnitListPrint = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskunitlistprint', null, {
    params: params
  })
}
// 保存检查基本信息-保存 (FromBody)
export const GovernTaskUnitUpdate = (params) => {
  return request.post('/api/hyun/dcgoverntask/governtaskunitupdate', params)
}
// 验证单位信息是否已经设置-保存 (FromBody)
export const PrintCheck = (params) => {
  return request.post('/api/hyun/dcgoverntask/printcheck', params)
}
// 查询问题与隐患清单周/月统计表-打印 (FromBody)
export const ProblemDangerStatisticsPrintPrint = (params) => {
  return request.post('/api/hyun/dcgoverntask/problemdangerstatisticsprintprint', params)
}
// 问题隐患清单-查询 (FromBody)
export const ProblemGovernRectifyListFind = (params) => {
  return request.post('/api/hyun/dcgoverntask/problemgovernrectifylistfind', params)
}
