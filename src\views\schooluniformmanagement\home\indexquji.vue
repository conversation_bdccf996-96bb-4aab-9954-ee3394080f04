<template>
  <div class="indexquji">
    <!-- 数据概览 -->
    <van-row gutter="10" class="data-overview">
      <van-col span="6">
        <van-card :num="121" title="校服采购学校总数" />
      </van-col>
      <van-col span="6">
        <van-card :num="102" title="已征求家长意见数" />
      </van-col>
      <van-col span="6">
        <van-card :num="75" title="已采购备案数" />
      </van-col>
      <van-col span="6">
        <van-card :num="51" title="已履约备案数" />
      </van-col>
      <van-col span="6">
        <van-card :num="51" title="已提交双检报告数" />
      </van-col>
    </van-row>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 校服选用情况折线图 -->
      <div ref="usageChart" style="width: 100%; height: 300px;"></div>
      <!-- 其他图表... -->
    </div>

    <!-- 待办事项 -->
    <div class="pending-tasks">
      <h3>待办事项</h3>
      <ul>
        <li v-for="(item, index) in pendingTasks" :key="index">
          <van-icon :name="item.icon" /> {{ item.title }}
        </li>
      </ul>
    </div>

    <!-- 资讯信息 -->
    <div class="news-info">
      <h3>资讯信息</h3>
      <ul>
        <li v-for="(item, index) in newsItems" :key="index">{{ item }}</li>
      </ul>
    </div>

    <!-- 明细表格 -->
    <div class="tables-section">
      <h3>校服征订明细</h3>
      <van-table :columns="orderColumns" :data="orderData" />

      <h3>校服资助明细</h3>
      <van-table :columns="supportColumns" :data="supportData" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { Card, Col, Row, Icon } from 'vant';

export default {
  components: {
    [Card.name]: Card,
    [Col.name]: Col,
    [Row.name]: Row,
    [Icon.name]: Icon,
  },
  data() {
    return {
      // 数据概览数据
      pendingTasks: [
        { icon: 'todo-list-o', title: '选用结果审核' },
        { icon: 'todo-list-o', title: '采购申请审核' },
        { icon: 'todo-list-o', title: '指示结果审核' },
        { icon: 'todo-list-o', title: '合同履约审核' },
      ],
      newsItems: [
        '通知公告',
        '政策法规',
        '帮助文档',
        '平台操作文档',
        '教育部关于加强和改进中小学实验...',
        '平台使用说明',
        '中小学实验室及实验教学平台上线啦',
      ],
      orderColumns: [
        { title: '学校名称', key: 'schoolName' },
        { title: '订购人数', key: 'orderCount' },
        { title: '订购金额（元）', key: 'orderAmount' },
      ],
      orderData: [
        { schoolName: '第一中学', orderCount: 5200, orderAmount: '¥2,184,000' },
        { schoolName: '第二中学', orderCount: 4800, orderAmount: '¥2,016,000' },
        { schoolName: '第三中学', orderCount: 4500, orderAmount: '¥1,890,000' },
      ],
      supportColumns: [
        { title: '学校名称', key: 'schoolName' },
        { title: '资助学生数', key: 'studentCount' },
        { title: '资助金额（元）', key: 'supportAmount' },
      ],
      supportData: [
        { schoolName: '第一中学', studentCount: 450, supportAmount: '¥180,000' },
        { schoolName: '第二中学', studentCount: 320, supportAmount: '¥128,000' },
        { schoolName: '第三中学', studentCount: 280, supportAmount: '¥112,000' },
      ],
    };
  },
  mounted() {
    this.initCharts();
  },
  methods: {
    initCharts() {
      const usageChart = echarts.init(this.$refs.usageChart);
      // 配置项...
      usageChart.setOption({
        // ...图表配置
      });
    },
  },
};
</script>

<style scoped>
.indexquji {
  padding: 20px;
}

.data-overview {
  margin-bottom: 20px;
}

.charts-section {
  margin-bottom: 20px;
}

.pending-tasks, .news-info, .tables-section {
  margin-bottom: 20px;
}
</style>