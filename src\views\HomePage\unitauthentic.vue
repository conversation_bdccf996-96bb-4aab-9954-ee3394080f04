<script setup>
import { ref, onMounted } from 'vue'
import HomeHeader from '@/views/HomePage/HomeHeader.vue';
import Footer from '@/views/HomePage/footer.vue';
import {
    QuestionFilled, UploadFilled, FolderChecked, Delete
} from '@element-plus/icons-vue'
import {
    AuditGetmyunit, PsupplierschoolauditApply, Getpagedbytype, AttachmentUpload, psupplierschoolauditDelfilebyid
} from '@/api/user.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { foundReturn, fileDownload } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const formData = ref({})
const refForm = ref()
const AreaList = ref([])
const areaValue = ref([])
const uploadFileData = ref([])
const ruleForm = {
    Name: [
        { required: true, message: '请输入名称', trigger: 'change' },
    ],
    SocialCreditCode: [
        { required: true, message: '请输入组织机构代码', trigger: 'change' },
        {
            pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
            message: '请输入正确的组织机构代码',
            trigger: 'blur'
        }
    ],
    areaValue: [
        { required: true, message: '请选择省所在地区', trigger: 'change' },
    ],
    Address: [
        { required: true, message: '请输入详细地址', trigger: 'change' },
    ],
    ContactUser: [
        { required: true, message: '请输入联系人', trigger: 'change' },
    ],
    Tel: [
        { required: true, message: '请输入联系电话', trigger: 'change' },
        {
            pattern: /^(1\d{10}|(\d{3,4}-)?\d{7,8})$/,
            message: '请输入正确的手机号或电话号码',
            trigger: 'blur'
        }
    ],
}

// mounted生命周期
onMounted(() => {
    if (userStore.userInfo.UnitStatus == 2 && (userStore.userInfo.UnitType == 3 || userStore.userInfo.UnitType == 4)) {
        GetpagedbytypeUser()
        AuditGetmyunitUser()
    } else {
        router.push('/')
    }
})

// 提交认证
const HandleAttest = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        // 判断必传的附件是否已传
        let found = foundReturn(uploadFileData.value)
        if (found) {
            return
        }
        ElMessageBox.confirm('确定申请认证吗?')
            .then(() => {
                formData.value.AttachmentIdList = fileIdList.value.map(t => t.Id)
                PsupplierschoolauditApplyUser()
            })
            .catch((err) => {
                console.info(err)
            })
    })
}

// 获取单位信息
const AuditGetmyunitUser = () => {
    AuditGetmyunit().then((res) => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            formData.value = rows

            let categoryList = rows.ListAttachment || []
            if (categoryList.length > 0) {
                // 遍历数组 b 的每个元素
                categoryList.forEach(item => {
                    // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
                    let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
                    // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
                    if (match) {
                        match.categoryList.push(item);
                    }
                });
            }

            AreaList.value = other.listArea || [];//省、市、区
            let provinceId = rows.ProvinceId || undefined;
            let cityId = rows.CityId || undefined;
            let countyId = rows.CountyId || undefined;
            formData.value.areaValue = [provinceId, cityId, countyId];//省、市、区
            uploadFileData.value.forEach(item => {
                item.fileLChildList = []
            })

            if (rows.AuthStatuz == 1) {
                ElMessage({
                    message: '认证审核已通过，请退出重新登录',
                    type: 'success',
                })
            }
            refForm.value.resetFields()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 提交认证
const PsupplierschoolauditApplyUser = () => {
    let params = {
        Name: formData.value.Name,// 企业全称
        SocialCreditCode: formData.value.SocialCreditCode,  //组织机构代码
        ProvinceId: formData.value.areaValue,//省
        CityId: formData.value.areaValue,//市
        CountyId: formData.value.areaValue,//区
        Address: formData.value.Address,// 详细地址
        ListId: fileIdList.value.map(t => t.Id),// 附件Id集合
        Url: formData.value.Url,// 电商网址
        Introduction: formData.value.Introduction,// 公司简介
        ContactUser: formData.value.ContactUser,// 联系人
        Tel: formData.value.Tel,// 联系电话
    }

    PsupplierschoolauditApply(params).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '申请认证成功')
            AuditGetmyunitUser()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 106 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.fileLChildList = []
                    item.categoryList = []
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const fileFile = ref()
const uploadRef = ref()
const fileIdList = ref([])
const numberDisabled = ref(false)
// 达到最大数量后限制上传
const MaxFileNumberClick = (item) => {
    let length = item.categoryList.length + item.fileLChildList.length
    if (item.MaxFileNumber && length >= item.MaxFileNumber) {
        numberDisabled.value = true
        ElMessage.error("上传数量已达到上限,请先删除后再上传")
        return
    } else {
        numberDisabled.value = false
    }
}
// 导入前校验
const beforeAvatarUpload = (item, file) => {

    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase()
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }

    return extension && isSize
}

// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: item.FileCategory }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data
            // 将上传的附件加入对应的模块下
            uploadFileData.value[index].fileLChildList.push(rows[0])
            // 获取上传后附件Id
            fileIdList.value.push(rows[0])
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 保存前删除  删除列表
const delFile = (item, index) => {
    uploadFileData.value[index].fileLChildList = uploadFileData.value[index].fileLChildList.filter(t => t.Id != item.Id)
    fileIdList.value = fileIdList.value.filter(t => t.Id != item.Id)
}
// 保存后删除 调用接口
const delCateFile = (item, index) => {
    psupplierschoolauditDelfilebyid({ id: item.Id }).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '删除成功')
            uploadFileData.value[index].categoryList = uploadFileData.value[index].categoryList.filter(t => t.Id != item.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
    // console.log(e)
    let path = e.Path;
    viewPhotoList.value = imgList.map(t => t.Path)
    if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (path == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        let title = e.Title + e.Ext
        fileDownload(e.Path, title)
    }
}

</script>
<template>
    <header class="headerNav">
        <HomeHeader></HomeHeader>
    </header>
    <section class="section">
        <el-form style="width: 900px;" class="mobile-box" @submit.prevent ref="refForm" :model="formData"
            :rules="ruleForm" label-width="240px" status-icon>
            <el-form-item label="企业全称：" prop="Name">
                <el-input v-model="formData.Name" auto-complete="off" placeholder="与单位公章一致"
                    style="width: 50%;"></el-input>
            </el-form-item>
            <el-form-item label="组织机构代码：" prop="SocialCreditCode">
                <template #label>
                    <el-tooltip class="item" effect="dark" content="这是单位唯一标识，请保证正确；认证通过后只能由运营商修改" placement="top">
                        <div>
                            <el-icon color="#E6A23C" class="tipIcon">
                                <QuestionFilled />
                            </el-icon>
                        </div>
                    </el-tooltip>
                    <span> 组织机构代码： </span>
                </template>

                <el-input v-model="formData.SocialCreditCode" auto-complete="off" style="width: 50%;"></el-input>
            </el-form-item>

            <el-form-item label="所在地区：" prop="areaValue">
                <el-cascader placeholder="请选择省市区" :options="AreaList" filterable v-model="formData.areaValue"
                    :props="{ value: 'Id', label: 'Name', children: 'Children' }" style="width: 50%;"></el-cascader>
            </el-form-item>
            <el-form-item label="详细地址：" prop="Address">
                <el-input v-model="formData.Address" auto-complete="off" placeholder="详细地址"
                    style="width: 50%;"></el-input>
            </el-form-item>
            <el-form-item label="联系人：" prop="ContactUser">
                <el-input v-model="formData.ContactUser" auto-complete="off" placeholder="用于家长购买校服的联系"
                    style="width: 50%;"></el-input>
            </el-form-item>
            <el-form-item label="联系电话：" prop="Tel">
                <el-input v-model="formData.Tel" auto-complete="off" placeholder="用于家长购买校服的联系"
                    style="width: 50%;"></el-input>
            </el-form-item>
            <el-form-item label="电商地址：">
                <el-input v-model="formData.Url" auto-complete="off" placeholder="电商地址" style="width: 50%;"></el-input>
            </el-form-item>
            <el-form-item label="公司简介：">
                <el-input v-model="formData.Introduction" maxlength="500" style="width: 50%" placeholder="用于平台中企业主页宣传"
                    show-word-limit :autosize="{ minRows: 2, maxRows: 6 }" type="textarea" />
            </el-form-item>
            <el-form-item v-for="(item, index) in uploadFileData" :key="index"
                v-if="formData.AuthStatuz == -1 || formData.AuthStatuz == 2">
                <template #label>
                    <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                    <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                        <div>
                            <el-icon color="#E6A23C" class="tipIcon">
                                <QuestionFilled />
                            </el-icon>
                        </div>
                    </el-tooltip>
                    <span> {{ item.Name }}： </span>
                </template>
                <el-upload ref="uploadRef" class="upload-demo" :show-file-list="false"
                    :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                    :http-request="httpRequest.bind(null, item, index)" :disabled="numberDisabled">
                    <el-button type="success" size="small" :icon="UploadFilled"
                        @click="MaxFileNumberClick(item)">上传</el-button>
                </el-upload>
                <div class="fileFlex">
                    <div v-for="(itemCate) in item.categoryList" :key="itemCate.Id" style="color:#409EFF ;">
                        <el-icon color="#F56C6C" style="cursor: pointer;" @click="delCateFile(itemCate, index)">
                            <Delete />
                        </el-icon>
                        <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                            {{ itemCate.Title }}{{ itemCate.Ext }}
                        </span>
                    </div>
                    <div v-for="(itemChild) in item.fileLChildList" :key="itemChild.Id">
                        <el-icon color="#F56C6C" style="cursor: pointer;" @click="delFile(itemChild, index)">
                            <Delete />
                        </el-icon>
                        <span style="cursor: pointer;" @click="fileListDownload(itemChild, item.fileLChildList)">
                            {{ itemChild.Title }}{{ itemChild.Ext }}
                        </span>
                    </div>
                </div>
            </el-form-item>
            <el-form-item v-if="formData.AuthStatuz == 2">
                <template #label>
                    <span style="color: #F56C6C;"> 审核不通过：</span>
                </template>
                <el-input v-model="formData.Reason" disabled style="width: 80%" show-word-limit
                    :autosize="{ minRows: 2, maxRows: 6 }" type="textarea" />
            </el-form-item>
            <el-form-item label-width="100px" style=" padding-top: 10px; border-top: 1px solid #eee;">
                <div class="attestBox">
                    <p v-if="formData.AuthStatuz == -1">请按要求完善单位信息，点击【申请认证】，等待后台审核。如需加急，请联系客服：025-84292160</p>
                    <p v-if="formData.AuthStatuz == 0">请等待后台认证，预计需要1-2个工作日。</p>
                    <p v-if="formData.AuthStatuz == 2">认证审核未通过，需重新提交申请认证</p>
                    <p v-if="formData.AuthStatuz == 1" style="color: #333;">认证审核已通过，请退出重新登录</p>

                    <el-button v-if="formData.AuthStatuz == 0" type="success" disabled> 认证中 </el-button>
                    <el-button v-else type="success" :icon="FolderChecked" @click="HandleAttest">
                        {{ formData.AuthStatuz == -1 ? '申请认证' : '重新认证' }} </el-button>
                </div>
            </el-form-item>
        </el-form>
        <!-- 图片预览 -->
        <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
    </section>
    <footer class="footer">
        <Footer></Footer>
    </footer>

</template>
<style lang="scss" scoped>
.headerNav {
    margin-bottom: 60px;
}


.section {
    height: 100%;
    background-color: #fff;
    padding: 50px 100px;
}

.footer {
    background-color: #545c64;
    color: #fff !important;
    padding: 10px 0;
    position: fixed;
    bottom: 0px;
    left: 0px;
    width: 100%;
    z-index: 99;
}

.attestBox {
    p {
        font-size: 16px;
        color: #F56C6C;
    }
}
</style>
