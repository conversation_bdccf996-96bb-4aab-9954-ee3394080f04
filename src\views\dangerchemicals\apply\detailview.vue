<script setup>
defineOptions({
    name: 'dangerchemicalsapplydetailview'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    DcapplyView
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore } from "@/utils/index.js";
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'
const route = useRoute()
const userStore = useUserStore()
const purchase = ref({})
const approvalList = ref([])
const activeNames = ref([])
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleViewData()
    }
})
onActivated(() => {
    tagsListStore(userStore.tagsList, route)
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleViewData()
        }
    })
})

// 详情
const HandleViewData = () => {
    DcapplyView({ id: route.query.id }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            purchase.value = rows;
            approvalList.value = other;
            activeNames.value = approvalList.value.map((item) => item.Id)
            activeNames.value.push(purchase.value.Id)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
</script>
<template>
    <div class="demo-collapse">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="领用信息" :name="purchase.Id">
                <template #title>
                    <div style="padding-left: 10px;">
                        领用信息
                    </div>
                </template>
                <div class="content">
                    <div><span class="content_label">领用人：</span>{{ purchase.UserName }}</div>
                    <div><span class="content_label">申领批次：</span>{{ purchase.BatchNo }}</div>
                    <div><span class="content_label">危化品名称：</span>{{ purchase.Name }}</div>
                    <div><span class="content_label">数量：</span>{{ purchase.Num }} &nbsp;&nbsp;({{ purchase.UnitName }})
                    </div>
                    <div><span class="content_label">规格属性：</span>{{ purchase.Model ? purchase.Model : '--' }}</div>
                    <div><span class="content_label">品牌：</span>{{ purchase.Brand ? purchase.Brand : '--' }}</div>
                    <div class="content_label_div"><span class="content_label">使用时间：</span>
                        {{ purchase.UseTime ? purchase.UseTime.substring(0, 16) : '--' }}</div>
                    <div class="content_label_div"><span class="content_label">用途：</span>{{ purchase.Remark ?
                        purchase.Remark : '无' }}</div>
                </div>
            </el-collapse-item>
            <el-collapse-item v-for="(item, index) in approvalList" :key="index" title="领用审核" :name="item.Id">
                <template #title>
                    <div style="padding-left: 10px;">
                        领用审核
                    </div>
                </template>
                <div class="content">
                    <div class="content_label_div"><span class="content_label">审核结果：</span>
                        <span v-if="item.ProcessNumber == 10">{{ item.ApprovalStatuz == 11 ? "不通过" : "通过" }}</span>
                        <span v-if="item.ProcessNumber == 20">{{ item.ApprovalStatuz == 21 ? "不通过" : "通过" }}</span>
                    </div>
                    <div class="content_label_div"><span class="content_label">审核意见：</span>{{ item.ApprovalRemark }}
                    </div>
                    <div><span class="content_label">审核人：</span>{{ item.AuditUserName }}</div>
                    <div><span class="content_label">审核日期：</span>
                        {{ item.RegTime ? item.RegTime.substring(0, 10) : '--' }}</div>
                </div>
            </el-collapse-item>
        </el-collapse>
    </div>
</template>
<style lang="scss" scoped>
.content {
    display: flex;
    flex-wrap: wrap;
    color: #333;
    font-size: 14px;
    width: 640px;
    padding-left: 30px;

    div {
        width: 300px;
        padding: 5px 10px;
    }

    .content_label_div {
        width: 600px;
    }

    .content_label {
        width: 200px;
        text-align: right;
        font-weight: bold;
    }

}

:deep(.el-collapse-item__header) {

    font-size: 14px;
    color: #0000ee;
}
</style>