import request from '@/utils/request.js'
// 用户登录
export const userLogin = (params) => {
  return request.post('/api/hyun/login/accountlogin', null, { params: params })
}
//  获取用户菜单
export const GetNavigationBar = (params) => {
  return request.get('/api/permission/GetNavigationBar', { params: params })
}
// 学校/企业注册登录
//  学校/企业 获取图片验证码
export const LoginRefreshcaptchaimage = (params) => {
  return request.post('/api/hyun/login/refreshcaptchaimage', params)
}
// 学校/企业 获取手机校验码
export const LoginXfsendcode = (params) => {
  return request.post('/api/hyun/login/xfsendcode', params)
}
// 学校/企业 提交注册
export const LoginSchoolandcompanyreg = (params) => {
  return request.post('/api/hyun/login/schoolandcompanyreg', params)
}
// 家长/班主任  注册登录
//  家长/班主任 获取手机号验证码
export const teacherGetvalidatecode = (params) => {
  return request.post('/api/hyun/login/getvalidatecode', null, { params: params })
}
//  家长/班主任 注册登录
export const teacherUserLogin = (params) => {
  return request.post('/api/hyun/login/userlogin', null, { params: params })
}
// 平台设置=====================================================================
// 单位管理++++++++++++++++++++++++++
// 单位基本信息
export const Unitgetschoolinfo = (params) => {
  return request.post('/api/hyun/punit/unitgetschoolinfo', params)
}
// 单位属性
export const Getdictionarycombox = (params) => {
  return request.post('/api/hyun/bdictionary/getdictionarycombox', null, { params: params })
}
// 单位信息修改保存
export const Unitupdateschoolinfo = (params) => {
  return request.post('/api/hyun/punit/unitupdateschoolinfo', params)
}
// 区县： 本单位信息管理*******
// 单位信息
export const Uitgetinfo = (params) => {
  return request.post('/api/hyun/punit/unitgetinfo', params)
}
//  单位所在地
export const Areagetbyid = (params) => {
  return request.post('/api/hyun/barea/areagetbyid', null, { params: params })
}
//  省、市、区
export const Areagetbypid = (params) => {
  return request.post('/api/hyun/barea/areagetbypid', null, { params: params })
}
// 地点 绑定
export const Getareabyunitid = (params) => {
  return request.post('/api/hyun/barea/getareabyunitid', null, { params: params })
}
// 单位信息保存 (区县、市级)
export const PunitSetxfunitinfo = (params) => {
  return request.post('/api/hyun/punit/setxfunitinfo', params)
}
// 单位信息保存
export const Unitinsertupdate = (params) => {
  return request.post('/api/hyun/punit/unitinsertupdate', params)
}
// 区县： 下属单位信息管理*******
// 获取下属单位
export const Unitfindchildren = (params) => {
  return request.post('/api/hyun/punit/unitfindchildren', params)
}
// 获取下属单位详情
export const Unitgetbyid = (params) => {
  return request.post('/api/hyun/punit/unitgetbyid', null, { params: params })
}
// // 下属单位删除
export const Unitdelbatch = (params) => {
  return request.post('/api/hyun/punit/unitdelbatch', null, { params: params })
}
// 修改排序值
export const Unitsortedit = (params) => {
  return request.post('/api/hyun/punit/unitsortedit', null, { params: params })
}

// 下属单位-下属单位导入
export const SuperAdminUploadUnitFile = (params) => {
  return request.post('/api/hyun/punit/superadminuploadunitfile', params)
}

// 下属单位信息查询*******
// 下属单位信息列表
export const Schoolinfofind = (params) => {
  return request.post('/api/hyun/punit/schoolinfofind', params)
}
// 单位锁定解锁
export const batchlockunlock = (params) => {
  return request.post('/api/hyun/puser/batchlockunlock', null, { params: params })
}
export const AreaFind = (params) => {
  return request.get('/api/hyun/barea/areafind', { params: params })
}

// 超管： 所有单位信息管理*******
// 获取所有单位
export const Unitfind = (params) => {
  return request.post('/api/hyun/punit/unitfind', params)
}
// 获取 单位
export const Unitfindidname = (params) => {
  return request.post('/api/hyun/punit/unitfindidname', params)
}
// 用户账户  hyun/puser/schoolinfouserfind
export const Schoolinfouserfind = (params) => {
  return request.post('/api/hyun/puser/schoolinfouserfind', params)
}
// 场所信息列表  hyun/puser/schoolinfopropertyfind
export const Schoolinfopropertyfind = (params) => {
  return request.post('/api/hyun/puser/schoolinfopropertyfind', params)
}
// 单位明细  hyun/puser/schoolinfogetbyid
export const Schoolinfogetbyid = (params) => {
  return request.post('/api/hyun/puser/schoolinfogetbyid', null, { params: params })
}
export const SchoolInfoAdminFind = (params) => {
  return request.post('/api/hyun/puser/schoolinfoadminfind', params)
}
// 用户账号管理*******
// 获取用户列表
export const Userfindmyunit = (params) => {
  return request.post('/api/hyun/puser/userfindmyunit', params)
}
//用户管理页面：根据单位类型加载角色信息
export const Rolefindmyunit = (params) => {
  return request.post('/api/hyun/prole/rolefindmyunit', params)
}
//用户管理页面：获取角色模块信息
export const Usergetbyid = (params) => {
  return request.post('/api/hyun/puser/usergetbyid', null, { params: params })
}
// 新增与修改账户
export const Usersavemyunit = (params) => {
  return request.post('/api/hyun/puser/usersavemyunit', params)
}

// 批量设置所属部门
export const Departmentuserbatchset = (params) => {
  return request.post('/api/hyun/punit/departmentuserbatchset', params)
}
// 删除用户账号
export const Userdelbatch = (params) => {
  return request.post('/api/hyun/puser/userdelbatch', params)
}
// 启用或禁用账户
export const Usermyunitupdateuserstatuz = (params) => {
  return request.post('/api/hyun/puser/usermyunitupdateuserstatuz', null, { params: params })
}

// 重置密码
export const Useraccountreset = (params) => {
  return request.post('/api/hyun/puser/useraccountreset', null, { params: params })
}

// 账户解冻
export const Useraccountunlock = (params) => {
  return request.post('/api/hyun/puser/useraccountunlock', null, { params: params })
}
// 下属用户管理*******
// 获取下属单位用户列表
export const Userfindchildren = (params) => {
  return request.post('/api/hyun/puser/userfindchildren', params)
}
// 下属单位用户列表-导出
export const ExportChildUser = (params) => {
  return request.post('/api/hyun/puser/exportchilduser', params)
}
// 下属单位用户-权限设置

export const UserRoleChildUnitBatchSet = (params) => {
  return request.post('/api/hyun/punit/userrolechildunitbatchset', params)
}

// 下属单位-下属单位导入
export const UploadUserFile = (params) => {
  return request.post('/api/hyun/puser/uploaduserfile', params)
}
// 下属单位角色
export const Rolefindbyunittype = (params) => {
  return request.post('/api/hyun/prole/rolefindbyunittype', null, { params: params })
}
// 获取单位
export const Unitfindchildrenidname = (params) => {
  return request.post('/api/hyun/punit/unitfindchildrenidname', params)
}
// 下属单位用户：添加/修改提交
export const Usersavechildren = (params) => {
  return request.post('/api/hyun/puser/usersavechildren', params)
}
// 下属单位用户： 获取权限设置角色信息
export const RoleGetPlatformRoleList = (params) => {
  return request.get('/api/hyun/prole/rolegetplatformrolelist', { params: params })
}
// 下属单位用解冻列表
export const Userfindchildrenall = (params) => {
  return request.post('/api/hyun/puser/userfindchildrenall', params)
}

// 所有单位用户：添加/修改提交
export const Usersave = (params) => {
  return request.post('/api/hyun/puser/usersave', params)
}
// 禁用/启用
export const userupdateuserstatuz = (params) => {
  return request.post('/api/hyun/puser/userupdateuserstatuz', null, { params: params })
}
// 所有单位用户管理*******
// 获取单位用户
export const Userfind = (params) => {
  return request.post('/api/hyun/puser/userfind', params)
}
// 设置有效期
export const Usersetuservalidate = (params) => {
  return request.post('/api/hyun/puser/usersetuservalidate', params)
}
// 角色管理页面
// 获取角色列表
export const Rolefind = (params) => {
  return request.post('/api/hyun/prole/rolefind', params)
}
// 项目配置信息
export const Bconfigsetgetbyunit = (params) => {
  return request.post('/api/hyun/bconfigset/bconfigsetgetbyunit', null, { params: params })
}
// 项目配置信息保存
export const Bconfigsetsavebyunit = (params) => {
  return request.post('/api/hyun/bconfigset/bconfigsetsavebyunit', params)
}
// 页面管理页面+++++++
// 获取页面列表
export const Syspagefind = (params) => {
  return request.post('/api/hyun/psyspage/syspagefind', params)
}
// 添加修改页面
export const Syspageinsertupdate = (params) => {
  return request.post('/api/hyun/psyspage/syspageinsertupdate', params)
}
// 获取页面详情
export const Syspagegetbyid = (params) => {
  return request.post('/api/hyun/psyspage/syspagegetbyid', null, { params: params })
}
// 删除页面
export const Syspagedelbatch = (params) => {
  return request.post('/api/hyun/psyspage/syspagedelbatch', null, { params: params })
}
// 接口管理页面 ++++++++++
// 获取接口列表
export const Modulefind = (params) => {
  return request.post('/api/module/modulefind', params)
}
//  添加接口
export const ModuleSave = (params) => {
  return request.post('/api/module/save', params)
}
//  修改接口
export const ModuleUpdate = (params) => {
  return request.post('/api/module/update', params)
}
// 获取页面详情
export const ModuleGetbyid = (params) => {
  return request.post('/api/module/getbyid', null, { params: params })
}
// 删除接口
export const ModuleDeletebatch = (params) => {
  return request.post('/api/module/deletebatch', null, { params: params })
}
// 角色页面权限管理+++++
// 获取角色权限
export const Objectpermissionfind = (params) => {
  return request.post('/api/hyun/pobjectpermission/objectpermissionfind', params)
}
//  保存权限
export const Objectpermissionsave = (params) => {
  return request.post('/api/hyun/pobjectpermission/objectpermissionsave', params)
}
// 个人设置
// 个人信息保存
export const Useredit = (params) => {
  return request.post('/api/hyun/puser/useredit', params)
}
//  密码修改
export const Userchangepass = (params) => {
  return request.post('/api/hyun/puser/userchangepass', params)
}
// 关联账号+++++++++
// 获取关联账号列表
export const Multaccountfind = (params) => {
  return request.post('/api/hyun/puser/multaccountfind', params)
}
// 删除关联账号
export const Multaccountdelbyid = (params) => {
  return request.post('/api/hyun/puser/multaccountdelbyid', null, { params: params })
}
// 导出：获取路径和名称
export const Usermyunitexport = (params) => {
  return request.post('/api/hyun/puser/usermyunitexport', params)
}
// 附件上传
export const UploadPostfile = (params) => {
  return request.post('/api/hyun/upload/postfile', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 上传头像
export const UploadPosthead = (params) => {
  return request.post('/api/hyun/upload/posthead', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 单位用户exsl上传
export const Uploaduserfile = (params) => {
  return request.post('/api/hyun/puser/uploaduserfile', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下属单位exsl上传
export const Superadminuploadunitfile = (params) => {
  return request.post('/api/hyun/punit/superadminuploadunitfile', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 获取用户列表
export const getUserListPage = (params) => {
  return request.get('/api/user/get', { params: params })
}
// 删除用户
export const removeUser = (params) => {
  return request.delete('/api/user/delete', { params: params })
}
// 编辑用户
export const editUser = (params) => {
  return request.put('/api/user/put', params)
}
// 添加用户
export const addUser = (params) => {
  return request.post('/api/user/post', params)
}
// 重置密码(管理员)
export const ResetPass = (params) => {
  return request.put('/api/user/ResetPass', params)
}
////////////校服管理平台/////////////////
// 获取配置
export const Getpaged = (params) => {
  return request.get('/api/hyun/xuniformconfig/getpaged', { params: params })
}
// 修改配置
export const Setbyid = (params) => {
  return request.put('/api/hyun/xuniformconfig/setbyid', null, { params: params })
}
// 获取指标信息详情
export const XuniformGetbyid = (params) => {
  return request.put('/api/hyun/xuniformconfig/getbyid', null, { params: params })
}
// 修改指标信息
export const XuniformEdit = (params) => {
  return request.post('/api/hyun/xuniformconfig/edit', params)
}
export const Setscore = (params) => {
  return request.put('/api/hyun/xuniformconfig/setscore', null, { params: params })
}
// 校服展示配置 列表
export const XuniformshelfGetpagelist = (params) => {
  return request.post('/api/hyun/xuniformshelf/getpagelist', params)
}
// 校服管理-管理员设置是否展示
export const XuniformshelfSetisshow = (params) => {
  return request.put('/api/hyun/xuniformshelf/setisshow', null, { params: params })
}
// 附件配置
// 备案附件列表
export const BatGetpaged = (params) => {
  return request.post('/api/hyun/battachmentconfig/getpaged', params)
}
// 新增
export const BattAdd = (params) => {
  return request.post('/api/hyun/battachmentconfig/add', params)
}
// 修改
export const BattEdit = (params) => {
  return request.post('/api/hyun/battachmentconfig/edit', params)
}
// 删除
export const BattDelete = (params) => {
  return request.delete('/api/hyun/battachmentconfig/deletebyid', { params: params })
}
// 根据Id启用禁用备案附件
export const BattSetstatuz = (params) => {
  return request.get('/api/hyun/battachmentconfig/setstatuz', { params: params })
}
// 详情
export const BattByid = (params) => {
  return request.get('/api/hyun/battachmentconfig/getattachmentconfigbyid', { params: params })
}
// 模块类型
export const Getattachmentconfigbyid = (params) => {
  return request.get('/api/hyun/battachmentconfig/getattachmentconfigbyid', { params: params })
}
// 根据模块类型获取备案附件信息
export const Getpagedbytype = (params) => {
  return request.get('/api/hyun/battachmentconfig/getpagedbytype', { params: params })
}
//上传附件
export const AttachmentUpload = (params) => {
  console.log('params', params)
  return request.post(
    '/api/attachment/upload',
    { file: params.file },
    {
      headers: {
        'Content-Type': 'multipart/form-data',
        filecategory: params.filecategory
      }
    }
  )
}
//附件保存
export const AttachmentSave = (params) => {
  return request.post('/api/attachment/save', params)
}
//附件删除
export const AttachmentDelete = (params) => {
  return request.post('/api/attachment/delete', params)
}
// 根据区县获取学校
export const Punitgetschoolbycountyid = (params) => {
  return request.post('/api/hyun/punit/punitgetschoolbycountyid', null, { params: params })
}
// 班主任事务-学生管理-列表
export const PstudentGetpagedbyteacher = (params) => {
  return request.post('/api/hyun/pstudent/getpagedbyteacher', params)
}
// 班主任事务-学生管理-添加
export const PstudentTeacheraddstu = (params) => {
  return request.post('/api/hyun/pstudent/teacheraddstu', params)
}
// 班主任事务-学生管理-修改
export const PstudentTeachereditstu = (params) => {
  return request.post('/api/hyun/pstudent/teachereditstu', params)
}
// 班主任事务-学生管理-导入
export const PstudentTeacherimportstu = (params) => {
  return request.post('/api/hyun/pstudent/teacherimportstu', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 班主任事务-学生管理-删除
export const PstudentTeacherdelstu = (params) => {
  return request.delete('/api/hyun/pstudent/teacherdelstu', { params: params })
}
// execl上传
export const UploadPostexecl = (params) => {
  return request.post('/api/hyun/upload/postexecl', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 年级班级-列表
export const PclassinfoGetpaged = (params) => {
  return request.post('/api/hyun/pclassinfo/getpaged', params)
}
// 年级班级-添加班级
export const PclassinfoAdd = (params) => {
  return request.post('/api/hyun/pclassinfo/add', params)
}
// 年级班级-修改班级
export const PclassinfoEdit = (params) => {
  return request.post('/api/hyun/pclassinfo/edit', params)
}
// 年级班级-删除
export const PclassinfoDeletebyid = (params) => {
  return request.delete('/api/hyun/pclassinfo/deletebyid', { params: params })
}
// 年级班级-班级信息
export const PclassinfoGetbyeditid = (params) => {
  return request.get('/api/hyun/pclassinfo/getbyeditid', { params: params })
}
// 年级班级-年级升级
export const PclassinfoGradeupgrade = (params) => {
  return request.get('/api/hyun/pclassinfo/gradeupgrade', { params: params })
}
// 年级班级-班级信息导入
export const PclassinfoImport = (params) => {
  return request.post('/api/hyun/pclassinfo/import', params, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 年级班级-学生名单-列表
export const PstudentGetpaged = (params) => {
  return request.post('/api/hyun/pstudent/getpaged', params)
}
// 年级班级-学生名单-新增
export const pstudentAdd = (params) => {
  return request.post('/api/hyun/pstudent/add', params)
}
// 年级班级-学生名单-修改
export const pstudentEdit = (params) => {
  return request.post('/api/hyun/pstudent/edit', params)
}
// 年级班级-学生名单-学生信息
export const PstudentGetbyeditid = (params) => {
  return request.get('/api/hyun/pstudent/getbyeditid', { params: params })
}
// 年级班级-学生名单-删除
export const PstudentDeletebyid = (params) => {
  return request.delete('/api/hyun/pstudent/deletebyid', { params: params })
}
// 年级班级-学生名单-导入
export const PstudentImport = (params) => {
  return request.post('/api/hyun/pstudent/import', params)
}
// 年级班级-学生名单-导出
export const PstudentExportschoolstu = (params) => {
  return request.post('/api/hyun/pstudent/exportschoolstu', params)
}
// 获取单位信息（学校、企业）
export const AuditGetmyunit = (params) => {
  return request.get('/api/hyun/psupplierschoolaudit/getmyunit', { params: params })
}
// 获取单位信息（区县、市级）
export const PunitGetxfunitinfo = (params) => {
  return request.get('/api/hyun/punit/getxfunitinfo', { params: params })
}
// 获取单位信息（新装备：学校）
export const GetSchoolUnitInfo = (params) => {
  return request.get('/api/hyun/punit/getschoolunitinfo', { params: params })
}
// 保存学校信息 （新装备：学校）
export const SaveSchoolUnitInfo = (params) => {
  return request.post('/api/hyun/punit/saveschoolunitinfo', params)
}
// 单位信息-申请认证（学校、企业）
export const PsupplierschoolauditApply = (params) => {
  return request.post('/api/hyun/psupplierschoolaudit/apply', params)
}
// 删除学校企业申请认证附件
export const psupplierschoolauditDelfilebyid = (params) => {
  return request.delete('/api/hyun/psupplierschoolaudit/delfilebyid', { params: params })
}
// 运行商（超管）-单位认证审核列表
export const PsupplierschoolauditGetpaged = (params) => {
  return request.post('/api/hyun/psupplierschoolaudit/getpaged', params)
}
// 运行商（超管）-单位认证审核列表  -获取待审核信息
export const PsupplierschoolauditGetbyid = (params) => {
  return request.get('/api/hyun/psupplierschoolaudit/getbyid', { params: params })
}
//运行商（超管）-单位认证审核列表  - 运营商审核
export const PsupplierschoolauditAuditunit = (params) => {
  return request.post('/api/hyun/psupplierschoolaudit/auditunit', params)
}
//  运行商（超管）-家长班主任账号管理-列表
export const PuserGetuserxfpage = (params) => {
  return request.post('/api/hyun/puser/getuserxfpage', params)
}
//  运行商（超管）-家长班主任账号管理-重置密码
export const PuserSetuserxfpwd = (params) => {
  return request.post('/api/hyun/puser/setuserxfpwd', params)
}
// 个人中心=个人信息
export const PuserGetuserinfo = (params) => {
  return request.get('/api/hyun/puser/getuserinfo', { params: params })
}
// 个人中心=个人信息 -修改
export const PuserSetuserinfo = (params) => {
  return request.post('/api/hyun/puser/setuserinfo', params)
}
// 个人中心=密码修改
export const PuseruSerchangepass = (params) => {
  return request.post('/api/hyun/puser/userchangepass', params)
}
// 资讯管理/////////////////////
// 资讯分类列表
export const ArticleGetcategorypaged = (params) => {
  return request.post('/api/hyun/uniformarticle/getcategorypaged', params)
}
// 添加资讯分类
export const ArticleAddcategory = (params) => {
  return request.post('/api/hyun/uniformarticle/addcategory', params)
}
// 修改资讯分类
export const ArticleEditcategory = (params) => {
  return request.post('/api/hyun/uniformarticle/editcategory', params)
}
// 根据资讯分类Id查询资讯分类信息
export const ArticleGetbycategoryid = (params) => {
  return request.get('/api/hyun/uniformarticle/getbycategoryid', { params: params })
}
// 删除资讯分类
export const ArticleDelcategory = (params) => {
  return request.delete('/api/hyun/uniformarticle/delcategory', { params: params })
}
// 资讯列表
export const ArticleGetarticlepaged = (params) => {
  return request.post('/api/hyun/uniformarticle/getarticlepaged', params)
}
// 添加资讯
export const ArticleAddarticle = (params) => {
  return request.post('/api/hyun/uniformarticle/addarticle', params)
}
// 修改资讯
export const ArticleEditarticle = (params) => {
  return request.post('/api/hyun/uniformarticle/editarticle', params)
}
// 根据资讯Id查询资讯信息
export const ArticleGetbyarticleid = (params) => {
  return request.get('/api/hyun/uniformarticle/getbyarticleid', { params: params })
}
// 删除资讯
export const ArticleDelarticle = (params) => {
  return request.delete('/api/hyun/uniformarticle/delarticle', { params: params })
}
// 资讯列表-发布和取消发布
export const ArticlePubarticle = (params) => {
  return request.get('/api/hyun/uniformarticle/pubarticle', { params: params })
}
// 项目配置信息
export const AnonGetconfigbymodule = (params) => {
  return request.post('/api/hyun/anon/getconfigbymodule', null, { params: params })
}
// 获取底部资讯分类信息
export const ArticleGetbottomcatetype = (params) => {
  return request.get('/api/hyun/uniformarticle/getbottomcatetype', { params: params })
}
// 获取资讯首页信息
export const ArticleGetinformation = (params) => {
  return request.get('/api/hyun/uniformarticle/getinformation', { params: params })
}
// 根据资讯分类Id查询资讯信息
export const ArticleGetinformationbycid = (params) => {
  return request.get('/api/hyun/uniformarticle/getinformationbycid', { params: params })
}
// 根据资讯Id查询资讯详情信息
export const ArticleGetinformationdetail = (params) => {
  return request.get('/api/hyun/uniformarticle/getinformationdetail', { params: params })
}
// 获取运营商资讯分类信息及默认显示信息
export const ArticleGetoperator = (params) => {
  return request.get('/api/hyun/uniformarticle/getoperator', { params: params })
}
// 运营商资讯更加分类Id查询资讯信息
export const ArticleGetoperatorlist = (params) => {
  return request.get('/api/hyun/uniformarticle/getoperatorlist', { params: params })
}

// 消息配置列表
// 消息配置列表获取
export const PostMsgConfigList = (params) => {
  return request.post('/api/hyun/workflow/postmsgconfiglist', params)
}
// 新增修改消息配置
export const PostMsgConfigInsertUpdate = (params) => {
  return request.post('/api/hyun/workflow/postmsgconfiginsertupdate', params)
}
// 批量开启关闭
export const PostMsgConfigSetSatuz = (params) => {
  return request.post('/api/hyun/workflow/postmsgconfigsetsatuz', params)
}
// 根据Id获取消息配置信息
export const GetMsgConfigById = (params) => {
  return request.get('/api/hyun/workflow/getmsgconfigbyid', { params: params })
}

// 部门管理-部门管理列表
export const DepartmentListFind = (params) => {
  return request.post('/api/hyun/punit/departmentlistfind', params)
}
// 部门管理-添加修改部门信息
export const DepartmentInsertUpdate = (params) => {
  return request.post('/api/hyun/punit/departmentinsertupdate', params)
}
// 部门管理-删除部门信息
export const DepartmentDelById = (params) => {
  return request.delete('/api/hyun/punit/departmentdelbyid', { params: params })
}
// 部门管理-启用禁用部门信息
export const DepartmentEnable = (params) => {
  return request.get('/api/hyun/punit/departmentenable', { params: params })
}

// 部门管理-弹框添加列表
export const DepartmentUserListFind = (params) => {
  return request.post('/api/hyun/punit/departmentuserlistfind', params)
}
// 部门管理-批量添加
export const UserInDepartBatchInsert = (params) => {
  return request.get('/api/hyun/punit/userindepartbatchinsert', { params: params })
}

// 部门管理-点修改跳出的列表
export const UserInDepartListFind = (params) => {
  return request.post('/api/hyun/punit/userindepartlistfind', params)
}
// 部门管理-修改列表中删除
export const UserInDepartBatchDel = (params) => {
  return request.delete('/api/hyun/punit/userindepartbatchdel', { params: params })
}

// 部门管理-单条修改批量修改  部门下拉框数据
export const GetAllDepartment = (params) => {
  return request.post('/api/hyun/punit/getalldepartment', params)
}

// 部门管理-修改列表中单条修改、批量修改
export const DepartmentUserUpdate = (params) => {
  return request.post('/api/hyun/punit/departmentuserupdate', params)
}

// 场所地点管理- 获取地址信息
export const AddressFind = (params) => {
  return request.post('/api/hyun/baddress/addressfind', params)
}
// 场所地点管理- 根据Id获取地址信息
export const AddressGetById = (params) => {
  return request.get('/api/hyun/baddress/addressgetbyid', { params: params })
}

// 场所地点管理- 添加修改楼宇场馆 /场所
export const AddressInsertUpdate = (params) => {
  return request.post('/api/hyun/baddress/addressinsertupdate', params)
}

// 地点导入
export const UploadAddressFile = (params) => {
  return request.post('/api/hyun/baddress/uploadaddressfile', params)
}
// 删除地址信息
export const AddressDelBatch = (params) => {
  return request.delete('/api/hyun/baddress/addressdelbatch', { params: params })
}

// 供应商信息
export const GetSupplierFindPage = (params) => {
  return request.post('/api/hyun/punit/getsupplierpage', params)
}

//帮助中心文档

// 帮助中心文档-获取帮助信息
export const DHelpDocById = (params) => {
  return request.get('/api/hyun/dhelpdoc/dhelpdocbyid', { params: params })
}

// 帮助中心文档-获取帮助信息
export const DHelpDocSave = (params) => {
  return request.post('/api/hyun/dhelpdoc/dhelpdocsave', params)
}

// 帮助中心文档-获取帮助信息
export const DHelpDocFind = (params) => {
  return request.post('/api/hyun/dhelpdoc/dhelpdocfind', params)
}

// 帮助中心文档-删除帮助内容项信息
export const DHelpItemDeleteByIds = (params) => {
    return request.delete('/api/hyun/dhelpdoc/dhelpdocitemdelete', { params: params })
}

// 帮助中心文档-获取帮助内容项信息
export const DHelpItemFind = (params) => {
  return request.post('/api/hyun/dhelpdoc/dhelpdocitemfind', params)
}

// 帮助中心文档-删除帮助内容项信息
export const DHelpDocDeleteByIds = (params) => {
    return request.delete('api/hyun/dhelpdoc/dhelpdocdeletebyids', { params: params })
}

// 帮助中心文档-设置状态帮助内容项信息
export const DHelpItemSave = (params) => {
  return request.post('/api/hyun/dhelpdoc/dhelpdocitemsave', params)
}