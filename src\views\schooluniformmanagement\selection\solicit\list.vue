<script setup>
defineOptions({
  name: 'solicitlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  InfoFilled, Refresh, Search
} from '@element-plus/icons-vue'
import {
  SchemeGetpaged, SchemeSavefiling
} from '@/api/selection.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, SourceType: 2 })
const yearDateList = ref([])
const StatuzFilingList = ref([])
const StatuzSeekList = ref([])
const PurchaseMethodList = ref([])

//加载数据
onMounted(() => {
  yearDateList.value = previousYearDate()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

//查看
const HandleDetail = (row) => {
  router.push({ path: "./opiniondetail", query: { id: row.Id } })
}
//提交
const HandleSubmit = (row) => {
  ElMessageBox.confirm('确定提交备案吗?')
    .then(() => {
      SchemeSavefiling({ id: row.Id }).then((res) => {
        if (res.data.flag == 1) {
          HandleTableData()
          ElMessage.success(res.data.msg || '提交成功')
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
//搜索
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  SchemeGetpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        PurchaseMethodList.value = other.PurchaseMethod || [];//采购方式
        StatuzFilingList.value = other.StatuzFiling || [];//备案状态
        StatuzSeekList.value = other.StatuzSeek || [];//征求状态
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.SchemeYear = undefined
  filters.value.PurchaseMethod = undefined
  filters.value.SeekStatuz = undefined
  filters.value.StatuzFiling = undefined
  filters.value.Name = undefined
  filters.value.pageIndex = 1
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SchemeYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.PurchaseMethod" clearable placeholder="采购方式" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in PurchaseMethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SeekStatuz" clearable placeholder="征求状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzSeekList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.StatuzFiling" clearable placeholder="备案状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzFilingList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="选用批次或采购方式" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="SchemeYear" label="年度" min-width="80" align="center"></el-table-column>
      <el-table-column prop="SchemeNo" label="选用批次" min-width="140" align="center"></el-table-column>
      <el-table-column prop="SolicitedNum" label="应征求人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="AgreeNum" label="同意选用数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="AgreeRate" label="同意率" min-width="100" align="center">
        <template #default="{ row }">
          <span v-if="row.AgreeRate > 0.67">{{ row.AgreeRate * 100 }}%</span>
          <span v-else style="color: #F56C6C">{{ row.AgreeRate * 100 }}% </span>
        </template>
      </el-table-column>
      <el-table-column prop="PurchaseMethodName" label="采购方式" min-width="120" align="center"></el-table-column>
      <el-table-column label="意见详情" min-width="90" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="SolicitedStatuzName" label="征求状态" min-width="100" align="center">
      </el-table-column>
      <el-table-column prop="FilingStatuzName" label="备案状态" min-width="100" align="center"></el-table-column>
      <el-table-column fixed="right" label="选用备案" min-width="100" align="center">
        <template #default="{ row }">
          <el-button v-if="[0, 11, 21].includes(row.FilingStatuz) && row.IsCountyManager == 1" type="primary" link
            @click="HandleSubmit(row)">提交</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>