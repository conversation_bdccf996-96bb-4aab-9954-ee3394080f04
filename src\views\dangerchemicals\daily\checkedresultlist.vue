<script setup>
defineOptions({
    name: 'dangerchemicalsdailycheckedresultlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    GovernTaskedListFind
} from '@/api/daily.js'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import { pageQuery } from "@/utils/index.js";
import { ElMessageBox, ElMessage } from 'element-plus'
// import {  } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
const EndDate = ref()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, Statuz: 1 })
const summation = ref({})
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
// 列表
const HandleTableData = () => {
    GovernTaskedListFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount

            if (rows.Statistics) {
                summation.value = res.data.data.rows.Statistics[0]
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.BeginDate = undefined
    filters.value.BeginDatele = undefined
    EndDate.value = undefined
    filters.value.Name = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
const beginDateChange = (val) => {
    if (!val) filters.value.BeginDate = undefined
    HandleTableData()
}
const endDateChange = (val) => {
    if (val) {
        filters.value.BeginDatele = val + " 23:59:59"
    } else {
        filters.value.BeginDatele = undefined
    }
    HandleTableData()
}

// 总计
const getSummaries = (param) => {
    const { columns, data } = param;
    const sums = [];
    // 单元格赋值
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '总计：';
            return;
        }
        else if (index == 1) {
            sums[index] = summation.value.CheckingUnitNum
            return;
        }
        else if (index == 5) {
            sums[index] = summation.value.ProblemNum
            return;
        }
        else if (index == 6) {
            sums[index] = summation.value.RectifyProblemNum
            return;
        }
        else if (index == 7) {
            sums[index] = summation.value.DangerNum
            return;
        }
        else if (index == 8) {
            sums[index] = summation.value.RectifyDangerNum
            return;
        }
    });
    return sums;
}


//问题隐患清单
const btnDanger = (param) => {
    router.push({
        path: "/dangerchemicals/daily/dcproblemrectifylist", query: {
            taskId: param.Id
        }
    })
}

//学校检查记录
const btnView = (param) => {
    router.push({
        path: "/dangerchemicals/daily/checkedschoollist", query: {
            taskId: param.Id
        }
    })
}
// 打印
const HandlePrint = (row) => {
    router.push({
        path: "./checkedresultprint",
        query: {
            GovernTaskId: row.Id,
            path: './checkedresultlist'
        }
    })
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!EndDate.value) return false;
    return time >= new Date(EndDate.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.BeginDate) return false;
    return time < new Date(filters.value.BeginDate + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.BeginDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="检查时间"
                            @change="beginDateChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="EndDate" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="检查时间" @change="endDateChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filters.Name" placeholder="请输入检查任务名称" style="width: 180px"> </el-input>
                    </el-form-item>

                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe :summary-method="getSummaries"
            show-summary header-cell-class-name="headerClassName">
            <el-table-column prop="Name" label="检查任务名称" min-width="160"></el-table-column>
            <el-table-column prop="CheckingUnitNum" label="检查单位数" min-width="100" align="center"></el-table-column>
            <el-table-column prop="BeginDate" label="开始时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="EndDate" label="结束时间" min-width="140" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DeviceDetail" label="问题隐患清单" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="btnDanger(row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column prop="ProblemNum" label="发现问题" align="center" min-width="100"></el-table-column>
            <el-table-column prop="RectifyProblemNum" label="已整改问题" align="center" min-width="100"></el-table-column>
            <el-table-column prop="DangerNum" label="发现隐患" align="center" min-width="100"></el-table-column>
            <el-table-column prop="RectifyDangerNum" label="已整改隐患" align="center" min-width="100"></el-table-column>
            <el-table-column prop="CheckingUserNum" label="检查人员数" align="center" min-width="100"></el-table-column>
            <el-table-column fixed="right" label="操作" min-width="120" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="btnView(row)">查看</el-button>
                    <el-button type="primary" link @click="HandlePrint(row)">打印</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>