<script setup>
defineOptions({
  name: 'selectionlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search
} from '@element-plus/icons-vue'
import {
  Punitgetschoolbycountyid
} from '@/api/user.js'
import {
  SchemeGetpaged, SchemeConfirmfiling, SchemeFilingbackout, FilingRevoked
} from '@/api/selection.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
// 表格初始化
const tableData = ref([])
const CountyList = ref([])
const SchoolList = ref([])
const PurchaseMethodList = ref([])
const StatuzSeekList = ref([])
const StatuzFilingList = ref([])
const yearDateList = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
//加载数据
onMounted(() => {
  yearDateList.value = previousYearDate()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

//新增&编辑操作
const dialogVisible = ref(false)
const isRecord = ref(1)
const recordId = ref(0)
const dialogData = ref({})
const refForm = ref()
const ruleForm = {
  statuz: [
    { required: true, message: '请选择是否通过审核', trigger: 'change' },
  ],
  explanation: [
    { required: true, message: '请输入原因', trigger: 'change' },
  ],
}
// 审核||退回
const HandleRecord = (row, e) => {
  isRecord.value = e
  recordId.value = row.Id
  dialogVisible.value = true
  nextTick(() => {
    dialogData.value = {}
    refForm.value.resetFields()
  })
}

// 撤销
const HandleRevoke = (row) => {
  ElMessageBox.confirm('确定撤销审核结果吗?')
    .then(() => {
      FilingRevoked({ id: row.Id }).then(res => {
        if (res.data.flag == 1) {
          ElMessage.success(res.data.msg || '撤销成功')
          HandleTableData()
        } else {
          ElMessage.error(res.data.msg)
        }
      })
    })
    .catch((err) => {
      console.info(err)
    })
}
//查看
const HandleDetail = (row) => {
  router.push({ path: "./opiniondetail", query: { id: row.Id } })
}
//提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    if (isRecord.value == 1) {
      SchemeConfirmfilingUser()
    } else {
      SchemeFilingbackoutUser()
    }
  })
    .catch((err) => {
      console.info(err)
    })
}
//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  SchemeGetpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        if (hUnitType.value == 1) {
          CountyList.value = other.CountyList || [];//区县名称
        }
        if (hUnitType.value == 1 || hUnitType.value == 2) {
          SchoolList.value = other.SchoolList || [];//学校名称
        }
        PurchaseMethodList.value = other.PurchaseMethod || [];//采购方式
        StatuzSeekList.value = other.StatuzSeek || [];//征求状态
        StatuzFilingList.value = other.StatuzFiling || [];//备案状态
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.SchemeYear = undefined
  filters.value.CountyId = undefined
  filters.value.SchoolId = undefined
  if (hUnitType.value == 1) {
    SchoolList.value = []
  }
  filters.value.PurchaseMethod = undefined
  filters.value.SeekStatuz = undefined
  filters.value.StatuzFiling = undefined
  filters.value.Name = undefined
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }
const CountyChange = (e) => {
  if (!e) {
    filters.value.SchoolId = undefined
  }
  HandleTableData()
  PunitgetschoolbycountyidUser(e)
}
// 选择区县获取学校
const PunitgetschoolbycountyidUser = (id) => {
  Punitgetschoolbycountyid({ CountyId: id }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      filters.value.SchoolId = undefined
      SchoolList.value = rows || []//学校名称
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

// 审核
const SchemeConfirmfilingUser = (id) => {
  SchemeConfirmfiling({ id: recordId.value, statuz: dialogData.value.statuz, explanation: dialogData.value.explanation }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '提交审核成功')
      dialogVisible.value = false
      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 退回
const SchemeFilingbackoutUser = (id) => {
  SchemeFilingbackout({ id: recordId.value, explanation: dialogData.value.explanation }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '退回成功')
      dialogVisible.value = false
      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SchemeYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称" style="width: 160px"
              @change="CountyChange">
              <el-option v-for="item in CountyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.UnitId" :label="item.UnitName" :value="item.UnitId" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 2">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.PurchaseMethod" clearable placeholder="采购方式" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in PurchaseMethodList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SeekStatuz" clearable placeholder="征求状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzSeekList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.StatuzFiling" clearable placeholder="备案状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzFilingList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="选用批次" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="SchemeYear" label="年度" min-width="80" align="center"></el-table-column>
      <el-table-column prop="CountyName" label="区县名称" min-width="140" v-if="hUnitType == 1"></el-table-column>
      <el-table-column prop="SchoolName" label="学校名称" min-width="140"></el-table-column>
      <el-table-column prop="SchemeNo" label="选用批次" min-width="140" align="center"></el-table-column>
      <el-table-column prop="SolicitedNum" label="应征求人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="AgreeNum" label="同意选用数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="AgreeRate" label="同意率" min-width="80" align="center">
        <template #default="{ row }">
          <span v-if="row.AgreeRate > 0.67">{{ row.AgreeRate * 100 }}%</span>
          <span v-else style="color: #F56C6C">{{ row.AgreeRate * 100 }}% </span>
        </template>
      </el-table-column>
      <el-table-column prop="PurchaseMethodName" label="采购方式" min-width="120" align="center"></el-table-column>
      <el-table-column label="意见详情" min-width="100" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="SolicitedStatuzName" label="征求状态" min-width="100" align="center">
        <template #default="{ row }">
          {{ row.SolicitedStatuzName }}
        </template>
      </el-table-column>
      <el-table-column prop="FilingStatuzName" label="备案状态" min-width="100" align="center"></el-table-column>
      <el-table-column v-if="hUnitType == 2" fixed="right" label="操作" min-width="160" align="center">
        <template #default="{ row }">
          <el-button v-if="row.FilingStatuz == 10" type="primary" link @click="HandleRecord(row, 1)">审核</el-button>
          <el-button v-else-if="row.FilingStatuz == 100 && row.IsFiling == 0" type="primary" link
            @click="HandleRecord(row, 2)">退回</el-button>
          <el-button v-else-if="row.FilingStatuz == 100 && row.IsFiling == 1" type="primary" link
            @click="HandleRevoke(row)">撤销</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
    <el-dialog v-model="dialogVisible" :title="isRecord == 1 ? '审核' : '退回'" draggable width="560px"
      :close-on-click-modal="false">
      <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" status-icon>
        <el-form-item label="审核结果：" prop="statuz" label-width="120px" v-if="isRecord == 1">
          <el-radio-group v-model="dialogData.statuz">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="isRecord == 1 ? '不通过原因：' : ''" :label-width="isRecord == 1 ? '120px' : '0px'"
          prop="explanation" v-if="isRecord == 1 && dialogData.statuz == 2 || isRecord == 2">
          <el-input type="textarea" v-model="dialogData.explanation" :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入原因"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleSubmit">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped></style>