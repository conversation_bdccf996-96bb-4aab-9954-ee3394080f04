<script setup>
import { onMounted, ref, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  Modulefind,
  ModuleSave,
  ModuleUpdate,
  ModuleGetbyid,
  ModuleDeletebatch
} from '@/api/user.js'

// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const selectRows = ref([])
const filtersKey = ref({ key: '', value: 'Name' })
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const options = ref([
  { value: 'Name', label: '名称', },
  { value: 'LinkUrl', label: '地址', }
])

const HandleSelectChange = (selection) => {
  selectRows.value = selection
}


// 翻页
watch(() => filters.value.pageIndex, () => {
  HandleSearch()
})
watch(() => filters.value.pageSize, () => {
  filters.value.pageIndex = 1
  HandleSearch()
})

//加载数据
onMounted(() => {
  HandleSearch()
})

//新增&编辑操作
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()

const ruleForm = {
  Name: [
    { required: true, message: '接口名称不能为空', trigger: 'change' },
  ],
  LinkUrl: [
    { required: true, message: '接口地址不能为空', trigger: 'change' },
  ]
}

//新增
const HandleAdd = () => {
  formData.value = { PageType: 0, IsShow: 1 }
  dialogVisible.value = true
}

//编辑
const HandleEdit = (row) => {
  dialogVisible.value = true
  formData.value.Id = row.Id
  ModuleGetbyid({ id: row.Id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      formData.value = rows
    } else {
      ElMessage.error(res.data.msg)
    }

  })
}
//删除
const HandleDel = (row) => {
  ElMessageBox.confirm('确定删除吗?')
    .then(() => {
      ModuleDeletebatch({ ids: row.Id }).then((res) => {
        HandleSearch()
        ElMessage.success('删除成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })



}
//批量删除
const HandleBatchDel = (row) => {
  if (!row || row.length == 0) {
    ElMessage.error('请选择要操作的数据!')
    return;
  }
  ElMessageBox.confirm(`确认要删除[${row.map(t => t.Id).join(',')}]吗?`)
    .then(() => {
      let ids = row.map(t => t.Id).join(',')
      ModuleDeletebatch({ ids: ids }).then((res) => {
        HandleSearch()
        ElMessage.success('删除成功')
      })
    })
    .catch((err) => {
      console.info(err)
    })

}
//提交
const HandleSubmit = () => {
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    formData.value.IsDeleted = 0
    if (formData.value.Id) {
      //编辑
      ModuleUpdate(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '修改成功')
      })
    } else {
      //新增
      ModuleSave(formData.value).then((res) => {
        HandleSearch()
        dialogVisible.value = false
        ElMessage.success(res.data.msg || '添加成功')
      })
    }
  })
}

//搜索
const HandleSearch = (page) => {

  if (page) filters.value.pageIndex = page
  filters.value.Name = undefined;
  filters.value.LinkUrl = undefined;
  // 按类型搜索
  if (filtersKey.value.key) {
    filters.value[filtersKey.value.value] = filtersKey.value.key;
  } else {
    filters.value[filtersKey.value.value] = undefined;

  }
  selectRows.value = []

  Modulefind(filters.value).then(res => {
    if (res.data.flag == 1) {
      tableData.value = res.data.data.rows;
      tableTotal.value = Number(res.data.data.total);
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}

// 重置
const HandleReset = (page) => {
  filtersKey.value.key = ''
  HandleSearch(page)
}

</script>
<template>
  <!-- 搜索 -->
  <el-row>
    <el-col>
      <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
        <el-form-item class="flexItem">
          <el-button type="primary" plain @click="HandleAdd">添加</el-button>
          <el-button type="danger" plain @click="HandleBatchDel(selectRows)">批量删除</el-button>
        </el-form-item>
        <div class="verticalIdel"></div>
        <el-form-item label="" class="flexItem" label-width="60">
          <el-input v-model.trim="filtersKey.key" placeholder="请输入" style="width: 240px" class="input-with-select">
            <template #prepend>
              <el-select v-model="filtersKey.value" style="width: 80px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="flexItem">
          <el-button type="primary" plain @click="HandleSearch(1)">查询</el-button>
          <el-button type="primary" plain @click="HandleReset(1)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
  <!-- 内容 -->
  <el-table ref="refTable" :data="tableData" highlight-current-row @selection-change="HandleSelectChange" border stripe
    row-key="Id" header-cell-class-name="headerClassName">
    <el-table-column type="selection" width="50"></el-table-column>
    <el-table-column prop="Id" label="编号" min-width="160" align="center"></el-table-column>
    <el-table-column prop="Name" label="名称" min-width="140"></el-table-column>
    <el-table-column prop="LinkUrl" label="接口地址" min-width="300"></el-table-column>
    <el-table-column prop="IsCommon" label="是否公用接口" min-width="120" align="center">
      <template #default="{ row }">
        <el-tag :type="row.IsCommon ? 'success' : 'danger'" disable-transitions>{{ !row.IsCommon ? "否" : "是" }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="Description" label="描述" min-width="180" show-overflow-tooltip align="center">
    </el-table-column>
    <el-table-column prop="opt" label="操作" min-width="120" align="center">
      <template #default="{ row }">
        <el-button type="primary" link @click="HandleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="HandleDel(row)">删除</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>

  <!-- 分页 -->
  <el-row>
    <el-col class="flexBox">
      <el-pagination class="flexItem" size="small" background layout="total, prev, pager, next, sizes, jumper"
        :total="tableTotal" v-model:current-page="filters.pageIndex" v-model:page-size="filters.pageSize" />
    </el-col>
  </el-row>
  <!-- 弹窗 -->
  <el-dialog v-model="dialogVisible" :title="formData.Id ? '修改Api接口信息' : '添加Api接口信息'" width="750px">

    <el-form @submit.prevent ref="refForm" :model="formData" :rules="ruleForm" label-width="120px" status-icon>
      <el-form-item label="接口名称：" class="flexItem" prop="Name">
        <el-input v-model="formData.Name" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="接口地址：" prop="LinkUrl">
        <el-input v-model="formData.LinkUrl" auto-complete="off"></el-input>
      </el-form-item>
      <el-form-item label="是否公用接口：">
        <el-radio-group v-model="formData.IsCommon">
          <el-radio class="radio" :value="true">是</el-radio>
          <el-radio class="radio" :value="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="描述：">
        <el-input type="textarea" v-model="formData.Description"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.flexBox {
  display: flex;
  flex-wrap: wrap;

  .flexItem {
    color: #fff;
    font-size: 25px;
    margin-top: 8px;
    margin-right: 5px;
    cursor: pointer;
    flex-wrap: wrap;
  }

  .flexContent {
    width: 200px;
  }
}

.taskNameConent {
  width: 100%;
  /* 具体宽度，例如 200px 或 100% */
  ;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>