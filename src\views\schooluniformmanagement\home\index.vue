<script setup>
defineOptions({
    name: 'homeindex'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import {
    GetProcessIndexInfo
} from '@/api/workflow.js'
import {
    GetSchoolIndexTodo, GetCountyCityIndexTodo, GetEvaluatePieData, GetPurchasePieData, XUniformFillStatuz, XUniformBuyAgreeRate, GetHistoryOrderPerson
} from '@/api/homepage.js'
import { MoreFilled } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
import { ElMessage } from 'element-plus'
import { previousYearDate, formatNumberWithCommas, tagsListStore, fileDownload } from "@/utils/index.js";
import router from '@/router'
import * as echarts from 'echarts';
const userStore = useUserStore()
const echartNumRef = ref(null);
const echartAmountRef = ref(null);
const echartEvaluationRef = ref(null);
const echartSelectRef = ref(null);
const echartSelectedRef = ref(null);
const echartFilingdRef = ref(null);
const echartPeopleRef = ref(null);
const yearDateList = ref([])
const route = useRoute()
const year = ref('')//年度
const calendarVal = ref(new Date())
const msg = ref('')
const imageList = ref([])
onMounted(() => {
    document.documentElement.style.fontSize = (window.innerWidth / 1920) * 16 + 'px';
    year.value = new Date().getFullYear()
    yearDateList.value = previousYearDate()
    // HandleTableData()
    if (userStore.userInfo.UnitType == 3) {
        GetSchoolIndexTodoUser()

        echartSelectFun()//校服选用结果（学校）
    } else {
        GetCountyCityIndexTodoUser()
        XUniformBuyAgreeRateFun()//校服选用情况（区县、市级）
        XUniformFillStatuzFun()//校服备案情况（区县、市级）
        echartPeopleFun()//订购人数分布（区县、市级）
        GetHistoryOrderPersonFun()//历史订购人数（区县、市级）
    }

    GetPurchasePieDataFun()//采购数量比例\采购金额比例\学校校服订购信息 

    GetEvaluatePieDataFun()//校服评价结果 


})




//圆环图标封装
const numEchartFun = (myEchart, data) => {
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{b}<br/>{c} ({d}%)',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            textStyle: {
                color: '#333'
            },
            borderWidth: 1,
            padding: 15
        },
        grid: {
            top: 10,
            bottom: 10,
            left: 10,
            right: 10,
            containLabel: true
        },
        series: [
            {
                name: '数据分布',
                type: 'pie',
                radius: ['55%', '85%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderWidth: 1,
                    shadowBlur: 5,
                    shadowColor: 'rgba(0, 0, 0, 0.1)'
                },
                label: {
                    show: true,
                    position: 'inside',
                    formatter: '{b}\n{d}%',
                    fontSize: 14,
                    textShadow: '1px 1px 3px rgba(0, 0, 0, 0.5)',
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 16,
                    },
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.3)'
                    }
                },
                data: data
            }
        ]
    };
    myEchart.setOption(option);
}
const echartSelectFun = () => {
    const myEchart = echarts.init(echartSelectRef.value);
    const option = {
        grid: {
            top: 30,
            right: 30,
            bottom: 40,
        },
        xAxis: {
            data: ['家长同意\n选用比例', '选用组织家长和\n学生代表比例'],
            axisLabel: {
                interval: 0,
                rotate: 0,  // 如果需要可以调整旋转角度
                fontSize: 10  // 调整字体大小以适应多行文本
            }
        },
        yAxis: {
            axisLabel: {
                show: false // 隐藏Y轴的数值标签
            },
            splitLine: {
                show: false // 隐藏Y轴的网格线（背景线）
            }
        },
        series: [
            // 圆柱真实数据背景
            {
                data: [150, 230],
                type: 'bar',
                barWidth: 45,
                barGap: '-100%',
                z: 12,
                itemStyle: {
                    color: function (params) {
                        var colorList = ['#F0A87A', '#F07A52'];
                        return colorList[params.dataIndex];
                    }
                },
            },
            // 圆柱顶面
            {
                type: 'pictorialBar',
                symbolSize: [45, 10],
                symbolOffset: [0, -5],
                z: 12,
                itemStyle: {
                    color: function (params) {
                        var colorList = ['#F0A87A', '#F07A52'];
                        return colorList[params.dataIndex];
                    }
                },
                symbolPosition: 'end',
                data: [150, 230],
                label: {
                    show: true,
                    position: 'top',
                    formatter: '{c}%'
                }
            },

        ],
    };

    myEchart.setOption(option);
}


const echartPeopleFun = () => {
    const myEchart = echarts.init(echartPeopleRef.value);
    const option = {
        xAxis: {
            type: 'value',
            show: false,
        },
        yAxis: {
            type: 'category',
            data: ['小学', '初中', '高中']
        },
        grid: {
            top: 10,
            bottom: 10,
            left: 10,
            right: 10,
            containLabel: true
        },
        series: [
            {
                type: 'bar',
                data: [2420, 8340, 4215],
                barWidth: "30%",
                label: {
                    show: true,
                    position: 'right'
                }
            }
        ]
    };

    myEchart.setOption(option);
}

// 查看资讯列表
const listClick = (item) => {
    const { href } = router.resolve({
        path: "/articlelist",
        query: { Id: item.Id, IsMany: item.IsMany }
    });
    window.open(href, "_blank");
}
//   获取学校顶部统计数据
const navSchoolList = ref([
    { id: 1, label: '已征求家长意见' },
    { id: 2, label: '已纳入三重一大' },
    { id: 3, label: '采购已备案' },
    { id: 4, label: '履约已备案' },
    { id: 5, label: '已提交双检报告' },
])
const GetSchoolIndexTodoUser = () => {
    GetSchoolIndexTodo({ year: year.value }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data;
            navSchoolList.value = [
                { id: 1, label: '已征求家长意见', value: rows.IsSchemeOpinion },
                { id: 2, label: '已纳入三重一大', value: rows.IsTriple },
                { id: 3, label: '采购已备案', value: rows.IsBuy },
                { id: 4, label: '履约已备案', value: rows.IsPurchase },
                { id: 5, label: '已提交双检报告', value: rows.IsDoubleReduction },
            ]
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
//   获取区县、市级顶部统计数据
const navCountyList = ref([
    { id: 1, label: '校服采购学校总数' },
    { id: 2, label: '已征求家长意见数' },
    { id: 3, label: '采购已备案数' },
    { id: 4, label: '履约已备案数' },
    { id: 5, label: '已提交双检报告' },
])
const GetCountyCityIndexTodoUser = () => {
    GetCountyCityIndexTodo({ year: year.value }).then((res) => {
        if (res.data.flag == 1) {
            const { other, rows } = res.data.data;
            navCountyList.value = [
                { id: 1, label: '校服采购学校总数', value: other.SchoolCount },
                { id: 2, label: '已征求家长意见数', value: other.SchemeOpinionTotal },
                { id: 3, label: '采购已备案数', value: other.BuyedTotal },
                { id: 4, label: '履约已备案数', value: other.PurchaseedTotal },
                { id: 5, label: '已提交双检报告', value: other.DoubleReductionTotal },
            ]
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 学校业务操作导航
const BusinessNavList = [
    { id: 1, label: '校服选用方案', url: '/schooluniformmanagement/selection/project/list' },
    { id: 2, label: '选用征求结果', url: '/schooluniformmanagement/selection/solicit/list' },
    { id: 3, label: '校服采购申请', url: '/schooluniformmanagement/procurement/application/list' },
    { id: 4, label: '校服招标结果', url: '/schooluniformmanagement/procurement/bidding/list' },
    { id: 5, label: '校服合同履约', url: '/schooluniformmanagement/purchase/management/list' },
    // { id: 6, label: '校服信息审核', url: '/schooluniformmanagement/purchase/audit/auditlist' },
    { id: 7, label: '校服资助信息', url: '/schooluniformmanagement/subsidize/subsidize/list' },
    { id: 8, label: '校服评价管理', url: '/schooluniformmanagement/evaluate/list' },
    { id: 9, label: '征订单管理', url: '/schooluniformmanagement/purchase/solicitsubscrip/orderlist' },
    { id: 10, label: '调换单管理', url: '/schooluniformmanagement/exchange/launch/list' },
]
const BusinessNavClick = (url) => {
    router.push(url)
}
// 根据年度获取校、区、市评价饼状图数据
const GetEvaluatePieDataFun = () => {
    GetEvaluatePieData({ year: year.value }).then(res => {
        if (res.data.flag == 1) {
            const { other, rows } = res.data.data;
            let color = ['#6a7ee3', '#5ac4c4', '#f5b47f', '#F07A52']
            let data = []
            for (let i = 0; i < other.length; i++) {
                data[i] = { value: other[i], name: rows[i], itemStyle: { color: color[i] } }
            }
            const myEchart = echarts.init(echartEvaluationRef.value);
            numEchartFun(myEchart, data)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 学校、区县、市级获取采购数量比例、采购金额比例饼状图 和学校校服订购信息列表
let orderList = ref([])
const GetPurchasePieDataFun = () => {

    GetPurchasePieData({ year: year.value }).then(res => {
        if (res.data.flag == 1) {
            const { headers, other, rows, footer } = res.data.data;
            let color = ['#6a7ee3', '#5ac4c4', '#f5b47f', '#F07A52']
            // 采购数量比例
            let numData = []
            for (let i = 0; i < rows.length; i++) {
                numData[i] = { value: rows[i], name: headers[i], itemStyle: { color: color[i] } }
            }
            echartNumFun(numData)
            // 采购金额比例
            let amountData = []
            for (let i = 0; i < footer.length; i++) {
                amountData[i] = { value: footer[i], name: headers[i], itemStyle: { color: color[i] } }
            }
            echartAmountFun(amountData)
            // 学校校服订购信息列表
            orderList.value = other
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}

const echartNumFun = (data) => {
    const myEchart = echarts.init(echartNumRef.value);
    numEchartFun(myEchart, data)
}
const echartAmountFun = (data) => {
    const myEchart = echarts.init(echartAmountRef.value);
    numEchartFun(myEchart, data)
}
// 校服备案情况
const XUniformFillStatuzFun = () => {
    XUniformFillStatuz({ year: year.value }).then(res => {
        if (res.data.flag == 1) {
            const { other, rows } = res.data.data;
            let arr1 = rows[0].filter(t => t !== 'product')
            let arr2 = []
            let arr3 = []
            let arr4 = []
            for (let i = 0; i < 3; i++) {
                arr2[i] = rows[i + 1][0]
                arr3[i] = rows[i + 1][1]
                arr4[i] = rows[i + 1][2]
            }
            const myEchart = echarts.init(echartFilingdRef.value);
            const option = {
                xAxis: {
                    data: arr2
                },
                yAxis: {},
                grid: {
                    top: 20,
                    bottom: 20,
                    left: 20,
                    right: 200,
                    containLabel: true
                },
                legend: {
                    orient: 'vertical',
                    right: 20,
                    top: 'center',
                    data: arr1
                },
                series: [
                    {
                        name: arr1[0],
                        type: 'bar',
                        data: arr3,
                        barWidth: "25%",
                        label: {
                            show: true,
                            position: 'top'
                        }
                    },
                    {
                        name: arr1[1],
                        type: 'bar',
                        data: arr4,
                        barWidth: "25%",
                        label: {
                            show: true,
                            position: 'top'
                        }
                    }
                ]
            };

            myEchart.setOption(option);

        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 校服选用情况
const XUniformBuyAgreeRateFun = () => {
    console.log("校服选用情况")
    XUniformBuyAgreeRate({ year: year.value }).then(res => {
        if (res.data.flag == 1) {
            const { other, rows } = res.data.data;
            let AgreeRate = rows.AgreeRate
            let ParentStudentRate = rows.ParentStudentRate
            let Title = rows.Title

            const myEchart = echarts.init(echartSelectedRef.value);
            const option = {
                xAxis: {
                    data: ['最高值', '最低值', '平均值']
                },
                yAxis: {},
                grid: {
                    top: 20,
                    bottom: 20,
                    left: 20,
                    right: 200,
                    containLabel: true
                },
                legend: {
                    orient: 'vertical',
                    right: 20,
                    top: 'center',
                    data: ['家长同意选用比例', '选用组织家长和\n学生代表比例']
                },
                series: [
                    {
                        name: '家长同意选用比例',
                        data: AgreeRate,
                        type: 'line',
                        "smooth": true,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%'
                        }
                    },
                    {
                        name: '选用组织家长和\n学生代表比例',
                        data: ParentStudentRate,
                        type: 'line',
                        "smooth": true,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%'
                        }
                    }
                ]
            };

            myEchart.setOption(option);

        } else {
            ElMessage.error(res.data.msg)
        }
    })


}

// 区县市级获取近几年历史订购人数数据（区市共用）
const OrderPersonList = []
const GetHistoryOrderPersonFun = () => {
    GetHistoryOrderPerson().then(res => {
        if (res.data.flag == 1) {
            const { headers, other, rows, footer } = res.data.data;
        } else {
            ElMessage.error(res.data.msg)
        }
    })

}
</script>
<template>
    <div class="homebody">
        <!-- <div ref="echartRef" style="width: 200px; height: 200px;"></div> -->
        <div class="container" style="display: flex;">
            <div class="container_left">
                <ul class="navUi" v-if="userStore.userInfo.UnitType == 3">
                    <li class="nav_item div_shadow" v-for="item in navSchoolList" :key="item.id">
                        <span>{{ item.label }}
                            <span v-if="item.value == 1">（是）</span><span v-else class="fontcolor">（否）</span>
                        </span>
                    </li>
                </ul>
                <ul class="navUi" v-else>
                    <li class="nav_item div_shadow" v-for="item in navCountyList" :key="item.id">
                        <span>{{ item.label }}
                            <span style="padding-left: 10px;color: 24px;">{{ item.value }}</span>
                        </span>
                    </li>
                </ul>
                <div class="content_top" v-if="userStore.userInfo.UnitType == 3">
                    <div class="content_top_left div_shadow">
                        <div class="headerDiv">业务操作导航</div>
                        <ul>
                            <li v-for="item in BusinessNavList" :key="item.id" @click="BusinessNavClick(item.url)">
                                <SvgIcon name="xiangyouanniu" class="svg-icon" color="#333" width="20px" height="20px">
                                </SvgIcon>
                                {{ item.label }}
                            </li>
                        </ul>
                    </div>
                    <div class="content_top_right div_shadow">
                        <div class="headerDiv">校服订购信息</div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>校服种类</th>
                                        <th>订购总人数</th>
                                        <th>订购总套数</th>
                                        <th>订购总金额（元）</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(item, index) in orderList" :key="index">
                                        <td style="width: 140px;">{{ item.Category }}</td>
                                        <td style="width: 140px;">{{ item.PersonTotal }}</td>
                                        <td style="width: 140px;">{{ item.NumTotal }}</td>
                                        <td style="width: 180px;text-align: right;">
                                            {{ formatNumberWithCommas(item.AmountTotal) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="content_top" v-else>
                    <div class="content_top_left div_shadow">
                        <div class="headerDiv">校服选用情况</div>
                        <div ref="echartSelectedRef" class="echart_countycity"></div>
                    </div>
                    <div class="content_top_right div_shadow">
                        <div class="headerDiv">校服备案情况</div>
                        <div ref="echartFilingdRef" class="echart_countycity"></div>

                    </div>
                </div>
                <div class="content_middle">
                    <div class="content_middle_left">
                        <div class="content_middle_item div_shadow">
                            <div class="headerDiv">采购数量比例</div>
                            <div ref="echartNumRef" class="echart_item"></div>
                        </div>
                        <div class="content_middle_item div_shadow">
                            <div class="headerDiv">采购金额比例</div>
                            <div ref="echartAmountRef" class="echart_item"></div>
                        </div>
                    </div>
                    <div class="content_middle_right">
                        <div class="content_middle_item div_shadow" v-if="userStore.userInfo.UnitType == 3">
                            <div class="headerDiv">校服选用结果</div>
                            <div ref="echartSelectRef" class="echart_item"></div>
                        </div>
                        <div class="content_middle_item div_shadow" v-else>
                            <div class="headerDiv">订购人数分布</div>
                            <div ref="echartPeopleRef" class="echart_item"></div>
                        </div>
                        <div class="content_middle_item div_shadow">
                            <div class="headerDiv">校服评价结果</div>
                            <div ref="echartEvaluationRef" class="echart_item"></div>
                        </div>
                    </div>
                </div>
                <div class="content_bottom">
                    <div class="content_bottom_item div_shadow">
                        <div class="section_footer">
                            <div class="horizontal-timeline">
                                <div class="timeline-item">
                                    <div class="timeline-top">项目启动</div>
                                    <div class="timeline-point"></div>
                                    <div class="timeline-bottom">项目启动</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-top">项目启动</div>
                                    <div class="timeline-point"></div>
                                    <div class="timeline-bottom">需求分析</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-top">项目启动</div>
                                    <div class="timeline-point"></div>
                                    <div class="timeline-bottom">开发阶段</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-top">项目启动</div>
                                    <div class="timeline-point"></div>
                                    <div class="timeline-bottom">项目交付</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="container_right">
                <div class="right_content1  div_shadow">
                    <div style="height: 40px; display: flex;align-items: center;justify-content: center;">
                        <el-select v-model="year" placeholder="年度" style="width: 80%">
                            <el-option v-for="item in yearDateList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </div>
                </div>
                <div class="right_content2  div_shadow">
                    <div style="height:320px;width: 100%; margin: 10px 0;">
                        <el-calendar v-model="calendarVal" />
                    </div>
                </div>
                <div class="right_content3  div_shadow">
                    <div style="height: 370px;"></div>

                </div>
            </div>
        </div>

    </div>
</template>
<style lang="scss" scoped>
/* 调整整体容器大小 */
:deep(.el-calendar__body) {
    width: 280px;
    /* 自定义宽度 */
    height: 280px;
    /* 自定义高度 */
    padding: 0;
    margin: 0 auto;
}

/* 调整日历单元格大小 */
:deep(.el-calendar-table td) {
    // padding: 2px !important;
}

/* 调整日期数字字体大小 */
:deep(.el-calendar-day) {
    font-size: 14px;
    /* 调整日期字体大小 */

    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 24px;

}

/* 调整头部月份导航栏 */
:deep(.el-calendar__header) {
    padding: 4px 10px;
    /* 调整顶部月份栏高度 */
    // display: none;
}

:deep(.el-calendar-table thead th) {
    padding: 5px 0;
}

.homebody {
    // width: 100%;
    // height: 100%;
    background: #FAFCFF;
}

.container {
    display: flex;
}

.headerDiv {
    height: 36px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

ul {

    list-style-type: none;
    padding: 0;
    margin: 0;

    li {
        list-style-type: none;

    }
}

.container_left {
    width: 1280px;
    flex-shrink: 0;
    margin-right: 20px;

    .navUi {
        width: 100%;
        display: flex;
        justify-content: space-between;
        gap: 1rem;

        li {
            width: 20%;
            height: 60px;
            cursor: pointer;

            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 16px;
            // font-weight: bold;
            padding: 0 20px;
        }
    }


    .content_top {
        width: 100%;
        margin: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;

        .content_top_left,
        .content_top_right {
            width: 50%;
            height: 240px;
        }

        .content_top_left {
            ul {
                padding: 10px;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                align-items: center;

                li {
                    width: 30%;
                    flex-shrink: 0;
                    padding: 16px 0;
                    display: flex;
                    align-items: center;
                    padding-left: 10px;
                    cursor: pointer;

                }

                li:hover {
                    color: var(--el-color-primary);
                    text-decoration: underline;
                }
            }
        }
    }

    .content_middle {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;
        margin-bottom: 10px;

        .content_middle_left,
        .content_middle_right {
            width: 50%;
            height: 308px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
        }

        .content_middle_item {
            width: 50%;
            height: 100%;
        }

    }

    .content_bottom {
        width: 100%;

        .content_bottom_item {
            // width: 50%;

            height: 110px;
        }
    }

}

.container_right {
    width: 380px;
    flex-shrink: 0;
    // width: 100%;
}

.emptyMsg {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    color: #999;
}


.home_title {
    padding: 10px 0px;
    background-color: #FAFAFA !important;

}

.article {
    width: 100%;
    height: 240px;
    margin-bottom: 10px;
}


.div_shadow {
    background: #F2F6FC;
    background: #ffffff;
    border-radius: 5px;
    box-shadow: 0 0px 4px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0.25rem 0.5rem rgb(0 0 0 / 10%);


}

.echart_item {
    width: 260px;
    height: 260px;
    margin: 6px auto;
}

.echart_countycity {
    width: 100%;
    height: 200px;
}

.fontcolor {
    color: #F56C6C;
}


table {
    width: 580px;
    margin: 0 auto;
    border-collapse: collapse;

    thead tr {
        height: 42px;
        border: 1px solid rgba(125, 180, 248, 0.3) !important;
        border-radius: 5px;
    }


    td {
        text-align: center;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;

    }

    th {
        text-align: center;
        font-size: 16px;
        color: #000000;
        font-weight: 400;
    }
}

svg {
    padding-right: 10px;
}



/* 水平时间线样式 */
.horizontal-timeline {
    position: relative;
    height: 110px;
    // margin: 50px 0;
    display: flex;
    justify-content: space-around;
}

.horizontal-timeline::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #4facfe, #00f2fe);
    transform: translateY(-50%);
}

.timeline-item {
    position: relative;
    width: 120px;
    text-align: center;
}

.timeline-point {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background: #fff;
    border: 3px solid #4facfe;
    border-radius: 50%;
    z-index: 2;
}

.timeline-top {
    margin-top: 10px;
    font-size: 14px;
    color: #555;
}

.timeline-bottom {
    margin-top: 40px;
    font-size: 14px;
    color: #555;
}

.vertical-point {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: #fff;
    border: 3px solid #00f2fe;
    border-radius: 50%;
    z-index: 2;
}

/* 动画效果 */
.timeline-point {
    transition: all 0.3s ease;
}

.timeline-point:hover {
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 10px rgba(0, 242, 254, 0.8);
}
</style>
