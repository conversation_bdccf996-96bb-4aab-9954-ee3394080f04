<script setup>
defineOptions({
    name: 'dangerchemicalswastereportlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, EditPen
} from '@element-plus/icons-vue'
import {
    UploadPostfile, DcWasteDisposalAuditFind, DcWasteDisposalGetById, DcWasteDisposalReport
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { fileDownload } from "@/utils/index.js";//上传附件
import { Getpagedbytype, AttachmentUpload } from '@/api/user.js'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const batchNoList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const RegDatele = ref()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Id", SortType: "DESC" }] })
const dialogVisible = ref(false)
const formData = ref({})
const refForm = ref()
const ruleForm = {
    CompanyName: [
        { required: true, message: '处置企业名称', trigger: 'change' },
    ],
    DisposalDate: [
        { required: true, message: '请选择处置时间', trigger: 'change' },
    ],
}
const options = ref([
    { value: 'BatchNo', label: '处置批次', },
    { value: 'UserName', label: '申请人', }
])
const filtersKey = ref('')
const filtersValue = ref('BatchNo')
//加载数据
onMounted(() => {
    GetpagedbytypeUser()

    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})
const wasteId = ref('')
const wasteNUm = ref(1)
// 填报
const HandleEdit = (row, e) => {
    dialogVisible.value = true
    wasteId.value = row.Id
    wasteNUm.value = e
    if (e == 1) {
        formData.value = {}
        nextTick(() => {
            refForm.value.resetFields()
        })
    } else {
        DcWasteDisposalGetById({ Id: row.Id }).then(res => {
            if (res.data.flag == 1) {
                const { rows } = res.data.data
                formData.value = rows
                //置空附件
                formData.value.ProcessFilePath = undefined;
                formData.value.ProcessFileTitle = undefined;
                formData.value.ProcessFileExt = undefined;
                formData.value.ProcessImgPath = undefined;
                formData.value.ProcessImgTitle = undefined;
                formData.value.ProcessImgExt = undefined;

                //处理附件
                if (rows.ProcessFile && rows.ProcessFile.includes("|")) {
                    //解析出扩展名和名称，路径
                    const fileStrings = rows.ProcessFile.split("|");
                    formData.value.ProcessFilePath = fileStrings[0];
                    formData.value.ProcessFileTitle = fileStrings[1];
                    formData.value.ProcessFileExt = "." + fileStrings[1].split(".").pop();
                }
                if (rows.ProcessImg && rows.ProcessImg.includes("|")) {
                    //解析出扩展名和名称，路径
                    const fileStrings = rows.ProcessImg.split("|");
                    formData.value.ProcessImgPath = fileStrings[0];
                    formData.value.ProcessImgTitle = fileStrings[1];
                    formData.value.ProcessImgExt = "." + fileStrings[1].split(".").pop();
                }
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    }

}
const HandleDetail = (row) => {
    router.push({
        path: "./detaillist", query: {
            Id: row.Id,
            batchNo: row.BatchNo,
            path: '/dangerchemicals/waste/reportlist'
        }
    })

}
const regDategeChange = (val) => {
    if (!val) filters.value.RegDatege = undefined
    HandleTableData()
}
const regDateleChange = (val) => {
    if (val) {
        filters.value.RegDatele = val + " 23:59:59"
    } else {
        filters.value.RegDatele = undefined
    }
    HandleTableData()
}
// 列表
const HandleTableData = () => {
    filters.value.BatchNo = undefined;
    filters.value.UserName = undefined;
    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }
    DcWasteDisposalAuditFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filtersKey.value = ''
    filters.value.SourceType = undefined
    filters.value.Statuz = undefined
    filters.value.RegDatege = undefined
    filters.value.RegDatele = undefined
    RegDatele.value = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
//提交
const HandleSubmit = () => {
    let paraData = {
        id: wasteId.value,
        CompanyName: formData.value.CompanyName,
        DisposalDate: formData.value.DisposalDate,
        DisposalRemark: formData.value.DisposalRemark,
        processFile: formData.value.ProcessFile,//附件1
        processImg: formData.value.ProcessImg,//附件2
    }
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        DcWasteDisposalReport(paraData).then((res) => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '处置成功')
                dialogVisible.value = false
                HandleTableData()
            } else {
                ElMessage.error(res.data.msg)
            }
        })
    })
}

const filtersChange = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}

// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
    Getpagedbytype({ moduleType: 12 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            uploadFileData.value = rows || []
            if (uploadFileData.value.length > 0) {
                uploadFileData.value.forEach(item => {
                    item.UploadFileTypeAccept = item.UploadFileType.split('.').join(',.')
                })
            }
            console.log('uploadFileData.value', uploadFileData.value)
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

//附件上传
const uploadFileData = ref([])
const fileFile = ref()
const uploadRef = ref()

// 导入前校验
const beforeAvatarUpload = (item, file) => {
    console.log("----2025-07-16 14:03:46--item--:", item);
    console.log(file);
    fileFile.value = file
    let str = file.name.split('.')[1]
    let name = str.toLowerCase();
    let arr = item.UploadFileType.split('.')
    let ary = arr.filter(t => t != '')
    const extension = ary.includes(name)
    if (!extension) {
        ElMessage({
            message: `上传文件只能是${item.UploadFileType}格式!`,
            type: 'error'
        })
    }
    // // 校验文件大小
    let FileSize = item.FileSize
    if (item.FileSize == 0) {
        FileSize = 10
    }
    const isSize = file.size / 1024 / 1024 < FileSize;
    if (!isSize) {
        ElMessage({
            message: `文件大小不能超出${FileSize}M`,
            type: 'error'
        })
    }
    return extension && isSize
}
// 附件上传
const httpRequest = (item, index) => {
    AttachmentUpload({ file: fileFile.value, filecategory: Number(item.FileCategory) }).then(res => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '上传成功')
            const { rows } = res.data.data

            tableData.value[index].MsdsFilePath = rows[0].Path;
            tableData.value[index].MsdsFileTitle = rows[0].Title;
            tableData.value[index].Ext = rows[0].Ext;
            tableData.value[index].MsdsFile = rows[0].Path + "|" + rows[0].Title + rows[0].Ext;

            if (Number(item.FileCategory) == 2982) {
                formData.value.ProcessFilePath = rows[0].Path;
                formData.value.ProcessFileTitle = rows[0].Title + rows[0].Ext;
                formData.value.ProcessFileExt = rows[0].Ext;
                formData.value.ProcessFile = rows[0].Path + "|" + rows[0].Title + rows[0].Ext;
            }
            if (Number(item.FileCategory) == 2983) {
                formData.value.ProcessImgPath = rows[0].Path;
                formData.value.ProcessImgTitle = rows[0].Title + rows[0].Ext;
                formData.value.ProcessImgExt = rows[0].Ext;
                formData.value.ProcessImg = rows[0].Path + "|" + rows[0].Title + rows[0].Ext;
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)

// 附件图片预览与文件下载
const lookFileListDownload = (filePath, ext, title) => {
    viewPhotoList.value = [filePath];
    ext = (typeof ext === "string") ? ext.toLowerCase() : "";
    if (ext == ".png" || ext == ".jpg" || ext == ".jpng") {
        showViewer.value = true;
        viewPhotoList.value.forEach((item, index) => {
            if (filePath == item) {
                imgSrcIndex.value = index;
            }
        });
        // //大图预览从点击的那张开始
        let tempImgList = [...viewPhotoList.value];
        let temp = [];
        for (let i = 0; i < imgSrcIndex.value; i++) {
            temp.push(tempImgList.shift());
        }
        viewPhotoList.value = tempImgList.concat(temp);
    } else {
        fileDownload(filePath, title)
    }
}
// 限制开始日期不能晚于结束日期
const disabledStartDate = (time) => {
    if (!RegDatele.value) return false;
    return time >= new Date(RegDatele.value + ' 23:59:59');
};

// 限制结束日期不能早于开始日期
const disabledEndDate = (time) => {
    if (!filters.value.RegDatege) return false;
    return time < new Date(filters.value.RegDatege + ' 00:00:00');
};
</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    待处置信息填报 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li>只有填报处置信息后，危废物或危化品才能处置完毕；</li>
                    <li>填报的处置信息是后期主管部门检查的重要依据。</li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-date-picker v-model="filters.RegDatege" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" clearable placeholder="申请时间"
                            @change="regDategeChange" style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="flexItem" label="至">
                        <el-date-picker v-model="RegDatele" type="date" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                            :disabled-date="disabledEndDate" clearable placeholder="申请时间" @change="regDateleChange"
                            style="width: 180px;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.SourceType" filterable clearable placeholder="来源"
                            style="width: 160px" @change="filtersChange">
                            <el-option v-for="item in batchNoList" :key="item.PurchaseBatchNo"
                                :label="item.PurchaseBatchNo" :value="item.PurchaseBatchNo" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Statuz" filterable clearable placeholder="状态" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in batchNoList" :key="item.PurchaseBatchNo"
                                :label="item.PurchaseBatchNo" :value="item.PurchaseBatchNo" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                            class="input-with-select">
                            <template #prepend>
                                <el-select v-model="filtersValue" style="width: 120px">
                                    <el-option v-for="item in options" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>

                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="BatchNo" label="处置批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="UserName" label="申请人" min-width="120" align="center"></el-table-column>
            <el-table-column prop="RegDate" label="申请时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.RegDate ? row.RegDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="DisposalDate" label="处置时间" min-width="120" align="center">
                <template #default="{ row }">
                    {{ row.DisposalDate ? row.DisposalDate.substring(0, 10) : '--' }}
                </template>
            </el-table-column>
            <el-table-column prop="SourceType" label="来源" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.SourceType == 1 ? '危废物' : row.SourceType == 2 ? '危化品' : '' }}
                </template>
            </el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="120" align="center">
                <template #default="{ row }">
                    <el-tooltip v-if="row.Statuz == 1 || row.Statuz == 2" class="item" effect="dark"
                        :content="'审核意见：' + row.AuditRemark || '无'" placement="top">
                        <span :style="{ color: row.Statuz == 1 ? 'green' : 'red' }">
                            {{ row.Statuz == 1 ? '审核通过' : '审核不通过' }}
                        </span>
                    </el-tooltip>
                    <span v-else :style="{ color: row.Statuz == 3 ? 'green' : '' }">
                        {{ row.Statuz == 3 ? '完成处置' : '待审核' }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="明细清单" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="处置信息" width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 3" type="primary" link @click="HandleEdit(row, 2)">查看</el-button>
                    <el-button v-else-if="row.Statuz == 1" type="primary" link
                        @click="HandleEdit(row, 1)">填报</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :width="680" :lazy="true" :title="wasteNUm == 1 ? '处置信息填报' : '处置信息修改'">
            <template #content>
                <el-form style="min-width: 320px; " class="mobile-box" @submit.prevent ref="refForm" :model="formData"
                    :rules="ruleForm" label-width="160px" status-icon>
                    <el-form-item label="处置企业名称：" prop="CompanyName">
                        <el-input v-model="formData.CompanyName" clearable style="width: 80%"></el-input>
                    </el-form-item>
                    <el-form-item label="处置时间：" prop="DisposalDate">
                        <el-date-picker v-model="formData.DisposalDate" type="date" format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" clearable placeholder="现场处置时间" style="width: 80%;">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="备注：">
                        <el-input type="textarea" v-model="formData.DisposalRemark" style="width: 80%"></el-input>
                    </el-form-item>
                    <!-- 附件上传 -->
                    <el-form-item v-for="(item, index) in uploadFileData" :key="index">
                        <template #label>
                            <span v-if="item.IsFilled == 1" style="color: #F56C6C;">*</span>
                            <el-tooltip class="item" effect="dark" :content="item.Memo" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> {{ item.Name }}： </span>
                        </template>
                        <el-upload v-if="!Id" ref="uploadRef" class="upload-demo" :show-file-list="false"
                            :accept="item.UploadFileTypeAccept" :before-upload="beforeAvatarUpload.bind(null, item)"
                            :http-request="httpRequest.bind(null, item, index)">
                            <el-button type="success" size="small" :icon="UploadFilled">上传</el-button>
                        </el-upload>
                        <div class="fileFlex">
                            <div v-if="item.FileCategory == 2982 && formData.ProcessFile"
                                style="color:#409EFF ;width:200px">
                                <span style="cursor: pointer;"
                                    @click="lookFileListDownload(formData.ProcessFilePath, formData.ProcessFileExt, formData.ProcessFileTitle)">
                                    {{ formData.ProcessFileTitle }}
                                </span>
                            </div>
                            <div v-else-if="item.FileCategory == 2983 && formData.ProcessImg"
                                style="color:#409EFF ;width:200px">
                                <span style="cursor: pointer;"
                                    @click="lookFileListDownload(formData.ProcessImgPath, formData.ProcessImgExt, formData.ProcessImgTitle)">
                                    {{ formData.ProcessImgTitle }}
                                </span>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 确认 </el-button>
                </span>
            </template>
        </app-box>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
</template>
<style lang="scss" scoped></style>