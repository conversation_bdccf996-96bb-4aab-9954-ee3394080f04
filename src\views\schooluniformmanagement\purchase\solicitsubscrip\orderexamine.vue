<script setup>
defineOptions({
  name: 'solicitsubscriporderexamine'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search
} from '@element-plus/icons-vue'
import {
  Punitgetschoolbycountyid,
} from '@/api/user.js'
import {
  PurchaseGetorderpaged
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import router from '@/router'
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
// 表格初始化
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const CountyList = ref([])
const SchoolList = ref([])
const SupplierList = ref([])
const SubscriptionStatuzList = ref([])

//加载数据
onMounted(() => {
  yearDateList.value = previousYearDate()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})
//查看
const HandleDetail = (row) => {
  router.push({ path: "./orderdetail", query: { id: row.Id } })
}
const CountyChange = (e) => {
  if (!e) {
    filters.value.SchoolId = undefined
  }
  HandleTableData()
  PunitgetschoolbycountyidUser(e)
}
// 选择区县获取学校
const PunitgetschoolbycountyidUser = (id) => {
  Punitgetschoolbycountyid({ CountyId: id }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      filters.value.SchoolId = undefined
      SchoolList.value = rows || []//学校名称
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  PurchaseGetorderpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        SupplierList.value = other.SupplierList || [];//供应商
        SubscriptionStatuzList.value = other.SubscriptionStatuzList || [];//征订状态
        if (hUnitType.value == 1) {
          CountyList.value = other.CountyList || []//区县名称
        }
        if (hUnitType.value == 2 || hUnitType.value == 4) {
          SchoolList.value = other.SchoolList || []//学校名称
        }
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = (page) => {
  filters.value.pageIndex = 1
  filters.value.PurchaseYear = undefined
  filters.value.CountyId = undefined
  filters.value.SchoolId = undefined
  filters.value.CompanyId = undefined
  filters.value.SubscriptionStatuz = undefined
  filters.value.Name = undefined
  if (hUnitType.value == 1) {
    SchoolList.value = []
  }
  HandleTableData()
}
// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称" style="width: 160px"
              @change="CountyChange">
              <el-option v-for="item in CountyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.UnitId" :label="item.UnitName" :value="item.UnitId" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 2 || hUnitType == 4">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1 || hUnitType == 2 || hUnitType == 3">
            <el-select v-model="filters.CompanyId" clearable filterable placeholder="供应商" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SupplierList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.SubscriptionStatuz" clearable placeholder="征订状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SubscriptionStatuzList" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="合同批次" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="PurchaseYear" label="年度" min-width="100" align="center"></el-table-column>
      <el-table-column prop="CountyName" label="区县名称" min-width="120" v-if="hUnitType == 1"></el-table-column>
      <el-table-column prop="SchoolName" label="学校名称" min-width="160"
        v-if="hUnitType == 1 || hUnitType == 2 || hUnitType == 4"></el-table-column>
      <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
      <el-table-column prop="OrderNum" label="需订购人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="OrderedNum" label="已订购人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="SubscriptionDeadline" label="征订截止时间" min-width="120" align="center">
        <template #default="{ row }">
          <span v-if="!row.SubscriptionDeadline">--</span>
          <span v-else-if="new Date(row.SubscriptionDeadline).setHours(23, 59, 59, 0) < new Date()"
            style="color: #F56C6C;">{{
              row.SubscriptionDeadline.substring(0, 10) }}</span>
          <span v-else>{{ row.SubscriptionDeadline.substring(0, 10) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="SupplierName" label="供应商" min-width="200" v-if="hUnitType != 4"></el-table-column>
      <el-table-column prop="SubscriptionStatuz" label="征订状态" min-width="100" align="center">
        <template #default="{ row }">
          <!-- （0：填报中 10：正在征订 100：征订结束） -->
          {{ row.SubscriptionStatuz == 0 ? '填报中' : row.SubscriptionStatuz == 10 ? '正在征订' : '征订结束' }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="征订单明细" min-width="100" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
  </div>
</template>
<style lang="scss" scoped></style>