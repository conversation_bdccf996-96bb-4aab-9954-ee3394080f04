<script setup>
defineOptions({
    name: 'dangerchemicalsstandbookwaste'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, DocumentCopy, Position
} from '@element-plus/icons-vue'
import {
    DcStandbookWasteFind
} from '@/api/dangerchemicals.js'
import { ExcelDownload } from "@/utils/index.js"
import {
    ExportDcStandbookWasteFind
} from '@/api/directdata.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页 
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 50, sortModel: [{ SortCode: "Path", SortType: "ASC" }] })
//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
    }
    HandleTableData()
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
        }
    })
})

// 分页
const handlePage = (val) => {
    HandleTableData()
}

// 列表
const HandleTableData = () => {
    DcStandbookWasteFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const HandlePrint = (e) => {
    router.push({
        path: "./wasteprint"
    })
}
//导出
const HandleExport = () => {
    ExportDcStandbookWasteFind(filters.value).then(res => {
        ExcelDownload(res)
    });
} 
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="DocumentCopy" @click="HandlePrint"
                            :disabled="tableData.length == 0">打印</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="Position" :disabled="tableData.length == 0"
                            @click="HandleExport">导出</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" id="printArea" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="OneClassName" label="一级分类" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Name" label="二级分类" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitsMeasurement" label="单位" min-width="110" align="center"> </el-table-column>
            <el-table-column prop="WaitNum" label="待处置数量" min-width="110" align="right"></el-table-column>
            <el-table-column prop="DisposalNum" label="已处置数量" min-width="110" align="right"></el-table-column>

            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
    </div>
</template>
<style lang="scss" scoped></style>