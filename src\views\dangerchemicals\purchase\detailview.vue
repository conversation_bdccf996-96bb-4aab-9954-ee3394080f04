<script setup>
defineOptions({
  name: 'dangerchemicalspurchasedetailview'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search
} from '@element-plus/icons-vue'

import {
  DcPurchaseEndGetById
} from '@/api/dangerchemicals.js'

import { ElMessageBox, ElMessage } from 'element-plus'
import { tagsListStore, fileDownload } from "@/utils/index.js";
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores'

const route = useRoute()
const userStore = useUserStore()
const purchase = ref({})
const approvalList = ref([])
const activeNames = ref([])

//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    HandleViewData()
  }

})
onActivated(() => {
  tagsListStore(userStore.tagsList, route)
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleViewData()
    }
  })
})


// 详情
const HandleViewData = () => {

  DcPurchaseEndGetById({ id: route.query.Id }).then(res => {
    if (res.data.flag == 1) {

      purchase.value = res.data.data.rows;
      approvalList.value = res.data.data.other;

      //
      activeNames.value = approvalList.value.map((item) => item.Id)
      activeNames.value.push(purchase.value.Id)

    } else {
      ElMessage.error(res.data.msg)
    }
  })
}


const HandleDetailView = () => {
  // console.log("purchase.Id:",purchase.Id);
  // console.log("purchase.BatchNo:",purchase.BatchNo);
  router.push({
    path: "/dangerchemicals/purchase/detaillist", query: {
      Id: purchase.value.Id,
      batchNo: purchase.value.BatchNo
    }
  })

}

const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileClick = (e) => {
  let index = e[1].lastIndexOf('.');
  let Ext = e[1].substring(index + 1);

  if (Ext == "png" || Ext == "jpg" || Ext == "jpng") {
    showViewer.value = true;
    // viewPhotoList.value = ['http://localhost:9291'+e[0]]

  } else {
    fileDownload('http://localhost:9291' + e[0], e[1])
  }

}


</script>


<template>
  <div class="demo-collapse">
    <el-collapse v-model="activeNames">
      <el-collapse-item title="采购信息" :name="purchase.Id">

        <template #title>
          <div style="padding-left: 10px;">
            采购信息
          </div>
        </template>
        <div class="content">
          <div class="content_label_div"><span class="content_label">采购批次：</span>{{ purchase.BatchNo }}</div>
          <div><span class="content_label">参考金额：</span>
            {{ purchase.Amount ? '￥' + Number(purchase.Amount).toFixed(2) : '--' }}
          </div>
          <div><span class="content_label">危化品清单：</span>
            <el-button :icon="Search" @click="HandleDetailView">查看</el-button>
          </div>
          <div><span class="content_label">申请人：</span>{{ purchase.UserName }} </div>
          <div><span class="content_label">申请时间：</span>
            {{ purchase.RegDate ? purchase.RegDate.substring(0, 10) : '--' }}</div>
        </div>
      </el-collapse-item>


      <el-collapse-item v-for="(item, index) in approvalList" :key="index" :name="item.Id">

        <template #title>
          <div style="padding-left: 10px;">
            {{ item.ProcessNumber == 10 ? '采购审核' : '采购审批' }}
          </div>
        </template>
        <div class="content">
          <div class="content_label_div"><span class="content_label">审核结果：</span>
            {{ item.ApprovalStatuz % 10 == 1 ? "不通过" : "通过" }}
          </div>
          <div class="content_label_div"><span class="content_label">审核意见：</span>{{ item.ApprovalRemark }}
          </div>
          <div><span class="content_label">审核人：</span>{{ item.AuditUserName }}</div>
          <div><span class="content_label">审核日期：</span>
            {{ item.RegTime ? item.RegTime.substring(0, 10) : '--' }}</div>
          <div class="content_label_div" v-if="item.ProcessNumber == 10"><span class="content_label">公安审批文件：</span>
            <span v-if="item.SecurityApprovalFile" @click="fileClick(item.SecurityApprovalFile.split('|'))">
              {{ item.SecurityApprovalFile.split('|')[1] }}
            </span>
            <span v-else>--</span>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
  </div>
</template>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-wrap: wrap;
  color: #333;
  font-size: 14px;
  width: 640px;
  padding-left: 30px;

  div {
    width: 300px;
    padding: 5px 10px;
  }

  .content_label_div {
    width: 600px;
  }

  .content_label {
    width: 200px;
    text-align: right;
    font-weight: bold;
  }

}

:deep(.el-collapse-item__header) {

  font-size: 14px;
  color: #0000ee;
}
</style>