<script setup>
defineOptions({
    name: 'dangerchemicalsapplyusernumstatistics'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search
} from '@element-plus/icons-vue'
import {
    DccatalogGetClassTwo, DcApplyUserNumStatisticsFind, GetDcApplyStatisticsYear, GetDcApplyStatisticsUser
} from '@/api/dangerchemicals.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const userList = ref([])
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ sortModel: [{ SortCode: "ClassOneId", SortType: "ASC" }] })

//加载数据
onMounted(() => {
    HandleTableData()
    DccatalogGetClassTwoUser()
    GetDcApplyStatisticsUserUser()
    GetDcApplyStatisticsYearUser()
    if (route.query.isTagRouter) {
    }
})

//  列表
const HandleTableData = () => {
    if (yearz.value?.length > 0) {
        filters.value.years = yearz.value.join(',')
    } else {
        filters.value.years = undefined
    }
    DcApplyUserNumStatisticsFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 搜索
const HandleSearch = () => {
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.ClassTwoId = undefined
    yearz.value = []
    HandleTableData()
}
const StatuzSolicitedList = ref([])
const yearzList = ref([])
const yearz = ref([])
// 危化品分类
const DccatalogGetClassTwoUser = () => {
    DccatalogGetClassTwo({ unitId: 0 }).then((res) => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            StatuzSolicitedList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取领用人
const GetDcApplyStatisticsUserUser = () => {
    GetDcApplyStatisticsUser().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            userList.value = rows.data || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 获取年份
const GetDcApplyStatisticsYearUser = () => {
    GetDcApplyStatisticsYear().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            yearzList.value = rows.data
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const filtersChange = () => {
    HandleTableData()
} 
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-select v-model="yearz" multiple placeholder="年度" @change="filtersChange"
                            style="min-width: 160px">
                            <el-option v-for="item in yearzList" ::key="item.Yearz" :label="item.Yearz"
                                :value="item.Yearz" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.ClassTwoId" clearable placeholder="危化品分类" style="width: 160px"
                            @change="filtersChange">
                            <el-option v-for="item in StatuzSolicitedList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" max-height="580" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="ClassTwoName" label="危化品分类" min-width="120" align="center"
                show-overflow-tooltip></el-table-column>
            <el-table-column prop="DeviceName" label="危化品名称" min-width="140" show-overflow-tooltip></el-table-column>
            <el-table-column prop="Model" label="规格属性" min-width="160" show-overflow-tooltip></el-table-column>
            <el-table-column prop="UnitName" label="单位" min-width="90" align="center"></el-table-column>
            <el-table-column v-for="item in userList" :key="item.Id" :label="item.Name" min-width="120" align="right">
                <template #default="{ row }">
                    {{ row[item.Name] }}
                </template>
            </el-table-column>
            <el-table-column prop="SumYear" label="小计" min-width="140" align="right">
                <template #default="{ row }">
                    {{ row['小计'] }}
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<style lang="scss" scoped></style>