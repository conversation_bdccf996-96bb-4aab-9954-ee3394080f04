<template>
    <div class="flex flex-col border border-br">
        <Toolbar class="border-b border-br" :editor="editorRef" :mode="mode" :defaultConfig="toolbarConfig" />
        <Editor class="flex-1 overflow-y-auto" :mode="mode" :defaultConfig="editorConfig" v-model="editorVal"
            @onCreated="handleCreated" @onChange="handleChange" @onFocus="handleFocus" />
    </div>
</template>

<script setup name="wngEditor">
import '@wangeditor/editor/dist/css/style.css';
import { reactive, shallowRef, ref, watch, nextTick, onBeforeUnmount, onMounted } from 'vue';
// @ts-ignore
import { Toolbar, Editor } from '@wangeditor/editor-for-vue';

// 定义父组件传过来的值
const props = defineProps({
    // 是否禁用
    disable: {
        type: Boolean,
        default: () => false,
    },
    // 内容框默认 placeholder
    placeholder: {
        type: String,
        default: () => '请输入内容...',
    },
    //编辑回显内容
    defaultHtml: {
        type: String,
        default: () => '',
    },
    mode: {
        type: String,
        default: () => 'default',
    },
    // 高度
    height: {
        type: String,
        default: () => '310',
    },
    // 宽度
    width: {
        type: String,
        default: () => 'auto',
    },
    // 双向绑定，用于获取 editor.getHtml() 包含样式的
    getHtml: {
        type: String,
        default: () => '<p><br></p>',
    },
    // 双向绑定，用于获取 editor.getText() 存文字
    getText: String,
    //文件上传路径
    uploadFileUrl: {
        type: String,
        default: `/admin/sys-file/upload`,
    },
});
const isFirefox = navigator.userAgent.includes('Firefox')
// 定义子组件向父组件传值/事件
const emit = defineEmits(['editorChange', 'update:getHtml', 'update:getText']);

const editorRef = shallowRef();
//工具栏配置
const toolbarConfig = ref({
    excludeKeys: [
        "group-image",
        "group-video",
        'emotion',
        'insertLink'
    ]
})


// const state = ref({
//     editorConfig: {
//         placeholder: props.placeholder,
//     },
//     editorVal: props.getHtml,
// }); 
const editorVal = ref('<p> </p>');
const editorConfig = ref({
    placeholder: '请输入内容...',
    autoFocus: !isFirefox, //  在 Firefox 中建议关闭自动聚焦
    scroll: true,
    MENU_CONF: {}
});

// Firefox 兼容性修复扩展
const useFirefoxFixExtension = () => {
    useExtension((editor) => {
        return {
            name: 'firefoxFix',
            onDestroy: () => {
                // 清理工作
            }
        }
    })
}
// 延迟设置内容以避免 Firefox 中的初始化问题
const delayedSetContent = (editor, html) => {
    console.log('delayed延迟设置内容以避免 Firefox 中的初始化问题延迟设置内容以避免 Firefox 中的初始化问题延迟设置内容以避免 Firefox 中的初始化问题SetContent');
    if (isFirefox) {
        setTimeout(() => {
            editor.setHtml(html)
        }, 100)
    } else {
        editor.setHtml(html)
    }
}

// 确保编辑器就绪的辅助函数
const ensureEditorReady = (editor) => {
    try {
        const children = editor.children
        if (!children || children.length === 0) {
            // 如果编辑器内容为空，插入默认内容
            editor.insertText('')
        }
    } catch (error) {
        console.warn('编辑器状态检查失败:', error)
    }
}
// 编辑器回调函数
const handleCreated = (editor) => {
    editorRef.value = editor;
    // Firefox 特定处理
    if (isFirefox) {
        // 延迟检查编辑器状态
        setTimeout(() => {
            ensureEditorReady(editor)
        }, 100)

        // 延迟设置内容
        setTimeout(() => {
            if (props.defaultHtml) {
                delayedSetContent(editor, props.defaultHtml)
            }

            // 添加输入事件监听器以处理 Firefox 中的汉字输入问题
            editor.getEditableContainer().addEventListener('input', (e) => {
                // 防止某些 Firefox 特定的 DOM 操作错误
            })
        }, 200)
    }
};
// 聚焦时的处理
const handleFocus = (editor) => {
    console.log('编辑器获得焦点')
    // 可以在这里添加 Firefox 特定的焦点处理
}
// 编辑器内容改变时
const handleChange = (editor) => {
    // console.log("编辑器内容改变时editor", editor, editor.getHtml())
    emit('update:getHtml', editor.getHtml());
    emit('update:getText', editor.getText());
    emit('editorChange', editor.getText(), editor.getHtml())
};

// 页面销毁时
onBeforeUnmount(() => {
    const editor = editorRef.value;
    if (editor == null) return;
    emit('update:getHtml', '');
    emit('update:getText', '');
    editorRef.value.destroy()
    editorRef.value = null
    // editor.destroy();
});

// 监听是否禁用改变
onMounted(() => {
    nextTick(() => {
        const editor = editorRef.value;
        if (editor == null) return;
        props.disable ? editor.disable() : editor.enable();
        //编辑回显
        setTimeout(() => {
            // state.value.editorVal = props.defaultHtml;
            editorVal.value = props.defaultHtml;
        }, 500);
    });
});

// 监听双向绑定值改变，用于回显
watch(
    () => props.getHtml,
    (val) => {
        // state.value.editorVal  = val;
        editorVal.value = val;
    },
    {
        deep: true,
    }
);
// // 监听默认内容变化
// watch(() => props.defaultHtml, (newVal) => {
//     if (editorRef.value) {
//         delayedSetContent(editorRef.value, newVal)
//     }
// })


</script>
<style lang="scss" scoped>
.flex.flex-col.border.border-br {
    border: 1px solid #ccc !important;

}

:deep(.w-e-bar.w-e-bar-show.w-e-toolbar) {
    border-bottom: 1px solid #ccc !important;
}

:deep(.w-e-scroll) {
    min-height: 300px !important;
    overflow: auto !important;
}
</style>