<script setup>
defineOptions({
  name: 'organizedetaillist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
  Refresh, Search
} from '@element-plus/icons-vue'
import {
  Punitgetschoolbycountyid, Getpagedbytype,
} from '@/api/user.js'
import {
  OrganizationGetpaged, OrganizationGeteditbyid, OrganizationConfirmfiling, OrganizationFilingbackout,
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import { previousYearDate, fileDownload } from "@/utils/index.js";
import { useUserStore } from '@/stores';
import { useRoute } from 'vue-router'
const route = useRoute()
const userStore = useUserStore()
const hUnitType = ref(userStore.userInfo.UnitType)
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const StatuzList = ref([])
const CountyList = ref([])
const SchoolList = ref([])
const uploadFileData = ref([])
const recordVisible = ref(false)
const detailVisible = ref(false)
const recordData = ref({})
const isRecord = ref(true)
const recordId = ref(0)
const detailData = ref({})
const refRecord = ref()
const ruleRecord = {
  statuz: [
    { required: true, message: '请选择是否通过审核', trigger: 'change' },
  ],
  explanation: [
    { required: true, message: '请输入退回原因', trigger: 'change' },
  ],
}

//加载数据
onMounted(() => {
  yearDateList.value = previousYearDate()
  GetpagedbytypeUser()
  if (route.query.isTagRouter) {
    HandleTableData(true);
  }
})
onActivated(() => {
  fileIdList.value = []
  nextTick(() => {
    if (!route.query.isTagRouter) {
      HandleTableData(true);
    }
  })
})

// 查看
const HandleDetail = (row) => {
  uploadFileData.value.forEach(item => {
    item.fileLChildList = []
    item.categoryList = []
  })
  OrganizationGeteditbyidUser(row.Id)
  detailVisible.value = true
}

// 审核||退回 
const HandleRecord = (row, e) => {
  isRecord.value = e
  recordId.value = row.XUniformOrganizationId
  recordVisible.value = true
}
//  备案||审核||退回 提交
const HandleRecordSubmit = (row) => {
  if (hUnitType.value == 2) {
    refRecord.value.validate((valid, fields) => {
      if (!valid && fields) return;
      if (isRecord.value) {
        OrganizationConfirmfilingUser()
      } else {
        OrganizationFilingbackoutUser()
      }
    })
      .catch((err) => {
        console.info(err)
      })
  }
}
// 选择区县
const CountyChange = (e) => {
  if (!e) {
    filters.value.SchoolId = undefined
  }
  HandleTableData()
  PunitgetschoolbycountyidUser(e)
}
// 根据模块类型获取备案附件信息 
const GetpagedbytypeUser = () => {
  Getpagedbytype({ moduleType: 104 }).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      uploadFileData.value = rows || []
      if (uploadFileData.value.length > 0) {
        uploadFileData.value.forEach(item => {
          item.fileLChildList = []
          item.categoryList = []
        })
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 选择区县获取学校
const PunitgetschoolbycountyidUser = (id) => {
  Punitgetschoolbycountyid({ CountyId: id }).then(res => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      filters.value.SchoolId = undefined
      SchoolList.value = rows || []//学校名称
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

// 审核
const OrganizationConfirmfilingUser = (id) => {
  OrganizationConfirmfiling({ id: recordId.value, statuz: recordData.value.statuz, explanation: recordData.value.explanation || undefined }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '提交审核成功')
      recordVisible.value = false
      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 退回
const OrganizationFilingbackoutUser = (id) => {
  OrganizationFilingbackout({ id: recordId.value, explanation: recordData.value.explanation }).then(res => {
    if (res.data.flag == 1) {
      ElMessage.success(res.data.msg || '退回成功')
      recordVisible.value = false
      HandleTableData()
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

//列表
const HandleTableData = (isFirst) => {
  filters.value.isFirst = isFirst
  OrganizationGetpaged(filters.value).then(res => {
    if (res.data.flag == 1) {
      const { rows, total, other } = res.data.data
      tableData.value = rows;
      tableTotal.value = Number(total)
      if (isFirst) {
        StatuzList.value = other.StatuzList || []//状态
        if (hUnitType.value == 1 || hUnitType.value == 2) {
          if (hUnitType.value == 1) {
            CountyList.value = other.CountyList || []//区县名称
          }
          if (hUnitType.value == 1 || hUnitType.value == 2) {
            SchoolList.value = other.SchoolList || []//学校名称
          }
        }
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  });
}
// 搜索
const HandleSearch = () => {
  filters.value.pageIndex = 1
  HandleTableData()
}
// 重置
const HandleReset = () => {
  filters.value.pageIndex = 1
  filters.value.OrganizationYear = undefined
  filters.value.Statuz = undefined
  filters.value.CountyId = undefined
  filters.value.SchoolId = undefined
  filters.value.Name = undefined
  if (hUnitType.value == 1) {
    SchoolList.value = []
  }
  HandleTableData()
}

// 分页
const handlePage = (val) => {
  HandleTableData()
}
const filtersChange = () => { HandleTableData() }

// 详情
const OrganizationGeteditbyidUser = (id) => {
  OrganizationGeteditbyid({ id: id }).then((res) => {
    if (res.data.flag == 1) {
      const { rows, footer } = res.data.data
      detailData.value = rows
      uploadFileData.value.forEach(item => {
        item.fileLChildList = []
        item.categoryList = []
      })
      let categoryList = footer || []
      if (categoryList.length > 0) {
        // 遍历数组 b 的每个元素
        categoryList.forEach(item => {
          // 查找数组 a 中具有相同 Id 和 FileCategory 的对象
          let match = uploadFileData.value.find(obj => obj.FileCategory == item.FileCategory);
          // 如果找到了匹配的对象，则将 item 添加到对应的 categoryList
          if (match) {
            match.categoryList.push(item);
          }
        });
      }
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

const fileIdList = ref([])
const showViewer = ref(false)
const viewPhotoList = ref([])
const imgSrcIndex = ref(0)
// 附件图片预览与文件下载
const fileListDownload = (e, imgList) => {
  // console.log(e)
  let path = e.Path;
  viewPhotoList.value = imgList.map(t => t.Path)
  if (e.Ext == ".png" || e.Ext == ".jpg" || e.Ext == ".jpeg") {
    showViewer.value = true;
    viewPhotoList.value.forEach((item, index) => {
      if (path == item) {
        imgSrcIndex.value = index;
      }
    });
    // //大图预览从点击的那张开始
    let tempImgList = [...viewPhotoList.value];
    let temp = [];
    for (let i = 0; i < imgSrcIndex.value; i++) {
      temp.push(tempImgList.shift());
    }
    viewPhotoList.value = tempImgList.concat(temp);
  } else {
    let title = e.Title + e.Ext
    fileDownload(e.Path, title)
  }
}
</script>
<template>
  <div class="viewContainer">
    <el-row class="navFlexBox">
      <el-col>
        <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.OrganizationYear" clearable placeholder="年度" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in yearDateList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.CountyId" clearable filterable placeholder="区县名称" style="width: 160px"
              @change="CountyChange">
              <el-option v-for="item in CountyList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 1">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.UnitId" :label="item.UnitName" :value="item.UnitId" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem" v-if="hUnitType == 2">
            <el-select v-model="filters.SchoolId" clearable filterable placeholder="学校名称" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in SchoolList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-select v-model="filters.Statuz" clearable placeholder="备案状态" @change="filtersChange"
              style="width: 160px">
              <el-option v-for="item in StatuzList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="" class="flexItem">
            <el-input v-model.trim="filters.Name" clearable placeholder="合同批次" style="width: 240px"></el-input>
          </el-form-item>
          <el-form-item class="flexItem">
            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
      header-cell-class-name="headerClassName">
      <el-table-column prop="OrganizationYear" label="年度" min-width="80" align="center"></el-table-column>
      <el-table-column prop="AreaName" label="区县名称" min-width="120" v-if="hUnitType == 1"></el-table-column>
      <el-table-column prop="SchoolName" label="学校名称" min-width="140"
        v-if="hUnitType == 1 || hUnitType == 2"></el-table-column>
      <!-- <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column> -->
      <el-table-column prop="TotalNum" label="总人数" min-width="100" align="center"></el-table-column>
      <el-table-column prop="ParentStudentRatio" label="家长和学生人数占比" min-width="120" align="center">
        <template #default="{ row }">
          {{ row.ParentStudentRatio }}%
        </template></el-table-column>
      <el-table-column prop="ParentNum" label="家长代表" min-width="90" align="center"></el-table-column>
      <el-table-column prop="StudentNum" label="学生代表" min-width="90" align="center"></el-table-column>
      <el-table-column prop="TeacherNum" label="教师代表" min-width="90" align="center"></el-table-column>
      <el-table-column prop="SchoolAdminNum" label="学校管理人员" min-width="110" align="center"></el-table-column>
      <el-table-column prop="OtherNum" label="其他人员" min-width="90" align="center"></el-table-column>
      <el-table-column label="组织管理" min-width="90" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="HandleDetail(row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="StatuzName" label="备案状态" min-width="90" align="center"></el-table-column>
      <el-table-column v-if="hUnitType == 2" fixed="right" label="备案操作" min-width="90" align="center">
        <template #default="{ row }">
          <el-button v-if="row.Statuz == 10" type="primary" link @click="HandleRecord(row, true)">审核</el-button>
          <el-button v-else-if="row.Statuz == 100 && row.IsFiling == 0" type="primary" link
            @click="HandleRecord(row, false)">退回</el-button>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="没有数据"></el-empty>
      </template>
    </el-table>
    <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
      @handleChange="handlePage" />
    <!-- 查看弹窗 -->
    <app-box v-model="detailVisible" :width="680" :lazy="true" title="组织管理">
      <template #content>
        <div class="detailDialog">
          <el-form @submit.prevent :model="detailData" label-width="180px" status-icon>
            <el-form-item label="区县名称：" v-if="hUnitType == 1">
              <span> {{ detailData.CountyName }}</span>
            </el-form-item>
            <el-form-item label="学校名称：" v-if="hUnitType == 1 || hUnitType == 2">
              <span> {{ detailData.SchoolName }}</span>
            </el-form-item>
            <el-form-item label="年度：">
              <span> {{ detailData.OrganizationYear }}</span>
            </el-form-item>
            <el-form-item label="家长代表（人）：">
              <span style="display: inline-block;width: 25%;">{{ detailData.ParentNum }}</span> <span
                style="color: #999;padding-left: 50px;">占比：{{ detailData.ParentNumRatio }}%</span>
            </el-form-item>
            <el-form-item label="学生代表（人）：">
              <span style="display: inline-block;width: 25%;">{{ detailData.StudentNum }}</span> <span
                style="color: #999;padding-left: 50px;">占比：{{ detailData.StudentNumRatio }}%</span>
            </el-form-item>
            <el-form-item label="教师代表（人）：">
              <span style="display: inline-block;width: 25%;">{{ detailData.TeacherNum }}</span> <span
                style="color: #999;padding-left: 50px;">占比：{{ detailData.TeacherNumRatio }}%</span>
            </el-form-item>
            <el-form-item label="学校管理人员（人）：">
              <span style="display: inline-block;width: 25%;">{{ detailData.SchoolAdminNum }}</span> <span
                style="color: #999;padding-left: 50px;">占比：{{ detailData.SchoolAdminNumRatio }}%</span>
            </el-form-item>
            <el-form-item label="其他人员（人）：">
              <span style="display: inline-block;width: 25%;">{{ detailData.OtherNum }}</span> <span
                style="color: #999;padding-left: 50px;">占比：{{ detailData.OtherNumRatio }}%</span>
            </el-form-item>
            <el-form-item label="总人数（人）：">
              <span>{{ detailData.TotalNum }}</span>
            </el-form-item>
            <el-form-item label="其中家长和学生人数占比：">
              <span style="display: inline-block;width: 25%;">{{ detailData.ParentStudentRatio }}%</span>
            </el-form-item>
            <el-form-item v-for="(item, index) in uploadFileData" :key="index">
              <template #label>
                <span>
                  {{ item.Name }}:
                </span>
              </template>
              <div class="fileFlex">
                <div v-for="(itemCate, indexCate) in item.categoryList" :key="itemCate.Id">
                  <span style="cursor: pointer;" @click="fileListDownload(itemCate, item.categoryList)">
                    {{ itemCate.Title }}{{ itemCate.Ext }}
                  </span>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </span>
      </template>
    </app-box>

    <!-- 审核与退回弹窗 -->
    <el-dialog v-model="recordVisible" :title="isRecord ? '审核' : '退回'" width="420px" draggable
      :close-on-click-modal="false">
      <el-form @submit.prevent ref="refRecord" :model="recordData" :rules="ruleRecord" status-icon>
        <el-form-item label="审核：" prop="statuz" label-width="100px" v-if="isRecord">
          <el-radio-group v-model="recordData.statuz">
            <el-radio value="1">通过</el-radio>
            <el-radio value="2">退回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="isRecord ? '退回原因：' : ''" :label-width="isRecord ? '100px' : '0px'" prop="explanation"
          v-if="(isRecord && recordData.statuz == 2) || !isRecord">
          <el-input type="textarea" v-model="recordData.explanation" :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入退回原因"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="recordVisible = false">取消</el-button>
          <el-button type="primary" @click="HandleRecordSubmit">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }" :url-list="viewPhotoList" />
  </div>
</template>
<style lang="scss" scoped>
.redColor {
  color: #F56C6C;
}

.dialog-content {
  height: 500px;
  overflow: auto;
}

.detailDialog {
  :deep(.el-form-item__label) {
    color: #409EFF;
  }

  :deep(.el-form-item) {
    margin-bottom: 5px !important;
    margin-top: 5px !important;
    border-bottom: 1px solid #f5f5f5;
  }
}
</style>