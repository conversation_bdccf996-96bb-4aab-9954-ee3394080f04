<script setup>
defineOptions({
    name: 'dangerchemicalsbasemodellist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Search, TopRight
} from '@element-plus/icons-vue'
import {
    DictionaryFind, DcbaseCatalogCmboget, DcbaseModelextensionFind, DcbaseModelextensiongetByid, DcbaseModelextensionSave, DcdcbaseModelextensionconfigSave
} from '@/api/dangerchemicals.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";//弹窗
// import { useRoute } from 'vue-router'
// const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10, sortModel: [{ SortCode: "Code", SortType: "ASC" }] })

const tableData = ref([])
const selectRows = ref([])//列表选择项
const tableTotal = ref(0)
const HandleSelectChange = (e) => {
    selectRows.value = e
}

onMounted(() => {
    HandleTableData()
})
const options = ref([
    { value: 'Name', label: '危化品名称', },
    { value: 'Model', label: '规格属性', },
    { value: 'age', label: '规格属性111', }
])

const dialogVisible = ref(false)


const dialogData = ref({})
const refForm = ref()
const ruleForm = {
    Name: [
        { required: true, message: '请输入模块名称', trigger: 'change' },
    ],
    UseUnitId: [
        { required: true, message: '请选择应用单位', trigger: 'change' },
    ],
    Usage: [
        { required: true, message: '请选择使用方式', trigger: 'change' },
    ],
}
//创建/修改选用组织  提交
const HandleSubmit = (hType) => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
    })
}


const filtersKey = ref('')
const filtersValue = ref('Name')
// 列表
const HandleTableData = () => {
    filters.value.Name = undefined;
    filters.value.Model = undefined;
    filters.value.age = undefined;

    if (filtersKey.value) {
        filters.value[filtersValue.value] = filtersKey.value;
    } else {
        filters.value[filtersValue.value] = undefined;
    }

    DcbaseModelextensionFind(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows } = res.data.data
            tableData.value = rows.data
            tableTotal.value = rows.dataCount
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
</script>
<template>
    <div>
        <div class="viewContainer">
            <el-row class="navFlexBox">
                <el-col>
                    <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                        <el-form-item label="" class="flexItem">
                            <el-select v-model="filters.Statuz" clearable placeholder="是否启用" @change="filtersChange"
                                style="width: 160px">
                                <el-option :value="1" label="是" />
                                <el-option :value="2" label="否" />
                            </el-select>
                        </el-form-item>
                        <el-form-item class="flexItem">
                            <el-input v-model.trim="filtersKey" placeholder="请输入" style="width: 280px"
                                class="input-with-select">
                                <template #prepend>
                                    <el-select v-model="filtersValue" style="width: 120px">
                                        <el-option v-for="item in options" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </template>
                            </el-input>
                        </el-form-item>
                        <!-- 直接输入 -->
                        <el-form-item class="flexItem">
                            <el-input v-model.trim="filters.Key" placeholder="请输入" style="width: 280px"></el-input>
                        </el-form-item>
                        <el-form-item class="flexItem">
                            <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                            <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                        </el-form-item>

                    </el-form>
                </el-col>
            </el-row>
            <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
                @selection-change="HandleSelectChange" header-cell-class-name="headerClassName">
                <el-table-column type="selection" width="50"></el-table-column>
                <el-table-column prop="Pname" label="危化品分类" min-width="120" show-overflow-tooltip>
                    <template #default="{ row }">
                        电话卡收到货款等哈斯柯达贺卡上科大算啦好卡打撒了哈扩大 行程卡你吃饭撒电力科技拉萨亮机卡打撒亮机卡
                    </template>
                </el-table-column>
                <el-table-column prop="Statuz" label="是否启用" min-width="100" align="center">
                    <template #default="{ row }">
                        <span v-if="row.Statuz == 1" style="color: #F56C6C;">是</span>
                        <span v-else-if="row.Statuz == 2">否</span>
                        <span v-else>设置中</span>
                    </template>
                </el-table-column>
                <el-table-column prop="Statuz" label="是否启用" min-width="100" align="center">
                    <template #default="{ row }">
                        {{ row.Statuz == 1 ? '是' : row.Statuz == 2 ? '是' : '设置中' }}
                    </template>
                </el-table-column>
                <el-table-column prop="Remark" label="备注" min-width="120" show-overflow-tooltip> </el-table-column>
                <el-table-column label="操作" fixed="right" min-width="90" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    </template>
                </el-table-column>
                <template #empty>
                    <el-empty description="没有数据"></el-empty>
                </template>
            </el-table>
            <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
                @handleChange="handlePage" />
        </div>

        <app-box v-model="dialogVisible" :width="680" :lazy="true" title=" 添加模块">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                    status-icon>
                    <el-form-item label="危化品名称：" v-if="hType == 1000">
                        <span>{{ dialogData.Name }}</span>
                    </el-form-item>
                    <el-form-item label="危化品分类：" prop="Pname" v-if="hType == 100">
                        <el-select v-model="dialogData.Pname" @change="pnameChange" style="width: 80%">
                            <el-option v-for="item in bogetList" :key="item.id" :label="item.text" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="危化品名称：" prop="BaseCatalogId" v-if="hType == 100">
                        <el-select v-model="dialogData.BaseCatalogId" style="width: 80%">
                            <el-option v-for="item in baseNameList" :key="item.Id" :label="item.Name"
                                :value="item.Id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="规格、属性：" prop="Model" v-if="hType == 100 || hType == 1000">
                        <el-input v-model="dialogData.Model" style="width: 80%"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit(hType)"> 提交 </el-button>
                </span>
            </template>
        </app-box>


    </div>
</template>
<style lang="scss" scoped></style>