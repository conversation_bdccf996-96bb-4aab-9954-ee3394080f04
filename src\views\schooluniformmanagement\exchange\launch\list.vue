<script setup>
defineOptions({
    name: 'launchlist'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search,
} from '@element-plus/icons-vue'
import {
    purchaseGetswappaged, PurchaseLaunch
} from '@/api/purchase.js'
import { ElMessage } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import { previousYearDate } from "@/utils/index.js";
import { useRoute } from 'vue-router'
const route = useRoute()
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const yearDateList = ref([])
const SupplierList = ref([])
const StatuzList = ref([])
const dialogVisible = ref(false)
const dialogData = ref({ Type: '1' })
const isAdd = ref(false)
const refForm = ref()
const ruleForm = {
    date: [
        { required: true, message: '请选择调换时间', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    yearDateList.value = previousYearDate()
    if (route.query.isTagRouter) {
        HandleTableData(true);
    }
})
onActivated(() => {
    if (!route.query.isTagRouter) {
        HandleTableData(true);
    }
})
// 修改
const HandleEdit = (row, e) => {
    isAdd.value = e
    if (e) {
        dialogData.value.data = []
    } else {
        dialogData.value.SwapBegin = row.SwapBegin.substring(0, 10)
        dialogData.value.SwapDeadline = row.SwapDeadline.substring(0, 10)
        dialogData.value.date = [row.SwapBegin.substring(0, 10), row.SwapDeadline.substring(0, 10)]
    }
    dialogData.value.Id = row.Id
    dialogVisible.value = true
}
//组织修改 提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        let paraData = {
            Id: dialogData.value.Id,
            SwapBegin: dialogData.value.SwapBegin,
            SwapDeadline: dialogData.value.SwapDeadline
        }

        PurchaseLaunch(paraData).then(res => {
            if (res.data.flag == 1) {
                ElMessage.success(res.data.msg || '发起成功')
                HandleTableData()
                dialogVisible.value = false
            } else {
                ElMessage.error(res.data.msg)
            }
        });
    })
}
// 调换起止日期
const HandleDateSearch = (date) => {
    //  console.log("date", date)
    if (date) {
        dialogData.value.SwapBegin = date[0]
        dialogData.value.SwapDeadline = date[1]
    } else {
        nextTick(() => {
            dialogData.value.data = []
        });
    }
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    purchaseGetswappaged(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, total, other } = res.data.data
            tableData.value = rows;
            tableTotal.value = Number(total)
            if (isFirst) {
                StatuzList.value = other.listStatuz || [];//状态
                SupplierList.value = other.listSupplier || [];//供应商
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.PurchaseYear = undefined
    filters.value.CompanyId = undefined
    filters.value.Statuz = undefined
    filters.value.Key = undefined
    HandleTableData()
}

// 分页
const handlePage = (val) => {
    HandleTableData()
}
const filtersChange = () => { HandleTableData() }

</script>
<template>
    <div class="viewContainer">
        <el-collapse>
            <el-collapse-item>
                <template #title>
                    操作提示 &nbsp;
                    <el-icon color="#E6A23C" :size="16">
                        <QuestionFilled />
                    </el-icon>
                </template>
                <ol class="rowFill">
                    <li> 点击【发起】按钮，发起校服调换。 </li>
                </ol>
            </el-collapse-item>
        </el-collapse>
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.PurchaseYear" clearable placeholder="年度" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in yearDateList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.CompanyId" clearable filterable placeholder="供应商"
                            @change="filtersChange" style="width: 160px">
                            <el-option v-for="item in SupplierList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.Statuz" clearable placeholder="状态" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in StatuzList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Key" clearable placeholder="合同批次"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName">
            <el-table-column prop="PurchaseYear" label="年度" min-width="100" align="center"></el-table-column>
            <el-table-column prop="PurchaseNo" label="合同批次" min-width="140" align="center"></el-table-column>
            <el-table-column prop="SwapDeadline" label="调换截止时间" min-width="160" align="center">
                <template #default="{ row }">
                    <span v-if="!row.SwapDeadline">--</span>
                    <span v-else-if="new Date(row.SwapDeadline).setHours(23, 59, 59, 0) < new Date()"
                        style="color: #F56C6C;">{{
                            row.SwapDeadline.substring(0, 10) }}</span>
                    <span v-else>{{ row.SwapDeadline.substring(0, 10) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="SupplierName" label="供应商" min-width="200"></el-table-column>
            <el-table-column prop="Statuz" label="状态" min-width="100" align="center">
                <template #default="{ row }">
                    {{ row.Statuz == 1 ? '待发起' : row.Statuz == 2 ? '已发起' : '已结束' }}
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="组织管理" min-width="100" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.Statuz == 1" type="primary" link @click="HandleEdit(row, true)">发起</el-button>
                    <el-button v-else-if="row.Statuz == 2" type="primary" link
                        @click="HandleEdit(row, false)">修改</el-button>
                    <span v-else>--</span>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" draggable />
        <el-dialog v-model="dialogVisible" :title="isAdd ? '发起调换日期' : '修改调换日期'" width="680px">
            <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="180px"
                status-icon>
                <el-form-item label="调换时间：" class="flexItem" prop="date">
                    <el-date-picker v-model="dialogData.date" type="daterange" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" range-separator="至" start-placeholder="起始日期" end-placeholder="截止日期"
                        style="width: 260px" @change="HandleDateSearch" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit">
                        {{ isAdd ? '发起' : '修改' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<style lang="scss" scoped>
.dialog-content {
    height: 500px;
    overflow: auto;
}
</style>