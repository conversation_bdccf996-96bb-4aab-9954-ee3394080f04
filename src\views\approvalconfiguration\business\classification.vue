<script setup>
defineOptions({
  name: 'businessclassification'
});
import { onMounted, onActivated, nextTick, ref } from 'vue'
import { Select } from '@element-plus/icons-vue'
import {
  GetGroupItemUserList, GetWaitAuditUserList, SetGroupUnitAuditUser
} from '@/api/workflow.js'
import { useRoute } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import AppBox from "@/components/Approve/AppBox.vue";
import { formatDate, tagsListStore } from "@/utils/index.js";
import { useUserStore } from '@/stores';
const userStore = useUserStore()
const route = useRoute()
const tableData = ref([])
const refTable = ref()
const dialogData = ref({ ListUserId: [] })
const refForm = ref()
const auditUserList = ref([])
const dialogVisible = ref(false)
const DicId = ref('')
const dictionaryName = ref('')
const ruleForm = {
  ListUserId: [
    { required: true, message: '请选择操作人', trigger: 'change' },
  ],
}
//加载数据
onMounted(() => {
  if (route.query.isTagRouter) {
    GetGroupItemUserListUser()
    GetWaitAuditUserListUser()
  }
})
onActivated(() => {
  tagsListStore(userStore.tagsList, route)
  nextTick(() => {
    if (!route.query.isTagRouter) {
      GetGroupItemUserListUser()
      GetWaitAuditUserListUser()
    }
  })
})

// 设置操作人
const HandleSet = (row, item) => {
  console.log(row)
  dialogVisible.value = true
  DicId.value = row.DicId
  dialogData.value.ListUserId = row.ListUserId || []
}
// 提交设置操作人
const HandleSubmit = (row) => {
  let formData = {
    ModuleId: route.query.ModuleId,
    ProcessId: route.query.ProcessId,
    ProcessNodeId: route.query.ProcessNodeId,
    GroupValue: DicId.value,
    ListUserId: dialogData.value.ListUserId,
    IsLook: 2,
  }
  refForm.value.validate((valid, fields) => {
    if (!valid && fields) return;
    SetGroupUnitAuditUser(formData).then((res) => {
      if (res.data.flag == 1) {
        ElMessage.success(res.data.msg || '提交成功')
        dialogVisible.value = false

        GetGroupItemUserListUser()
      } else {
        ElMessage.error(res.data.msg)
      }
    })
  })
}
// 选择设置操作人
const changeAuditUserId = (val) => {
  console.log(val)
}
// 获取权限设置列表
const GetGroupItemUserListUser = () => {
  let formData = {
    ProcessId: route.query.ProcessId,
    ProcessNodeId: route.query.ProcessNodeId,
    GroupId: route.query.GroupId
  }

  GetGroupItemUserList(formData).then((res) => {
    if (res.data.flag == 1) {
      const { rows } = res.data.data
      // console.log("rows", rows)
      dictionaryName.value = rows.Other?.dictionaryName || '字典名称'
      tableData.value = rows.data
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}
// 获取设置的操作人数据信息
const GetWaitAuditUserListUser = () => {
  let formData = {
    ProcessId: route.query.ProcessId,
    ProcessNodeId: route.query.ProcessNodeId,
  }
  GetWaitAuditUserList(formData).then((res) => {
    if (res.data.flag == 1) {
      const { rows, headers } = res.data.data
      // console.log("rows", rows)
      auditUserList.value = rows
    } else {
      ElMessage.error(res.data.msg)
    }
  })
}

</script>
<template>
  <el-table ref="refTable" :data="tableData" max-height="620" highlight-current-row border stripe
    header-cell-class-name="headerClassName">
    <el-table-column prop="DiName" :label="dictionaryName" width="200" align="center"></el-table-column>
    <el-table-column prop="UserNames" label="操作人" width="400" show-overflow-tooltip align="center">
    </el-table-column>
    <el-table-column label="操作" width="100" fixed="right" align="center">
      <template #default="{ row }">
        <el-button type="primary" link @click="HandleSet(row, item)">设置</el-button>
      </template>
    </el-table-column>
    <template #empty>
      <el-empty description="没有数据"></el-empty>
    </template>
  </el-table>
  <app-box v-model="dialogVisible" :width="680" :lazy="true" title="设置操作人">
    <template #content>

      <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="120px" status-icon>
        <el-form-item label="操作人员：" prop="ListUserId">
          <el-checkbox-group v-model="dialogData.ListUserId" @change="changeAuditUserId">
            <el-checkbox v-for="item in auditUserList" :key="item.UserId" :label="item.UserName" :value="item.UserId">
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
      </span>
    </template>
  </app-box>
</template>
<style lang="scss" scoped>
:deep(.el-checkbox-group) {
  // width: 100%;
}

:deep(.el-checkbox) {
  // width: 20%;
  // margin-right: 0;
}
</style>
