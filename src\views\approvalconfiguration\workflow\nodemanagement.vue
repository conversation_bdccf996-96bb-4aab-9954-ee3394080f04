<script setup>
defineOptions({
    name: 'nodemanagement'
});
import { onMounted, ref, nextTick, onActivated } from 'vue'
import {
    Refresh, Search, FolderAdd, QuestionFilled, Delete
} from '@element-plus/icons-vue'
import {
    FindnodeList, NodeinsertUpdate, Nodefindbyid, NodeDeletebyid, GetDataSource, GetNextProcessNode
} from '@/api/workflow.js'

import { ElMessage, ElMessageBox } from 'element-plus'
import TablePage from '@/components/TablePagination/index.vue' //分页
import AppBox from "@/components/Approve/AppBox.vue";
import router from '@/router'
import { useRoute } from 'vue-router'
const route = useRoute()
const filters = ref({ pageIndex: 1, pageSize: 10 })
const tableData = ref([])
const tableTotal = ref(0)
const refTable = ref()
const AduitTypeList = ref([])
const IsWithdrawList = ref([])
const ModuleIdList = ref([])
const NodeTypeList = ref([])
const ProcessLevelList = ref([])
const NextProcessNodeIdsList = ref([])
const nextProcessNodeIds = ref([])
const yesNoList = ref([{ value: '1', label: '是' }, { value: '2', label: '否' }])
const dialogVisible = ref(false)
const dialogData = ref({ SubmitButtonName: '转交下一步' })
const editId = ref()
const selectRows = ref([])
const refForm = ref()
const ruleForm = {
    ModuleId: [
        { required: true, message: '请选择模块', trigger: 'change' },
    ],
    NodeName: [
        { required: true, message: '请输入节点名称', trigger: 'change' },
    ],
    NodeShowName: [
        { required: true, message: '请输入节点显示名称', trigger: 'change' },
    ],
    NodeType: [
        { required: true, message: '请选择节点类型', trigger: 'change' },
    ],
    ProcessLevel: [
        { required: true, message: '请选择节点级别', trigger: 'change' },
    ],
    NextProcessNodeIds: [
        { required: true, message: '请选择指定人所在节点', trigger: 'change' },
    ],
    AduitUserType: [
        { required: true, message: '请选择指定方式', trigger: 'change' },
    ],
}

//加载数据
onMounted(() => {
    if (route.query.isTagRouter) {
        HandleTableData(true);
        GetDataSourceUser()
    }
})
onActivated(() => {
    nextTick(() => {
        if (!route.query.isTagRouter) {
            HandleTableData(true);
            GetDataSourceUser()
        }
    })
})
const HandleSelectChange = (selection) => {
    selectRows.value = selection
}
// 创建
const HandleAdd = () => {
    editId.value = undefined
    dialogVisible.value = true
    nextTick(() => {
        refForm.value.resetFields()
        dialogData.value = {
            IsDepartProcess: '2',
            AduitType: '1',
            UseSameList: '1',
            IsAllowExport: '2',
            IsWithdraw: '1',
            IsLockProjectAmount: '2',
            IsUsetListSum: '1',
            BackIsSendMsg: '2',
            IsOpen: '2',
            NextProcessNodeIds: [],
            NextIsSendMsg: '2',
            IsBegin: '2',
            AuditBackCanDel: '1',
            IsBringOut: '2',
            IsUsePreData: '2',
            TreatHandle: '待处理',
            StopHandle: '已处理',
            SubmitButtonName: '转交下一步',
            Sort: 0,
            TreatHandleUrl: 'approvalconfiguration/pages/node/pendinglist',
            StopHandleUrl: 'approvalconfiguration/pages/node/processedlist'
        }
    })

}
// 修改
const HandleEdit = (row) => {
    NodefindbyidUser(row.Id)
    dialogVisible.value = true
}
//删除
const HandleDel = () => {
    let row = selectRows.value[0]
    // console.log(row)
    ElMessageBox.confirm(`确定删除节点【${row.NodeName}】吗?`)
        .then(() => {
            NodeDeletebyid({ id: row.Id }).then((res) => {
                if (res.data.flag == 1) {
                    ElMessage.success(res.data.msg || '删除成功')
                    HandleTableData()
                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        })
        .catch((err) => {
            console.info(err)
        })
}
// 表单设置
const HandleSetting = (row) => {
    console.log(row)
    router.push({ path: "../formdraggable/index", query: { id: row.Id } })
}
// 列表设置
const HandleList = (row) => {
    console.log(row)
    // ProcessNodeId:节点Id  ModuleId：模块Id
    router.push({ path: "./node/listconfiguration", query: { ModuleId: row.ModuleId, ProcessNodeId: row.Id } })
}
// 联动设置
const HandleLinkage = (row) => {
    console.log(row)
    // ProcessNodeId:节点Id  ModuleId：模块Id
    // 联动数据配置linkagecfglist
    // 联动配置linkagecfgset
    router.push({ path: "./node/linkagecfglist", query: { ModuleId: row.ModuleId, ProcessNodeId: row.Id } })
}
// 条件控制
const HandleCondition = (row) => {
    console.log(row)
    // ProcessNodeId:节点Id  ModuleId：模块Id
    // 联动数据配置linkagecfglist
    // 联动配置linkagecfgset
    router.push({ path: "./node/conditionlist", query: { ModuleId: row.ModuleId, ProcessNodeId: row.Id } })
}
//创建/修改选用组织  提交
const HandleSubmit = () => {
    refForm.value.validate((valid, fields) => {
        if (!valid && fields) return;
        NodeinsertUpdateUser()
    })
}

//列表
const HandleTableData = (isFirst) => {
    filters.value.isFirst = isFirst
    FindnodeList(filters.value).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            tableData.value = rows.data;
            tableTotal.value = rows.dataCount
            if (isFirst) {
                AduitTypeList.value = other.listAduitType || [];//审批方式 
                IsWithdrawList.value = other.listIsWithdraw || [];//是否可撤回
                ModuleIdList.value = other.listModuleId || [];//模块
                NodeTypeList.value = other.listNodeType || [];//节点类型
                ProcessLevelList.value = other.listProcessLevel || [];//流程级别 
            }
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
// 搜索
const HandleSearch = () => {
    filters.value.pageIndex = 1
    HandleTableData()
}
// 重置
const HandleReset = () => {
    filters.value.pageIndex = 1
    filters.value.ModuleId = undefined
    filters.value.Name = undefined
    HandleTableData()
}
// 分页
const handlePage = (val) => {
    HandleTableData()
}
// 选择模块获取节点
const getModuleIdChange = (e) => {
    GetNextProcessNode({ moduleId: e, processNodeId: dialogData.value.Id || 0 }).then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            // nextProcessNodeIds.value = []
            NextProcessNodeIdsList.value = rows || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const filtersChange = () => { HandleTableData() }

// 添加/修改提交
const NodeinsertUpdateUser = () => {
    if (dialogData.value.StrUseGroupValue) {
        dialogData.value.UseGroupValue = dialogData.value.StrUseGroupValue.split('_')[0]
    } else {
        dialogData.value.UseGroupValue = '2'
    }
    let formData = JSON.parse(JSON.stringify(dialogData.value))
    formData.NextProcessNodeIds = dialogData.value.NextProcessNodeIds.join(',')
    NodeinsertUpdate(formData).then((res) => {
        if (res.data.flag == 1) {
            ElMessage.success(res.data.msg || '修改成功')
            dialogVisible.value = false
            HandleTableData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
// 详情
const NodefindbyidUser = (id) => {
    Nodefindbyid({ id: id }).then((res) => {
        if (res.data.flag == 1) {
            const { rows, headers } = res.data.data
            dialogData.value = {
                Id: rows.Id,
                ModuleId: rows.ModuleId,
                NodeName: rows.NodeName,
                NodeShowName: rows.NodeShowName,
                StopHandle: rows.StopHandle,
                TreatHandle: rows.TreatHandle,
                UseSameList: String(rows.UseSameList),
                IsUsePreData: rows.IsUsePreData ? String(rows.IsUsePreData) : '2',
                NodeType: String(rows.NodeType),
                IsDepartProcess: String(rows.IsDepartProcess),
                ProcessLevel: String(rows.ProcessLevel),
                AduitType: String(rows.AduitType),
                IsAllowExport: String(rows.IsAllowExport),
                IsWithdraw: String(rows.IsWithdraw),
                IsLockProjectAmount: String(rows.IsLockProjectAmount),
                IsUsetListSum: String(rows.IsUsetListSum),
                IsOpen: String(rows.IsOpen || '2'),
                BackIsSendMsg: String(rows.BackIsSendMsg),
                AduitUserType: String(rows.AduitUserType),
                NextIsSendMsg: String(rows.NextIsSendMsg),
                IsBegin: String(rows.IsBegin),
                AuditBackCanDel: String(rows.AuditBackCanDel),
                IsBringOut: String(rows.IsBringOut),
                AmountName: rows.AmountName || undefined,
                SubmitButtonName: rows.SubmitButtonName,
                RoleName: rows.RoleName,
                Sort: rows.Sort,
                TreatHandleUrl: 'approvalconfiguration/pages/node/pendinglist',
                StopHandleUrl: 'approvalconfiguration/pages/node/processedlist',
                TreatTipMsg: rows.TreatTipMsg,
                StopTipMsg: rows.StopTipMsg,
                StagingButton: rows.StagingButton,
            }
            if (!rows.UseGroupValue || rows.UseGroupValue == '0' || rows.UseGroupValue == '2') {
                dialogData.value.StrUseGroupValue = undefined
            } else {
                dialogData.value.StrUseGroupValue = String(rows.StrUseGroupValue)
            }
            if (rows.NextProcessNodeIds) {
                dialogData.value.NextProcessNodeIds = rows.NextProcessNodeIds.split(',')
            } else {
                dialogData.value.NextProcessNodeIds = []
            }
            NextProcessNodeIdsList.value = headers || []
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}
const dicSelectList = ref([])//下拉框数据源
// 获取字典数据源
const GetDataSourceUser = () => {
    GetDataSource().then(res => {
        if (res.data.flag == 1) {
            const { rows, other } = res.data.data
            let dicList = rows || []
            dicSelectList.value = dicList.filter((t) => {
                let val = t.value.split('_')[1]
                return val == '2' || val == '3'
            })
            // dicSelectList.value.unshift({ value: 0, label: '不启用' })
        } else {
            ElMessage.error(res.data.msg)
        }
    });
}
</script>
<template>
    <div class="viewContainer">
        <el-row class="navFlexBox">
            <el-col>
                <el-form @submit.prevent :inline="true" :model="filters" class="flexBox">
                    <el-form-item class="flexItem">
                        <el-button type="success" :icon="FolderAdd" @click="HandleAdd">创建</el-button>
                        <el-button type="danger" :icon="Delete" @click="HandleDel"
                            :disabled="selectRows.length != 1">删除</el-button>
                    </el-form-item>
                    <div class="verticalIdel"></div>
                    <el-form-item label="" class="flexItem">
                        <el-select v-model="filters.ModuleId" clearable placeholder="模块名称" @change="filtersChange"
                            style="width: 160px">
                            <el-option v-for="item in ModuleIdList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="" class="flexItem">
                        <el-input v-model.trim="filters.Name" clearable placeholder="节点名称"
                            style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item class="flexItem">
                        <el-button type="primary" :icon="Search" @click="HandleSearch">搜索</el-button>
                        <el-button :icon="Refresh" @click="HandleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <el-table ref="refTable" :data="tableData" highlight-current-row border stripe
            header-cell-class-name="headerClassName" @selection-change="HandleSelectChange">
            <el-table-column type="selection" width="50"></el-table-column>
            <el-table-column prop="ModuleName" label="模块名称" min-width="140"></el-table-column>
            <el-table-column prop="NodeName" label="节点名称" min-width="160"></el-table-column>
            <el-table-column prop="NodeShowName" label="节点显示名称" min-width="160"></el-table-column>
            <el-table-column prop="StrNodeType" label="类型" min-width="90" align="center"></el-table-column>
            <el-table-column prop="StrProcessLevel" label="流程级别" min-width="90" align="center"></el-table-column>
            <el-table-column prop="StrIsWithdraw" label="是否可撤回" min-width="110" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" width="360" align="center">
                <template #default="{ row }">
                    <el-button type="primary" link @click="HandleEdit(row)">修改</el-button>
                    <el-button type="primary" link @click="HandleSetting(row)">表单设置</el-button>
                    <el-button type="primary" link @click="HandleList(row)">列表设置</el-button>
                    <el-button type="primary" link @click="HandleLinkage(row)">联动设置</el-button>
                    <el-button type="primary" link @click="HandleCondition(row)">条件控制</el-button>
                    <!-- <el-button type="primary" link @click="HandleDel(row)">删除</el-button> -->
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="没有数据"></el-empty>
            </template>
        </el-table>
        <table-page :total="tableTotal" v-model:pageIndex="filters.pageIndex" v-model:pageSize="filters.pageSize"
            @handleChange="handlePage" />
        <app-box v-model="dialogVisible" :height="600" :width="800" :lazy="true" :title="editId ? '修改节点' : '添加节点'">
            <template #content>
                <el-form @submit.prevent ref="refForm" :model="dialogData" :rules="ruleForm" label-width="260px"
                    status-icon style="padding-right: 100px;">
                    <el-form-item label="模块名称：" prop="ModuleId" style="margin-bottom: 15px !important;">
                        <el-select v-model="dialogData.ModuleId" @change="getModuleIdChange">
                            <el-option v-for="item in ModuleIdList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="节点名称：" prop="NodeName" style="margin-bottom: 15px !important;">
                        <el-input v-model="dialogData.NodeName"></el-input>
                    </el-form-item>
                    <el-form-item label="节点显示名称：" prop="NodeShowName" style="margin-bottom: 15px !important;">
                        <el-input v-model="dialogData.NodeShowName"></el-input>
                    </el-form-item>
                    <el-form-item label="节点类型：" prop="NodeType" style="margin-bottom: 15px !important;">
                        <el-radio-group v-model="dialogData.NodeType">
                            <el-radio v-for="item in NodeTypeList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否开始节点：">
                        <el-radio-group v-model="dialogData.IsBegin">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="待处理菜单名称：">
                        <el-input v-model="dialogData.TreatHandle"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="待处理菜单地址：">
                        <el-input v-model="dialogData.TreatHandleUrl" disabled></el-input>
                    </el-form-item> -->
                    <el-form-item label="待处理页面提示文字：">
                        <el-input v-model="dialogData.TreatTipMsg" type="textarea"
                            :autosize="{ minRows: 2, maxRows: 4 }" placeholder="页面顶部操作提示文字" />
                    </el-form-item>
                    <el-form-item label="已处理菜单名称：" v-if="dialogData.IsBegin == 2">
                        <el-input v-model="dialogData.StopHandle"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="已处理菜单地址：" v-if="dialogData.IsBegin == 2">
                        <el-input v-model="dialogData.StopHandleUrl" disabled></el-input>
                    </el-form-item> -->
                    <el-form-item label="已处理页面提示文字：" v-if="dialogData.IsBegin == 2">
                        <el-input v-model="dialogData.StopTipMsg" :autosize="{ minRows: 2, maxRows: 4 }" type="textarea"
                            placeholder="页面顶部操作提示文字" />
                    </el-form-item>
                    <el-form-item label="待处理列表启用分组筛选：">
                        <template #label>
                            <el-tooltip class="item" effect="dark"
                                content="如果启用此功能，需要在《流程管理》内‘分组设置’配置分组名称与字典类型，（字典类型） 需与此处选择一致" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> 待处理列表启用分组筛选： </span>
                        </template>
                        <el-select v-model="dialogData.StrUseGroupValue" clearable>
                            <el-option v-for="item in dicSelectList" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否使用相同配置列表：" v-if="dialogData.IsBegin == 2">
                        <template #label>
                            <el-tooltip class="item" effect="dark" content="除按钮外其它列不需要再次配置" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> 是否使用相同配置列表： </span>
                        </template>
                        <el-radio-group v-model="dialogData.UseSameList">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否为部门审批：">
                        <el-radio-group v-model="dialogData.IsDepartProcess">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="流程级别：" prop="ProcessLevel">
                        <el-radio-group v-model="dialogData.ProcessLevel">
                            <el-radio v-for="item in ProcessLevelList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="审批方式：">
                        <el-radio-group v-model="dialogData.AduitType">
                            <el-radio v-for="item in AduitTypeList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否允许导出Excel：">
                        <el-radio-group v-model="dialogData.IsAllowExport">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否可撤回：">
                        <el-radio-group v-model="dialogData.IsWithdraw">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="提交时是否锁金额并验证清单总和：">
                        <el-radio-group v-model="dialogData.IsLockProjectAmount">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="金额是否使用清单总和：">
                        <el-radio-group v-model="dialogData.IsUsetListSum">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否开启指定人：">
                        <el-radio-group v-model="dialogData.IsOpen">
                            <el-radio value="1"> 开启</el-radio>
                            <el-radio value="2"> 关闭</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="指定人所在节点：" prop="NextProcessNodeIds" v-if="dialogData.IsOpen == 1">
                        <el-select v-model="dialogData.NextProcessNodeIds" multiple>
                            <el-option v-for="item in NextProcessNodeIdsList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="指定方式：" prop="AduitUserType" v-if="dialogData.IsOpen == 1">
                        <el-radio-group v-model="dialogData.AduitUserType">
                            <!-- <el-radio value="1"> 所有人（该节点授权的的所有人都需要审批）无需下拉框</el-radio>
                            <el-radio value="2"> 用户手工指定( 单人 需要单选下拉框，多人 需要多选框)</el-radio> -->
                            <div class="radio-item">
                                <el-radio value="1">所有人</el-radio>
                                <span class="comment">该节点授权的所有人都需要审批无需下拉框</span>
                            </div>
                            <div class="radio-item">
                                <el-radio value="2">用户手工指定</el-radio>
                                <span class="comment">单人需要单选下拉框，多人需要多选框</span>
                            </div>
                        </el-radio-group>

                    </el-form-item>
                    <el-form-item label="退回是否发送短信：">
                        <el-radio-group v-model="dialogData.BackIsSendMsg">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="转交下一步是否发送短信：">
                        <el-radio-group v-model="dialogData.NextIsSendMsg">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="审核不通过退回源头是否可删除：">
                        <el-radio-group v-model="dialogData.AuditBackCanDel">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="业务内容：">
                        <el-input v-model="dialogData.RoleName"></el-input>
                    </el-form-item>
                    <el-form-item label=" 填报页面是否带出主表数据：">
                        <el-radio-group v-model="dialogData.IsBringOut">
                            <el-radio v-for="item in yesNoList" :key="item.value" :value="item.value">
                                {{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="暂存按钮文字配置：">
                        <template #label>
                            <el-tooltip v-if="dialogData.NodeType == 2" class="item" effect="dark"
                                content="主要用于审批页面：配置文字后即显示该文字按钮。例如配置‘暂存’，待审核审批页面就显示暂存按钮" placement="top">
                                <div>
                                    <el-icon color="#E6A23C" class="tipIcon">
                                        <QuestionFilled />
                                    </el-icon>
                                </div>
                            </el-tooltip>
                            <span> 暂存按钮文字配置： </span>
                        </template>
                        <el-input v-model="dialogData.StagingButton"></el-input>
                    </el-form-item>
                    <el-form-item label="转交下一步按钮显示名称：">
                        <el-input v-model="dialogData.SubmitButtonName"></el-input>
                    </el-form-item>
                    <el-form-item label="转交下一步提示文字：" v-if="dialogData.NodeType == 1">
                        <el-input v-model="dialogData.NextTipMsg"></el-input>
                    </el-form-item>
                    <el-form-item label="合计名称：">
                        <el-input v-model="dialogData.AmountName"></el-input>
                    </el-form-item>
                    <el-form-item label="排序值：">
                        <el-input type="number" v-model="dialogData.Sort"></el-input>
                    </el-form-item>
                </el-form>
            </template>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="HandleSubmit"> 提交 </el-button>
                </span>
            </template>
        </app-box>
    </div>
</template>
<style lang="scss" scoped>
.redColor {
    color: #F56C6C;
}


:deep(.el-dialog) {

    .el-form-item {
        margin-bottom: 5px !important;
    }
}


.radio-item {
    display: flex;
    align-items: center;
}

.comment {
    color: #999;
    font-size: 14px;
    margin-left: 10px;
}
</style>